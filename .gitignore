.bmad-core/

# dependencies
node_modules/

# expo
.expo/
dist/
web-build/
expo-env.d.ts

# native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macos
.DS_Store
*.pem

# local env files
.env*.local
.env

# typescript
*.tsbuildinfo

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
# Environment variables
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific

# Task files
# tasks.json


.env.local

# Sentry local auth tokens (DO NOT COMMIT)
android/sentry.properties
ios/sentry.properties

# Template projects
template-projects/

# Development tools and config
.claude/
.kiro/
.serena/
.playwright-mcp/
.cleanup-logs/
.github/

# Documentation and planning
docs/

# Development assets and scripts
scripts/
aromachat-icons/
database/
root/

.gemini/
gha-creds-*.json

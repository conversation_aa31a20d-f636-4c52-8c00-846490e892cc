{"expo": {"name": "AromaCHAT GO", "slug": "aromachat-go", "scheme": "aromachat", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "automatic", "jsEngine": "hermes", "platforms": ["ios", "android", "web"], "runtimeVersion": "1.0.0", "splash": {"image": "./assets/splash-aromachat.png", "resizeMode": "contain", "backgroundColor": "#1C1C1E"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "icon": "./assets/icons/Assets.xcassets/AppIcon.appiconset/1024.png", "bundleIdentifier": "com.aromachat.app", "infoPlist": {"NSFaceIDUsageDescription": "Use Face ID to securely sign in to AromaCHAT"}}, "android": {"icon": "./assets/icons/android/mipmap-xxxhdpi/aromachat_icon.png", "adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#6E56CF"}, "package": "com.aromachat.app", "softwareKeyboardLayoutMode": "resize", "permissions": ["USE_BIOMETRIC", "USE_FINGERPRINT"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#1C1C1E", "image": "./assets/splash-aromachat.png"}], "expo-localization", "expo-font", "expo-web-browser", "expo-local-authentication", ["@sentry/react-native/expo", {"url": "https://sentry.io/", "project": "react-native", "organization": "aromachat"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "6c5eb81f-fe82-44de-bf5c-cde6bae1bca6"}}}}
# Clerk Authentication
EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_YOUR_PUBLISHABLE_KEY_HERE

# PostHog Analytics
EXPO_PUBLIC_POSTHOG_API_KEY=phc_your_api_key_here
EXPO_PUBLIC_POSTHOG_HOST=https://us.i.posthog.com

# Supabase Configuration
EXPO_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Instructions:
# 1. Copy this file to .env in the root directory
# 2. Replace YOUR_PUBLISHABLE_KEY_HERE with your actual Clerk publishable key
# 3. Get your keys from: https://dashboard.clerk.com/
# 4. Get your PostHog API key from: https://app.posthog.com/project/settings
# 5. Use the correct host for your PostHog instance (US: https://us.i.posthog.com, EU: https://eu.i.posthog.com)
# 
# For production deployment:
# - Use a production publishable key (starts with pk_live_)
# - Never commit real keys to version control
# - Use environment variables in your deployment platform

# Documentation Examples & Guidelines

## Example Step Documentation Structure

Here's what a well-documented step should look like:

### Complete Example Format (All 7 Required Sections):
```markdown
# Health Concern Step Analysis

## 1. Input Handling

**Reasoning:**
- **Primary Component**: HealthConcernScreen in `src/features/create-recipe/components/screens/health-concern-screen.tsx:45`
- **Input Component**: TextInput with controlled input pattern
- **Validation**: Schema validation via `recipe-schemas.ts:12`
- **Validation Rules**: Minimum 10 characters enforced at schema level
- **Custom Hooks**: Uses input validation and state management hooks

**Conclusion:** Input handled by controlled TextInput component with min 10 char validation enforced at schema level.

## 2. Data Persistence

**Reasoning:**
- **Store Action**: `setHealthConcern` action in `recipe.slice.ts:23`
- **Store Access**: `useRecipeData` hook in `combined-store.ts:39`
- **Data Interface**: Typed interface from `recipe.types.ts:15`
- **Storage Location**: Zustand store under `healthConcern` property
- **State Management**: Centralized state with typed interfaces

**Conclusion:** Persisted in Zustand store under healthConcern property with typed interface from recipe.types.ts:15.

## 3. Navigation Flow

**Reasoning:**
- **Navigation Management**: `useRecipeNavigation` hook in `use-recipe-navigation.ts:34`
- **Trigger Function**: `handleNext` function in `health-concern-screen.tsx:67`
- **Advancement Condition**: After validation passes
- **Navigation Pattern**: Linear progression to demographics step
- **State Control**: Zustand store navigation state with validation gates

**Conclusion:** Linear navigation to demographics step controlled by Zustand store navigation state with validation gates.

## 4. Pre-navigation Processing

**Reasoning:**
- **API Calls**: None at this step
- **Data Transformation**: No transformations occur
- **Validation Processing**: Data validation handled by schema in `recipe-schemas.ts:12`
- **Analytics**: Tracked via `recipe-analytics.ts:45`
- **Business Logic**: Minimal processing, primarily validation

**Conclusion:** Minimal processing - validation and analytics tracking only, no business logic execution.

## 5. End-to-End Data Architecture for This Step

**NOTE:** This section requires a "Conclusion-First, Evidence-Second" analytical format, as specified in the main prompt's "Analytical Focus" guidelines. This example demonstrates the required depth.

**Analysis & Conclusion:** When all properties are enriched, `calculatePostEnrichmentScores` is called, but only the `final_relevance_score` and `specialization_score` are extracted and applied to the properties in the store. The rest of the returned object (`suggested_oils`, `safety_library`, and `safety_library_formatted`) is discarded at this stage.

**Evidence & Chain of Reasoning:**
- **Function Return Value:** The `calculatePostEnrichmentScores` function returns a `FinalAPIPayload` object (e.g., `features/create-recipe/utils/post-enrichment-scoring.ts`) containing: `suggested_oils` (a full array), `safety_library` (an object), and `safety_library_formatted` (a string).
- **State Logic:** This function is called within the `updatePropertyWithEnrichedOils` action in the store (e.g., `features/create-recipe/store/recipe.slice.ts`).
- **Data Extraction:** Inside this action, a `hybridScoreMap` is created by mapping *only* the `final_relevance_score` and `specialization_score` from the result.
- **Final State Update:** The `therapeuticProperties` state is then updated *only* with these two new scores. The full `suggested_oils` array and other data from the `scoringResult` object are not used in this specific state update and are effectively discarded.

## 6. File Dependencies

**Reasoning:**
- **Parent Files**:
  - `_layout.tsx` wraps screen in navigation stack
- **Child Components**:
  - TextInput from React Native
  - Validation components from `recipe-schemas.ts`
- **Related Files**:
  - `recipe.types.ts` for interfaces
  - `recipe.constants.ts` for constraints
- **Shared Dependencies**:
  - Zustand store for state management
  - Schema validation utilities

**Conclusion:** Minimal dependencies - primarily uses shared validation and type definitions.

## 7. Previous and Next Steps to be Documented

- **Previous:** None (first step)
- **Current:** create-recipe-docs/steps/doc-001-step-health-concern-analysis.md
- **Next:** Demographics step (major user-facing step per list-major-minor-steps.md)
````

## Common Patterns to Document

### State Management Patterns

  - Look for `use[Feature]Store()` hooks
  - Check `[feature].slice.ts` files for state shape
  - Document selector hooks and their return types

### Navigation Patterns

  - Find `use-recipe-navigation.ts` or similar navigation hooks
  - Look for `canNavigateToStep()` validation functions
  - Document step advancement conditions

### API Integration Patterns

  - Check `services/` folder for API calls
  - Look for streaming endpoints and SSE connections
  - Document error handling and retry logic

### Component Patterns

  - Find screen components in `components/screens/[step-name]/`
  - Look for shared components from `/shared/components/`
  - Document prop interfaces and component composition

## File Path Patterns to Follow

### Always Reference These Locations:

  - **Route File**: `src/app/(drawer)/(tabs)/create-recipe/[step].tsx`
  - **Screen Component**: `src/features/create-recipe/components/screens/[step-screen].tsx`
  - **Store**: `src/features/create-recipe/store/combined-store.ts`
  - **Types**: `src/features/create-recipe/types/recipe.types.ts`
  - **Constants**: `src/features/create-recipe/constants/recipe.constants.ts`

### Look for Step-Specific Files:

  - Hooks: `src/features/create-recipe/hooks/use-[step]-*.ts`
  - Services: `src/features/create-recipe/services/[relevant-service].ts`
  - Validation: `src/features/create-recipe/schemas/recipe-schemas.ts`

## What NOT to Include

❌ **Don't include code blocks** - reference file paths and line numbers instead
❌ **Don't write long explanations** - keep conclusions concise
❌ **Don't make assumptions** - if you can't find concrete evidence, use bracketed placeholders like `[ComponentName]` or `[EvidenceNotFound]`
❌ **Don't duplicate architecture info** - assume reader has the architecture doc
❌ **Don't speculate about implementation** - only document what you can verify by reading actual files

## Evidence-Based Documentation Requirements

✅ **Always read actual files** - verify every statement with concrete file evidence
✅ **Provide specific line numbers** - reference exact locations where patterns are implemented
✅ **Trace data flows systematically** - follow imports, function calls, and state updates through the codebase
✅ **Use bracketed placeholders** - when evidence is not found, explicitly state `[FunctionNotFound]` or `[PatternNotVerified]`
✅ **Verify component names and paths** - ensure all references are accurate by examining the source code

## Reasoning Before Conclusion Pattern

Every subsection **(except Section 5)** must follow this pattern:

1.  **Reasoning**: "Found X in file Y at line Z. Uses pattern A because B."
2.  **Conclusion**: "Brief summary of findings and key technical locations."

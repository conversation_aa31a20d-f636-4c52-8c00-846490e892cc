####
If the user asked you to run this prompt, first you need to ask and confirm the (Feature Identifier:) and (Current Step Identifier:).

Do not proceed if the user did not provide that information. If you are 100% sure that the user provided the correct information, then you can proceed.

Here are the "super macro-zoom" constants that it is needed to know for the task:

* **Feature Identifier:**
    e.g,: `create-recipe`

* **Current Step Identifier:**
    e.g,:`health-concern`

###
Analyze and document the recipe creation multi-step flow architecture in the AromaChat application, focusing in detail on the **[CURRENT STEP TO DOCUMENT]**. This is a **SYSTEMATIC DATA FLOW ANALYSIS** that requires **EVIDENCE-BASED DOCUMENTATION** with **NO ASSUMPTIONS**.

**CRITICAL REQUIREMENTS:**
-   **Evidence-Only**: Every statement must be backed by actual code/file evidence with specific file paths and line numbers
-   **No Assumptions**: If you cannot find concrete evidence in the codebase, use bracketed placeholders like `[ComponentName]` or `[MethodNotFound]`
-   **Systematic Investigation**: Read actual files, search for patterns, trace data flows through the codebase
-   **Technical Artifacts**: Reference specific file paths, component names, function calls, and implementation details
-   **Complete Coverage**: Persist until all checklist items are complete with concrete evidence

For each aspect below, provide comprehensive technical documentation, referencing specific file paths, component names, and related resources. Follow the sub-sections and bullet items as outlined. Do not provide code examples; instead, reference file paths, line numbers (where possible), and details on where to find the relevant implementation or further information.

**INVESTIGATION PROCESS:** Think hard step by step about each bullet, referencing technical artifacts, before compiling your answer. Chain your reasoning before final documentation so that every assertion is fully supported by actual code evidence.
---
## [CURRENT STEP TO DOCUMENT] Step Analysis
### 1. Input Handling
-   Identify the precise input component or screen used for the **[CURRENT STEP TO DOCUMENT]**. Provide its file path and any custom hooks or utilities invoked for input.
-   Document all validation rules, constraints, and where these are enforced. Reference any validation schema (e.g., Yup/JSON Schema) file and line number if apparent.
-   Show the data structure/schema for the data related to **[CURRENT STEP TO DOCUMENT]**. Reference where the shape/type/interface for this data is defined (e.g., `/types/[StepName]Types.d.ts`).
-   Identify autocomplete, search, suggestion, or toggling functionality, listing files and relevant methods or hooks handling this.
### 2. Data Persistence
-   Document where the data for **[CURRENT STEP TO DOCUMENT]** is held after user input (e.g., React local state, Redux store, context, etc.). Reference the specific slices, atoms, context objects, or state hook lines.
-   Provide the exact storage format, data fields, and corresponding type/interface definition locations.
-   Identify any caching, temporary storage, or middleware involved (give file and line references).
-   Document data validation prior to storage, including references to functions or schemas responsible.
### 3. Navigation Flow
-   Map the navigation path from the **[CURRENT STEP TO DOCUMENT]** input to the following step, referencing the navigator/screen stack setup (e.g., react-navigation stack, `routes.js`, etc.).
-   Identify the trigger condition(s) for advancing (e.g., button click with non-empty, valid data), referencing handlers and event files.
-   Document any intermediate loading, success, or feedback screens and their component definitions.
-   Reference the navigation logic’s implementation, listing function/component/file names and paths.
### 4. Pre-navigation Processing
-   Document any data transformation, normalization, or composition occurring prior to advancing, including function names and file paths.
-   Identify API calls, validation checks, or core business logic executed at this juncture, listing file paths and logical sequence.
-   Reference any error handling or fallback UI/logging mechanisms (components/middleware/services).
-   Document analytics or event tracking, referencing the file and method/event names.
---
## Complete System Data Flow

---
### Analytical Focus for Section 5: Trace the Data, Not Just the Files
**Note:** Section 5 requires a specific analytical style, different from the other sections. Your primary goal here is not just to list files but to **trace the complete data flow** and its consequences.

You must connect the dots and:
1.  **Follow the Payload:** Show the path a piece of data takes from its origin (this step) to a hook, into a service, to an API, back from the API, and finally into the state store.
2.  **Analyze Transformations:** Reference any utility functions (e.g., `calculatePostEnrichmentScores`) or service-layer logic that transforms this data.
3.  **Identify Used vs. Discarded Data:** This is the most critical part. When a function or API returns a large object (like a `FinalAPIPayload`), you MUST explicitly document which specific fields are actually extracted and used (e.g., `final_relevance_score`) and which fields are **discarded or ignored**.
4.  **Adopt a "Conclusion-First" Style FOR THIS SECTION:** Start your reasoning with the analytical conclusion, then provide the step-by-step evidence.

**Golden Standard Example for Section 5:**
> **Analysis & Conclusion:** When all properties are enriched, `calculatePostEnrichmentScores` is called, but only the `final_relevance_score` and `specialization_score` are extracted and applied to the properties in the store. The rest of the returned object (`suggested_oils`, `safety_library`, and `safety_library_formatted`) is discarded at this stage.
>
> **Evidence & Chain of Reasoning:**
> 1.  **Function Return Value:** The `calculatePostEnrichmentScores` function returns a `FinalAPIPayload` object containing: `suggested_oils`, `safety_library`, and `safety_library_formatted`.
> 2.  **State Logic:** This function is called within the `updatePropertyWithEnrichedOils` action in the `recipe.slice.ts`.
> 3.  **Data Extraction:** Inside this action, a `hybridScoreMap` is created by mapping *only* the `final_relevance_score` and `specialization_score` from the result.
> 4.  **Final State Update:** The `therapeuticProperties` state is then updated *only* with these two new scores. The full `suggested_oils` array, `safety_library`, and `safety_library_formatted` from the `scoringResult` object are not used in this state update and are effectively discarded.
---

### 5. End-to-End Data Architecture for This Step
-   Provide a textual or visual description (text format) mapping the **[CURRENT STEP TO DOCUMENT]** input’s path from user entry throughout the recipe creation flow. Reference each file/step along the journey.
-   Explain how the data from **[CURRENT STEP TO DOCUMENT]** impacts **ONLY THE NEXT STEP** downstream (e.g., filtering subsequent options, altering API request parameters, changing UI rendering), referencing any dependent modules or selectors.
-   Document data dependencies between steps, explaining shared contexts or state (with file references).
-   Identify shared state management patterns (Redux, MobX, Context, etc.) and their implementation files.
### 6. File Dependencies
-   **Parent Files:** List the parent components/containers onboarding or wrapping the **[CURRENT STEP TO DOCUMENT]** input, giving file paths and key line numbers.
-   **Child Files:** List all imported or passed-down components, utilities, and services from the **[CURRENT STEP TO DOCUMENT]** module, with locations.
-   **Related Files:** Identify auxiliary configuration, constants, types/interfaces, and API endpoint definitions—provide their reference paths.
-   **Shared Dependencies:** Document utilities, hooks, or services used in multiple steps of the recipe creation, with locations.
### 7. Previous and Next Steps to be Documented:
-   **Previous (if it exists):** Provide the full path where the documentation for the previous step was saved (e.g., `docs/recipe-flow/step-1-cuisine-selection.md`).
-   **Current:** Provide the full path where you will save this new documentation file (e.g., `docs/recipe-flow/step-2-dietary-restrictions.md`).
-   **Next (if it exists):** State the name of the next screen/step to be documented. **IMPORTANT:** Refer to `/create-recipe-docs/list-major-minor-steps.md` for the complete sequence of major user-facing steps AND minor internal automated steps, not just the high-level macro steps from `/create-recipe-docs/list-major-steps.md`.
---
### Output Format
Respond with a structured markdown document, with major sections and all subsections present.
-   Each subsection should contain:
    -   **Reasoning:** Use organized bullet points with clear categories and labels for easy scanning. Group related information logically (e.g., "Store Action", "API Call", "Parent Files"). Format as:
        ```markdown
        **Reasoning:**
        - **Category Name**: Description with file path and line numbers
        - **Another Category**: More details with specific references
        - **Third Category**: Additional technical details
        ```
    -   **Conclusion:** A concise paragraph summarizing findings and key technical locations for each bullet point.
-   For each subsection, *always place reasoning before the conclusion*. (Note: This applies to all sections *except* Section 5, which follows the special "Analytical Focus" format above).
-   Do not include code excerpts.
-   For file or method names, use bracketed placeholders if specific detail is not known: e.g. (`[path/to/file.js]`, `[ComponentName]`, `[handleInputChange]`, etc.).
-   **Format Requirements**: Use bullet points instead of dense paragraphs in reasoning sections. Organize information with clear category labels for optimal scanning and readability.
---
#### Edge Cases & Considerations
-   If any step is unclear or uses dynamic imports, note the location or pattern.
-   For missing types or unclear validation, reference the function/constant definition or describe its typical implementation pattern.
-   Constants should be referenced by their file path and exported name where possible.
---
**IMPORTANT INSTRUCTIONS & OBJECTIVE REMINDER:** Your primary objective is to analyze and document each sub-aspect of the **[CURRENT STEP TO DOCUMENT]** in the AromaChat recipe creation workflow through **SYSTEMATIC DATA FLOW ANALYSIS**.

**EVIDENCE-BASED REQUIREMENTS:**
-   **Mandatory File Reading**: You MUST read actual files to verify every statement
-   **Concrete References**: Every assertion must include specific file paths and line numbers from actual code
-   **No Speculation**: If evidence is not found in the codebase, explicitly state `[EvidenceNotFound]` or use bracketed placeholders
-   **Data Flow Tracing**: Follow the actual data flow through the codebase by reading files and searching for function calls, imports, and state management patterns
-   **Technical Verification**: Verify component names, function signatures, and implementation patterns by examining the actual source code

For each point, provide your reasoning first (with concrete file evidence), followed by a conclusion. Do not include code. Your final output must be a single, structured markdown document as specified in the "Output Format" section.

**SAVE OUTPUT:** After completing your analysis, you MUST save the structured markdown document to the file path specified in section 7 ("Current" step documentation path). Use the Write tool to create the .md file at the appropriate location (e.g., `create-recipe-docs/steps/[doc-XXX]-step-[identifier]-[step-name].md`).
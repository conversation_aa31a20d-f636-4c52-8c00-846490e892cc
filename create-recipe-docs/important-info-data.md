***

### Universal Feature Step: Key Points Checklist

This is a reference list of important points to notice for any given step within any feature. Use the bracketed placeholders `[...]` to fill in the specific names relevant to the feature and step you are documenting.

#### Key File Paths
* **Screen Route File:** `app/(...)/[feature-name]/[screen-file-name].tsx`
    * *Defines the route for the screen in the navigation stack.*
* **Screen Implementation File:** `features/[feature-name]/components/screens/[component-file-name].tsx`
    * *The main file containing the UI and logic for the current step.*
* **Feature Layout/Navigator:** `app/(...)/[feature-name]/_layout.tsx`
    * *Defines the navigator for the feature's flow, including the `"[route-name]"` for this step.*
* **Feature Index/Hub:** `app/(...)/[feature-name]/index.tsx`
    * *The main entry point screen for the feature, if applicable.*

#### Core Components & Hooks
* **Primary Screen Component:** `[PrimaryScreenComponent]`
    * *The main React component that renders the UI for this step.*
* **Shared or Specific Input Component(s):** `[InputComponent(s)]`
    * *The UI component(s) used for user input in this step.*
* **Primary Layout Wrapper:** `ScreenWrapper`
    * *The standard layout component that wraps every screen.*
* **State Management Hook:** `[FeatureStateHook]`
    * *The primary hook (e.g., from Zustand, Redux) used to access and manipulate the feature's state.*
* **Navigation Logic Hook:** `[NavigationLogicHook]`
    * *A custom hook, if it exists, that controls the navigation logic for this feature.*

#### State Management
* **Store Definition File:** `features/[feature-name]/store/[store-file-name].ts`
    * *The entry point for the feature's state management.*
* **Relevant State Slice:** `[RelevantStateSlice]`
    * *The specific slice of the store managing data for this feature.*
* **State Property for this Step:** `[statePropertyForThisStep]`
    * *The property within the slice that holds the data for this specific step.*
* **State Update Function:** `[stateUpdateFunction]`
    * *The action/function used to save the user's input from this step to the store.*

#### Data Structures & Types
* **Types Definition File:** `features/[feature-name]/types/[types-file-name].ts`
    * *Contains all TypeScript interfaces for the feature.*
* **Data Interface for this Step:** `[DataInterfaceForThisStep]`
    * *The specific interface defining the shape of the data for this step.*
* **Step Enum/Type (if applicable):** `[StepEnumOrType]`
    * *The enum or type member representing this step in a multi-step flow.*

#### Validation & Configuration
* **Constants File:** `features/[feature-name]/constants/[constants-file-name].ts`
    * *The source of truth for the feature's static configuration values.*
* **Validation Rules Constant:** `[ValidationRulesConstant]`
    * *An object within the constants file defining validation rules (e.g., min/max length).*
* **Schema Definition File:** `features/[feature-name]/schemas/[schema-file-name].ts`
    * *Contains the validation schemas (e.g., Zod, Yup) for the feature.*
* **Validation Schema for this Step:** `[ValidationSchemaForThisStep]`
    * *The specific schema used to validate the input for this step.*

#### Analytics & Tracking
* **Analytics Utility File:** `[AnalyticsUtilityFile]`
    * *The file containing functions for tracking user interactions (can be feature-specific or shared).*
* **Relevant Tracking Events:** `[RelevantTrackingEvents]`
    * *The specific functions or event names that are called during this step to log user progress and actions.*
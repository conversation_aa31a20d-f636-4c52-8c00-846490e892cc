Here is the complete and corrected list of the major user-facing steps in the `create-recipe` feature, with their proper names as defined in the application's constants and navigation stack.

***

### Create Recipe Feature: Major Steps

1.  **Health Concern**
    * **Proper Name:** `Health Concern`
    * **Description:** The user describes their primary health issue or goal.

2.  **Demographics**
    * **Proper Name:** `Demographics`
    * **Description:** The user provides basic information like age and gender for personalized safety recommendations.

3.  **Causes**
    * **Proper Name:** `Potential Causes`
    * **Description:** The user selects from a list of potential underlying causes related to their health concern.

4.  **Symptoms**
    * **Proper Name:** `Symptoms`
    * **Description:** The user selects the specific symptoms they are experiencing.

5.  **Properties**
    * **Proper Name:** `Therapeutic Properties`
    * **Description:** The application presents relevant therapeutic properties and automatically suggests and enriches essential oils based on the previous steps.

6.  **Final Recipes**
    * **Proper Name:** `Your Personalized Recipes`
    * **Description:** The final, personalized recipes for morning, mid-day, and night are generated and displayed to the user.
# Symptoms Step Analysis

## 1. Input Handling

**Reasoning:**
- **Primary Component**: SymptomsScreen in `src/app/(drawer)/(tabs)/create-recipe/symptoms.tsx:16`
- **Selection Component**: SymptomsSelection in `src/features/create-recipe/components/screens/symptoms-selection/symptoms-selection-screen.tsx:31`
- **Input Interface**: SelectableDataList component from `@/shared/components` at line 398
- **Data Source**: `potentialSymptoms` from `useCombinedRecipeStore` at line 41 (populated by causes step API streaming)
- **Selection Validation**: `symptomsSelectionSchema` from `src/features/create-recipe/schemas/recipe-schemas.ts` with z.object validation
- **Selection Constraints**: `SELECTION_REQUIREMENTS.symptoms` from `src/features/create-recipe/constants/recipe.constants.ts:228` (min: 1, max: 15)
- **Header Component**: `EnhancedSymptomsHeader` from `./enhanced-symptoms-header.tsx:13`
- **Toggle Handler**: `handleSymptomToggle` function at line 131 manages selection/deselection with max limit enforcement

**Conclusion:** Input handled through SelectableDataList component with multi-select functionality, validation constraints (1-15 selections), and real-time selection tracking with potentialSymptoms data from previous causes analysis API streaming.

## 2. Data Persistence

**Reasoning:**
- **Store Action**: `updateSelectedSymptoms` in `recipe.slice.ts:321`
- **Store Access**: `useCombinedRecipeStore` hook at line 40-48
- **Data Interface**: `PotentialSymptom` interface from `src/features/create-recipe/types/recipe.types.ts:63-68` with fields: symptom_id, symptom_name, symptom_suggestion, explanation
- **Storage Location**: Zustand combined store under `selectedSymptoms` array property
- **Validation Schema**: `symptomsSelectionSchema` validates selectedSymptoms array with min/max constraints via Zod
- **Store Effects**: Updates clear dependent data (therapeuticProperties, suggestedOils) at lines 324-327
- **State Coordination**: `useStepScreenCoordination` hook manages validation state between screen and form components

**Conclusion:** Data persisted in Zustand store as PotentialSymptom array with automatic clearing of downstream dependent data and validation coordination between screen components using systematic store effect cleanup.

## 3. Navigation Flow

**Reasoning:**
- **Navigation Management**: `useStepScreenCoordination` hook at lines 21-28 in symptoms.tsx
- **Trigger Function**: `handleContinue` callback from coordination hook at line 27
- **Form Submission**: `handleSubmit` function at line 341 triggered via coordination pattern
- **Navigation Action**: `completeStepAndGoNext` from `useRecipeNavigation` hook at line 39, called at line 321
- **Route Configuration**: Symptoms screen defined in navigation stack configuration
- **Flow Pattern**: Selection → Validation → API Streaming → Auto-navigation after completion
- **Loading States**: Managed through coordination hook with `onLoadingChange` callbacks at lines 383-389

**Conclusion:** Navigation uses step coordination pattern triggering API streaming for therapeutic properties analysis, then automatically advances to properties step after successful completion following the established wizard pattern.

## 4. Pre-navigation Processing

**Reasoning:**
- **Primary Processing**: `streamTherapeuticPropertiesData` function at lines 151-338
- **API Integration**: `streamRecipeStep` from `rotinanatural-client.ts` called at line 203
- **API Request Data**: Includes healthConcern, demographics, selectedCauses, selectedSymptoms, and user language at lines 175-195
- **Streaming Modal**: `StreamingBottomSheet` component at lines 424-445 provides UI feedback
- **Data Processing**: Real-time streaming with `structured_data` and `structured_complete` handling at lines 234-324
- **Error Handling**: Try-catch blocks at lines 172 and 332 for API failures
- **Loading States**: `isStreaming` and `isSubmitting` states managed at lines 62-66
- **Haptic Feedback**: `haptics.success()` at line 319 on completion
- **Modal State Management**: Comprehensive modal visibility tracking at lines 90-104

**Conclusion:** Extensive processing including real-time AI streaming API call for therapeutic properties analysis, modal UI feedback, comprehensive error handling, haptic responses, and systematic modal state management before navigation.

## 5. End-to-End Data Architecture for This Step

**Reasoning:**
- **Data Flow Path**: `potentialSymptoms` (input) → `selectedSymptoms` (via SelectableDataList) → `updateSelectedSymptoms` → API request payload
- **API Integration**: Combined with healthConcern, demographics, and selectedCauses data for therapeutic properties analysis API call
- **Response Processing**: `updateTherapeuticProperties` at line 314 stores API response data
- **Data Transformation**: Structured streaming data mapped to `TherapeuticProperty` interface at lines 299-311
- **Next Step Impact**: Generates `therapeuticProperties` array required for properties screen with addresses_cause_ids and addresses_symptom_ids mapping
- **Store Dependencies**: Requires healthConcern, demographics, and selectedCauses from previous steps for API call at lines 155-158
- **UI Feedback**: StreamingBottomSheet provides real-time progress visualization during API processing with systematic state tracking

**Conclusion:** Data flows from previous causes analysis through user symptom selection to API streaming that generates therapeutic properties data for next step, with comprehensive state management, real-time UI feedback, and complete data dependency coordination.

## 6. File Dependencies

**Reasoning:**
- **Parent Files**:
  - `src/app/(drawer)/(tabs)/create-recipe/symptoms.tsx` - route file importing SymptomsSelection
  - Route configuration in `_layout.tsx` for navigation stack
- **Child Components**:
  - `SymptomsSelection` - main selection component from symptoms-selection directory
  - `SelectableDataList` - shared component from `@/shared/components`
  - `EnhancedSymptomsHeader` - header component at line 14
  - `StreamingBottomSheet` - modal component from `@/shared/components/modals/streaming`
  - `RecipeActionControls` - navigation component from create-recipe features
- **Related Files**:
  - `symptomsSelectionSchema` - validation schema from schemas directory
  - `SELECTION_REQUIREMENTS` - constants from constants directory
  - `PotentialSymptom` interface - type definitions from types directory
  - `useStepScreenCoordination` - coordination hook from hooks directory
- **Shared Dependencies**:
  - `useTheme`, `useLanguage` - shared hooks
  - `useCombinedRecipeStore` - Zustand store access
  - `useRecipeNavigation` - navigation hook
  - `haptics` utility - tactile feedback
  - `react-i18next` - internationalization

**Conclusion:** Complex dependencies involving shared selection components, validation schemas, streaming modals, step coordination patterns, and comprehensive store integration with Material Design 3 theming and accessibility support.

## 7. Previous and Next Steps to be Documented

- **Previous:** create-recipe-docs/steps/doc-003-step-causes-analysis.md
- **Current:** create-recipe-docs/steps/doc-004-step-symptoms-analysis.md
- **Next:** Automated Internal Step: Therapeutic Properties Analysis (API Stream) per list-major-minor-steps.md:29-32
# Automated Internal Step: Potential Symptoms Analysis (API Stream)

## 1. Input Handling

**Reasoning:**
- **Trigger Location**: Causes completion in `src/features/create-recipe/components/screens/causes-selection/causes-selection-screen.tsx:207`
- **Triggering Function**: `streamPotentialSymptoms` function called automatically after causes form submission via `await streamPotentialSymptoms()` at line 207
- **Input Source**: Health concern, demographics, and selected causes data from Zustand store accessed via `useCombinedRecipeStore.getState()` at line 129
- **API Request Structure**: Includes health_concern, gender, age_category, age_specific, user_language, and selected_causes at lines 140-150
- **API Client**: `streamRecipeStep` from `@/shared/services/api/rotinanatural-client` at line 152
- **Streaming Endpoint**: `/ai/streaming` with step: 'potential-symptoms' at lines 148-150
- **Data Validation**: Checks for healthConcern and demographics availability at lines 130-135

**Conclusion:** Input handled automatically after causes completion using combined health concern, demographics, and selected causes data through streaming API endpoint with comprehensive data validation.

## 2. Data Persistence

**Reasoning:**
- **Store Action**: `setPotentialSymptoms` action in `recipe.slice.ts` called at line 183
- **Store Access**: `useCombinedRecipeStore.getState()` provides access to setPotentialSymptoms function at line 182
- **Data Interface**: `PotentialSymptom` interface mapping from API response at lines 176-181 with fields: symptom_id, symptom_name, symptom_suggestion, explanation
- **API Response Processing**: `structured_complete` event processing at lines 173-187
- **Data Transformation**: API response mapped to PotentialSymptom interface with fallback symptom IDs at line 177
- **Storage Location**: Zustand store under `potentialSymptoms` array property
- **Response Source**: Uses structured_complete data instead of streaming items for complete data at line 175

**Conclusion:** Data persisted in Zustand store as PotentialSymptom array after transforming API response from structured_complete event with proper interface mapping and fallback handling.

## 3. Navigation Flow

**Reasoning:**
- **Navigation Trigger**: Automatic completion after successful API streaming at line 186
- **Navigation Action**: `completeStepAndGoNext` from `useRecipeNavigation` hook called at line 186
- **Modal Coordination**: `StreamingBottomSheet` provides UI feedback during streaming (referenced in component)
- **Modal State Management**: `setShowStreamingModal` manages modal visibility at lines 136 and 185
- **Completion Pattern**: Modal closes immediately after data storage and navigation triggers at line 185
- **Flow Pattern**: Causes → API Stream → Auto-navigation to Symptoms step
- **Haptic Feedback**: `haptics.success()` at line 184 provides tactile success feedback

**Conclusion:** Navigation automatically proceeds to Symptoms step after successful API streaming completion with modal UI feedback and haptic confirmation following established wizard flow pattern.

## 4. Pre-navigation Processing

**Reasoning:**
- **API Streaming**: Real-time Server-Sent Events processing via `readSseStream` from `@/shared/utils/sse-reader` at line 155
- **Stream Processing**: `structured_data` events with field 'potential_symptoms' processed at line 159
- **UI Updates**: Real-time streaming items display via `setStreamingItems` at lines 168-171
- **Error Handling**: Try-catch blocks at lines 139 and 195 for API failures with DEV-only logging
- **Loading States**: `isStreaming` state management at lines 137 and 174
- **Response Validation**: Checks for response body availability at line 154
- **Data Validation**: Duplicate prevention logic at lines 169-170
- **Callback Dependencies**: Function memoized with `useCallback` including dependencies at line 201

**Conclusion:** Extensive real-time streaming processing with immediate UI updates, comprehensive error handling, loading state management, and duplicate prevention before automatic navigation completion.

## 5. End-to-End Data Architecture for This Step

**Reasoning:**
- **Data Flow Path**: Causes completion → API request with health_concern + demographics + selected_causes → streaming response → PotentialSymptom objects → Symptoms step population
- **API Integration**: Uses platform-aware `streamRecipeStep` with feature: 'create-recipe' and step: 'potential-symptoms'
- **Response Processing**: Real-time streaming with `structured_data` and `structured_complete` event handling
- **Data Transformation**: API response fields mapped to PotentialSymptom interface requirements
- **Next Step Impact**: Populates `potentialSymptoms` array required for Symptoms selection screen
- **Store Dependencies**: Requires healthConcern, demographics, and selectedCauses from previous steps
- **UI Feedback Integration**: StreamingBottomSheet provides real-time progress visualization during API processing

**Conclusion:** Data flows from causes completion through automated API streaming to generate potential symptoms data for next step, with comprehensive real-time processing and immediate store population.

## 6. File Dependencies

**Reasoning:**
- **Parent Files**:
  - Causes selection component as trigger location
  - `useCombinedRecipeStore` for data access and storage
- **API Integration**:
  - `streamRecipeStep` from `@/shared/services/api/rotinanatural-client`
  - `readSseStream` from `@/shared/utils/sse-reader` for streaming processing
- **Related Files**:
  - `PotentialSymptom` interface from types directory
  - `haptics` utility for tactile feedback
  - `useLanguage` hook for API language parameter
- **UI Components**:
  - `StreamingBottomSheet` modal for user feedback
  - StreamingItem interface for real-time display
- **Shared Dependencies**:
  - `useRecipeNavigation` for completion navigation
  - React hooks (useCallback) for performance optimization
  - DEV environment checks for conditional logging

**Conclusion:** Dependencies span API services, streaming utilities, type definitions, UI feedback components, and navigation coordination with development-aware logging and comprehensive integration across the create-recipe feature architecture.

## 7. Previous and Next Steps to be Documented

- **Previous:** create-recipe-docs/steps/doc-003-step-causes-analysis.md (already documented)
- **Current:** create-recipe-docs/steps/doc-004A-internal-potential-symptoms-analysis.md
- **Next:** create-recipe-docs/steps/doc-004-step-symptoms-analysis.md (already documented)
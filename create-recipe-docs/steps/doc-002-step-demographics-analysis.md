# Demographics Step Analysis

## 1. Input Handling

**Reasoning:**
- **Primary Component**: DemographicsScreen in `src/app/(drawer)/(tabs)/create-recipe/demographics.tsx:16`
- **Form Component**: DemographicsForm in `src/features/create-recipe/components/screens/demographics/demographics-form.tsx:41`
- **Validation**: React Hook Form with zodResolver using `demographicsSchema` from `src/features/create-recipe/schemas/recipe-schemas.ts:41`
- **Gender Input**: StandardizedSegmentedButtons component at line 315
- **Age Input**: Dual control system - slider (lines 412-428) and stepper buttons (lines 356-408)
- **Age Category**: Auto-determined via `determineAgeCategory` function from constants at line 119
- **Custom Hooks**: Uses `useStepScreenCoordination` for form validation state management

**Conclusion:** Input handled by DemographicsForm with React Hook Form validation, segmented buttons for gender selection, and dual-control age selection (slider + stepper) with auto-determined age category chips.

## 2. Data Persistence

**Reasoning:**
- **Store Action**: `updateDemographics` action in `recipe.slice.ts:302`
- **Store Access**: `useCombinedRecipeStore` hook at `demographics-form.tsx:50`
- **Data Interface**: `DemographicsData` with fields: gender, specificAge, computed ageCategory
- **State Coordination**: `useStepScreenCoordination` hook from `use-step-screen-coordination.ts:13`
- **Validation State**: Form validation state managed between screen and form components
- **Store Location**: Combined Zustand store under `demographics` property

**Conclusion:** Persisted in Zustand combined store under demographics property with computed age category and form validation state coordination between screen and form components.

## 3. Navigation Flow

**Reasoning:**
- **Navigation Management**: `useStepScreenCoordination` hook with `handleContinue` callback at `demographics.tsx:29`
- **Form Submission**: Triggers `streamPotentialCauses` function at `demographics-form.tsx:122`
- **Navigation Action**: `completeStepAndGoNext` call at line 233 after API completion
- **Route Configuration**: Demographics screen defined in `_layout.tsx:40` within Stack navigator
- **Flow**: Form → AI Streaming → Auto-navigation to causes step

**Conclusion:** Navigation triggers AI streaming modal for potential causes analysis, then automatically advances to causes step after successful API response completion.

## 4. Pre-navigation Processing

**Reasoning:**
- **AI API Call**: `streamRecipeStep` from `rotinanatural-client.ts:160` called at line 151
- **API Request Data**: Includes healthConcern, demographics data, and user language
- **UI Feedback**: `StreamingBottomSheet` modal displays at line 444 during processing
- **Haptic Feedback**: `haptics.success()` at line 231 on completion
- **Error Handling**: Try-catch blocks at line 244 for API failures
- **Data Processing**: Real-time streaming with structured_complete handling

**Conclusion:** Significant processing - real-time AI streaming for potential causes analysis, modal UI feedback, haptic responses, and error handling before navigation advancement.

## 5. End-to-End Data Architecture for This Step

**Reasoning:**
- **Data Flow Path**: Form inputs → `updateDemographics` → `recipe.slice` → AI API request
- **API Integration**: Combined with `healthConcern` data for streaming request
- **Response Processing**: `setPotentialCauses` at line 226 stores API results
- **Data Transformation**: `structured_complete` data mapped to `PotentialCause` interface at line 219
- **UI Feedback**: `StreamingBottomSheet` provides real-time streaming progress
- **Next Step Impact**: Generates `potentialCauses` data required for causes selection screen

**Conclusion:** Data flows to Zustand store then triggers AI analysis that generates potentialCauses data for the next step, with real-time streaming UI showing analysis progress.

## 6. File Dependencies

**Reasoning:**
- **Parent Files**:
  - `_layout.tsx:40` defines screen route configuration
- **Child Components**:
  - `DemographicsForm` - main form component
  - `useStepScreenCoordination` - step coordination hook
  - `RecipeActionControls` - navigation controls
- **Related Files**:
  - `demographicsSchema` - validation schema
  - `DemographicsData` interface - type definitions
  - `AGE_VALIDATION` constants - validation rules
  - `StreamingBottomSheet` - modal component
- **Shared Dependencies**:
  - React Hook Form - form management
  - `StandardizedSegmentedButtons` - UI component
  - `useTheme` hook - theming
  - `haptics` utility - tactile feedback

**Conclusion:** Complex dependencies involving form management, validation schemas, streaming modals, and step coordination patterns with Material Design 3 UI components.

## 7. Previous and Next Steps to be Documented

- **Previous:** create-recipe-docs/steps/doc-001-step-health-concern-analysis.md
- **Current:** create-recipe-docs/steps/doc-002-step-demographics-analysis.md
- **Next:** Automated Internal Step: Potential Causes Analysis (API Stream) per list-major-minor-steps.md:13
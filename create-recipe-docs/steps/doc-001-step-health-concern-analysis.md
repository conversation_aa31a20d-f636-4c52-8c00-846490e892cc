# Health Concern Step Analysis

## 1. Input Handling

**Reasoning:**
- **Primary Component**: HealthConcernScreen in `src/features/create-recipe/components/screens/health-concern-screen.tsx:23`
- **Input Component**: ChatInputBar from `src/shared/components/layout/chat-input-bar.tsx:131`
- **Validation Schema**: `healthConcernSchema` from `src/features/create-recipe/schemas/recipe-schemas.ts:22`
- **Validation Constants**: `HEALTH_CONCERN_VALIDATION` from `src/features/create-recipe/constants/recipe.constants.ts:208`
- **Character Limits**: 3-100 characters enforced via Zod schema
- **Input Suggestions**: Loaded from i18n translations at `healthConcern.suggestions` key
- **UI Pattern**: Chat-style input with suggestion chips

**Conclusion:** Input handled by ChatInputBar component with comprehensive validation via Zod schema enforcing 3-100 character limits and suggestion chips for common health concerns.

## 2. Data Persistence

**Reasoning:**
- **Store Action**: `updateHealthConcern` action in `recipe.slice.ts:288`
- **Store Access**: `useCombinedRecipeStore` hook in `combined-store.ts:25`
- **Data Interface**: `HealthConcernData` interface from `recipe.types.ts:21`
- **Storage Location**: Zustand combined store under `healthConcern` property
- **Analytics Integration**: `trackWizardStarted` call in `recipe.slice.ts:292` for first-time input
- **Session Management**: Wizard session UUID generation at line 36

**Conclusion:** Persisted in Zustand combined store under healthConcern property with typed interface and automatic wizard analytics tracking on initial input.

## 3. Navigation Flow

**Reasoning:**
- **Navigation Management**: `useRecipeNavigation` hook from `use-recipe-navigation.ts:11`
- **Advancement Trigger**: `completeStepAndGoNext` function at line 124 after validation
- **Route Configuration**: Defined in `_layout.tsx:34` mapping health-concern to Stack screen
- **Next Step**: Demographics as defined in `stepPaths` at `use-recipe-navigation.ts:25`
- **Navigation Pattern**: Linear progression with validation gates
- **Router Integration**: Expo Router Stack navigation

**Conclusion:** Linear navigation to demographics step controlled by Zustand store navigation state with validation gates and Expo Router Stack navigation.

## 4. Pre-navigation Processing

**Reasoning:**
- **API Calls**: None at this step
- **Data Transformation**: No transformations occur
- **Validation Processing**: `healthConcernSchema` validation in `recipe-schemas.ts:22`
- **Analytics Tracking**: `trackWizardStarted` in `recipe.slice.ts:292`
- **Session Management**: Wizard session UUID generation at line 36
- **Haptic Feedback**: `haptics.error()` on validation failure at line 69
- **Business Logic**: Minimal processing, primarily validation

**Conclusion:** Minimal processing - schema validation, analytics tracking, and haptic feedback only, no business logic or API calls executed.

## 5. End-to-End Data Architecture for This Step

**Reasoning:**
- **Data Flow Path**: ChatInputBar → `handleSendMessage` → `updateHealthConcern` → `recipe.slice`
- **Store Integration**: Data accessed via `useRecipeData` selector hook in `combined-store.ts:39`
- **Next Step Impact**: `healthConcern` becomes part of API request payload for potential causes analysis
- **API Integration**: Used in demographics step for AI streaming context per `list-major-minor-steps.md:13`
- **State Sharing**: Centralized Zustand store enables cross-step data access
- **Data Dependencies**: Required for personalized AI responses in subsequent steps

**Conclusion:** Data passes directly to Zustand store and later consumed by AI streaming requests in demographics step for personalized cause analysis.

## 6. File Dependencies

**Reasoning:**
- **Parent Files**:
  - `_layout.tsx:34` wraps screen in Stack navigator
- **Child Components**:
  - `ChatInputBar` from `shared/components/layout`
  - `ScreenWrapper` for layout management
  - `Chip` components from react-native-paper
- **Related Files**:
  - `recipe.types.ts:21` for `HealthConcernData` interface
  - `recipe.constants.ts:208` for validation constants
  - `recipe-schemas.ts:22` for Zod validation
- **Shared Dependencies**:
  - `useTheme` hook for theming
  - `haptics` utility for tactile feedback
  - i18n translations for suggestions

**Conclusion:** Minimal dependencies focused on shared UI components, validation schemas, and theme integration with standard React Native Paper components.

## 7. Previous and Next Steps to be Documented

- **Previous:** None (first step in wizard)
- **Current:** create-recipe-docs/steps/doc-001-step-health-concern-analysis.md
- **Next:** Demographics step (major user-facing step per list-major-minor-steps.md:10)
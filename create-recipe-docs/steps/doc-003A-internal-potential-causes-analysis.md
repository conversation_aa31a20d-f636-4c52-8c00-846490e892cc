# Automated Internal Step: Potential Causes Analysis (API Stream)

## 1. Input Handling

**Reasoning:**
- **Trigger Location**: Demographics completion in `src/features/create-recipe/components/screens/demographics/demographics-form.tsx:222`
- **Triggering Function**: `streamPotentialCauses` function called automatically after demographics form submission via `await streamPotentialCauses()` at line 222
- **Input Source**: Health concern and demographics data from Zustand store
- **API Request Structure**: Includes health_concern, gender, age_category, age_specific, and user_language at lines 139-145
- **Age Category Logic**: Auto-determined via `determinedAgeCategory` at line 119 using `determineAgeCategory(currentAge)` utility
- **API Client**: `streamRecipeStep` from `@/shared/services/api/rotinanatural-client` at line 151
- **Streaming Endpoint**: `/ai/streaming` with step: 'potential-causes' at lines 152-155

**Conclusion:** Input handled automatically after demographics completion using combined health concern and demographics data through streaming API endpoint with auto-determined age categorization.

## 2. Data Persistence

**Reasoning:**
- **Store Action**: `setPotentialCauses` action in `recipe.slice.ts` called at line 226
- **Store Access**: `useCombinedRecipeStore` hook provides access to setPotentialCauses function
- **Data Interface**: `PotentialCause` interface mapping from API response at lines 219-224 with fields: cause_id, cause_name, cause_suggestion, explanation
- **API Response Processing**: `structured_complete` event processing at lines 208-224
- **Data Transformation**: API response mapped to PotentialCause interface with fallback cause IDs at line 220
- **Storage Location**: Zustand store under `potentialCauses` array property
- **Response Source**: Uses structured_complete data instead of streaming items for complete UUID data

**Conclusion:** Data persisted in Zustand store as PotentialCause array after transforming API response from structured_complete event with proper interface mapping and fallback handling.

## 3. Navigation Flow

**Reasoning:**
- **Navigation Trigger**: Automatic completion after successful API streaming at line 228
- **Navigation Action**: `completeStepAndGoNext` from `useRecipeNavigation` hook at line 29
- **Modal Coordination**: `StreamingBottomSheet` provides UI feedback during streaming at lines 313-334
- **Modal State Management**: `showStreamingModal` and `setShowStreamingModal` coordinate modal visibility
- **Completion Pattern**: Modal closes immediately after data storage and navigation triggers
- **Flow Pattern**: Demographics → API Stream → Auto-navigation to Causes step
- **Haptic Feedback**: `haptics.success()` at line 227 provides tactile success feedback

**Conclusion:** Navigation automatically proceeds to Causes step after successful API streaming completion with modal UI feedback and haptic confirmation following established wizard flow pattern.

## 4. Pre-navigation Processing

**Reasoning:**
- **API Streaming**: Real-time Server-Sent Events processing via `readSseStream` from `@/shared/utils/sse-reader` at line 168
- **Stream Processing**: `structured_data` events with field 'potential_causes' processed at line 184
- **UI Updates**: Real-time streaming items display via `setStreamingItems` at lines 197-204
- **Error Handling**: Try-catch blocks at lines 135 and 235 for API failures
- **Loading States**: `isStreaming` state management at lines 132 and 211
- **Response Validation**: Checks for response body availability at line 163
- **Data Validation**: Duplicate prevention logic at lines 198-202

**Conclusion:** Extensive real-time streaming processing with immediate UI updates, comprehensive error handling, loading state management, and duplicate prevention before automatic navigation completion.

## 5. End-to-End Data Architecture for This Step

**Reasoning:**
- **Data Flow Path**: Demographics completion → API request with health_concern + demographics → streaming response → PotentialCause objects → Causes step population
- **API Integration**: Uses platform-aware `streamRecipeStep` with feature: 'create-recipe' and step: 'potential-causes'
- **Response Processing**: Real-time streaming with `structured_data` and `structured_complete` event handling
- **Data Transformation**: API response fields mapped to PotentialCause interface requirements
- **Next Step Impact**: Populates `potentialCauses` array required for Causes selection screen
- **Store Dependencies**: Requires healthConcern and completed demographics from previous steps
- **UI Feedback Integration**: StreamingBottomSheet provides real-time progress visualization

**Conclusion:** Data flows from demographics completion through automated API streaming to generate potential causes data for next step, with comprehensive real-time processing and immediate store population.

## 6. File Dependencies

**Reasoning:**
- **Parent Files**:
  - Demographics form component as trigger location
  - `useCombinedRecipeStore` for data access and storage
- **API Integration**:
  - `streamRecipeStep` from `@/shared/services/api/rotinanatural-client`
  - `readSseStream` from `@/shared/utils/sse-reader` for streaming processing
- **Related Files**:
  - `PotentialCause` interface from types directory
  - `determineAgeCategory` utility for age categorization
  - `haptics` utility for tactile feedback
- **UI Components**:
  - `StreamingBottomSheet` modal for user feedback
  - StreamingItem interface for real-time display
- **Shared Dependencies**:
  - `useLanguage` hook for API language parameter
  - `useRecipeNavigation` for completion navigation
  - React Native streaming utilities

**Conclusion:** Dependencies span API services, streaming utilities, type definitions, UI feedback components, and navigation coordination with comprehensive integration across the create-recipe feature architecture.

## 7. Previous and Next Steps to be Documented

- **Previous:** create-recipe-docs/steps/doc-002-step-demographics-analysis.md
- **Current:** create-recipe-docs/steps/doc-003A-internal-potential-causes-analysis.md
- **Next:** create-recipe-docs/steps/doc-003-step-causes-analysis.md (already documented)
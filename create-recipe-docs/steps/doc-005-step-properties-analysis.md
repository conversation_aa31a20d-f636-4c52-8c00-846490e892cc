# Properties Step Analysis

## 1. Input Handling

**Reasoning:**
- **Primary Component**: PropertiesScreen in `src/app/(drawer)/(tabs)/create-recipe/properties.tsx:18`
- **Display Component**: PropertiesSelectionMinimal in `src/features/create-recipe/components/screens/properties-selection/properties-selection.tsx:23`
- **Input Interface**: Auto-display of therapeutic properties without user selection - properties are auto-selected via `useEffect` at line 250-255
- **Data Source**: `therapeuticProperties` from `useCombinedRecipeStore` at line 21 (populated by symptoms step API streaming)
- **Header Component**: `EnhancedPropertiesHeader` from `./enhanced-properties-header.tsx:19` with PropertyChips and OilCountChip
- **UI Components**: PropertiesListUI at line 200 displays accordion-style property list with oil details
- **Auto-Selection Logic**: All properties automatically selected when loaded via setSelectedPropertyIds in usePropertiesSelection hook
- **Banner System**: Oil suggestion banner appears after 5s delay during streaming at lines 47-73 with dismissible state management

**Conclusion:** Input handled through auto-display of therapeutic properties with no user selection required - properties are automatically processed for oil suggestions streaming with rich header showing property chips and live oil count tracking.

## 2. Data Persistence

**Reasoning:**
- **Store Operations**: Multiple therapeutic property store actions including `updateTherapeuticProperties` at `recipe.slice.ts:331`, `updatePropertyWithEnrichedOils` at line 419, and `setPropertyEnrichmentStatus`
- **Store Access**: `useCombinedRecipeStore` hook at line 17-29 in usePropertiesSelection
- **Data Interface**: `TherapeuticProperty` interface from `src/features/create-recipe/types/recipe.types.ts:73-85` with fields including isEnriched, suggested_oils, addresses_cause_ids, addresses_symptom_ids
- **Storage Location**: Zustand combined store under `therapeuticProperties` array with enrichment status tracking
- **Enrichment State**: Complex state management with `propertyEnrichmentStates` at line 54-58 tracking per-property enrichment status
- **Store Effects**: Updates clear dependent data (suggestedOils) at line 362
- **Auto-Selection State**: `selectedPropertyIds` local state with auto-selection logic for all properties
- **Final Recipes Storage**: `updateFinalRecipes` store action for storing generated recipes by time slot

**Conclusion:** Data persisted through complex Zustand store operations managing therapeutic properties, enrichment states, oil suggestions, and final recipe generation with systematic state cleanup and per-property enrichment tracking.

## 3. Navigation Flow

**Reasoning:**
- **Navigation Management**: `usePropertiesScreen` hook at lines 32-45 with dynamic UI calculations
- **Dynamic Button Logic**: Button text and action change based on `allPropertiesEnriched` state at lines 61-85
- **Trigger Function**: `handleAction` callback from properties screen hook at line 101
- **Form Submission**: Either `handleSubmit` for oil suggestions or `handleGenerateFinalRecipes` for recipe generation at lines 167-168
- **Navigation Action**: `completeStepAndGoNext` from `useRecipeNavigation` hook at line 32, called at line 534
- **Loading States**: Complex loading coordination with `isSubmitting`, `parallelStreamingState.isStreaming`, and `isGeneratingRecipes` at line 171
- **Modal Coordination**: `showRecipeStreamingModal` state manages final recipe generation UI feedback

**Conclusion:** Navigation uses sophisticated dynamic button pattern with state-dependent actions - either triggering oil suggestions streaming or final recipe generation based on enrichment completion status, then auto-advancing to final recipes after successful processing.

## 4. Pre-navigation Processing

**Reasoning:**
- **Primary Processing**: Two-stage API processing - oil suggestions streaming via `handleSubmit` at lines 446-487, then enrichment via `autoTriggerEnrichment` at lines 111-242
- **Oil Suggestions API**: `startOilSuggestionStreaming` called at line 473 with parallel streaming for all properties
- **Enrichment API**: `batchEnrichOils` from `rotinanatural-client.ts` called at line 145 for safety data enrichment
- **Final Recipe Generation**: `startFinalRecipeStreaming` at line 510 for generating three time-slot specific recipes
- **Streaming Coordination**: Complex parallel streaming state management with `parallelStreamingState` tracking results and errors
- **Auto-Trigger Logic**: Automatic oil suggestion streaming when properties loaded via useEffect at lines 258-297
- **Error Handling**: Comprehensive error handling with retry logic (exponential backoff) at lines 213-234
- **Completion Logic**: Automatic processing completion detection at lines 402-426

**Conclusion:** Extensive multi-stage processing including automatic oil suggestions streaming, immediate enrichment with retry logic, comprehensive error handling, and final recipe generation with sophisticated parallel API coordination and automatic completion detection.

## 5. End-to-End Data Architecture for This Step

**Analysis & Conclusion:** When all properties are enriched, `calculatePostEnrichmentScores` is called and returns a complete `FinalAPIPayload` object containing `suggested_oils`, `safety_library`, and `safety_library_formatted`. However, only the `final_relevance_score` and `specialization_score` fields are extracted and applied to the properties in the store state. The complete `suggested_oils` array and safety libraries are stored separately for final recipe generation but are NOT used in the immediate `therapeuticProperties` state update - effectively creating a separation between property-level scoring and system-level recipe data.

**Evidence & Chain of Reasoning:**
- **Function Return Value:** The `calculatePostEnrichmentScores` function in `post-enrichment-scoring.ts:178` returns a `FinalAPIPayload` object containing: `suggested_oils` (complete ScoredOil array), `safety_library` (SafetyLibrary object), and `safety_library_formatted` (formatted string).
- **Store Action Logic:** This function is called within the `updatePropertyWithEnrichedOils` action in `recipe.slice.ts:655` after all properties reach enriched status.
- **Data Extraction Pattern:** Inside this action, a `hybridScoreMap` is created at lines 663-671 by mapping *only* the `final_relevance_score` and `specialization_score` from the `scoringResult.suggested_oils` array.
- **Selective State Update:** The `therapeuticProperties` state is then updated at lines 674-679 with *only* these two extracted scores applied to each oil within each property's `suggested_oils` array.
- **Parallel Storage Strategy:** The complete `suggested_oils` array, `safety_library`, and `safety_library_formatted` from the `scoringResult` are stored separately as `finalScoredOils`, `finalSafetyLibrary`, and `finalSafetyLibraryFormatted` at lines 658-660 for later use in final recipe generation.
- **Data Usage vs. Discard:** The immediate property state update discards the comprehensive oil contexts, property relationships, and safety data from the scoring result, retaining only the numerical scores for UI display purposes while preserving the complete dataset for downstream recipe generation.

## 6. File Dependencies

**Reasoning:**
- **Parent Files**:
  - `src/app/(drawer)/(tabs)/create-recipe/properties.tsx` - route file importing PropertiesSelection
  - Route configuration in navigation stack
- **Child Components**:
  - `PropertiesSelectionMinimal` - main container component from properties-selection directory
  - `EnhancedPropertiesHeader` - rich header with PropertyChips and OilCountChip
  - `PropertiesListUI` - accordion-style property list UI component
  - `ParallelStreamingBottomSheet` - final recipe generation modal
- **Related Files**:
  - `usePropertiesScreen` - screen coordination hook from hooks directory
  - `usePropertiesSelection` - complex business logic hook with streaming coordination
  - `useCreateRecipeStreaming` - streaming operations hook
  - `TherapeuticProperty` interface - type definitions from types directory
  - `batchEnrichOils` - API client function for enrichment
- **Shared Dependencies**:
  - `useTheme`, `useLanguage` - shared hooks
  - `useCombinedRecipeStore` - Zustand store access with multiple property-related actions
  - `useRecipeNavigation` - navigation hook
  - `haptics` utility - tactile feedback
  - `react-i18next` - internationalization

**Conclusion:** Complex dependencies involving sophisticated business logic hooks, parallel streaming coordination, enrichment APIs, modal systems, and comprehensive store integration with Material Design 3 theming and accessibility support.

## 7. Previous and Next Steps to be Documented

- **Previous:** create-recipe-docs/steps/doc-004-step-symptoms-analysis.md
- **Current:** create-recipe-docs/steps/doc-005-step-properties-analysis.md
- **Next:** Final Recipes step (major user-facing step per list-major-minor-steps.md:52-58)
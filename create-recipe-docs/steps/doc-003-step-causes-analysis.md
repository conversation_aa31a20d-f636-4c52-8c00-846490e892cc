# Causes Step Analysis

## 1. Input Handling

**Reasoning:**
- **Primary Component**: CausesScreen in `src/app/(drawer)/(tabs)/create-recipe/causes.tsx:16`
- **Selection Component**: CausesSelection in `src/features/create-recipe/components/screens/causes-selection/causes-selection-screen.tsx:22`
- **Input Interface**: SelectableDataList component from `src/shared/components` at line 225
- **Data Source**: `potentialCauses` from `useCombinedRecipeStore` at line 32 (populated by demographics step)
- **Selection Validation**: `causesSelectionSchema` from `src/features/create-recipe/schemas/recipe-schemas.ts:64`
- **Selection Constraints**: `SELECTION_REQUIREMENTS.causes` from `src/features/create-recipe/constants/recipe.constants.ts:226-228` (min: 1, max: 10)
- **Header Component**: `EnhancedCausesHeader` from `./enhanced-causes-header.tsx:13`
- **Toggle Handler**: `handleCauseToggle` function at line 93 manages selection/deselection

**Conclusion:** Input handled through SelectableDataList component with multi-select functionality, validation constraints (1-10 selections), and real-time selection tracking with potentialCauses data from previous demographic analysis.

## 2. Data Persistence

**Reasoning:**
- **Store Action**: `updateSelectedCauses` in `recipe.slice.ts:309`
- **Store Access**: `useCombinedRecipeStore` hook at line 31
- **Data Interface**: `PotentialCause` interface from `src/features/create-recipe/types/recipe.types.ts:53-58` with fields: cause_id, cause_name, cause_suggestion, explanation
- **Storage Location**: Zustand combined store under `selectedCauses` array property
- **Validation Schema**: `causesSelectionSchema` validates selectedCauses array with min/max constraints
- **Store Effects**: Updates clear dependent data (selectedSymptoms, therapeuticProperties) at lines 312-314
- **State Coordination**: `useStepScreenCoordination` hook manages validation state between screen and form

**Conclusion:** Data persisted in Zustand store as PotentialCause array with automatic clearing of downstream dependent data and validation coordination between screen components.

## 3. Navigation Flow

**Reasoning:**
- **Navigation Management**: `useStepScreenCoordination` hook at line 21-28
- **Trigger Function**: `handleContinue` callback from coordination hook at line 27
- **Form Submission**: `handleSubmit` function at line 203 triggered via coordination pattern
- **Navigation Action**: `completeStepAndGoNext` from `useRecipeNavigation` hook at line 30, called at line 186
- **Route Configuration**: Causes screen defined in `_layout.tsx` at route configuration
- **Flow Pattern**: Selection → Validation → API Streaming → Auto-navigation after completion
- **Loading States**: Managed through coordination hook with `onLoadingChange` callbacks

**Conclusion:** Navigation uses step coordination pattern triggering API streaming for symptoms analysis, then automatically advances to symptoms step after successful completion.

## 4. Pre-navigation Processing

**Reasoning:**
- **Primary Processing**: `streamPotentialSymptoms` function at line 128-201
- **API Integration**: `streamRecipeStep` from `rotinanatural-client.ts` called at line 152
- **API Request Data**: Includes healthConcern, demographics, selectedCauses, and user language at lines 140-150
- **Streaming Modal**: `StreamingBottomSheet` component at lines 251-268 provides UI feedback
- **Data Processing**: Real-time streaming with `structured_data` and `structured_complete` handling at lines 159-188
- **Error Handling**: Try-catch blocks at lines 139 and 195 for API failures
- **Loading States**: `isStreaming` and `isSubmitting` states managed at lines 54-56
- **Haptic Feedback**: `haptics.success()` at line 184 on completion

**Conclusion:** Extensive processing including real-time AI streaming API call for symptoms analysis, modal UI feedback, comprehensive error handling, and haptic responses before navigation.

## 5. End-to-End Data Architecture for This Step

**Reasoning:**
- **Data Flow Path**: `potentialCauses` (input) → `selectedCauses` (via SelectableDataList) → `updateSelectedCauses` → API request payload
- **API Integration**: Combined with healthConcern and demographics data for symptoms analysis API call
- **Response Processing**: `setPotentialSymptoms` at line 182 stores API response data
- **Data Transformation**: Structured streaming data mapped to `PotentialSymptom` interface at lines 176-181
- **Next Step Impact**: Generates `potentialSymptoms` array required for symptoms selection screen
- **Store Dependencies**: Requires healthConcern and demographics from previous steps for API call at lines 129-135
- **UI Feedback**: StreamingBottomSheet provides real-time progress visualization during API processing

**Conclusion:** Data flows from previous demographics analysis through user selection to API streaming that generates symptoms data for next step, with comprehensive state management and real-time UI feedback.

## 6. File Dependencies

**Reasoning:**
- **Parent Files**:
  - `src/app/(drawer)/(tabs)/create-recipe/causes.tsx` - route file importing CausesSelection
  - Route configuration in `_layout.tsx` for navigation stack
- **Child Components**:
  - `CausesSelection` - main selection component from causes-selection directory
  - `SelectableDataList` - shared component from `@/shared/components`
  - `EnhancedCausesHeader` - header component at line 13
  - `StreamingBottomSheet` - modal component from `@/shared/components/modals/streaming`
- **Related Files**:
  - `causesSelectionSchema` - validation schema from schemas directory
  - `SELECTION_REQUIREMENTS` - constants from constants directory
  - `PotentialCause` interface - type definitions from types directory
  - `useStepScreenCoordination` - coordination hook from hooks directory
- **Shared Dependencies**:
  - `useTheme`, `useLanguage` - shared hooks
  - `useCombinedRecipeStore` - Zustand store access
  - `useRecipeNavigation` - navigation hook
  - `haptics` utility - tactile feedback
  - `react-i18next` - internationalization

**Conclusion:** Complex dependencies involving shared selection components, validation schemas, streaming modals, step coordination patterns, and comprehensive store integration with Material Design 3 theming.

## 7. Previous and Next Steps to be Documented

- **Previous:** create-recipe-docs/steps/doc-002-step-demographics-analysis.md
- **Current:** create-recipe-docs/steps/doc-003-step-causes-analysis.md
- **Next:** Automated Internal Step: Potential Symptoms Analysis (API Stream) per list-major-minor-steps.md:21
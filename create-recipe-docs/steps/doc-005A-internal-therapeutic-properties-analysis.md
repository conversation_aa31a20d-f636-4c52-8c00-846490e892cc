# Automated Internal Step: Therapeutic Properties Analysis (API Stream)

## 1. Input Handling

**Reasoning:**
- **Trigger Location**: Symptoms completion in `src/features/create-recipe/components/screens/symptoms-selection/symptoms-selection-screen.tsx:354`
- **Triggering Function**: `streamTherapeuticPropertiesData` function called automatically after symptoms form submission via `await streamTherapeuticPropertiesData()` at line 354
- **Input Source**: Health concern, demographics, selected causes, and selected symptoms data from Zustand store
- **API Request Structure**: Includes health_concern, gender, age_category, age_specific, user_language, selected_causes, and selected_symptoms at lines 176-194
- **API Client**: `streamRecipeStep` from `@/shared/services/api/rotinanatural-client` at line 200
- **Streaming Endpoint**: `/ai/streaming` with step: 'therapeutic-properties' at lines 187-195
- **Data Validation**: Comprehensive checks for healthConcern, demographics, selectedCauses, and selectedSymptoms at line 155
- **Function Optimization**: Memoized with `useCallback` to prevent recreation at line 151

**Conclusion:** Input handled automatically after symptoms completion using comprehensive recipe data (health concern, demographics, causes, symptoms) through streaming API endpoint with extensive data validation and performance optimization.

## 2. Data Persistence

**Reasoning:**
- **Store Action**: `updateTherapeuticProperties` action in `recipe.slice.ts` called at line 314
- **Store Access**: `useCombinedRecipeStore` hook provides access to updateTherapeuticProperties function at line 47
- **Data Interface**: `TherapeuticProperty` interface mapping from API response at lines 299-311 with fields: property_id, property_name_localized, property_name_english, description_contextual_localized, addresses_cause_ids, addresses_symptom_ids, relevancy_score, suggested_oils, isLoadingOils, errorLoadingOils, isEnriched
- **API Response Processing**: `structured_complete` event processing at lines 272-324
- **Data Transformation**: API response mapped to complete TherapeuticProperty interface with comprehensive field mapping
- **Storage Location**: Zustand store under `therapeuticProperties` array property with source tracking ('symptoms-selection-streaming')
- **Response Source**: Uses structured_complete data for complete UUID data at line 286
- **UUID Preservation**: Addresses_cause_ids and addresses_symptom_ids preserved with full UUID arrays

**Conclusion:** Data persisted in Zustand store as comprehensive TherapeuticProperty array with complete interface mapping, UUID preservation for relationship tracking, and source identification for debugging.

## 3. Navigation Flow

**Reasoning:**
- **Navigation Trigger**: Automatic completion after successful API streaming at line 321
- **Navigation Action**: `completeStepAndGoNext` from `useRecipeNavigation` hook called at line 321
- **Modal Coordination**: `StreamingBottomSheet` provides UI feedback during streaming at lines 424-445
- **Modal State Management**: `setShowStreamingModal` manages modal visibility at lines 167 and 320
- **Completion Pattern**: Modal closes immediately after data storage and navigation triggers at line 320
- **Flow Pattern**: Symptoms → API Stream → Auto-navigation to Properties step
- **Haptic Feedback**: `haptics.success()` at line 319 provides tactile success feedback
- **Error Navigation**: Modal closes on error without navigation at line 335

**Conclusion:** Navigation automatically proceeds to Properties step after successful API streaming completion with modal UI feedback, haptic confirmation, and error handling following established wizard flow pattern.

## 4. Pre-navigation Processing

**Reasoning:**
- **API Streaming**: Real-time Server-Sent Events processing via `readSseStream` from `@/shared/utils/sse-reader` at line 219
- **Stream Processing**: `structured_data` events with field 'therapeutic_properties' processed at line 235
- **UI Updates**: Real-time streaming items display via `setStreamingItems` at lines 261-268
- **Error Handling**: Comprehensive try-catch blocks at lines 172 and 332 for API failures
- **Loading States**: `isStreaming` state management at lines 169 and 283
- **Response Validation**: Detailed response analysis including headers and body availability at lines 205-216
- **Data Validation**: Extensive logging and debugging for UUID preservation at lines 277-296
- **Comprehensive Logging**: Detailed request/response logging throughout the streaming process

**Conclusion:** Extensive real-time streaming processing with immediate UI updates, comprehensive error handling, detailed logging for debugging, and UUID preservation validation before automatic navigation completion.

## 5. End-to-End Data Architecture for This Step

**Analysis & Conclusion:** When the API returns streaming therapeutic properties data, ALL fields from the API response are preserved and stored in the state - unlike other steps that selectively extract data. During real-time streaming, individual `structured_data` events display basic fields (title, subtitle, description) in the UI, but the final `structured_complete` event contains the complete API payload which is fully mapped to the TherapeuticProperty interface without any data loss. No fields are discarded, ensuring complete relationship preservation between properties, causes, and symptoms through their UUID arrays.

**Evidence & Chain of Reasoning:**
- **API Response Structure:** The streaming endpoint returns both individual `structured_data` events for real-time UI updates and a final `structured_complete` event containing the complete therapeutic properties array in `symptoms-selection-screen.tsx:272-286`.
- **Real-Time Processing:** During streaming, `structured_data` events at lines 235-268 use only display fields (`property_id`, `property_name_localized`, `property_name_english`, `description_contextual_localized`) for immediate UI feedback, but preserve ALL original data via spread operator (`...parsedData.data`) at line 251.
- **Complete Data Extraction:** The final storage uses `structured_complete` event data exclusively at line 286 (`const completeProperties = parsedData.data?.data?.therapeutic_properties`), ignoring the accumulated streaming items to ensure complete field availability.
- **Comprehensive Interface Mapping:** The complete API response is mapped to `TherapeuticProperty` interface at lines 299-311, preserving ALL fields including: `property_id`, `property_name_localized`, `property_name_english`, `description_contextual_localized`, `addresses_cause_ids`, `addresses_symptom_ids`, `relevancy_score`, and `suggested_oils`.
- **Store Preservation:** The `updateTherapeuticProperties` action at line 314 stores the complete interface with all API fields intact, and the store action at `recipe.slice.ts:355-367` maintains all canonical fields without discarding any data.
- **UUID Relationship Integrity:** Critical relationship arrays `addresses_cause_ids` and `addresses_symptom_ids` are fully preserved from API to store, maintaining complete traceability between therapeutic properties and user-selected causes/symptoms for downstream processing.

## 6. File Dependencies

**Reasoning:**
- **Parent Files**:
  - Symptoms selection component as trigger location
  - `useCombinedRecipeStore` for data access and comprehensive storage operations
- **API Integration**:
  - `streamRecipeStep` from `@/shared/services/api/rotinanatural-client`
  - `readSseStream` from `@/shared/utils/sse-reader` for streaming processing
- **Related Files**:
  - `TherapeuticProperty` interface from types directory with complete field definitions
  - `haptics` utility for tactile feedback
  - `useLanguage` hook for API language parameter
- **UI Components**:
  - `StreamingBottomSheet` modal for user feedback with therapeutic properties analysis type
  - StreamingItem interface for real-time display
- **Shared Dependencies**:
  - `useRecipeNavigation` for completion navigation
  - React hooks (useCallback) for performance optimization
  - Comprehensive logging utilities for debugging and monitoring

**Conclusion:** Dependencies span API services, streaming utilities, comprehensive type definitions, UI feedback components, and navigation coordination with extensive logging and performance optimization across the create-recipe feature architecture.

## 7. Previous and Next Steps to be Documented

- **Previous:** create-recipe-docs/steps/doc-004-step-symptoms-analysis.md (already documented)
- **Current:** create-recipe-docs/steps/doc-005A-internal-therapeutic-properties-analysis.md
- **Next:** create-recipe-docs/steps/doc-005-step-properties-analysis.md (already documented)
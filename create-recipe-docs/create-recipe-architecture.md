# Create Recipe Feature Architecture

## Feature Structure
```
src/features/create-recipe/
├── components/
│   ├── screens/
│   │   ├── causes-selection/
│   │   │   ├── causes-selection-screen.tsx      # Step 3: Potential causes selection
│   │   │   ├── causes-list.tsx                  # Interactive causes list component
│   │   │   └── causes-card.tsx                  # Individual cause card with selection
│   │   ├── demographics/
│   │   │   ├── demographics-form.tsx            # Step 2: Age/gender/lifestyle form
│   │   │   ├── age-selector.tsx                 # Age category and specific age input
│   │   │   ├── gender-selector.tsx              # Gender selection component
│   │   │   └── lifestyle-factors.tsx            # Additional demographic factors
│   │   ├── final-recipes/
│   │   │   ├── final-recipes-list.tsx           # Step 6: Generated recipes display
│   │   │   ├── recipe-time-slot-card.tsx        # Morning/midday/night recipe cards
│   │   │   ├── recipe-ingredients-list.tsx      # Essential oils + carrier oil display
│   │   │   ├── recipe-instructions.tsx          # Preparation and usage instructions
│   │   │   └── recipe-safety-warnings.tsx       # Safety warnings and contraindications
│   │   ├── health-concern-screen.tsx            # Step 1: Health concern input (main screen)
│   │   ├── properties-selection/
│   │   │   ├── properties-selection-screen.tsx  # Step 5: Therapeutic properties selection
│   │   │   ├── property-card.tsx               # Individual property card with oils
│   │   │   ├── property-oils-list.tsx          # Suggested oils for each property
│   │   │   ├── enrichment-progress.tsx         # Oil enrichment loading states
│   │   │   └── generate-recipes-button.tsx     # Final recipe generation trigger
│   │   └── symptoms-selection/
│   │       ├── symptoms-selection-screen.tsx    # Step 4: Symptoms selection
│   │       ├── symptoms-list.tsx               # Interactive symptoms list
│   │       └── symptom-card.tsx                # Individual symptom card with selection
│   ├── modals/
│   │   ├── final-recipe-details-modal/
│   │   │   ├── index.tsx                       # Main modal container
│   │   │   ├── final-recipe-details-modal.tsx  # Complete recipe details view
│   │   │   ├── recipe-header.tsx               # Recipe name, theme, time slot
│   │   │   ├── recipe-content.tsx              # Ingredients, instructions, benefits
│   │   │   ├── recipe-navigation.tsx           # Time slot navigation controls
│   │   │   └── oil-substitution-trigger.tsx    # Trigger for oil substitution
│   │   ├── oil-substitution-bottom-sheet.tsx   # Oil substitution modal
│   │   └── parallel-streaming-bottom-sheet.tsx # AI streaming progress modal
│   └── ui/
│       ├── recipe-progress-indicator.tsx       # Step progress visualization
│       ├── streaming-progress-card.tsx         # Real-time streaming progress
│       ├── oil-safety-badge.tsx               # Safety status indicators
│       └── recipe-theme-card.tsx              # Recipe theme display components
├── hooks/
│   ├── use-causes-selection.ts                # Step 3: Causes selection logic
│   ├── use-create-recipe-streaming.ts         # AI streaming orchestration
│   ├── use-demographics-form.ts               # Step 2: Demographics form logic
│   ├── use-final-recipes.ts                   # Step 6: Final recipes management
│   ├── use-health-concern.ts                  # Step 1: Health concern input logic
│   ├── use-parallel-streaming-engine.ts       # Core parallel streaming engine
│   ├── use-properties-screen.ts               # Step 5: Properties screen state
│   ├── use-properties-selection.ts            # Step 5: Properties selection logic
│   ├── use-recipe-index-screen.ts             # Hub screen logic
│   ├── use-recipe-navigation.ts               # Cross-step navigation logic
│   └── use-symptoms-selection.ts              # Step 4: Symptoms selection logic
├── store/
│   ├── combined-store.ts                      # Main store combining recipe + UI slices
│   ├── recipe.slice.ts                        # Core recipe data management
│   │                                          # - Health concern, demographics, selections
│   │                                          # - Therapeutic properties with enrichment
│   │                                          # - Post-enrichment scoring results storage
│   │                                          # - Final recipes for 3 time slots
│   │                                          # - Navigation state and step completion
│   └── ui.slice.ts                           # UI state management (loading, errors, modals)
├── services/
│   ├── index.ts                              # Service exports barrel
│   ├── recipe.service.ts                     # Core recipe API operations
│   │                                         # - Health concern analysis
│   │                                         # - Demographics processing
│   │                                         # - Causes/symptoms fetching
│   ├── streaming.service.ts                  # AI streaming business logic
│   │                                         # - Oil suggestion streaming requests
│   │                                         # - Final recipe streaming (uses pre-calculated data)
│   │                                         # - Parallel request orchestration
│   └── enrichment.service.ts                 # Oil enrichment operations
│                                             # - Batch oil enrichment API calls
│                                             # - Safety data integration
│                                             # - Botanical data enhancement
├── types/
│   └── recipe.types.ts                       # Complete type definitions
│                                             # - Recipe steps and navigation
│                                             # - Health data interfaces
│                                             # - Oil and property structures
│                                             # - API request/response types
│                                             # - Final recipe protocols
└── utils/
    ├── post-enrichment-scoring.ts            # Hybrid scoring algorithm
    │                                         # - Mathematical scoring engine
    │                                         # - Oil deduplication logic
    │                                         # - Safety library building
    │                                         # - API payload preparation
    ├── recipe-analytics.ts                   # Recipe analytics tracking
    └── navigation-utils.ts                   # Navigation helper functions
```

## Routes
```
src/app/(drawer)/(tabs)/create-recipe/
├── _layout.tsx                  # Navigator
├── index.tsx                    # Hub
├── health-concern.tsx           # Step 1
├── demographics.tsx             # Step 2
├── causes.tsx                   # Step 3
├── symptoms.tsx                 # Step 4
├── properties.tsx               # Step 5
└── final-recipes.tsx           # Step 6
```

## Key Patterns
- **State**: Zustand combined store (recipe.slice + ui.slice)
- **Streaming**: SSE for AI responses via streaming.service.ts
- **Navigation**: Linear 6-step progression with validation
- **Modals**: Instagram-style via useModal() hook

## Data Flow
1. Input Collection: health-concern → demographics → causes → symptoms
2. AI Processing: properties → oil suggestion → enrichment → scoring (once, stored)
3. Recipe Generation: 3 time-specific protocols using pre-calculated data

---

# Shared Folder Architecture

## Structure
```
src/shared/
├── components/
│   ├── layout/                  # screen-wrapper, app-bar, bottom-nav
│   ├── modals/streaming/        # AI streaming modals
│   ├── ui/                      # reusable components
│   └── data-display/           # universal-list, selectable-data-list
├── hooks/                       # use-theme, use-modal-manager, use-ai-streaming
├── services/                    # api clients, supabase, integrations
├── contexts/                    # user-preferences, email-verification
├── providers/                   # haptics, realtime
├── locales/                     # i18n (en/pt namespaces)
├── styles/                      # material-theme, design-tokens
└── utils/                       # mobile-sse-client, haptics, theme-utils
```

## Key Components
- **ScreenWrapper**: Standard layout + modal integration
- **Theme System**: useTheme() hook + Material Design 3
- **i18n**: Namespace-based (auth, create-recipe, oil-calculator)
- **Streaming**: Mobile SSE client for AI responses
- **Modal System**: Instagram-style modals via useModal()
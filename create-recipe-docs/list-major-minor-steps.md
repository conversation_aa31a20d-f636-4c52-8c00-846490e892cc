Here is a complete list of the major steps, including these internal processes.

***

### Create Recipe Feature: Complete Step-by-Step Flow

#### 1. **User-Facing Step: Health Concern**
* The user provides their primary health concern as a string.

#### 2. **User-Facing Step: Demographics**
* The user provides their age and gender.

    * **Automated Internal Step: Potential Causes Analysis (API Stream)**
        * **Purpose:** To generate a list of potential causes for the user's health concern, tailored to their demographics.
        * **Process:** An API call is made to the `/ai/streaming` endpoint with the `healthConcern` and `demographics` data. The API streams back a list of `PotentialCause` objects.
        * **Next Step Impact:** This process generates the `potentialCauses` data, which is essential for populating the selection list on the next screen.

#### 3. **User-Facing Step: Causes**
* The user selects relevant causes from the list generated in the previous step.

    * **Automated Internal Step: Potential Symptoms Analysis (API Stream)**
        * **Purpose:** To generate a list of symptoms related to the selected causes.
        * **Process:** An API call is made to the `/ai/streaming` endpoint, sending the `healthConcern`, `demographics`, and `selectedCauses`. The API streams back a list of `PotentialSymptom` objects.
        * **Next Step Impact:** This generates the `potentialSymptoms` data required to populate the list on the Symptoms screen.

#### 4. **User-Facing Step: Symptoms**
* The user selects the symptoms they are experiencing from the generated list.

    * **Automated Internal Step: Therapeutic Properties Analysis (API Stream)**
        * **Purpose:** To identify the therapeutic properties needed to address the selected causes and symptoms.
        * **Process:** An API call sends all previously collected data to the `/ai/streaming` endpoint. The API streams back a list of `TherapeuticProperty` objects.
        * **Next Step Impact:** This process provides the `therapeuticProperties` data that forms the basis of the Properties screen.

#### 5. **User-Facing Step: Properties**
* The application displays the generated therapeutic properties. Several automated processes are triggered upon entering this step to prepare for the final recipes.

    * **Automated Internal Step: Suggested Oils Analysis (Parallel API Stream)**
        * **Purpose:** To find suggested essential oils for each therapeutic property.
        * **Process:** Parallel streaming requests are made to the `/ai/streaming` endpoint for each property to get a list of suggested oils.
        * **Next Step Impact:** This populates the `suggested_oils` array within each `TherapeuticProperty` object, which is required for the *Oil Data Enrichment* step.

    * **Automated Internal Step: Oil Data Enrichment (Batch API Call)**
        * **Purpose:** To fetch detailed safety, botanical, and usage data for all suggested oils.
        * **Process:** Once suggested oils are received, a call is made to the `/ai/batch-enrichment` endpoint. This "enriches" the oil objects with comprehensive data and sets the `isEnriched` flag to true.
        * **Next Step Impact:** This provides the detailed oil data and safety information needed for the *Post-Enrichment Scoring* step.

    * **Automated Internal Step: Post-Enrichment Scoring (Calculation)**
        * **Purpose:** To calculate a final, holistic relevance score for each unique oil based on its effectiveness across all selected properties.
        * **Process:** The `calculatePostEnrichmentScores` utility runs after all oils are enriched. It computes a `final_relevance_score` and `specialization_score` for each oil.
        * **Next Step Impact:** This scoring is the final and most critical input for generating the final, optimized recipes.

#### 6. **User-Facing Step: Final Recipes**
* The user reviews the fully generated, personalized recipes for morning, mid-day, and night.

    * **Automated Internal Step 6A: Pre-Recipe Data Validation & Preparation**
        * **Purpose:** Validate that all prerequisite data is available and prepare for final recipe generation.
        * **Process:** Check for health concern, demographics, selected causes/symptoms, and enriched therapeutic properties. Verify that properties have been enriched with safety data. Located in `use-final-recipes.ts:47-64`.
        * **Next Step Impact:** Ensures data integrity before expensive API calls and prevents incomplete recipe generation.

    * **Automated Internal Step 6B: Post-Enrichment Scoring Execution**
        * **Purpose:** Calculate final relevance scores and build safety library from all enriched oils data.
        * **Process:** Execute `calculatePostEnrichmentScores()` function which performs oil deduplication, hybrid scoring algorithm, and safety library aggregation. Located in `use-final-recipes.ts:78-85`.
        * **Next Step Impact:** Creates optimized, scored oil selections and comprehensive safety data required for intelligent recipe generation.

    * **Automated Internal Step 6C: Time-Slot API Request Preparation**
        * **Purpose:** Prepare three parallel streaming requests with time-specific optimization data.
        * **Process:** Create base API data and generate individual requests for morning/mid-day/night time slots using `createFinalRecipeStreamingRequests()`. Each request includes scored oils, safety library, and time-of-day parameter. Located in `streaming.service.ts:103-165`.
        * **Next Step Impact:** Enables AI to generate time-of-day optimized recipes with comprehensive safety and effectiveness data.

    * **Automated Internal Step 6D: Parallel Recipe Generation (API Stream)**
        * **Purpose:** Generate three distinct, time-of-day specific recipes using parallel streaming.
        * **Process:** Three parallel streaming requests sent to `/ai/streaming` endpoint with step: 'final-recipes'. Each stream returns complete `FinalRecipeProtocol` with recipe details, selected oils, preparation steps, and usage instructions. Located in `use-final-recipes.ts:120-141`.
        * **Next Step Impact:** Produces complete time-specific recipes with ingredients, formulation, preparation steps, and safety warnings.

    * **Automated Internal Step 6E: Recipe Data Storage & State Management**
        * **Purpose:** Process streaming results and store individual recipes for each time slot.
        * **Process:** Parse each streaming response, validate recipe data structure, and call `updateFinalRecipes()` for morning/midDay/night time slots. Located in `use-final-recipes.ts:125-141`.
        * **Next Step Impact:** Populates `finalRecipes` state enabling user interface display of complete personalized recipes.

    * **Automated Internal Step 6F: UI State Coordination & Display Preparation**
        * **Purpose:** Coordinate loading states, error handling, and prepare recipe data for user interface display.
        * **Process:** Manage streaming modal visibility, process completion detection, error state handling, and recipe availability computation. Transform recipe data structure for modal consumption. Located in `final-recipes.tsx:225-253`.
        * **Next Step Impact:** Enables seamless user experience with appropriate loading feedback, error recovery, and complete recipe display interface.

#### 7. **User-Facing Step: Recipe Display & Interaction**
* The user views, interacts with, and manages their personalized aromatherapy recipes through a comprehensive interface.

    * **Automated Internal Step 7A: Recipe Data Presentation & Formatting**
        * **Purpose:** Transform stored recipe data into user-friendly display formats with proper localization and formatting.
        * **Process:** Access recipe data from Zustand store via computed properties, format oil names and quantities, localize preparation steps and usage instructions, and structure data for accordion-style display. Located in `final-recipes-list.tsx:142-436` and `final-recipes-tabs.tsx:131-145`.
        * **Next Step Impact:** Provides formatted, localized recipe content ready for user consumption with proper visual hierarchy and accessibility.

    * **Automated Internal Step 7B: Interactive Component Rendering & State Management**
        * **Purpose:** Render interactive recipe components with expandable sections, oil substitution capabilities, and responsive design.
        * **Process:** Manage accordion section expansion states, handle oil substitution interactions, coordinate tab navigation between Overview/Safety views, and maintain component-level state for user interactions. Located in `final-recipes-list.tsx:145-156` and `final-recipes-cards.tsx:50-85`.
        * **Next Step Impact:** Enables rich user interactions including recipe exploration, oil substitution, and multi-view navigation for comprehensive recipe management.

    * **Automated Internal Step 7C: Safety Information Aggregation & Display**
        * **Purpose:** Aggregate and display comprehensive safety warnings and guidance across all generated recipes.
        * **Process:** Collect safety warnings from all time slot recipes, deduplicate warnings by type and severity, format safety guidance with appropriate visual indicators, and present consolidated safety information in dedicated Safety tab. Located in `final-recipes-tabs.tsx:159-174`.
        * **Next Step Impact:** Provides users with critical safety information and usage guidance to ensure safe and effective aromatherapy practice.

    * **Automated Internal Step 7D: Recipe Persistence & Export Capabilities**
        * **Purpose:** Enable users to save, export, and manage their personalized recipe protocols for future reference.
        * **Process:** Integrate with saved recipes service for protocol persistence, provide export functionality for recipe sharing, enable recipe modification and re-generation, and maintain user recipe history. Located in `saved-recipes.service.ts:40-179` and related save recipe components.
        * **Next Step Impact:** Completes the recipe creation journey by enabling long-term recipe management and practical application of generated aromatherapy protocols.

***

### End-to-End Data Flow Summary

**Complete Data Journey:** User Input → AI Analysis → Data Enrichment → Scoring Optimization → Recipe Generation → UI Presentation → User Interaction

1. **User Data Collection (Steps 1-4):** Health concern string → Demographics object → Selected causes array → Selected symptoms array
2. **AI-Driven Analysis (Internal Steps):** Streaming API calls generate potential causes, symptoms, and therapeutic properties based on user context
3. **Oil Discovery & Enrichment (Step 5 Internal):** Parallel streaming finds suggested oils → Batch enrichment adds safety/botanical data → Properties marked as enriched
4. **Intelligent Scoring (Step 6A-6B):** Post-enrichment scoring calculates final relevance scores using hybrid algorithm (specialization 60% + holistic 30% + coverage 10%)
5. **Recipe Generation (Step 6C-6D):** Time-slot specific API requests → Parallel streaming generates morning/mid-day/night recipes → Complete FinalRecipeProtocol objects created
6. **Data Storage & State Management (Step 6E-6F):** Streaming results validated and stored in Zustand store → UI state computed from stored data → Modal and loading states coordinated
7. **User Interface & Interaction (Step 7A-7D):** Recipe data formatted for display → Interactive components rendered → Safety information aggregated → Persistence and export capabilities enabled

**Key Data Transformations:**
- Raw user input → Structured API requests → AI-generated suggestions → User selections → Enriched oil data → Scored recommendations → Time-optimized recipes → Interactive UI components → Persistent user protocols
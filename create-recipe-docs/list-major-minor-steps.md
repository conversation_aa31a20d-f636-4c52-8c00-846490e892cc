Here is a complete list of the major steps, including these internal processes.

***

### Create Recipe Feature: Complete Step-by-Step Flow

#### 1. **User-Facing Step: Health Concern**
* The user provides their primary health concern as a string.

#### 2. **User-Facing Step: Demographics**
* The user provides their age and gender.

    * **Automated Internal Step: Potential Causes Analysis (API Stream)**
        * **Purpose:** To generate a list of potential causes for the user's health concern, tailored to their demographics.
        * **Process:** An API call is made to the `/ai/streaming` endpoint with the `healthConcern` and `demographics` data. The API streams back a list of `PotentialCause` objects.
        * **Next Step Impact:** This process generates the `potentialCauses` data, which is essential for populating the selection list on the next screen.

#### 3. **User-Facing Step: Causes**
* The user selects relevant causes from the list generated in the previous step.

    * **Automated Internal Step: Potential Symptoms Analysis (API Stream)**
        * **Purpose:** To generate a list of symptoms related to the selected causes.
        * **Process:** An API call is made to the `/ai/streaming` endpoint, sending the `healthConcern`, `demographics`, and `selectedCauses`. The API streams back a list of `PotentialSymptom` objects.
        * **Next Step Impact:** This generates the `potentialSymptoms` data required to populate the list on the Symptoms screen.

#### 4. **User-Facing Step: Symptoms**
* The user selects the symptoms they are experiencing from the generated list.

    * **Automated Internal Step: Therapeutic Properties Analysis (API Stream)**
        * **Purpose:** To identify the therapeutic properties needed to address the selected causes and symptoms.
        * **Process:** An API call sends all previously collected data to the `/ai/streaming` endpoint. The API streams back a list of `TherapeuticProperty` objects.
        * **Next Step Impact:** This process provides the `therapeuticProperties` data that forms the basis of the Properties screen.

#### 5. **User-Facing Step: Properties**
* The application displays the generated therapeutic properties. Several automated processes are triggered upon entering this step to prepare for the final recipes.

    * **Automated Internal Step: Suggested Oils Analysis (Parallel API Stream)**
        * **Purpose:** To find suggested essential oils for each therapeutic property.
        * **Process:** Parallel streaming requests are made to the `/ai/streaming` endpoint for each property to get a list of suggested oils.
        * **Next Step Impact:** This populates the `suggested_oils` array within each `TherapeuticProperty` object, which is required for the *Oil Data Enrichment* step.

    * **Automated Internal Step: Oil Data Enrichment (Batch API Call)**
        * **Purpose:** To fetch detailed safety, botanical, and usage data for all suggested oils.
        * **Process:** Once suggested oils are received, a call is made to the `/ai/batch-enrichment` endpoint. This "enriches" the oil objects with comprehensive data and sets the `isEnriched` flag to true.
        * **Next Step Impact:** This provides the detailed oil data and safety information needed for the *Post-Enrichment Scoring* step.

    * **Automated Internal Step: Post-Enrichment Scoring (Calculation)**
        * **Purpose:** To calculate a final, holistic relevance score for each unique oil based on its effectiveness across all selected properties.
        * **Process:** The `calculatePostEnrichmentScores` utility runs after all oils are enriched. It computes a `final_relevance_score` and `specialization_score` for each oil.
        * **Next Step Impact:** This scoring is the final and most critical input for generating the final, optimized recipes.

#### 6. **User-Facing Step: Final Recipes**
* The user reviews the fully generated, personalized recipes for morning, mid-day, and night.

    * **Automated Internal Step 6A: Pre-Recipe Data Validation & Preparation**
        * **Purpose:** Validate that all prerequisite data is available and prepare for final recipe generation.
        * **Process:** Check for health concern, demographics, selected causes/symptoms, and enriched therapeutic properties. Verify that properties have been enriched with safety data. Located in `use-final-recipes.ts:47-64`.
        * **Next Step Impact:** Ensures data integrity before expensive API calls and prevents incomplete recipe generation.

    * **Automated Internal Step 6B: Post-Enrichment Scoring Execution**
        * **Purpose:** Calculate final relevance scores and build safety library from all enriched oils data.
        * **Process:** Execute `calculatePostEnrichmentScores()` function which performs oil deduplication, hybrid scoring algorithm, and safety library aggregation. Located in `use-final-recipes.ts:78-85`.
        * **Next Step Impact:** Creates optimized, scored oil selections and comprehensive safety data required for intelligent recipe generation.

    * **Automated Internal Step 6C: Time-Slot API Request Preparation**
        * **Purpose:** Prepare three parallel streaming requests with time-specific optimization data.
        * **Process:** Create base API data and generate individual requests for morning/mid-day/night time slots using `createFinalRecipeStreamingRequests()`. Each request includes scored oils, safety library, and time-of-day parameter. Located in `streaming.service.ts:103-165`.
        * **Next Step Impact:** Enables AI to generate time-of-day optimized recipes with comprehensive safety and effectiveness data.

    * **Automated Internal Step 6D: Parallel Recipe Generation (API Stream)**
        * **Purpose:** Generate three distinct, time-of-day specific recipes using parallel streaming.
        * **Process:** Three parallel streaming requests sent to `/ai/streaming` endpoint with step: 'final-recipes'. Each stream returns complete `FinalRecipeProtocol` with recipe details, selected oils, preparation steps, and usage instructions. Located in `use-final-recipes.ts:120-141`.
        * **Next Step Impact:** Produces complete time-specific recipes with ingredients, formulation, preparation steps, and safety warnings.

    * **Automated Internal Step 6E: Recipe Data Storage & State Management**
        * **Purpose:** Process streaming results and store individual recipes for each time slot.
        * **Process:** Parse each streaming response, validate recipe data structure, and call `updateFinalRecipes()` for morning/midDay/night time slots. Located in `use-final-recipes.ts:125-141`.
        * **Next Step Impact:** Populates `finalRecipes` state enabling user interface display of complete personalized recipes.

    * **Automated Internal Step 6F: UI State Coordination & Display Preparation**
        * **Purpose:** Coordinate loading states, error handling, and prepare recipe data for user interface display.
        * **Process:** Manage streaming modal visibility, process completion detection, error state handling, and recipe availability computation. Transform recipe data structure for modal consumption. Located in `final-recipes.tsx:225-253`.
        * **Next Step Impact:** Enables seamless user experience with appropriate loading feedback, error recovery, and complete recipe display interface.
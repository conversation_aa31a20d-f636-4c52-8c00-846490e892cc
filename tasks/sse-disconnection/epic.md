Epic: App Stability for Background/Foreground TransitionsGoal: To eliminate a critical stability issue where the app crashes or errors out if the user backgrounds it during a data-intensive streaming operation. This epic will make the application more robust and reliable, aligning with our "Meticulous Craft" and "Users First" design principles.User Story: STORY: Ensure App Stability When Switching To and From Other Apps1. Problem DescriptionThe User Experience Issue:When a user starts a long-running process in the app (like generating a recipe), they often switch to another app (e.g., to answer a message) and then return. Currently, this action causes the app to display a "XMLHttpRequest failed" error, and the process is broken. This is frustrating, causes the user to lose their progress, and makes the app feel unprofessional and unstable.The Technical Root Cause:Our application uses Server-Sent Events (SSE) for real-time data streaming, powered by the MobileSSEClient. When the app is moved to the background, the mobile operating system (iOS/Android) terminates the long-running XMLHttpRequest to save battery. Our code does not currently handle this interruption gracefully, leading to the crash.2. Where the User Encounters This IssueThis problem occurs specifically within the Create Recipe Wizard. The user interacts with this feature by navigating from the home screen to "Create Recipe" (app/(tabs)/create-recipe/index.tsx). The error is triggered on any of the following screens after the user has made their selections and a streaming API call is initiated:app/(tabs)/create-recipe/causes.tsx: After selecting demographics and tapping "Discover Potential Causes".app/(tabs)/create-recipe/symptoms.tsx: After selecting causes and tapping "Find Your Symptoms".app/(tabs)/create-recipe/properties.tsx: After selecting symptoms and tapping "Explore Properties" (auto-triggers oil suggestions).app/(tabs)/create-recipe/final-recipes.tsx: When the final three recipes are generated in parallel.3. Solution OverviewWe will implement the "Disconnect on Background, Manual Re-initiation with Feedback" architectural pattern.Detect App State: We will use React Native's built-in AppState API to detect when the app moves to the background or becomes inactive.Gracefully Disconnect: Before the OS can terminate the connection, our code will proactively abort all active SSE streams.Ensure Stability: When the user returns to the app, it will be in a stable, ready state, with no errors.Provide Contextual Feedback: <!-- QA Note: Added Snackbar to the solution overview. --> Upon returning to the app, a temporary Snackbar notification will inform the user that the previous action was interrupted and guide them to retry.Manual Re-initiation: The user will be able to simply tap the button again to restart the process from the beginning of that step.4. Business ValueIncreased Reliability: Drastically reduces app crashes, improving user trust.Improved User Experience: Aligns the app's behavior with modern mobile application standards and clearly communicates state changes to the user.Better Resource Management: Conserves user's battery and mobile data by not running network processes in the background.5. ScopeIn Scope:Implementing AppState listeners to manage SSE connections.Centralizing this logic within the use-parallel-streaming-engine.ts hook.<!-- QA Note: Added Snackbar implementation to the scope. --> Implementing a mechanism for the hook to signal an interruption to the UI layer, which will then display a Snackbar.Ensuring the app remains stable when backgrounded during any SSE stream within the "Create Recipe" flow.Out of Scope:Implementing an automatic reconnection or "resume" mechanism for backgrounded streams.Creating a new, app-wide notification system. The existing React Native Paper Snackbar is sufficient.6. Definition of DoneThe XMLHttpRequest failed error is no longer reproducible.The app gracefully handles backgrounding and foregrounding during all streaming steps of the recipe wizard.A Snackbar is displayed upon returning to the app after a stream was interrupted.The user can successfully restart a streaming process after returning to the app.All acceptance criteria in the related User Story are met and verified.
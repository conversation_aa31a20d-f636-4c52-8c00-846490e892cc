Engineering & Architectural Requirements: AppState-Aware SSE ManagementEpic: EPIC: App Stability for Background/Foreground TransitionsStory: STORY: Ensure App Stability When Switching To and From Other Apps1. Context & Architectural RationaleProblem: The MobileSSEClient does not handle being backgrounded by the mobile OS, leading to an unhandled error and app crash.Architectural Decision: We will centralize lifecycle-aware logic in our primary streaming hook, use-parallel-streaming-engine.ts. When the app is backgrounded, the hook will disconnect all active streams. <!-- QA Note: Added Snackbar rationale. --> Crucially, the hook will also provide a callback mechanism to notify the UI that an interruption occurred. The UI component will then be responsible for displaying a Snackbar to the user, maintaining a clean separation of concerns between the data layer (the hook) and the view layer (the component).2. Detailed Implementation InstructionsPart A: Modify the Streaming HookFile to Modify: features/create-recipe/hooks/use-parallel-streaming-engine.tsDependencies to Import: Add these to the top of the file.import { useEffect, useRef, useCallback } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { MobileSSEClient } from '../../utils/mobile-sse-client';
Step-by-Step Guide:Add onInterrupted Callback to Hook Signature:Modify the hook's function signature and its return type to include a new callback.// Modify the hook's props and return type
export interface UseParallelStreamingEngineProps {
  onInterrupted?: () => void; // New callback
}

export function useParallelStreamingEngine<T = any>({ onInterrupted }: UseParallelStreamingEngineProps = {}): UseParallelStreamingEngineReturn<T> {
  // ... existing hook logic
}
Initialize a Ref for Active Clients:(This step is unchanged)const activeClients = useRef<MobileSSEClient[]>([]);
Subscribe to AppState Changes (Updated):Modify the useEffect to call the onInterrupted callback.useEffect(() => {
  const handleAppStateChange = (nextAppState: AppStateStatus) => {
    if (nextAppState.match(/inactive|background/)) {
      if (activeClients.current.length > 0) {
        console.log('App has gone to the background. Disconnecting all active SSE streams.');
        activeClients.current.forEach(client => client.disconnect());
        activeClients.current = [];

        // === Add this line to notify the UI ===
        onInterrupted?.();
        // =======================================
      }
    }
  };

  const subscription = AppState.addEventListener('change', handleAppStateChange);

  return () => {
    subscription.remove();
    activeClients.current.forEach(client => client.disconnect());
  };
}, [onInterrupted]); // Add onInterrupted to the dependency array
Track and Clear Clients:(These steps from the previous version are unchanged and still required)Part B: Modify the UI Screens to Display a SnackbarFiles to Modify: app/(tabs)/create-recipe/causes.tsx, .../symptoms.tsx, .../properties.tsx, .../final-recipes.tsxDependencies to Import: Add useState, useCallback, and Snackbar from react-native-paper.Step-by-Step Guide for each screen:Add State for Snackbar:Inside the component (e.g., CausesScreen), add state to control the Snackbar's visibility.const [interruptionMessageVisible, setInterruptionMessageVisible] = useState(false);
Implement the onInterrupted Callback:Create a useCallback that will set the snackbar's state to visible.const handleStreamInterruption = useCallback(() => {
  // This will be called by the hook when the app is backgrounded
  setInterruptionMessageVisible(true);
}, []);
Pass the Callback to the Hook:When you call the streaming hook (e.g., usePropertiesSelection, which uses useParallelStreamingEngine internally), you'll need to pass this new callback. Note: This will require modifying the intermediate hooks (usePropertiesSelection, etc.) to accept and pass down the onInterrupted prop.// Example in properties.tsx
const { handleSubmit } = usePropertiesSelection({ onInterrupted: handleStreamInterruption });
Render the Snackbar Component:Add the Snackbar component to the ScreenWrapper's JSX.<ScreenWrapper {...props}>
  {/* ... existing screen content ... */}
  <Snackbar
    visible={interruptionMessageVisible}
    onDismiss={() => setInterruptionMessageVisible(false)}
    duration={Snackbar.DURATION_MEDIUM}
    action={{
      label: 'Dismiss',
      onPress: () => setInterruptionMessageVisible(false),
    }}>
    Your analysis was interrupted. Please try again.
  </Snackbar>
</ScreenWrapper>
3. Validation & Testing Plan (Updated)Launch & Navigate: (Unchanged)Initiate a Stream: (Unchanged)Background the App: (Unchanged)Check Console Logs: (Unchanged)Wait: (Unchanged)Foreground the App: (Unchanged)Verify Stability & Snackbar: Confirm the app is responsive, has NO errors, and a Snackbar notification is visible at the bottom of the screen.Verify Re-initiation: (Unchanged)Confirm Success: (Unchanged)Test Edge Case: (Unchanged)4. Technical Acceptance Criteria (Updated)(Unchanged) The use-parallel-streaming-engine.ts hook must correctly subscribe and unsubscribe to AppState changes.(Unchanged) All MobileSSEClient instances must be tracked in the activeClients.current ref.(Unchanged) When the app is backgrounded, disconnect must be called on all active clients.<!-- QA Note: Added criteria for the new callback and Snackbar. -->New: The onInterrupted callback must be invoked by the hook when a disconnection due to backgrounding occurs.New: The UI screen must render a Snackbar when the onInterrupted callback is fired.(Unchanged) The activeClients.current array must be cleared after disconnection or successful completion.(Unchanged) The implementation must not introduce regressions.
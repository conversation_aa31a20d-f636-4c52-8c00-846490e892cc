User Story: Ensure App Stability When Switching To and From Other AppsEpic: EPIC: App Stability for Background/Foreground TransitionsAs a user,I want the application to remain stable and not show an error when I switch to another app (background) and then return (foreground) while it's performing a long action, like generating recipes,So that I don't lose my context or become frustrated, and can seamlessly resume using the app.Acceptance CriteriaGiven I am in the "Create Recipe" wizard and have initiated a data streaming step (e.g., finding causes, symptoms, or properties),When I switch to another app for any duration and then return to the AromaCHAT app,Then I do not see an "XMLHttpRequest failed" error or any other crash-related message.Given I have returned to the app after it was backgrounded during a stream,Then the streaming modal (e.g., "Analyzing Potential Causes...") is dismissed, and the screen is in a ready state for me to restart the action.&lt;!-- QA Note: Added a new AC specifically for the Snackbar feedback. --&gt;

**Given** I have returned to the app after a stream was interrupted by backgrounding,
**When** the screen becomes visible,
**Then** a `Snackbar` briefly appears with a message like `"Your analysis was interrupted. Please try again."` to provide context.
Given I have returned to the app and the UI is in a ready state,When I tap the button to re-initiate the action (e.g., "Discover Potential Causes"),Then the streaming process begins again successfully from the start of that step.Given I am in a streaming step,When I background and foreground the app multiple times,Then the app remains responsive and stable each time I return.Technical Context for ImplementationWhere This Happens in the AppThis issue affects the Create Recipe feature, which involves several steps where the app streams data from our AI backend. A developer can trigger this by:Navigating to Create Recipe.Completing the "Health Concern" and "Demographics" steps.On the "Potential Causes" screen (app/(tabs)/create-recipe/causes.tsx), tapping the "Discover Potential Causes" button triggers the first streaming call.Backgrounding the app while the streaming modal is active will cause the crash upon return.This same behavior occurs on the subsequent Symptoms (.../symptoms.tsx) and Properties (.../properties.tsx) screens.Relevant Files for This Taskfeatures/create-recipe/hooks/use-parallel-streaming-engine.ts: This is the primary file to be modified. It is the central hook that manages all parallel SSE streaming requests. The AppState logic will be added here.The UI screens themselves (e.g., .../causes.tsx, .../symptoms.tsx): These screens will need to be modified to receive a signal from the hook and display the Snackbar.shared/utils/mobile-sse-client.ts: This file contains the MobileSSEClient class. No changes are needed in this file.Architectural DecisionWe are implementing the "Disconnect on Background, Manual Re-initiation with Feedback" pattern. The hook will manage the disconnection, and the UI will be responsible for showing a Snackbar to inform the user.

# Analysis of Mobile SSE `XMLHttpRequest failed` Error

## 1. Problem Summary

Users are encountering an `ERROR 📱 [API-ios] Mobile SSE error {"error": "XMLHttpRequest failed"}` when they background the application and then return to it while a Server-Sent Events (SSE) connection is active, particularly during the "create-recipe" feature. This indicates that the underlying `XMLHttpRequest` is being terminated, and the client is not gracefully handling this interruption.

## 2. Root Cause Analysis

The root cause of this issue is a fundamental behavior of mobile operating systems (iOS and Android). To preserve battery life and system resources, the OS will often suspend or terminate long-running network connections, such as the one used by our `MobileSSEClient`, when the application is moved to the background.

The current implementation of `MobileSSEClient` does not account for this. When the user backgrounds the app, the OS closes the TCP socket for the `XMLHttpRequest`. Upon returning to the app, the client's `xhr` object is in a failed state, which triggers the `onerror` event handler, leading to the "XMLHttpRequest failed" error. The client does not have a mechanism to automatically detect the app's state change and re-establish the connection.

## 3. Proposed Solution

The most robust solution is to make the `MobileSSEClient` lifecycle aware of the application's state. We can achieve this by using React Native's built-in `AppState` API.

The strategy is as follows:

1.  **Listen for AppState changes:** When the component that initiates the SSE connection mounts, it should subscribe to `AppState` changes.
2.  **Disconnect on Background:** When the app state changes to `background` or `inactive`, we should gracefully close the SSE connection by calling the `client.disconnect()` method. This prevents the OS from forcefully terminating it and putting the client into an error state.
3.  **Reconnect on Foreground:** When the app state changes back to `active`, we should re-establish the SSE connection by calling `client.connect()`.

This approach ensures that the connection is only active when the app is in the foreground, preventing unexpected errors and conserving resources.

## 4. High-Level Implementation Steps

This logic should be implemented in the React component or custom hook that utilizes the `MobileSSEClient`. Here is a conceptual example of how to wrap an SSE-dependent feature with `AppState` handling:

```typescript
import { AppState, AppStateStatus } from 'react-native';
import { useEffect, useRef, useState } from 'react';
import { MobileSSEClient } from './src/shared/utils/mobile-sse-client'; // Adjust path as needed

const useSseWithAppState = (url: string, requestData: any, headers: Record<string, string>) => {
  const sseClientRef = useRef<MobileSSEClient | null>(null);
  const appState = useRef(AppState.currentState);

  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        console.log('App has come to the foreground, reconnecting SSE...');
        connectSse();
      } else if (nextAppState.match(/inactive|background/)) {
        console.log('App has gone to the background, disconnecting SSE...');
        disconnectSse();
      }
      appState.current = nextAppState;
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    const connectSse = () => {
      if (sseClientRef.current && sseClientRef.current.isActive()) {
        console.log('SSE client is already active.');
        return;
      }
      
      const client = new MobileSSEClient({
        onMessage: (data) => {
          // Handle incoming messages
        },
        onError: (error) => {
          console.error('SSE Error:', error);
          // Optionally handle retries here
        },
        // ... other options
      });

      client.connect(url, requestData, headers).catch(err => {
        console.error('SSE connection failed to start:', err);
      });
      sseClientRef.current = client;
    };

    const disconnectSse = () => {
      if (sseClientRef.current) {
        sseClientRef.current.disconnect();
        sseClientRef.current = null;
      }
    };

    // Initial connection
    connectSse();

    return () => {
      subscription.remove();
      disconnectSse();
    };
  }, [url, requestData, headers]); // Add other dependencies as needed
};

// --- In your component ---
// useSseWithAppState('https://.../stream', { recipeId: '...' }, { Authorization: '...' });
```

## 5. Benefits of This Approach

*   **Increased Stability:** Prevents crashes and error states caused by OS-level network interruptions.
*   **Improved User Experience:** The connection is seamlessly restored when the user returns to the app, allowing the feature to continue functioning correctly.
*   **Resource Efficiency:** By disconnecting in the background, the app avoids unnecessary network activity and battery drain.
*   **Reduced Errors:** Eliminates the stream of "XMLHttpRequest failed" errors in logs, making debugging other issues easier.

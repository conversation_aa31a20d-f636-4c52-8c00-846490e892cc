sse-disconnection-analysis
substitution oil
save recipe


Please double check all files at 'tasks/recipe-oil-substitution' folder and make sure to find evidence in the codebase to cross check everything to make sure it will be able to implement this feature to perfection.\
  It was a junior dev that create this 'plan" and nobody checked yet and cross-reference with the actual codebase to see if the junior did not miss important details.\
  \
  Please ultrathink it to find problems or not, but make sure it will work after implementation. You will make a systematic review following the data and make and evidance-code-based analytical task. Output this analysis in a report at a .md file in the 
  tasks/recipe-oil-substitution folder in a separate file.
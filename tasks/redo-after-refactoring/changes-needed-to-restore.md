# Changes Needed to Restore After Protocol Detail Modal Refactoring

This file tracks all temporary changes made during the protocol detail modal refactoring period (3-5 days). Use this checklist to restore full functionality once the modal component is ready.

## File: `/home/<USER>/aromachat/src/app/(drawer)/saved-protocols.tsx`

### Import Statements to Restore

**Line 10**: Uncomment the useModalManager import
```tsx
// Current (commented):
// import { useModalManager } from '@/shared/hooks/use-modal-manager'; // TODO: Re-enable after modal refactoring

// Restore to:
import { useModalManager } from '@/shared/hooks/use-modal-manager';
```

**Line 14**: Uncomment the ProtocolDetailModal import
```tsx
// Current (commented):
// import { ProtocolDetailModal } from '@/features/create-recipe/components/modals/protocol-detail-modal'; // TODO: Re-enable after refactoring

// Restore to:
import { ProtocolDetailModal } from '@/features/create-recipe/components/modals/protocol-detail-modal';
```

### State Variables to Restore

**Lines 30-31**: Uncomment modal state management
```tsx
// Current (commented):
// const { modalRef: protocolModalRef, presentModal: presentProtocolModal, dismissModal: dismissProtocolModal } = useModalManager();
// const [selectedProtocol, setSelectedProtocol] = useState<SavedProtocol | null>(null);

// Restore to:
const { modalRef: protocolModalRef, presentModal: presentProtocolModal, dismissModal: dismissProtocolModal } = useModalManager();
const [selectedProtocol, setSelectedProtocol] = useState<SavedProtocol | null>(null);
```

### Event Handler to Restore

**Lines 98-102**: Replace placeholder handler with full modal functionality
```tsx
// Current (placeholder):
const handleViewProtocol = useCallback((protocol: SavedProtocol) => {
  // Placeholder: Show coming soon feedback
  haptics.light();
  console.log('Protocol detail view coming soon after refactoring');
}, []);

// Restore to:
const handleViewProtocol = useCallback((protocol: SavedProtocol) => {
  setSelectedProtocol(protocol);
  presentProtocolModal();
  haptics.light();
}, [presentProtocolModal]);
```

### Modal Component to Restore

**Lines 354-361**: Uncomment the ProtocolDetailModal component
```tsx
// Current (commented):
{/* Protocol Detail Modal - TODO: Re-enable after modal refactoring */}
{/*
<ProtocolDetailModal
  modalRef={protocolModalRef}
  visible={false}
  onDismiss={dismissProtocolModal}
  protocol={selectedProtocol}
/>
*/}

// Restore to:
{/* Protocol Detail Modal */}
<ProtocolDetailModal
  modalRef={protocolModalRef}
  visible={false}
  onDismiss={dismissProtocolModal}
  protocol={selectedProtocol}
/>
```

## Verification Checklist

After restoring all changes:

- [ ] Verify the `ProtocolDetailModal` component path is correct and component exists
- [ ] Test that tapping protocol cards opens the modal correctly
- [ ] Ensure modal can be dismissed properly
- [ ] Verify haptic feedback still works
- [ ] Run build to ensure no import errors
- [ ] Test on both iOS and Android if possible

## Notes

- All changes were made to maintain app stability during refactoring
- The placeholder handler provides safe fallback behavior
- Original functionality was fully preserved in comments for easy restoration
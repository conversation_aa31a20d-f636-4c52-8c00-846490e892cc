 LOG  🔍 Opening modal for: midday
 LOG  🔍 Recipe data available: {"hasRecipe": false, "hasSelectedOils": false, "selectedOilsCount": 0}
 LOG  🔍 Transformed recipes for modal: {"mid-day": {"hasRecipe": true, "hasSelectedOils": true, "selectedOilsCount": 5}, "morning": {"hasRecipe": true, "hasSelectedOils": true, "selectedOilsCount": 4}, "night": {"hasRecipe": true, "hasSelectedOils": true, "selectedOilsCount": 4}}
 LOG  🔍 Transformed recipes for modal: {"mid-day": {"hasRecipe": true, "hasSelectedOils": true, "selectedOilsCount": 5}, "morning": {"hasRecipe": true, "hasSelectedOils": true, "selectedOilsCount": 4}, "night": {"hasRecipe": true, "hasSelectedOils": true, "selectedOilsCount": 4}}
 LOG  🔍 [FinalRecipeList] Collecting alternative oils: {"enrichedOilsCount": 31, "oilsWithFinalScore": 31, "oilsWithSpecializationScore": 31, "propertiesCount": 5, "totalOilsBeforeDedup": 33}
 LOG  🔍 [FinalRecipeList] Alternative oils processing complete: {"filteredOutCount": 2, "filteredOutReasons": {"missing final_relevance_score": 2}, "finalUniqueOilsCount": 16, "totalOilsBeforeDedup": 33}
 WARN  ⚠️ [FinalRecipeList] Some oils were filtered out: {"count": 2, "examples": [{"oil": [Object], "reason": "missing final_relevance_score"}, {"oil": [Object], "reason": "missing final_relevance_score"}]}
 LOG  🔍 [OilSubstitution] Starting filtering process: {"originalOilId": undefined, "selectedOilsCount": 4, "timeSlot": "morning", "totalAlternatives": 16}
 LOG  🔍 [OilSubstitution] Selected oil IDs in recipe: ["56b7e686-5417-4bcf-a975-9888f559a4f3", "7fd15cfa-5a18-4e12-ba6b-6da0d62d70bc", "0ed154c2-cb91-4449-9b3b-37ffea79c9ac", "d685f4e0-f875-48b3-ab30-08a82d22f682"]
 LOG  🕵️‍♂️ [OilSubstitution] Inspecting data structures for filtering...
 LOG  Sample from `selectedOils` (in recipe): {
  "oil_id": "56b7e686-5417-4bcf-a975-9888f559a4f3",
  "name_localized": "Gengibre",
  "name_botanical": "Zingiber officinale",
  "drops_count": 4,
  "rationale_localized": "Gengibre ajuda a estimular a digestão e reduzir a inflamação no estômago, aliviando o desconforto após refeições especialmente gordurosas."
}
 LOG  Sample from `alternativeOils` (master list): {
  "botanical_name": "",
  "oil_id": "56b7e686-5417-4bcf-a975-9888f559a4f3",
  "name_english": "Ginger",
  "name_botanical": "Zingiber officinale",
  "name_localized": "Gengibre",
  "match_rationale_localized": "Propriedades que ajudam na digestão, aliviam náuseas e reduzem a inflamação gastrointestinal.",
  "relevancy_to_property_score": 4,
  "name_scientific": "Zingiber officinale",
  "safety": {
    "internal_use": {
      "id": "2343a180-7dd2-45b1-a402-065b1bf2bd7c",
      "code": "FOOD_GRADE_EO",
      "name": "Safe for Internal Use",
      "guidance": null,
      "description": "Essential oils in this category are generally recognized or have specific indications suggesting they may be safe for internal use when following appropriate dilutions, dosage, and professional guidance."
    },
    "dilution": {
      "id": "45c9d911-9b2b-472f-99aa-fdbaf3caa98d",
      "name": "[S] Sensitive",
      "ratio": "5:1",
      "description": "Sensitive. “Sensitive” oils are those that should be diluted before use on young or sensitive skin. Examples of “sensitive” oils are peppermint, ginger, eucalyptus, wintergreen, and black pepper.",
      "percentage_max": 0.2,
      "percentage_min": 0.15
    },
    "phototoxicity": {
      "id": "ae18d720-4473-479e-b9f3-7fa65192d639",
      "status": "Non-Phototoxic",
      "guidance": "No specific sun exposure restrictions related to phototoxicity are typically required after topical application. However, always follow general safety guidelines for dilution and application for all essential oils.",
      "description": "Generally not known to cause phototoxic reactions when skin is exposed to UV light after topical application, when used appropriately."
    },
    "pregnancy_nursing": [
      {
        "id": "1ae12b7d-04e1-4306-b218-7e4bd7b0865b",
        "code": "",
        "name": "",
        "description": "",
        "usage_guidance": "",
        "status_description": "pregnancy-safe-100"
      },
      {
        "id": "3735d6e6-89be-4887-901e-968170842c18",
        "code": null,
        "name": "",
        "description": "",
        "usage_guidance": null,
        "status_description": "pregnancy-safe-3months"
      }
    ],
    "child_safety": [],
    "internal_use_id": "2343a180-7dd2-45b1-a402-065b1bf2bd7c",
    "dilution_id": "45c9d911-9b2b-472f-99aa-fdbaf3caa98d",
    "phototoxicity_id": "ae18d720-4473-479e-b9f3-7fa65192d639",
    "pregnancy_nursing_ids": [
      "1ae12b7d-04e1-4306-b218-7e4bd7b0865b",
      "3735d6e6-89be-4887-901e-968170842c18"
    ],
    "child_safety_ids": []
  },
  "enrichment_status": "enriched",
  "botanical_mismatch": false,
  "similarity_score": 0.833497698083881,
  "search_query": "Ginger - Zingiber officinale",
  "enrichment_timestamp": "2025-09-12T00:42:06.280Z",
  "isEnriched": true,
  "final_relevance_score": 4.11,
  "specialization_score": 0.8
}
 LOG  🔍 [OilSubstitution] Filtering results: {"afterExcludingOriginal": 16, "afterExcludingRecipeOils": 12, "excludedByOriginal": 0, "excludedByRecipe": 4, "finalSortedCount": 12, "totalAlternatives": 16}
 WARN  ⚠️ [OilSubstitution] No originalOil provided to modal
 LOG  🔍 [FinalRecipeList] Collecting alternative oils: {"enrichedOilsCount": 31, "oilsWithFinalScore": 31, "oilsWithSpecializationScore": 31, "propertiesCount": 5, "totalOilsBeforeDedup": 33}
 LOG  🔍 [FinalRecipeList] Alternative oils processing complete: {"filteredOutCount": 2, "filteredOutReasons": {"missing final_relevance_score": 2}, "finalUniqueOilsCount": 16, "totalOilsBeforeDedup": 33}
 WARN  ⚠️ [FinalRecipeList] Some oils were filtered out: {"count": 2, "examples": [{"oil": [Object], "reason": "missing final_relevance_score"}, {"oil": [Object], "reason": "missing final_relevance_score"}]}
 LOG  🔍 [OilSubstitution] Starting filtering process: {"originalOilId": undefined, "selectedOilsCount": 4, "timeSlot": "morning", "totalAlternatives": 16}
 LOG  🔍 [OilSubstitution] Selected oil IDs in recipe: ["56b7e686-5417-4bcf-a975-9888f559a4f3", "7fd15cfa-5a18-4e12-ba6b-6da0d62d70bc", "0ed154c2-cb91-4449-9b3b-37ffea79c9ac", "d685f4e0-f875-48b3-ab30-08a82d22f682"]
 LOG  🕵️‍♂️ [OilSubstitution] Inspecting data structures for filtering...
 LOG  Sample from `selectedOils` (in recipe): {
  "oil_id": "56b7e686-5417-4bcf-a975-9888f559a4f3",
  "name_localized": "Gengibre",
  "name_botanical": "Zingiber officinale",
  "drops_count": 4,
  "rationale_localized": "Gengibre ajuda a estimular a digestão e reduzir a inflamação no estômago, aliviando o desconforto após refeições especialmente gordurosas."
}
 LOG  Sample from `alternativeOils` (master list): {
  "botanical_name": "",
  "oil_id": "56b7e686-5417-4bcf-a975-9888f559a4f3",
  "name_english": "Ginger",
  "name_botanical": "Zingiber officinale",
  "name_localized": "Gengibre",
  "match_rationale_localized": "Propriedades que ajudam na digestão, aliviam náuseas e reduzem a inflamação gastrointestinal.",
  "relevancy_to_property_score": 4,
  "name_scientific": "Zingiber officinale",
  "safety": {
    "internal_use": {
      "id": "2343a180-7dd2-45b1-a402-065b1bf2bd7c",
      "code": "FOOD_GRADE_EO",
      "name": "Safe for Internal Use",
      "guidance": null,
      "description": "Essential oils in this category are generally recognized or have specific indications suggesting they may be safe for internal use when following appropriate dilutions, dosage, and professional guidance."
    },
    "dilution": {
      "id": "45c9d911-9b2b-472f-99aa-fdbaf3caa98d",
      "name": "[S] Sensitive",
      "ratio": "5:1",
      "description": "Sensitive. “Sensitive” oils are those that should be diluted before use on young or sensitive skin. Examples of “sensitive” oils are peppermint, ginger, eucalyptus, wintergreen, and black pepper.",
      "percentage_max": 0.2,
      "percentage_min": 0.15
    },
    "phototoxicity": {
      "id": "ae18d720-4473-479e-b9f3-7fa65192d639",
      "status": "Non-Phototoxic",
      "guidance": "No specific sun exposure restrictions related to phototoxicity are typically required after topical application. However, always follow general safety guidelines for dilution and application for all essential oils.",
      "description": "Generally not known to cause phototoxic reactions when skin is exposed to UV light after topical application, when used appropriately."
    },
    "pregnancy_nursing": [
      {
        "id": "1ae12b7d-04e1-4306-b218-7e4bd7b0865b",
        "code": "",
        "name": "",
        "description": "",
        "usage_guidance": "",
        "status_description": "pregnancy-safe-100"
      },
      {
        "id": "3735d6e6-89be-4887-901e-968170842c18",
        "code": null,
        "name": "",
        "description": "",
        "usage_guidance": null,
        "status_description": "pregnancy-safe-3months"
      }
    ],
    "child_safety": [],
    "internal_use_id": "2343a180-7dd2-45b1-a402-065b1bf2bd7c",
    "dilution_id": "45c9d911-9b2b-472f-99aa-fdbaf3caa98d",
    "phototoxicity_id": "ae18d720-4473-479e-b9f3-7fa65192d639",
    "pregnancy_nursing_ids": [
      "1ae12b7d-04e1-4306-b218-7e4bd7b0865b",
      "3735d6e6-89be-4887-901e-968170842c18"
    ],
    "child_safety_ids": []
  },
  "enrichment_status": "enriched",
  "botanical_mismatch": false,
  "similarity_score": 0.833497698083881,
  "search_query": "Ginger - Zingiber officinale",
  "enrichment_timestamp": "2025-09-12T00:42:06.280Z",
  "isEnriched": true,
  "final_relevance_score": 4.11,
  "specialization_score": 0.8
}
 LOG  🔍 [OilSubstitution] Filtering results: {"afterExcludingOriginal": 16, "afterExcludingRecipeOils": 12, "excludedByOriginal": 0, "excludedByRecipe": 4, "finalSortedCount": 12, "totalAlternatives": 16}
 WARN  ⚠️ [OilSubstitution] No originalOil provided to modal
 ERROR  Warning: TypeError: Cannot read property 'recipe' of null

This error is located at:

  29 |
  30 | export const SaveProtocolBottomSheet: React.FC<SaveProtocolBottomSheetProps> = ({
> 31 |   modalRef,
     |           ^
  32 |   onDismiss,
  33 |   onSuccess
  34 | }) => {

Call Stack
  SaveProtocolBottomSheet (src/features/create-recipe/components/modals/save-protocol-bottom-sheet.tsx:31:11)
  FinalRecipeList (src/features/create-recipe/components/screens/final-recipes/final-recipes-list.tsx:163:41)
  Wrapper (<anonymous>)
  AppProviders (src/app/_layout.tsx:81:33)
  UserPreferencesProvider (src/shared/contexts/user-preferences-context.tsx:68:51)
  RNGestureHandlerRootView (<anonymous>)
  RootLayout (<anonymous>)
  RootApp(./_layout.tsx) (<anonymous>)
  RNCSafeAreaProvider (<anonymous>)
  App (<anonymous>)
  ErrorOverlay (<anonymous>)
# STORY: Implement Real-time Typing Detection and Broadcasting

## User Story
**As a** chat user  
**I want** to see when other users are typing  
**So that** I know someone is about to respond and can wait for their message

## Acceptance Criteria

### ✅ Core Typing Detection
- [ ] **Given** I start typing in the chat input
- [ ] **When** I've typed for >500ms continuously  
- [ ] **Then** other users should see "User is typing..." indicator
- [ ] **And** the indicator should appear below the message list

### ✅ Real-time Broadcasting
- [ ] **Given** another user is typing
- [ ] **When** they are actively composing a message
- [ ] **Then** I should see their typing indicator within 500ms
- [ ] **And** the indicator should update in real-time

### ✅ Smart Timeout Handling
- [ ] **Given** I stop typing mid-message
- [ ] **When** 2 seconds pass without input activity
- [ ] **Then** my typing indicator should disappear for others
- [ ] **And** cleanup should happen automatically

### ✅ Multiple Users Typing
- [ ] **Given** multiple users (<PERSON>, <PERSON>) are typing simultaneously
- [ ] **When** both are actively composing messages
- [ ] **Then** I should see "<PERSON> and <PERSON> are typing..."
- [ ] **And** names should update as users start/stop typing

### ✅ Message Sending Behavior
- [ ] **Given** I'm showing as typing to others
- [ ] **When** I send a message
- [ ] **Then** my typing indicator should immediately disappear
- [ ] **And** the sent message should appear normally

## Technical Implementation

### Supabase Presence Channel Extension
```tsx
// Add typing status to existing presence tracking
await messagesChannel.track({
  user_id: user.id,
  user_name: user.fullName || 'Anonymous',
  online_at: new Date().toISOString(),
  typing: true, // New field for typing status
  typing_updated_at: new Date().toISOString()
});
```

### Debounced Typing Detection
```tsx
const [typingUsers, setTypingUsers] = useState<string[]>([]);
const typingTimeoutRef = useRef<NodeJS.Timeout>();

const handleTypingStart = useCallback(async () => {
  // Clear existing timeout
  if (typingTimeoutRef.current) {
    clearTimeout(typingTimeoutRef.current);
  }
  
  // Broadcast typing status
  await channel?.track({
    ...currentPresence,
    typing: true,
    typing_updated_at: new Date().toISOString()
  });
  
  // Set timeout to clear typing status
  typingTimeoutRef.current = setTimeout(async () => {
    await channel?.track({
      ...currentPresence,
      typing: false
    });
  }, 2000);
}, [channel, currentPresence]);

// On input change
const handleInputChange = (text: string) => {
  setInputText(text);
  
  // Debounced typing detection
  if (text.length > 0) {
    handleTypingStart();
  }
};
```

### Typing Indicator UI Component
```tsx
const TypingIndicator: React.FC<{ typingUsers: string[] }> = ({ typingUsers }) => {
  if (typingUsers.length === 0) return null;
  
  const typingText = typingUsers.length === 1 
    ? `${typingUsers[0]} is typing...`
    : `${typingUsers.slice(0, -1).join(', ')} and ${typingUsers[typingUsers.length - 1]} are typing...`;
  
  return (
    <View style={styles.typingContainer}>
      <Text variant="bodySmall" style={styles.typingText}>
        {typingText}
      </Text>
      <View style={styles.typingDots}>
        {/* Animated typing dots */}
      </View>
    </View>
  );
};
```

### Presence State Processing
```tsx
.on('presence', { event: 'sync' }, () => {
  const presenceState = messagesChannel.presenceState();
  
  // Extract online users
  const onlineUsers = Object.keys(presenceState)
    .map(userId => presenceState[userId]?.[0])
    .filter(Boolean)
    .map(p => p.user_name);
    
  // Extract typing users (excluding self)
  const currentlyTyping = Object.keys(presenceState)
    .map(userId => presenceState[userId]?.[0])
    .filter(p => p && p.typing && p.user_id !== user?.id)
    .map(p => p.user_name);
    
  setOnlineUsers(onlineUsers);
  setTypingUsers(currentlyTyping);
});
```

## Definition of Done
- [ ] Typing indicators appear when users start typing (>500ms)
- [ ] Indicators disappear after 2s of inactivity
- [ ] Multiple users typing handled gracefully
- [ ] Real-time updates with <500ms latency
- [ ] No performance impact on message sending
- [ ] Smooth animations for typing indicators
- [ ] Works on both iOS and Android
- [ ] Automatic cleanup of stale typing states

## UI/UX Specifications
- **Location**: Fixed position above ChatInputBar
- **Animation**: Subtle fade in/out (200ms)
- **Colors**: `theme.colors.onSurfaceVariant`
- **Typography**: `variant="labelSmall"`
- **Height**: 24px when visible, 0px when hidden
- **Behavior**: Non-blocking, doesn't affect scroll

## Testing Scenarios
1. **Single User**: Start typing, stop typing, send message
2. **Multiple Users**: 2-3 users typing simultaneously  
3. **Edge Cases**: Network disconnection during typing
4. **Performance**: 100 users with typing activity
5. **Cleanup**: Stale typing indicators after user disconnect

## Dependencies
- Current Supabase presence implementation
- Debounced input handling
- Typing indicator UI component

## Estimated Effort
**Story Points**: 5  
**Development Time**: 1-2 days

## Notes
- Implement after core chat is stable
- Monitor realtime performance with typing events
- Consider rate limiting typing broadcasts

---

**Story Owner**: Mobile Development Team  
**Created**: 2025-01-09  
**Status**: YAGNI - Future Implementation  
**Priority**: Low (UX enhancement)
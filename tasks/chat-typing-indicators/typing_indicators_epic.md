# EPIC: Real-time Typing Indicators for Chat

## Overview
**YAGNI Implementation** - Future UX enhancement for production chat experience

Implement real-time typing indicators to show when users are actively composing messages, similar to WhatsApp, Telegram, and other modern chat applications.

## Business Context
- **Current State**: No typing awareness between users
- **Target Users**: Up to 100 concurrent users with 12-hour message retention
- **UX Goal**: Enhance conversational flow and user engagement
- **Technical Scope**: Realtime presence updates via Supabase channels

## Success Criteria
- [ ] Users see "User is typing..." when others are composing messages
- [ ] Typing indicators appear/disappear in real-time (<500ms latency)
- [ ] Smooth UX without performance impact
- [ ] Multiple users typing simultaneously handled gracefully
- [ ] Automatic cleanup of stale typing indicators

## Epic Scope
- **In Scope**: Real-time typing detection, presence broadcasting, UI indicators
- **Out of Scope**: Advanced typing patterns, message drafts, offline typing sync

## Technical Architecture
- **Presence Channel**: Supabase realtime presence for typing status
- **Debouncing**: Smart typing detection with timeout handling
- **UI Components**: Non-intrusive typing indicator display
- **Performance**: Throttled updates to prevent spam

## User Experience Goals
- **Visual**: Subtle typing indicator below message list
- **Timing**: Show after 500ms of typing, hide after 2s of inactivity  
- **Multiple Users**: "Alice and Bob are typing..." format
- **Performance**: No impact on message sending/receiving

## Dependencies
- Current realtime chat implementation (✅ Complete)
- Supabase presence channels
- Debounced input detection
- Typing indicator UI components

## Implementation Priority
**YAGNI Status**: Implement after core chat features are stable in production

## Related Stories
- [Story: Implement Real-time Typing Detection and Broadcasting](./typing_indicators_story.md)

---

**Epic Owner**: UX/Mobile Development Team  
**Created**: 2025-01-09  
**Status**: YAGNI - Future Implementation
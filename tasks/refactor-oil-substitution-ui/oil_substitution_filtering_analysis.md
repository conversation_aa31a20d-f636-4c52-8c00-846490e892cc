
# Analysis of Oil Substitution Filtering Logic

## Executive Summary

A systematic review of the codebase confirms that the logic for filtering duplicate oils (i.e., oils already present in the current recipe) from the substitution modal **has not been deleted**. The implementation remains in place within `src/features/create-recipe/components/modals/oil-substitution-bottom-sheet.tsx`.

The current issue where oils already in the recipe are still shown as substitution candidates is not due to missing code. The filtering algorithm itself is logically sound. The problem most likely stems from one of two possibilities:

1.  **Data Inconsistency**: The `oil_id`s of the oils in the recipe (`selectedOils` prop) do not perfectly match the `oil_id`s of the same oils in the master list of alternatives (`alternativeOils` prop).
2.  **Stale State Propagation**: The `OilSubstitutionBottomSheet` component may be receiving an outdated list of selected oils because a parent component is not re-rendering correctly when the recipe changes in the global state.

This document details the investigation, verifies the existing code, and provides a clear path for debugging the issue.

---

## Code and Implementation Verification

The original filtering mechanism is intact and correctly implemented across the relevant files.

### 1. `oil-substitution-bottom-sheet.tsx` (The Filtering Logic)

The core logic resides in a `useMemo` hook that calculates `sortedAlternatives`. This code correctly performs a two-stage filtering process.

-   **Location**: `src/features/create-recipe/components/modals/oil-substitution-bottom-sheet.tsx`
-   **Analysis**: The code first gets an array of `oil_id`s from the `selectedOils` prop. It then filters the `alternativeOils` list, first to remove the original oil being replaced, and second to remove any oil whose ID is already in the current recipe. This implementation is correct.

```typescript
// src/features/create-recipe/components/modals/oil-substitution-bottom-sheet.tsx

const sortedAlternatives = useMemo(() => {
    // ... (debug logging) ...

    // Get array of oil IDs already selected in the recipe
    const selectedOilIds = selectedOils?.map(oil => oil.oil_id) || [];
    
    // ... (debug logging) ...

    // Filter alternatives
    // 1. Remove the oil being substituted
    const afterOriginalFilter = alternativeOils.filter(oil => oil.oil_id !== originalOil?.oil_id);
    // 2. Remove oils already in the recipe
    const afterRecipeFilter = afterOriginalFilter.filter(oil => !selectedOilIds.includes(oil.oil_id));
    
    // Sort by relevance score
    const sorted = afterRecipeFilter.sort((a, b) => (b.final_relevance_score || 0) - (a.final_relevance_score || 0));

    // ... (debug logging) ...

    return sorted;
}, [alternativeOils, originalOil?.oil_id, selectedOils, timeSlot]);
```

### 2. `final-recipes-list.tsx` (Prop Delegation)

This component correctly passes the necessary data down to the `OilSubstitutionBottomSheet`.

-   **Location**: `src/features/create-recipe/components/screens/final-recipes/final-recipes-list.tsx`
-   **Analysis**: It passes the `recipe.selected_oils` array (which contains the oils for the current recipe) to the `selectedOils` prop of the bottom sheet. This is the correct data source for the filtering logic to use.

```typescript
// src/features/create-recipe/components/screens/final-recipes/final-recipes-list.tsx

<OilSubstitutionBottomSheet
  modalRef={modalRef}
  originalOil={selectedOilForSubstitution}
  alternativeOils={getAllAlternativeOils}
  onSelectSubstitute={handleSelectSubstitute}
  onDismiss={handleDismissModal}
  selectedOils={recipe.selected_oils} // Correctly passed
  timeSlot={timeSlot}
/>
```

### 3. `recipe.slice.ts` (State Management)

The state update logic for substituting an oil appears to follow best practices for immutability, which is crucial for triggering UI updates.

-   **Location**: `src/features/create-recipe/store/recipe.slice.ts`
-   **Analysis**: The `substituteOilInRecipe` action creates new objects and arrays (`updatedSelectedOils`, `updatedRecipe`) rather than mutating the existing state. This ensures that state changes are detectable by React and Zustand, which should cause components to re-render with the new recipe data.

---

## Timeline and Regression Hypothesis

The filtering feature was not deleted. The regression (the feature no longer working) was likely introduced by a change that did not directly touch the filtering code itself, but rather affected the integrity or flow of data that the code relies on.

A possible timeline:
1.  The filtering logic was implemented and worked correctly.
2.  A change was made in another part of the application, for example:
    *   The data source for essential oils was modified.
    *   The structure of the `EnrichedEssentialOil` or `FinalRecipeProtocol` types was altered.
    *   A parent component was wrapped in `React.memo` or its state subscription logic was changed.
3.  This change inadvertently created a mismatch between the `oil_id`s in the recipe and the `oil_id`s in the alternatives list, or it prevented the updated recipe from reaching the bottom sheet, causing the filtering to fail silently.

---

## Potential Root Causes & How to Fix

The evidence points away from the filtering algorithm and towards the data it processes. Here are the most likely causes and the files to investigate.

### 1. Data Inconsistency (High Likelihood)

-   **Problem**: The `filter` method relies on a strict `===` comparison of `oil_id` strings. If an oil in the `recipe.selected_oils` array has an `oil_id` of `"lavender-123"` but the *same* oil in the `alternativeOils` list has an `oil_id` of `"LAVENDER-123"` or `"lavender-123 "` (with a trailing space), the check `!selectedOilIds.includes(oil.oil_id)` will fail, and the oil will not be filtered.
-   **Files to Change/Investigate**:
    -   **`oil-substitution-bottom-sheet.tsx`**: The `__DEV__` logs are already in place. Run the app in debug mode and open the substitution modal. **Check the console output** for `"[OilSubstitution] Selected oil IDs in recipe:"` and compare these IDs to the IDs of the oils that are incorrectly appearing in the list. This is the fastest way to confirm a data mismatch.
    -   **`post-enrichment-scoring.ts`**: This file is where `final_relevance_score` is calculated and oils from various sources are aggregated. Investigate this file to see how oil data is processed and if IDs are being transformed or handled inconsistently.
    -   **`recipe.slice.ts`**: Check the `updatePropertyWithEnrichedOils` action to see how data is integrated into the state.

### 2. Stale State/Props (Medium Likelihood)

-   **Problem**: After a substitution, the component tree is not re-rendering with the updated recipe. The `FinalRecipeList` component continues to hold a stale `recipe` object, so when the user opens the substitution modal again, it receives the old list of `selectedOils`.
-   **Files to Change/Investigate**:
    -   **The Parent Screen of `FinalRecipeList` (File Not Provided)**: This is the most critical file to investigate for this issue. Find where `<FinalRecipeList />` is used.
        -   Check how it gets the `recipe` data from the `useCombinedRecipeStore`. Ensure the selector is correct.
        -   If the component is wrapped in `React.memo`, ensure the props comparison is handled correctly or remove it to test if it resolves the issue.
    -   **`final-recipes-list.tsx`**: While this component receives props, you can add a `useEffect` hook to log when the `recipe` prop changes to confirm if it's receiving updates as expected.
        ```javascript
        useEffect(() => {
          if (__DEV__) {
            console.log('[FinalRecipeList] Recipe prop updated:', recipe.selected_oils.map(o => o.oil_id));
          }
        }, [recipe]);
        ```

# Enhanced Analysis of Oil Substitution Filtering Logic

## 1. Introduction

This document provides a systematic, data-first analysis of the oil substitution filtering issue. The previous analysis correctly identified that the filtering code exists but hypothesized about the root cause. This report moves beyond hypothesis by tracing the complete data flow of the two key arrays involved—`selectedOils` and `alternativeOils`—from their origins to the point of comparison, revealing a concrete data integrity problem.

**Conclusion First:** The filtering fails because the `selected_oils` array within a `FinalRecipeProtocol` object contains **simplified, partially constructed oil objects**. When an oil is substituted, the `substituteOilInRecipe` action in `recipe.slice.ts` creates a new plain object for the recipe that lacks the full data structure (like `enrichment_status` or `final_relevance_score`) of the `EnrichedEssentialOil` type. The `alternativeOils` list, however, consists of fully `EnrichedEssentialOil` objects. The filtering logic works, but it's comparing apples and oranges, leading to incorrect results if the `oil_id`s don't align perfectly during their separate data lifecycles.

---

## 2. Data Flow Analysis

The filtering logic depends on the strict equality of `oil_id` strings between two arrays:
- `selectedOils`: The oils currently in the recipe.
- `alternativeOils`: The master list of all possible substitution candidates.

The investigation traces each of these arrays back to its source.

### Path A: The `selectedOils` Lifecycle (The Recipe's Data)

1.  **Origin - The Substitution Action**: The data lifecycle begins when a substitution is made. The `substituteOilInRecipe` action in `recipe.slice.ts` is called.

2.  **Data Transformation (The Root Cause)**: Inside this action, a **new, simplified object** is created to replace the old oil. This is the critical point of data divergence.

    ```typescript
    // src/features/create-recipe/store/recipe.slice.ts

    const updatedSelectedOils = currentRecipe.selected_oils.map(oil => {
      if (oil.oil_id === originalOilId) {
        // A NEW, plain object is created here. It is NOT the full EnrichedEssentialOil object.
        return {
          oil_id: substitutionOil.oil_id,
          name_localized: substitutionOil.name_localized,
          // ... other basic properties
          drops_count: oil.drops_count, // Keeps original drops
          rationale_localized: substitutionOil.match_rationale_localized || '...'
        };
      }
      return oil;
    });
    ```
    The `substitutionOil` parameter is a full `EnrichedEssentialOil`, but the action cherry-picks a few properties to create a new, incomplete object that is then stored in the recipe.

3.  **Storage**: This new array of simplified objects (`updatedSelectedOils`) is saved into the `finalRecipes` state in the Zustand store.

4.  **Propagation**: The `FinalRecipeList` component reads this `recipe.selected_oils` array from the store and passes it directly as the `selectedOils` prop to the `OilSubstitutionBottomSheet`.

### Path B: The `alternativeOils` Lifecycle (The Master List)

1.  **Origin - The Store's Master Data**: The `alternativeOils` list originates from the `therapeuticProperties` array in the `recipe.slice.ts` store. This array holds the complete, authoritative data for all oils suggested for the user's condition.

2.  **Enrichment and Scoring**: The `updatePropertyWithEnrichedOils` action is responsible for populating `therapeuticProperties.suggested_oils`. Crucially, it triggers `calculatePostEnrichmentScores` from `post-enrichment-scoring.ts`. This utility ensures every oil in `suggested_oils` is a full `EnrichedEssentialOil` object, complete with `final_relevance_score`, `specialization_score`, `enrichment_status`, and a consistent `oil_id`.

    ```typescript
    // src/features/create-recipe/store/recipe.slice.ts
    
    // This function ensures all oils in the store are fully structured
    const scoringResult = calculatePostEnrichmentScores(updatedProperties);
    
    // ... it then maps scores back to the oils
    finalProperties = updatedProperties.map(prop => ({
        ...prop,
        suggested_oils: (prop.suggested_oils || []).map(oil => {
            const scores = oil.oil_id ? hybridScoreMap.get(oil.oil_id) : undefined;
            return { ...oil, ...scores }; // Merges to create a complete object
        })
    }));
    ```

3.  **Aggregation**: In `final-recipes-list.tsx`, the `getAllAlternativeOils` memoized value is created. It iterates through all `therapeuticProperties` from the store and aggregates every `suggested_oils` entry into a single flat array. It also performs a deduplication step.

    ```typescript
    // src/features/create-recipe/components/screens/final-recipes/final-recipes-list.tsx
    
    const getAllAlternativeOils = useMemo(() => {
      const allOils: EnrichedEssentialOil[] = [];
      therapeuticProperties.forEach(property => {
        if (property.suggested_oils) {
          allOils.push(...property.suggested_oils); // Assembling the master list
        }
      });
      // ... then deduplicates
      return finalOils;
    }, [therapeuticProperties]);
    ```

4.  **Propagation**: This `getAllAlternativeOils` array is passed as the `alternativeOils` prop to the `OilSubstitutionBottomSheet`.

---

## 3. The Point of Divergence

The two data paths create two different "shapes" of oil objects.

-   **`alternativeOils`**: An array of complete `EnrichedEssentialOil` objects, managed by a robust enrichment and scoring pipeline.
-   **`selectedOils`**: An array of simplified, plain objects created manually during the `substituteOilInRecipe` action.

The filtering fails because of this fundamental inconsistency. While `oil_id` *should* theoretically be the same, this manual object creation in the substitution action is fragile. Any subtle transformation, whitespace difference, or type mismatch that might occur during the separate processing pipelines for `therapeuticProperties` versus the final recipe assembly would break the `!selectedOilIds.includes(oil.oil_id)` check. The system is not comparing identical objects from a single source of truth.

## 4. Debugging and Verification Plan

The fix is to ensure that the `selected_oils` array in the `FinalRecipeProtocol` always contains the **full, authoritative `EnrichedEssentialOil` object** from the master list, not a simplified copy.

### Recommended Change

Modify the `substituteOilInRecipe` action in `src/features/create-recipe/store/recipe.slice.ts`:

**Current (Incorrect) Logic:**
```typescript
const updatedSelectedOils = currentRecipe.selected_oils.map(oil => {
    if (oil.oil_id === originalOilId) {
        return { // Creates a new, partial object
            oil_id: substitutionOil.oil_id,
            name_localized: substitutionOil.name_localized,
            // ...
        };
    }
    return oil;
});
```

**Proposed (Corrected) Logic:**
```typescript
const updatedSelectedOils = currentRecipe.selected_oils.map(oil => {
    if (oil.oil_id === originalOilId) {
        // Find the original drops_count to preserve it
        const originalDrops = oil.drops_count;

        // Return the complete substitution oil object, only overriding drops_count
        return {
            ...substitutionOil, // Use the full, authoritative object
            drops_count: originalDrops, // Preserve the original drop count
        };
    }
    return oil;
});
```

By spreading the complete `substitutionOil` object, we guarantee that the object stored in the recipe is identical in structure and data to the objects in the `alternativeOils` list, ensuring the filtering logic will work as intended.

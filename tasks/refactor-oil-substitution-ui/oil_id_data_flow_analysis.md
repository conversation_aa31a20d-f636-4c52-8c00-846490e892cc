# Analysis of `oil_id` Data Flow and Final Recipe Generation

## 1. Executive Summary

This report details the end-to-end data flow of essential oil objects, tracing the `oil_id` from its origin to its two final destinations: the master `alternativeOils` list used for substitutions and the `selectedOils` list contained within a generated recipe. 

The core finding is that these two lists are populated by **two separate and distinct API pipelines**. The `alternativeOils` list is built from a client-side enrichment process that results in complete, fully-structured data objects. In contrast, the initial `selectedOils` list is created by a server-side recipe generation process that returns simplified, partial oil objects. 

This architectural divergence is the fundamental root cause of the data inconsistency. The `oil_id` itself is likely consistent, but it is attached to objects of different shapes and origins, making direct comparisons fragile.

---

## 2. The Two Pipelines: A Tale of Two Oil Objects

### Pipeline A: Creation of `alternativeOils` (The Master List)

This pipeline is responsible for creating the comprehensive list of all possible oil substitutions, ensuring each oil object is complete.

1.  **Origin - User Action**: The process begins in `use-properties-selection.ts` after a user selects their desired therapeutic properties. The `handleSubmit` function is called.

2.  **API Call 1: Oil Suggestions**: `handleSubmit` triggers `startOilSuggestionStreaming`, which calls an API to fetch a *basic* list of suggested oils for the selected properties.

3.  **API Call 2: Oil Enrichment**: As soon as suggestions for a property arrive, the `autoTriggerEnrichment` function is called. This immediately triggers a second, separate API call via the `batchEnrichOils` service. This service takes the basic oil data and returns a complete, detailed `EnrichedEssentialOil` object, including complex `safety` data, scores, and other metadata.

4.  **State Hydration**: The resulting array of complete `EnrichedEssentialOil` objects is saved into the `therapeuticProperties` state in the Zustand store via the `updatePropertyWithEnrichedOils` action.

5.  **Final Aggregation**: In `final-recipes-list.tsx`, the `getAllAlternativeOils` variable is created by iterating through the `therapeuticProperties` in the store and collecting all the fully-structured `suggested_oils` into a single, deduplicated list. 

**Conclusion**: `alternativeOils` are guaranteed to be full `EnrichedEssentialOil` objects from the client-side enrichment pipeline.

### Pipeline B: Creation of `selectedOils` (The Initial Recipe)

This pipeline is responsible for generating the final recipe that the user sees.

1.  **Origin - User Action**: The process begins in `use-final-recipes.ts` when the user clicks the button to generate recipes, calling `generateFinalRecipes`.

2.  **Data Preparation**: This function gathers all the user's data and, importantly, runs `calculatePostEnrichmentScores` on the `therapeuticProperties` to create a final scored list of oils to send to the server.

3.  **API Call 3: Recipe Generation**: The `startStreams` function is called, which makes a request to the **final recipe generation API endpoint**. This endpoint's job is to take the user's profile and the scored oils and intelligently compose the final recipes.

4.  **The Point of Divergence**: The server-side logic for recipe generation returns a `FinalRecipeProtocol` object. The `selected_oils` array within this object contains **new, simplified oil objects**. The server does not return the full `EnrichedEssentialOil` data structure; it returns only the minimal data required to display the recipe (e.g., `oil_id`, `name_localized`, `drops_count`).

5.  **State Hydration**: This `FinalRecipeProtocol`, with its simplified oil objects, is saved into the `finalRecipes` slice of the Zustand store via the `updateFinalRecipes` action.

**Conclusion**: The initial `selectedOils` in a recipe are partial objects generated by a separate, server-side recipe creation pipeline.

---

## 3. Synthesis and Root Cause

-   **Do the `oil_id`s come from the same source?** 
    No. The `alternativeOils` list is sourced from the client-side **enrichment pipeline**. The `selectedOils` list is sourced from the server-side **recipe generation pipeline**. They are born from different processes and different API endpoints.

-   **How are they mapped?** 
    They are intended to be mapped by the `oil_id`. However, the objects they are attached to are fundamentally different. The system is not comparing objects from a single, consistent source of truth.

-   **The True Root Cause**: 
    The bug is a symptom of this architectural divergence. The client has access to the complete, authoritative `EnrichedEssentialOil` objects but, upon recipe creation, this data is discarded in favor of simplified objects returned from the server. The `substituteOilInRecipe` action analyzed previously merely continues this flawed pattern by creating yet another partial object, ensuring the inconsistency persists.

To fix this robustly, the `selected_oils` array within the `FinalRecipeProtocol` should be populated with the full `EnrichedEssentialOil` objects available on the client, rather than using the simplified objects returned from the recipe generation API.

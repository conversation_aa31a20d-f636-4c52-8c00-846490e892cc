# Definitive Analysis of the `oil_id` Lifecycle and Data Inconsistency

## 1. Executive Summary

This report provides the final, most detailed analysis of the oil substitution bug, incorporating the user's hypothesis about ID transformation. The investigation confirms that the `oil_id` undergoes a crucial canonicalization step during the enrichment process. However, the root cause of the bug is not a change in the ID itself, but a **failure to persist the complete, enriched data object** associated with that canonical ID through the final recipe generation pipeline.

**The Confirmed Data Flow:**

1.  An initial, potentially non-canonical `oil_id` is fetched from a "suggestions" API.
2.  An "enrichment" API is then called, which returns a **canonical `oil_id`** (database UUID) along with a complete, fully-structured oil object.
3.  This canonical ID and complete object are correctly stored and used to populate the `alternativeOils` list for substitutions.
4.  However, when the final recipe is generated, the server returns a response containing the correct canonical `oil_id` but attached to a **new, simplified/partial oil object**, stripping away the rich data.
5.  This simplified object becomes the source of truth for the recipe's `selectedOils`, creating the data mismatch that breaks the filtering logic.

---

## 2. The Journey of an `oil_id`: From Suggestion to Discarded Enrichment

### Step 1: Birth - The Suggestion API

The process starts in `use-properties-selection.ts`. The `startOilSuggestionStreaming` function is called, which fetches an initial list of basic oil objects for a given therapeutic property. These objects contain an initial `oil_id`.

`oil_id` **Source**: Suggestions API.

### Step 2: Canonicalization - The Enrichment API

Your hypothesis was correct—this is the critical transformation step. 

-   Immediately after suggestions are received, the `autoTriggerEnrichment` function in `use-properties-selection.ts` calls the `batchEnrichOils` service.
-   This service sends the basic oil information (including the initial ID) to a server-side enrichment API.
-   The enrichment API looks up the canonical oil in its master database and returns a **complete `EnrichedEssentialOil` object**. This object contains the **canonical database UUID** as its `oil_id`.

**ID Transformation**: The enrichment API is the source of the true, canonical `oil_id`. The client-side code does not perform a swap; it correctly adopts the entire enriched object, including the canonical ID, that it receives from this API call.

`oil_id` **Source**: Enrichment API (Canonical)

### Step 3: Propagation - Scoring and Final Recipe Payload

-   The `updatePropertyWithEnrichedOils` action saves these complete objects (with the canonical `ID_from_enrichment`) into the `therapeuticProperties` array in the store.
-   The `calculatePostEnrichmentScores` utility reads these objects, uses the canonical `oil_id` for mapping, and attaches scores. **It does not modify the ID.**
-   Finally, `handleGenerateFinalRecipes` sends a list of these fully-enriched, correctly-ID'd oils to the recipe generation API.

**Status**: At this point, the client has the correct, canonical `oil_id` and the associated rich data.

### Step 4: The Breakpoint - The Recipe Generation API Response

This is the step where the data integrity is lost.

-   The recipe generation API on the server processes the request and succeeds in creating a recipe.
-   It responds with a `FinalRecipeProtocol` object. Inside this object, the `selected_oils` array contains oils with the correct **canonical `oil_id`**. 
-   **However, the API only returns a partial object for each oil.** It discards all the rich context (safety data, scores, botanical names, etc.) and sends back only the bare minimum: `{ oil_id, name_localized, drops_count }`.
-   The `updateFinalRecipes` action in the store receives this payload and overwrites the `finalRecipes` state with this new, incomplete data.

**Conclusion**: The `selectedOils` list in the final recipe has the correct IDs but is populated with simplified objects, directly causing the data mismatch with the fully-enriched `alternativeOils` list.

---

## 4. Final Recommendation

The problem is not that the ID is wrong, but that the rich data associated with the canonical ID is discarded and replaced with a partial object from the recipe generation API. 

The most robust solution is to treat the client-side enriched data as the single source of truth. When the final recipe is received from the server, the client should use the `oil_id` from the response to look up the corresponding **full `EnrichedEssentialOil` object** from its `therapeuticProperties` state and use *that* full object to populate the `finalRecipes.selected_oils` array. This ensures data consistency across the entire feature.

 LOG  🎭 SymptomsSelection - Modal State: {"isStreaming": false, "showStreamingModal": false, "streamingItemsCount": 5, "timestamp": "2025-09-06T15:33:00.688Z"}
 LOG  🔍 SymptomsSelection: First symptom data structure: {"explanation": "Sensação de plenitude ou inchaço após refeições ricas em gordura devido ao processamento dificultoso pelo sistema digestivo.", "symptom_id": "d3b07384-2a6d-4c65-8d6d-859f2b1a16c1", "symptom_name": "Sensação de plenitude abdominal após refeições", "symptom_suggestion": "Observe se ocorre após o consumo de alimentos gordurosos"}
 LOG  🔍 SymptomsSelection: Available fields: ["symptom_id", "symptom_name", "symptom_suggestion", "explanation"]
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: true
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 4, "total": 5}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🔍 getCauseNamesByIds called with UUIDs: ["d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b"]
 LOG  🔍 Available selectedCauses: [{"id":"d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b","name":"Dieta rica em alimentos gordurosos"}]
 LOG  🔍 Looking for cause UUID "d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b": FOUND "Dieta rica em alimentos gordurosos"
 LOG  🔍 getCauseNamesByIds final result: ["Dieta rica em alimentos gordurosos"]
 LOG  🔍 getSymptomNamesByIds called with UUIDs: ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"]
 LOG  🔍 Available selectedSymptoms: [{"id":"d3b07384-2a6d-4c65-8d6d-859f2b1a16c1","name":"Sensação de plenitude abdominal após refeições"}]
 LOG  🔍 Looking for symptom UUID "d3b07384-2a6d-4c65-8d6d-859f2b1a16c1": FOUND "Sensação de plenitude abdominal após refeições"
 LOG  🔍 getSymptomNamesByIds final result: ["Sensação de plenitude abdominal após refeições"]
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: true
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 4, "total": 5}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🔍 [MobileSSE] New data chunk: {"chunkLength": 417, "chunkPreview": "data: {\"type\":\"structured_data\",\"field\":\"suggested_oils\",\"index\":7,\"data\":{\"oil_id\":\"008b2c3d-4e5f-6"}
 LOG  🔍 [MobileSSE] Processing message block: {"messageLength": 415, "messagePreview": "data: {\"type\":\"structured_data\",\"field\":\"suggested_oils\",\"index\":7,\"data\":{\"oil_id\":\"008b2c3d-4e5f-6789-0a1b-2c3d4e5f6789\",\"name_english\":\"Camomila\",\""}
 LOG  🔍 [MobileSSE] Yielding data: {"dataLength": 409, "dataPreview": "{\"type\":\"structured_data\",\"field\":\"suggested_oils\",\"index\":7,\"data\":{\"oil_id\":\"008b2c3d-4e5f-6789-0a"}
 LOG  📱 [API-ios] Mobile SSE message received {"dataLength": 409, "dataPreview": "{\"type\":\"structured_data\",\"field\":\"suggested_oils\",\"index\":7,\"data\":{\"oil_id\":\"008b2c3d-4e5f-6789-0a", "requestId": "api-1757172755194-w12nnil5n"}
 LOG  📦 [SSE-ios] Chunk 8 received {"bufferLengthBefore": 0, "chunkPreview": "data: {\"type\":\"structured_data\",\"field\":\"suggested_oils\",\"index\":7,\"data\":{\"oil_id\":\"008b2c3d-4e5f-6...", "chunkSize": 419, "sessionId": "sse-1757172755201-9gpqn97x7", "totalBytes": 3432}
 LOG  🔄 [SSE-ios] Buffer updated {"bufferLength": 417, "bufferPreview": "data: {\"type\":\"structured_data\",\"field\":\"suggested_oils\",\"index\":7,\"data\":{\"oil_id\":\"008b2c3d-4e5f-6789-0a1b-2c3d4e5f6789\",\"name_english\":\"Camomila\",\"name_botanical\":\"Matricaria chamomilla\",\"name_loca...", "sessionId": "sse-1757172755201-9gpqn97x7"}
 LOG  🔍 [SSE-ios] Processing message block 1 {"messageBlock": "data: {\"type\":\"structured_data\",\"field\":\"suggested_oils\",\"index\":7,\"data\":{\"oil_id\":\"008b2c3d-4e5f-6789-0a1b-2c3d4e5f6789\",\"name_english\":\"Camomila\",\"...", "remainingBufferLength": 0, "sessionId": "sse-1757172755201-9gpqn97x7"}
 LOG  📤 [SSE-ios] Yielding data message 8 {"data": "{\"type\":\"structured_data\",\"field\":\"suggested_oils\",\"index\":7,\"data\":{\"oil_id\":\"008b2c3d-4e5f-6789-0a...", "dataLength": 409, "fullLine": "data: {\"type\":\"structured_data\",\"field\":\"suggested_oils\",\"index\":7,\"data\":{\"oil_id\":\"008b2c3d-4e5f-6789-0a1b-2c3d4e5f6789\",\"name_english\":\"Camomila\",\"...", "sessionId": "sse-1757172755201-9gpqn97x7"}
 LOG  ✨ [SSE-ios] Processed 1 message blocks in chunk 8 {"sessionId": "sse-1757172755201-9gpqn97x7", "totalMessagesYielded": 8}
 LOG  📥 [SSE-ios] Reading chunk 9 {"sessionId": "sse-1757172755201-9gpqn97x7"}
 LOG  🔍 [MobileSSE] New data chunk: {"chunkLength": 3734, "chunkPreview": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"Execução do processo de recomendaçã"}
 LOG  🔍 [MobileSSE] Processing message block: {"messageLength": 3732, "messagePreview": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"Execução do processo de recomendação de óleos essenciais\",\"request_id\":\"12345678-1234"}
 LOG  🔍 [MobileSSE] Yielding data: {"dataLength": 3726, "dataPreview": "{\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"Execução do processo de recomendação de ó"}
 LOG  📱 [API-ios] Mobile SSE message received {"dataLength": 3726, "dataPreview": "{\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"Execução do processo de recomendação de ó", "requestId": "api-1757172755194-w12nnil5n"}
 LOG  📦 [SSE-ios] Chunk 9 received {"bufferLengthBefore": 0, "chunkPreview": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"Execução do processo de recomendaçã...", "chunkSize": 3782, "sessionId": "sse-1757172755201-9gpqn97x7", "totalBytes": 7214}
 LOG  🔄 [SSE-ios] Buffer updated {"bufferLength": 3734, "bufferPreview": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"Execução do processo de recomendação de óleos essenciais\",\"request_id\":\"12345678-1234-1234-1234-123456789012\",\"timestamp_utc\":\"2023-10-...", "sessionId": "sse-1757172755201-9gpqn97x7"}
 LOG  🔍 [SSE-ios] Processing message block 1 {"messageBlock": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"Execução do processo de recomendação de óleos essenciais\",\"request_id\":\"12345678-1234...", "remainingBufferLength": 0, "sessionId": "sse-1757172755201-9gpqn97x7"}
 LOG  📤 [SSE-ios] Yielding data message 9 {"data": "{\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"Execução do processo de recomendação de ó...", "dataLength": 3726, "fullLine": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"Execução do processo de recomendação de óleos essenciais\",\"request_id\":\"12345678-1234...", "sessionId": "sse-1757172755201-9gpqn97x7"}
 LOG  🏁 [ParallelEngine] Processing structured_complete for c3f4e5a6-b7c8-9012-cd34-e5f678901234
 LOG  📋 [ParallelEngine] Complete data keys: ["meta", "data", "echo"]
 LOG  📋 [ParallelEngine] Data structure preview: {"dataKeys": ["meta", "data", "echo"], "firstLevelData": {"data": "object", "echo": "object", "meta": "object"}, "hasSuggestedOils": false, "hasTherapeuticPropertyContext": false}
 LOG  🔍 [RN-Streaming] ResponseParser received: {"dataKeys": ["therapeutic_property_context", "suggested_oils"], "finalDataKeys": ["meta", "data", "echo"], "hasData": true, "hasFinalData": true}
 LOG  ✅ [RN-Streaming] Found suggested_oils: {"hasContext": true, "oilsCount": 8}
 LOG  ✅ [ParallelEngine] Completed stream for c3f4e5a6-b7c8-9012-cd34-e5f678901234: SUCCESS
 LOG  ✅ [ParallelEngine] Stream result preview: {"hasOils": true, "oilsCount": 8, "resultKeys": ["therapeutic_property_context", "suggested_oils"]}
 LOG  🔒 [SSE-ios] Releasing reader lock {"sessionId": "sse-1757172755201-9gpqn97x7"}
 LOG  📋 [ParallelEngine] Added result for c3f4e5a6-b7c8-9012-cd34-e5f678901234. Total completed: 5
 LOG  🎯 [ParallelEngine] All streams completed. Results: 5, Errors: 0
 LOG  🚀 LAYER 11P-1: Streaming initiated! parallelStreamingState.isStreaming should now be TRUE
 LOG  🔍 [MobileSSE] Connection completed
 LOG  📱 [API-ios] Mobile SSE connection closed {"requestId": "api-1757172755194-w12nnil5n"}
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  📊 LAYER 8C: Map/Set sizes changed
 LOG  📊 LAYER 8C: Results size: 5
 LOG  📊 LAYER 8C: Errors size: 0
 LOG  🔄 LAYER 9B: Processing individual streaming results with STORE OPERATIONS...
 LOG  ✅ LAYER 9B: Updating property c3f4e5a6-b7c8-9012-cd34-e5f678901234 with 8 oils via STORE
 LOG  ACTION: updateTherapeuticProperties
 LOG  PAYLOAD: {"properties": [{"addresses_cause_ids": [Array], "addresses_symptom_ids": [Array], "description_contextual_localized": "Auxilia na melhora do processo de digestão, reduzindo o desconforto após refeições gordurosas, promovendo maior equilíbrio no sistema digestivo.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "a1f2c3d4-e5f6-7890-ab12-c3d4e5f67890", "property_name_english": "Digestive", "property_name_localized": "Digestivo", "relevancy_score": 5, "suggested_oils": [Array]}, {"addresses_cause_ids": [Array], "addresses_symptom_ids": [Array], "description_contextual_localized": "Reduz a inflamação no sistema digestivo, ajudando a aliviar o desconforto relacionado ao consumo de alimentos gordurosos.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "b2f3d4e5-f6a7-8901-bc23-d4e5f6789012", "property_name_english": "Anti-inflammatory", "property_name_localized": "Anti-inflamatório", "relevancy_score": 4, "suggested_oils": [Array]}, {"addresses_cause_ids": [Array], "addresses_symptom_ids": [Array], "description_contextual_localized": "Promove o equilíbrio emocional, ajudando a reduzir o estresse que pode impactar a digestão, promovendo uma sensação de tranquilidade.", "errorLoadingOils": null, "isEnriched": false, "isLoadingOils": false, "property_id": "c3f4e5a6-b7c8-9012-cd34-e5f678901234", "property_name_english": "Calming", "property_name_localized": "Calmante", "relevancy_score": 3, "suggested_oils": [Array]}, {"addresses_cause_ids": [Array], "addresses_symptom_ids": [Array], "description_contextual_localized": "Auxilia na revitalização do sistema digestivo, promovendo maior disposição após refeições, especialmente após ingerir gordura.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "d4f5e6b7-c8d9-0123-de45-f67890123456", "property_name_english": "Energizing", "property_name_localized": "Energizante", "relevancy_score": 2, "suggested_oils": [Array]}, {"addresses_cause_ids": [Array], "addresses_symptom_ids": [Array], "description_contextual_localized": "Ajuda a reduzir a ansiedade e o estresse que podem afetar a digestão, promovendo maior relaxamento emocional.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "e5f6a7b8-c9d0-1234-ea56-89ab01234567", "property_name_english": "Nervine Relaxant", "property_name_localized": "Nervino relaxante", "relevancy_score": 2, "suggested_oils": [Array]}], "source": "individual-result-c3f4e5a6-b7c8-9012-cd34-e5f678901234"}
 LOG  🤖 AUTO-ENRICH: Suggested oils arrived for property c3f4e5a6-b7c8-9012-cd34-e5f678901234, triggering auto-enrichment...
 LOG  🤖 AUTO-ENRICH: Starting auto-enrichment for property c3f4e5a6-b7c8-9012-cd34-e5f678901234 (attempt 1/4)
 LOG  Making batch enrichment API request: {"oilCount": 8, "url": "https://novo.rotinanatural.com.br/api/ai/batch-enrichment"}
 LOG  🎯 AUTO-COMPLETION: Checking streaming completion:
 LOG  🎯 AUTO-COMPLETION: totalSelected: 5
 LOG  🎯 AUTO-COMPLETION: totalCompleted: 5
 LOG  🎯 AUTO-COMPLETION: isStreaming: false
 LOG  🎯 AUTO-COMPLETION: isSubmitting: true
 LOG  🎯 AUTO-COMPLETION: ✅ Streaming finished, resetting isSubmitting
 LOG  🎯 AUTO-COMPLETION: Results count: 5
 LOG  🎯 AUTO-COMPLETION: Errors count: 0
 LOG  🔍 [CausesSelection] Store subscription debug: {"potentialCausesFromStore": 7, "renderTimestamp": "2025-09-06T15:33:06.155Z", "selectedCausesFromStore": 1}
 LOG  🔍 [CausesSelection] Counter Debug: {"SELECTION_REQUIREMENTS_CAUSES": {"max": 10, "min": 1}, "maxSelection": 10, "minSelection": 1, "selectedCauses": 1, "selectedCausesArray": [{"id": "d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b", "name": "Dieta rica em alimentos gordurosos"}], "selectionCount": 1}
 LOG  🩺 SymptomsSelection Debug: {"canSubmit": true, "selectedCount": 1, "selectedIds": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "totalSymptoms": 8}
 LOG  🎭 SymptomsSelection - Modal State: {"isStreaming": false, "showStreamingModal": false, "streamingItemsCount": 5, "timestamp": "2025-09-06T15:33:06.172Z"}
 LOG  🔍 SymptomsSelection: First symptom data structure: {"explanation": "Sensação de plenitude ou inchaço após refeições ricas em gordura devido ao processamento dificultoso pelo sistema digestivo.", "symptom_id": "d3b07384-2a6d-4c65-8d6d-859f2b1a16c1", "symptom_name": "Sensação de plenitude abdominal após refeições", "symptom_suggestion": "Observe se ocorre após o consumo de alimentos gordurosos"}
 LOG  🔍 SymptomsSelection: Available fields: ["symptom_id", "symptom_name", "symptom_suggestion", "explanation"]
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🔍 getCauseNamesByIds called with UUIDs: ["d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b"]
 LOG  🔍 Available selectedCauses: [{"id":"d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b","name":"Dieta rica em alimentos gordurosos"}]
 LOG  🔍 Looking for cause UUID "d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b": FOUND "Dieta rica em alimentos gordurosos"
 LOG  🔍 getCauseNamesByIds final result: ["Dieta rica em alimentos gordurosos"]
 LOG  🔍 getSymptomNamesByIds called with UUIDs: ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"]
 LOG  🔍 Available selectedSymptoms: [{"id":"d3b07384-2a6d-4c65-8d6d-859f2b1a16c1","name":"Sensação de plenitude abdominal após refeições"}]
 LOG  🔍 Looking for symptom UUID "d3b07384-2a6d-4c65-8d6d-859f2b1a16c1": FOUND "Sensação de plenitude abdominal após refeições"
 LOG  🔍 getSymptomNamesByIds final result: ["Sensação de plenitude abdominal após refeições"]
 LOG  🎯 AUTO-COMPLETION: Checking streaming completion:
 LOG  🎯 AUTO-COMPLETION: totalSelected: 5
 LOG  🎯 AUTO-COMPLETION: totalCompleted: 5
 LOG  🎯 AUTO-COMPLETION: isStreaming: false
 LOG  🎯 AUTO-COMPLETION: isSubmitting: false
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🚀 Triggering bottom space!
 LOG  ✅ AUTO-ENRICH: Successfully enriched property c3f4e5a6-b7c8-9012-cd34-e5f678901234 with 8 oils
 LOG  🔍 RAW API RESPONSE for property c3f4e5a6-b7c8-9012-cd34-e5f678901234: {
  "enriched_oils": [
    {
      "botanical_name": "",
      "oil_id": "001a2b3c-4d5e-678f-901a-2b3c4d5e6f78",
      "name_english": "Injustice",
      "name_botanical": "Injustice",
      "name_localized": "Injustice",
      "match_rationale_localized": "Indicado para aliviar azia, gastrite, má digestão e estufamento, auxiliando na regulação do sistema digestivo.",
      "relevancy_to_property_score": 5,
      "enrichment_status": "not_found",
      "search_query": "Injustice - Injustice",
      "enrichment_timestamp": "2025-09-06T15:33:08.293Z",
      "isEnriched": false
    },
    {
      "botanical_name": "",
      "oil_id": "caef2077-85b5-479f-a828-7b7be804e498",
      "name_english": "Limão Siciliano",
      "name_botanical": "Citrus limon",
      "name_localized": "Limão Siciliano",
      "match_rationale_localized": "Auxilia no combate à azia e desconfortos gástricos, promovendo uma sensação de alívio e relaxamento abdominal.",
      "relevancy_to_property_score": 4,
      "name_scientific": "Citrus limon",
      "safety": {
        "internal_use": {
          "id": "2343a180-7dd2-45b1-a402-065b1bf2bd7c",
          "code": "FOOD_GRADE_EO",
          "name": "Safe for Internal Use",
          "guidance": null,
          "description": "Essential oils in this category are generally recognized or have specific indications suggesting they may be safe for internal use when following appropriate dilutions, dosage, and professional guidance."
        },
        "dilution": {
          "id": "45c9d911-9b2b-472f-99aa-fdbaf3caa98d",
          "name": "[S] Sensitive",
          "ratio": "5:1",
          "description": "Sensitive. “Sensitive” oils are those that should be diluted before use on young or sensitive skin. Examples of “sensitive” oils are peppermint, ginger, eucalyptus, wintergreen, and black pepper.",
          "percentage_max": 0.2,
          "percentage_min": 0.15
        },
        "phototoxicity": {
          "id": "9a987a49-f246-4aa2-99d7-87d189a01d00",
          "status": "Phototoxic",
          "guidance": "Avoid direct sun exposure or UV light on the application area for at least 12 hours (or as specified for the particular oil) after topical use, even when diluted. Consider using during evenings or on skin areas covered by clothing if UV exposure is anticipated.",
          "description": "Contains compounds that can cause phototoxic reactions (e.g., skin sensitization, rash, burning, or hyperpigmentation) when skin is exposed to UV light (sunlight, tanning beds) within a certain timeframe after topical application."
        },
        "pregnancy_nursing": [
          {
            "id": "1ae12b7d-04e1-4306-b218-7e4bd7b0865b",
            "code": "",
            "name": "",
            "description": "",
            "usage_guidance": "",
            "status_description": "pregnancy-safe-100"
          },
          {
            "id": "3735d6e6-89be-4887-901e-968170842c18",
            "code": null,
            "name": "",
            "description": "",
            "usage_guidance": null,
            "status_description": "pregnancy-safe-3months"
          }
        ],
        "child_safety": [],
        "internal_use_id": "2343a180-7dd2-45b1-a402-065b1bf2bd7c",
        "dilution_id": "45c9d911-9b2b-472f-99aa-fdbaf3caa98d",
        "phototoxicity_id": "9a987a49-f246-4aa2-99d7-87d189a01d00",
        "pregnancy_nursing_ids": [
          "1ae12b7d-04e1-4306-b218-7e4bd7b0865b",
          "3735d6e6-89be-4887-901e-968170842c18"
        ],
        "child_safety_ids": []
      },
      "enrichment_status": "enriched",
      "botanical_mismatch": false,
      "similarity_score": 0.878763303609288,
      "search_query": "Limão Siciliano - Citrus limon",
      "enrichment_timestamp": "2025-09-06T15:33:08.294Z",
      "isEnriched": true
    },
    {
      "botanical_name": "",
      "oil_id": "7fd15cfa-5a18-4e12-ba6b-6da0d62d70bc",
      "name_english": "Funcho",
      "name_botanical": "Foeniculum vulgare",
      "name_localized": "Funcho",
      "match_rationale_localized": "Ajuda na digestão e alívio de cólicas intestinais, promovendo relaxamento do sistema gastrointestinal.",
      "relevancy_to_property_score": 4,
      "name_scientific": "Foeniculum vulgare",
      "safety": {
        "internal_use": {
          "id": "2343a180-7dd2-45b1-a402-065b1bf2bd7c",
          "code": "FOOD_GRADE_EO",
          "name": "Safe for Internal Use",
          "guidance": null,
          "description": "Essential oils in this category are generally recognized or have specific indications suggesting they may be safe for internal use when following appropriate dilutions, dosage, and professional guidance."
        },
        "dilution": {
          "id": "45c9d911-9b2b-472f-99aa-fdbaf3caa98d",
          "name": "[S] Sensitive",
          "ratio": "5:1",
          "description": "Sensitive. “Sensitive” oils are those that should be diluted before use on young or sensitive skin. Examples of “sensitive” oils are peppermint, ginger, eucalyptus, wintergreen, and black pepper.",
          "percentage_max": 0.2,
          "percentage_min": 0.15
        },
        "phototoxicity": {
          "id": "ae18d720-4473-479e-b9f3-7fa65192d639",
          "status": "Non-Phototoxic",
          "guidance": "No specific sun exposure restrictions related to phototoxicity are typically required after topical application. However, always follow general safety guidelines for dilution and application for all essential oils.",
          "description": "Generally not known to cause phototoxic reactions when skin is exposed to UV light after topical application, when used appropriately."
        },
        "pregnancy_nursing": [],
        "child_safety": [],
        "internal_use_id": "2343a180-7dd2-45b1-a402-065b1bf2bd7c",
        "dilution_id": "45c9d911-9b2b-472f-99aa-fdbaf3caa98d",
        "phototoxicity_id": "ae18d720-4473-479e-b9f3-7fa65192d639",
        "pregnancy_nursing_ids": [],
        "child_safety_ids": []
      },
      "enrichment_status": "enriched",
      "botanical_mismatch": false,
      "similarity_score": 0.786786888454804,
      "search_query": "Funcho - Foeniculum vulgare",
      "enrichment_timestamp": "2025-09-06T15:33:08.294Z",
      "isEnriched": true
    },
    {
      "botanical_name": "",
      "oil_id": "56b7e686-5417-4bcf-a975-9888f559a4f3",
      "name_english": "Gengibre",
      "name_botanical": "Zingiber officinale",
      "name_localized": "Gengibre",
      "match_rationale_localized": "Contribui para reduzir náuseas, azia e melhorar a digestão, além de possuir propriedades termogênicas.",
      "relevancy_to_property_score": 4,
      "name_scientific": "Zingiber officinale",
      "safety": {
        "internal_use": {
          "id": "2343a180-7dd2-45b1-a402-065b1bf2bd7c",
          "code": "FOOD_GRADE_EO",
          "name": "Safe for Internal Use",
          "guidance": null,
          "description": "Essential oils in this category are generally recognized or have specific indications suggesting they may be safe for internal use when following appropriate dilutions, dosage, and professional guidance."
        },
        "dilution": {
          "id": "45c9d911-9b2b-472f-99aa-fdbaf3caa98d",
          "name": "[S] Sensitive",
          "ratio": "5:1",
          "description": "Sensitive. “Sensitive” oils are those that should be diluted before use on young or sensitive skin. Examples of “sensitive” oils are peppermint, ginger, eucalyptus, wintergreen, and black pepper.",
          "percentage_max": 0.2,
          "percentage_min": 0.15
        },
        "phototoxicity": {
          "id": "ae18d720-4473-479e-b9f3-7fa65192d639",
          "status": "Non-Phototoxic",
          "guidance": "No specific sun exposure restrictions related to phototoxicity are typically required after topical application. However, always follow general safety guidelines for dilution and application for all essential oils.",
          "description": "Generally not known to cause phototoxic reactions when skin is exposed to UV light after topical application, when used appropriately."
        },
        "pregnancy_nursing": [
          {
            "id": "1ae12b7d-04e1-4306-b218-7e4bd7b0865b",
            "code": "",
            "name": "",
            "description": "",
            "usage_guidance": "",
            "status_description": "pregnancy-safe-100"
          },
          {
            "id": "3735d6e6-89be-4887-901e-968170842c18",
            "code": null,
            "name": "",
            "description": "",
            "usage_guidance": null,
            "status_description": "pregnancy-safe-3months"
          }
        ],
        "child_safety": [],
        "internal_use_id": "2343a180-7dd2-45b1-a402-065b1bf2bd7c",
        "dilution_id": "45c9d911-9b2b-472f-99aa-fdbaf3caa98d",
        "phototoxicity_id": "ae18d720-4473-479e-b9f3-7fa65192d639",
        "pregnancy_nursing_ids": [
          "1ae12b7d-04e1-4306-b218-7e4bd7b0865b",
          "3735d6e6-89be-4887-901e-968170842c18"
        ],
        "child_safety_ids": []
      },
      "enrichment_status": "enriched",
      "botanical_mismatch": false,
      "similarity_score": 0.887758632399914,
      "search_query": "Gengibre - Zingiber officinale",
      "enrichment_timestamp": "2025-09-06T15:33:08.294Z",
      "isEnriched": true
    },
    {
      "botanical_name": "",
      "oil_id": "ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf",
      "name_english": "Hortelã-Pimenta",
      "name_botanical": "Mentha piperita",
      "name_localized": "Hortelã-Pimenta",
      "match_rationale_localized": "Alivia desconfortos gástricos, azia e refluxo, promovendo sensação de frescor e relaxamento abdominal.",
      "relevancy_to_property_score": 4,
      "name_scientific": "Mentha Piperita",
      "safety": {
        "internal_use": {
          "id": "2343a180-7dd2-45b1-a402-065b1bf2bd7c",
          "code": "FOOD_GRADE_EO",
          "name": "Safe for Internal Use",
          "guidance": null,
          "description": "Essential oils in this category are generally recognized or have specific indications suggesting they may be safe for internal use when following appropriate dilutions, dosage, and professional guidance."
        },
        "dilution": {
          "id": "45c9d911-9b2b-472f-99aa-fdbaf3caa98d",
          "name": "[S] Sensitive",
          "ratio": "5:1",
          "description": "Sensitive. “Sensitive” oils are those that should be diluted before use on young or sensitive skin. Examples of “sensitive” oils are peppermint, ginger, eucalyptus, wintergreen, and black pepper.",
          "percentage_max": 0.2,
          "percentage_min": 0.15
        },
        "phototoxicity": {
          "id": "ae18d720-4473-479e-b9f3-7fa65192d639",
          "status": "Non-Phototoxic",
          "guidance": "No specific sun exposure restrictions related to phototoxicity are typically required after topical application. However, always follow general safety guidelines for dilution and application for all essential oils.",
          "description": "Generally not known to cause phototoxic reactions when skin is exposed to UV light after topical application, when used appropriately."
        },
        "pregnancy_nursing": [
          {
            "id": "1b330391-6fb2-4972-bda3-6872e9835f9a",
            "code": "",
            "name": "",
            "description": "",
            "usage_guidance": "",
            "status_description": "pregnancy-safe-50"
          }
        ],
        "child_safety": [],
        "internal_use_id": "2343a180-7dd2-45b1-a402-065b1bf2bd7c",
        "dilution_id": "45c9d911-9b2b-472f-99aa-fdbaf3caa98d",
        "phototoxicity_id": "ae18d720-4473-479e-b9f3-7fa65192d639",
        "pregnancy_nursing_ids": [
          "1b330391-6fb2-4972-bda3-6872e9835f9a"
        ],
        "child_safety_ids": []
      },
      "enrichment_status": "enriched",
      "botanical_mismatch": false,
      "similarity_score": 0.855765419223793,
      "search_query": "Hortelã-Pimenta - Mentha piperita",
      "enrichment_timestamp": "2025-09-06T15:33:08.295Z",
      "isEnriched": true
    },
    {
      "botanical_name": "",
      "oil_id": "0ed154c2-cb91-4449-9b3b-37ffea79c9ac",
      "name_english": "Tangerina",
      "name_botanical": "Citrus reticulata",
      "name_localized": "Tangerina",
      "match_rationale_localized": "Contribui para o relaxamento, alivia o estresse e auxilia na digestão por seu aroma suave e calmante.",
      "relevancy_to_property_score": 3,
      "name_scientific": "Citrus reticulata",
      "safety": {
        "internal_use": {
          "id": "2343a180-7dd2-45b1-a402-065b1bf2bd7c",
          "code": "FOOD_GRADE_EO",
          "name": "Safe for Internal Use",
          "guidance": null,
          "description": "Essential oils in this category are generally recognized or have specific indications suggesting they may be safe for internal use when following appropriate dilutions, dosage, and professional guidance."
        },
        "dilution": {
          "id": "3404c0a7-6158-4236-bbd9-724728538c3d",
          "name": "[N] Neat",
          "ratio": "1:1",
          "description": "Neat. Essential oils categorized as “neat” can be applied topically without dilution on most people. These oils are considered mild and generally do not induce skin sensitivity. Examples of “neat” essential oils are frankincense, lavender, melaleuca, melissa, and sandalwood.",
          "percentage_max": 0.5,
          "percentage_min": 0
        },
        "phototoxicity": {
          "id": "9a987a49-f246-4aa2-99d7-87d189a01d00",
          "status": "Phototoxic",
          "guidance": "Avoid direct sun exposure or UV light on the application area for at least 12 hours (or as specified for the particular oil) after topical use, even when diluted. Consider using during evenings or on skin areas covered by clothing if UV exposure is anticipated.",
          "description": "Contains compounds that can cause phototoxic reactions (e.g., skin sensitization, rash, burning, or hyperpigmentation) when skin is exposed to UV light (sunlight, tanning beds) within a certain timeframe after topical application."
        },
        "pregnancy_nursing": [
          {
            "id": "1ae12b7d-04e1-4306-b218-7e4bd7b0865b",
            "code": "",
            "name": "",
            "description": "",
            "usage_guidance": "",
            "status_description": "pregnancy-safe-100"
          }
        ],
        "child_safety": [],
        "internal_use_id": "2343a180-7dd2-45b1-a402-065b1bf2bd7c",
        "dilution_id": "3404c0a7-6158-4236-bbd9-724728538c3d",
        "phototoxicity_id": "9a987a49-f246-4aa2-99d7-87d189a01d00",
        "pregnancy_nursing_ids": [
          "1ae12b7d-04e1-4306-b218-7e4bd7b0865b"
        ],
        "child_safety_ids": []
      },
      "enrichment_status": "enriched",
      "botanical_mismatch": false,
      "similarity_score": 0.905911164418848,
      "search_query": "Tangerina - Citrus reticulata",
      "enrichment_timestamp": "2025-09-06T15:33:08.295Z",
      "isEnriched": true
    },
    {
      "botanical_name": "",
      "oil_id": "d354b218-cf17-42aa-8c40-d569ed5b3ab6",
      "name_english": "Júniper berries",
      "name_botanical": "Juniperus communis",
      "name_localized": "Bagas de Júniper",
      "match_rationale_localized": "Auxilia na digestão e ajuda na redução de inchaço abdominal, além de promover relaxamento.",
      "relevancy_to_property_score": 3,
      "name_scientific": "Juniperus communis",
      "safety": {
        "internal_use": {
          "id": "2343a180-7dd2-45b1-a402-065b1bf2bd7c",
          "code": "FOOD_GRADE_EO",
          "name": "Safe for Internal Use",
          "guidance": null,
          "description": "Essential oils in this category are generally recognized or have specific indications suggesting they may be safe for internal use when following appropriate dilutions, dosage, and professional guidance."
        },
        "dilution": {
          "id": "3404c0a7-6158-4236-bbd9-724728538c3d",
          "name": "[N] Neat",
          "ratio": "1:1",
          "description": "Neat. Essential oils categorized as “neat” can be applied topically without dilution on most people. These oils are considered mild and generally do not induce skin sensitivity. Examples of “neat” essential oils are frankincense, lavender, melaleuca, melissa, and sandalwood.",
          "percentage_max": 0.5,
          "percentage_min": 0
        },
        "phototoxicity": {
          "id": "ae18d720-4473-479e-b9f3-7fa65192d639",
          "status": "Non-Phototoxic",
          "guidance": "No specific sun exposure restrictions related to phototoxicity are typically required after topical application. However, always follow general safety guidelines for dilution and application for all essential oils.",
          "description": "Generally not known to cause phototoxic reactions when skin is exposed to UV light after topical application, when used appropriately."
        },
        "pregnancy_nursing": [
          {
            "id": "1ae12b7d-04e1-4306-b218-7e4bd7b0865b",
            "code": "",
            "name": "",
            "description": "",
            "usage_guidance": "",
            "status_description": "pregnancy-safe-100"
          }
        ],
        "child_safety": [],
        "internal_use_id": "2343a180-7dd2-45b1-a402-065b1bf2bd7c",
        "dilution_id": "3404c0a7-6158-4236-bbd9-724728538c3d",
        "phototoxicity_id": "ae18d720-4473-479e-b9f3-7fa65192d639",
        "pregnancy_nursing_ids": [
          "1ae12b7d-04e1-4306-b218-7e4bd7b0865b"
        ],
        "child_safety_ids": []
      },
      "enrichment_status": "enriched",
      "botanical_mismatch": false,
      "similarity_score": 0.753316982987468,
      "search_query": "Júniper berries - Juniperus communis",
      "enrichment_timestamp": "2025-09-06T15:33:08.296Z",
      "isEnriched": true
    },
    {
      "botanical_name": "",
      "oil_id": "8af64ea7-75eb-4f4f-a568-4e0b20758060",
      "name_english": "Camomila",
      "name_botanical": "Matricaria chamomilla",
      "name_localized": "Camomila",
      "match_rationale_localized": "Propriedades calmantes que auxiliam no relaxamento do sistema digestivo e redução do estresse.",
      "relevancy_to_property_score": 3,
      "name_scientific": "Matricaria recutita",
      "safety": {
        "internal_use": {
          "id": "247f091e-d55c-4a76-aaef-ea4485457b63",
          "code": "NON_INGESTIBLE",
          "name": "Not for Internal Use",
          "guidance": null,
          "description": "Essential oils in this category are not recommended or are unsafe for internal consumption. They are intended for topical application (diluted) or aromatic diffusion only."
        },
        "dilution": {
          "id": "3404c0a7-6158-4236-bbd9-724728538c3d",
          "name": "[N] Neat",
          "ratio": "1:1",
          "description": "Neat. Essential oils categorized as “neat” can be applied topically without dilution on most people. These oils are considered mild and generally do not induce skin sensitivity. Examples of “neat” essential oils are frankincense, lavender, melaleuca, melissa, and sandalwood.",
          "percentage_max": 0.5,
          "percentage_min": 0
        },
        "phototoxicity": {
          "id": "ae18d720-4473-479e-b9f3-7fa65192d639",
          "status": "Non-Phototoxic",
          "guidance": "No specific sun exposure restrictions related to phototoxicity are typically required after topical application. However, always follow general safety guidelines for dilution and application for all essential oils.",
          "description": "Generally not known to cause phototoxic reactions when skin is exposed to UV light after topical application, when used appropriately."
        },
        "pregnancy_nursing": [],
        "child_safety": [],
        "internal_use_id": "247f091e-d55c-4a76-aaef-ea4485457b63",
        "dilution_id": "3404c0a7-6158-4236-bbd9-724728538c3d",
        "phototoxicity_id": "ae18d720-4473-479e-b9f3-7fa65192d639",
        "pregnancy_nursing_ids": [],
        "child_safety_ids": []
      },
      "enrichment_status": "enriched",
      "botanical_mismatch": true,
      "similarity_score": 0.74121630858329,
      "search_query": "Camomila - Matricaria chamomilla",
      "enrichment_timestamp": "2025-09-06T15:33:08.296Z",
      "isEnriched": true
    }
  ],
  "total_input": 8,
  "total_enriched": 7,
  "total_not_found": 1,
  "total_discarded": 0,
  "processing_time_ms": 1909
}
 LOG  🔍 OIL 1 RAW DATA (Injustice): {"enrichment_status": "not_found", "hasSafety": false, "isEnriched": false, "name_botanical": "Injustice", "name_english": "Injustice", "oil_id": "001a2b3c-4d5e-678f-901a-2b3c4d5e6f78", "safetyRawData": "No safety data"}
 LOG  🔍 NO PREGNANCY DATA for Injustice
 LOG  🔍 NO CHILD SAFETY DATA for Injustice
 LOG  🔍 OIL 2 RAW DATA (Limão Siciliano): {"enrichment_status": "enriched", "hasSafety": true, "isEnriched": true, "name_botanical": "Citrus limon", "name_english": "Limão Siciliano", "oil_id": "caef2077-85b5-479f-a828-7b7be804e498", "safetyRawData": "{
  \"internal_use\": {
    \"id\": \"2343a180-7dd2-45b1-a402-065b1bf2bd7c\",
    \"code\": \"FOOD_GRADE_EO\",
    \"name\": \"Safe for Internal Use\",
    \"guidance\": null,
    \"description\": \"Essential oils in this category are generally recognized or have specific indications suggesting they may be safe for internal use when following appropriate dilutions, dosage, and professional guidance.\"
  },
  \"dilution\": {
    \"id\": \"45c9d911-9b2b-472f-99aa-fdbaf3caa98d\",
    \"name\": \"[S] Sensitive\",
    \"ratio\": \"5:1\",
    \"description\": \"Sensitive. “Sensitive” oils are those that should be diluted before use on young or sensitive skin. Examples of “sensitive” oils are peppermint, ginger, eucalyptus, wintergreen, and black pepper.\",
    \"percentage_max\": 0.2,
    \"percentage_min\": 0.15
  },
  \"phototoxicity\": {
    \"id\": \"9a987a49-f246-4aa2-99d7-87d189a01d00\",
    \"status\": \"Phototoxic\",
    \"guidance\": \"Avoid direct sun exposure or UV light on the application area for at least 12 hours (or as specified for the particular oil) after topical use, even when diluted. Consider using during evenings or on skin areas covered by clothing if UV exposure is anticipated.\",
    \"description\": \"Contains compounds that can cause phototoxic reactions (e.g., skin sensitization, rash, burning, or hyperpigmentation) when skin is exposed to UV light (sunlight, tanning beds) within a certain timeframe after topical application.\"
  },
  \"pregnancy_nursing\": [
    {
      \"id\": \"1ae12b7d-04e1-4306-b218-7e4bd7b0865b\",
      \"code\": \"\",
      \"name\": \"\",
      \"description\": \"\",
      \"usage_guidance\": \"\",
      \"status_description\": \"pregnancy-safe-100\"
    },
    {
      \"id\": \"3735d6e6-89be-4887-901e-968170842c18\",
      \"code\": null,
      \"name\": \"\",
      \"description\": \"\",
      \"usage_guidance\": null,
      \"status_description\": \"pregnancy-safe-3months\"
    }
  ],
  \"child_safety\": [],
  \"internal_use_id\": \"2343a180-7dd2-45b1-a402-065b1bf2bd7c\",
  \"dilution_id\": \"45c9d911-9b2b-472f-99aa-fdbaf3caa98d\",
  \"phototoxicity_id\": \"9a987a49-f246-4aa2-99d7-87d189a01d00\",
  \"pregnancy_nursing_ids\": [
    \"1ae12b7d-04e1-4306-b218-7e4bd7b0865b\",
    \"3735d6e6-89be-4887-901e-968170842c18\"
  ],
  \"child_safety_ids\": []
}"}
 LOG  🔍 PREGNANCY DATA for Limão Siciliano: {"pregnancyNursingIsArray": true, "pregnancyNursingKeys": ["0", "1"], "pregnancyNursingLength": 2, "pregnancyNursingRaw": "[
  {
    \"id\": \"1ae12b7d-04e1-4306-b218-7e4bd7b0865b\",
    \"code\": \"\",
    \"name\": \"\",
    \"description\": \"\",
    \"usage_guidance\": \"\",
    \"status_description\": \"pregnancy-safe-100\"
  },
  {
    \"id\": \"3735d6e6-89be-4887-901e-968170842c18\",
    \"code\": null,
    \"name\": \"\",
    \"description\": \"\",
    \"usage_guidance\": null,
    \"status_description\": \"pregnancy-safe-3months\"
  }
]", "pregnancyNursingType": "object"}
 LOG  🔍 CHILD SAFETY DATA for Limão Siciliano: {"childSafetyIsArray": true, "childSafetyLength": 0, "childSafetyRaw": "[]", "childSafetyType": "object"}
 LOG  🔍 OIL 3 RAW DATA (Funcho): {"enrichment_status": "enriched", "hasSafety": true, "isEnriched": true, "name_botanical": "Foeniculum vulgare", "name_english": "Funcho", "oil_id": "7fd15cfa-5a18-4e12-ba6b-6da0d62d70bc", "safetyRawData": "{
  \"internal_use\": {
    \"id\": \"2343a180-7dd2-45b1-a402-065b1bf2bd7c\",
    \"code\": \"FOOD_GRADE_EO\",
    \"name\": \"Safe for Internal Use\",
    \"guidance\": null,
    \"description\": \"Essential oils in this category are generally recognized or have specific indications suggesting they may be safe for internal use when following appropriate dilutions, dosage, and professional guidance.\"
  },
  \"dilution\": {
    \"id\": \"45c9d911-9b2b-472f-99aa-fdbaf3caa98d\",
    \"name\": \"[S] Sensitive\",
    \"ratio\": \"5:1\",
    \"description\": \"Sensitive. “Sensitive” oils are those that should be diluted before use on young or sensitive skin. Examples of “sensitive” oils are peppermint, ginger, eucalyptus, wintergreen, and black pepper.\",
    \"percentage_max\": 0.2,
    \"percentage_min\": 0.15
  },
  \"phototoxicity\": {
    \"id\": \"ae18d720-4473-479e-b9f3-7fa65192d639\",
    \"status\": \"Non-Phototoxic\",
    \"guidance\": \"No specific sun exposure restrictions related to phototoxicity are typically required after topical application. However, always follow general safety guidelines for dilution and application for all essential oils.\",
    \"description\": \"Generally not known to cause phototoxic reactions when skin is exposed to UV light after topical application, when used appropriately.\"
  },
  \"pregnancy_nursing\": [],
  \"child_safety\": [],
  \"internal_use_id\": \"2343a180-7dd2-45b1-a402-065b1bf2bd7c\",
  \"dilution_id\": \"45c9d911-9b2b-472f-99aa-fdbaf3caa98d\",
  \"phototoxicity_id\": \"ae18d720-4473-479e-b9f3-7fa65192d639\",
  \"pregnancy_nursing_ids\": [],
  \"child_safety_ids\": []
}"}
 LOG  🔍 PREGNANCY DATA for Funcho: {"pregnancyNursingIsArray": true, "pregnancyNursingKeys": [], "pregnancyNursingLength": 0, "pregnancyNursingRaw": "[]", "pregnancyNursingType": "object"}
 LOG  🔍 CHILD SAFETY DATA for Funcho: {"childSafetyIsArray": true, "childSafetyLength": 0, "childSafetyRaw": "[]", "childSafetyType": "object"}
 LOG  🔍 OIL 4 RAW DATA (Gengibre): {"enrichment_status": "enriched", "hasSafety": true, "isEnriched": true, "name_botanical": "Zingiber officinale", "name_english": "Gengibre", "oil_id": "56b7e686-5417-4bcf-a975-9888f559a4f3", "safetyRawData": "{
  \"internal_use\": {
    \"id\": \"2343a180-7dd2-45b1-a402-065b1bf2bd7c\",
    \"code\": \"FOOD_GRADE_EO\",
    \"name\": \"Safe for Internal Use\",
    \"guidance\": null,
    \"description\": \"Essential oils in this category are generally recognized or have specific indications suggesting they may be safe for internal use when following appropriate dilutions, dosage, and professional guidance.\"
  },
  \"dilution\": {
    \"id\": \"45c9d911-9b2b-472f-99aa-fdbaf3caa98d\",
    \"name\": \"[S] Sensitive\",
    \"ratio\": \"5:1\",
    \"description\": \"Sensitive. “Sensitive” oils are those that should be diluted before use on young or sensitive skin. Examples of “sensitive” oils are peppermint, ginger, eucalyptus, wintergreen, and black pepper.\",
    \"percentage_max\": 0.2,
    \"percentage_min\": 0.15
  },
  \"phototoxicity\": {
    \"id\": \"ae18d720-4473-479e-b9f3-7fa65192d639\",
    \"status\": \"Non-Phototoxic\",
    \"guidance\": \"No specific sun exposure restrictions related to phototoxicity are typically required after topical application. However, always follow general safety guidelines for dilution and application for all essential oils.\",
    \"description\": \"Generally not known to cause phototoxic reactions when skin is exposed to UV light after topical application, when used appropriately.\"
  },
  \"pregnancy_nursing\": [
    {
      \"id\": \"1ae12b7d-04e1-4306-b218-7e4bd7b0865b\",
      \"code\": \"\",
      \"name\": \"\",
      \"description\": \"\",
      \"usage_guidance\": \"\",
      \"status_description\": \"pregnancy-safe-100\"
    },
    {
      \"id\": \"3735d6e6-89be-4887-901e-968170842c18\",
      \"code\": null,
      \"name\": \"\",
      \"description\": \"\",
      \"usage_guidance\": null,
      \"status_description\": \"pregnancy-safe-3months\"
    }
  ],
  \"child_safety\": [],
  \"internal_use_id\": \"2343a180-7dd2-45b1-a402-065b1bf2bd7c\",
  \"dilution_id\": \"45c9d911-9b2b-472f-99aa-fdbaf3caa98d\",
  \"phototoxicity_id\": \"ae18d720-4473-479e-b9f3-7fa65192d639\",
  \"pregnancy_nursing_ids\": [
    \"1ae12b7d-04e1-4306-b218-7e4bd7b0865b\",
    \"3735d6e6-89be-4887-901e-968170842c18\"
  ],
  \"child_safety_ids\": []
}"}
 LOG  🔍 PREGNANCY DATA for Gengibre: {"pregnancyNursingIsArray": true, "pregnancyNursingKeys": ["0", "1"], "pregnancyNursingLength": 2, "pregnancyNursingRaw": "[
  {
    \"id\": \"1ae12b7d-04e1-4306-b218-7e4bd7b0865b\",
    \"code\": \"\",
    \"name\": \"\",
    \"description\": \"\",
    \"usage_guidance\": \"\",
    \"status_description\": \"pregnancy-safe-100\"
  },
  {
    \"id\": \"3735d6e6-89be-4887-901e-968170842c18\",
    \"code\": null,
    \"name\": \"\",
    \"description\": \"\",
    \"usage_guidance\": null,
    \"status_description\": \"pregnancy-safe-3months\"
  }
]", "pregnancyNursingType": "object"}
 LOG  🔍 CHILD SAFETY DATA for Gengibre: {"childSafetyIsArray": true, "childSafetyLength": 0, "childSafetyRaw": "[]", "childSafetyType": "object"}
 LOG  🔍 OIL 5 RAW DATA (Hortelã-Pimenta): {"enrichment_status": "enriched", "hasSafety": true, "isEnriched": true, "name_botanical": "Mentha piperita", "name_english": "Hortelã-Pimenta", "oil_id": "ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf", "safetyRawData": "{
  \"internal_use\": {
    \"id\": \"2343a180-7dd2-45b1-a402-065b1bf2bd7c\",
    \"code\": \"FOOD_GRADE_EO\",
    \"name\": \"Safe for Internal Use\",
    \"guidance\": null,
    \"description\": \"Essential oils in this category are generally recognized or have specific indications suggesting they may be safe for internal use when following appropriate dilutions, dosage, and professional guidance.\"
  },
  \"dilution\": {
    \"id\": \"45c9d911-9b2b-472f-99aa-fdbaf3caa98d\",
    \"name\": \"[S] Sensitive\",
    \"ratio\": \"5:1\",
    \"description\": \"Sensitive. “Sensitive” oils are those that should be diluted before use on young or sensitive skin. Examples of “sensitive” oils are peppermint, ginger, eucalyptus, wintergreen, and black pepper.\",
    \"percentage_max\": 0.2,
    \"percentage_min\": 0.15
  },
  \"phototoxicity\": {
    \"id\": \"ae18d720-4473-479e-b9f3-7fa65192d639\",
    \"status\": \"Non-Phototoxic\",
    \"guidance\": \"No specific sun exposure restrictions related to phototoxicity are typically required after topical application. However, always follow general safety guidelines for dilution and application for all essential oils.\",
    \"description\": \"Generally not known to cause phototoxic reactions when skin is exposed to UV light after topical application, when used appropriately.\"
  },
  \"pregnancy_nursing\": [
    {
      \"id\": \"1b330391-6fb2-4972-bda3-6872e9835f9a\",
      \"code\": \"\",
      \"name\": \"\",
      \"description\": \"\",
      \"usage_guidance\": \"\",
      \"status_description\": \"pregnancy-safe-50\"
    }
  ],
  \"child_safety\": [],
  \"internal_use_id\": \"2343a180-7dd2-45b1-a402-065b1bf2bd7c\",
  \"dilution_id\": \"45c9d911-9b2b-472f-99aa-fdbaf3caa98d\",
  \"phototoxicity_id\": \"ae18d720-4473-479e-b9f3-7fa65192d639\",
  \"pregnancy_nursing_ids\": [
    \"1b330391-6fb2-4972-bda3-6872e9835f9a\"
  ],
  \"child_safety_ids\": []
}"}
 LOG  🔍 PREGNANCY DATA for Hortelã-Pimenta: {"pregnancyNursingIsArray": true, "pregnancyNursingKeys": ["0"], "pregnancyNursingLength": 1, "pregnancyNursingRaw": "[
  {
    \"id\": \"1b330391-6fb2-4972-bda3-6872e9835f9a\",
    \"code\": \"\",
    \"name\": \"\",
    \"description\": \"\",
    \"usage_guidance\": \"\",
    \"status_description\": \"pregnancy-safe-50\"
  }
]", "pregnancyNursingType": "object"}
 LOG  🔍 CHILD SAFETY DATA for Hortelã-Pimenta: {"childSafetyIsArray": true, "childSafetyLength": 0, "childSafetyRaw": "[]", "childSafetyType": "object"}
 LOG  🔍 OIL 6 RAW DATA (Tangerina): {"enrichment_status": "enriched", "hasSafety": true, "isEnriched": true, "name_botanical": "Citrus reticulata", "name_english": "Tangerina", "oil_id": "0ed154c2-cb91-4449-9b3b-37ffea79c9ac", "safetyRawData": "{
  \"internal_use\": {
    \"id\": \"2343a180-7dd2-45b1-a402-065b1bf2bd7c\",
    \"code\": \"FOOD_GRADE_EO\",
    \"name\": \"Safe for Internal Use\",
    \"guidance\": null,
    \"description\": \"Essential oils in this category are generally recognized or have specific indications suggesting they may be safe for internal use when following appropriate dilutions, dosage, and professional guidance.\"
  },
  \"dilution\": {
    \"id\": \"3404c0a7-6158-4236-bbd9-724728538c3d\",
    \"name\": \"[N] Neat\",
    \"ratio\": \"1:1\",
    \"description\": \"Neat. Essential oils categorized as “neat” can be applied topically without dilution on most people. These oils are considered mild and generally do not induce skin sensitivity. Examples of “neat” essential oils are frankincense, lavender, melaleuca, melissa, and sandalwood.\",
    \"percentage_max\": 0.5,
    \"percentage_min\": 0
  },
  \"phototoxicity\": {
    \"id\": \"9a987a49-f246-4aa2-99d7-87d189a01d00\",
    \"status\": \"Phototoxic\",
    \"guidance\": \"Avoid direct sun exposure or UV light on the application area for at least 12 hours (or as specified for the particular oil) after topical use, even when diluted. Consider using during evenings or on skin areas covered by clothing if UV exposure is anticipated.\",
    \"description\": \"Contains compounds that can cause phototoxic reactions (e.g., skin sensitization, rash, burning, or hyperpigmentation) when skin is exposed to UV light (sunlight, tanning beds) within a certain timeframe after topical application.\"
  },
  \"pregnancy_nursing\": [
    {
      \"id\": \"1ae12b7d-04e1-4306-b218-7e4bd7b0865b\",
      \"code\": \"\",
      \"name\": \"\",
      \"description\": \"\",
      \"usage_guidance\": \"\",
      \"status_description\": \"pregnancy-safe-100\"
    }
  ],
  \"child_safety\": [],
  \"internal_use_id\": \"2343a180-7dd2-45b1-a402-065b1bf2bd7c\",
  \"dilution_id\": \"3404c0a7-6158-4236-bbd9-724728538c3d\",
  \"phototoxicity_id\": \"9a987a49-f246-4aa2-99d7-87d189a01d00\",
  \"pregnancy_nursing_ids\": [
    \"1ae12b7d-04e1-4306-b218-7e4bd7b0865b\"
  ],
  \"child_safety_ids\": []
}"}
 LOG  🔍 PREGNANCY DATA for Tangerina: {"pregnancyNursingIsArray": true, "pregnancyNursingKeys": ["0"], "pregnancyNursingLength": 1, "pregnancyNursingRaw": "[
  {
    \"id\": \"1ae12b7d-04e1-4306-b218-7e4bd7b0865b\",
    \"code\": \"\",
    \"name\": \"\",
    \"description\": \"\",
    \"usage_guidance\": \"\",
    \"status_description\": \"pregnancy-safe-100\"
  }
]", "pregnancyNursingType": "object"}
 LOG  🔍 CHILD SAFETY DATA for Tangerina: {"childSafetyIsArray": true, "childSafetyLength": 0, "childSafetyRaw": "[]", "childSafetyType": "object"}
 LOG  🔍 OIL 7 RAW DATA (Júniper berries): {"enrichment_status": "enriched", "hasSafety": true, "isEnriched": true, "name_botanical": "Juniperus communis", "name_english": "Júniper berries", "oil_id": "d354b218-cf17-42aa-8c40-d569ed5b3ab6", "safetyRawData": "{
  \"internal_use\": {
    \"id\": \"2343a180-7dd2-45b1-a402-065b1bf2bd7c\",
    \"code\": \"FOOD_GRADE_EO\",
    \"name\": \"Safe for Internal Use\",
    \"guidance\": null,
    \"description\": \"Essential oils in this category are generally recognized or have specific indications suggesting they may be safe for internal use when following appropriate dilutions, dosage, and professional guidance.\"
  },
  \"dilution\": {
    \"id\": \"3404c0a7-6158-4236-bbd9-724728538c3d\",
    \"name\": \"[N] Neat\",
    \"ratio\": \"1:1\",
    \"description\": \"Neat. Essential oils categorized as “neat” can be applied topically without dilution on most people. These oils are considered mild and generally do not induce skin sensitivity. Examples of “neat” essential oils are frankincense, lavender, melaleuca, melissa, and sandalwood.\",
    \"percentage_max\": 0.5,
    \"percentage_min\": 0
  },
  \"phototoxicity\": {
    \"id\": \"ae18d720-4473-479e-b9f3-7fa65192d639\",
    \"status\": \"Non-Phototoxic\",
    \"guidance\": \"No specific sun exposure restrictions related to phototoxicity are typically required after topical application. However, always follow general safety guidelines for dilution and application for all essential oils.\",
    \"description\": \"Generally not known to cause phototoxic reactions when skin is exposed to UV light after topical application, when used appropriately.\"
  },
  \"pregnancy_nursing\": [
    {
      \"id\": \"1ae12b7d-04e1-4306-b218-7e4bd7b0865b\",
      \"code\": \"\",
      \"name\": \"\",
      \"description\": \"\",
      \"usage_guidance\": \"\",
      \"status_description\": \"pregnancy-safe-100\"
    }
  ],
  \"child_safety\": [],
  \"internal_use_id\": \"2343a180-7dd2-45b1-a402-065b1bf2bd7c\",
  \"dilution_id\": \"3404c0a7-6158-4236-bbd9-724728538c3d\",
  \"phototoxicity_id\": \"ae18d720-4473-479e-b9f3-7fa65192d639\",
  \"pregnancy_nursing_ids\": [
    \"1ae12b7d-04e1-4306-b218-7e4bd7b0865b\"
  ],
  \"child_safety_ids\": []
}"}
 LOG  🔍 PREGNANCY DATA for Júniper berries: {"pregnancyNursingIsArray": true, "pregnancyNursingKeys": ["0"], "pregnancyNursingLength": 1, "pregnancyNursingRaw": "[
  {
    \"id\": \"1ae12b7d-04e1-4306-b218-7e4bd7b0865b\",
    \"code\": \"\",
    \"name\": \"\",
    \"description\": \"\",
    \"usage_guidance\": \"\",
    \"status_description\": \"pregnancy-safe-100\"
  }
]", "pregnancyNursingType": "object"}
 LOG  🔍 CHILD SAFETY DATA for Júniper berries: {"childSafetyIsArray": true, "childSafetyLength": 0, "childSafetyRaw": "[]", "childSafetyType": "object"}
 LOG  🔍 OIL 8 RAW DATA (Camomila): {"enrichment_status": "enriched", "hasSafety": true, "isEnriched": true, "name_botanical": "Matricaria chamomilla", "name_english": "Camomila", "oil_id": "8af64ea7-75eb-4f4f-a568-4e0b20758060", "safetyRawData": "{
  \"internal_use\": {
    \"id\": \"247f091e-d55c-4a76-aaef-ea4485457b63\",
    \"code\": \"NON_INGESTIBLE\",
    \"name\": \"Not for Internal Use\",
    \"guidance\": null,
    \"description\": \"Essential oils in this category are not recommended or are unsafe for internal consumption. They are intended for topical application (diluted) or aromatic diffusion only.\"
  },
  \"dilution\": {
    \"id\": \"3404c0a7-6158-4236-bbd9-724728538c3d\",
    \"name\": \"[N] Neat\",
    \"ratio\": \"1:1\",
    \"description\": \"Neat. Essential oils categorized as “neat” can be applied topically without dilution on most people. These oils are considered mild and generally do not induce skin sensitivity. Examples of “neat” essential oils are frankincense, lavender, melaleuca, melissa, and sandalwood.\",
    \"percentage_max\": 0.5,
    \"percentage_min\": 0
  },
  \"phototoxicity\": {
    \"id\": \"ae18d720-4473-479e-b9f3-7fa65192d639\",
    \"status\": \"Non-Phototoxic\",
    \"guidance\": \"No specific sun exposure restrictions related to phototoxicity are typically required after topical application. However, always follow general safety guidelines for dilution and application for all essential oils.\",
    \"description\": \"Generally not known to cause phototoxic reactions when skin is exposed to UV light after topical application, when used appropriately.\"
  },
  \"pregnancy_nursing\": [],
  \"child_safety\": [],
  \"internal_use_id\": \"247f091e-d55c-4a76-aaef-ea4485457b63\",
  \"dilution_id\": \"3404c0a7-6158-4236-bbd9-724728538c3d\",
  \"phototoxicity_id\": \"ae18d720-4473-479e-b9f3-7fa65192d639\",
  \"pregnancy_nursing_ids\": [],
  \"child_safety_ids\": []
}"}
 LOG  🔍 PREGNANCY DATA for Camomila: {"pregnancyNursingIsArray": true, "pregnancyNursingKeys": [], "pregnancyNursingLength": 0, "pregnancyNursingRaw": "[]", "pregnancyNursingType": "object"}
 LOG  🔍 CHILD SAFETY DATA for Camomila: {"childSafetyIsArray": true, "childSafetyLength": 0, "childSafetyRaw": "[]", "childSafetyType": "object"}
 LOG  ACTION: updatePropertyWithEnrichedOils
 LOG  PAYLOAD: {"enrichedOils": [{"botanical_name": "", "enrichment_status": "not_found", "enrichment_timestamp": "2025-09-06T15:33:08.293Z", "isEnriched": false, "match_rationale_localized": "Indicado para aliviar azia, gastrite, má digestão e estufamento, auxiliando na regulação do sistema digestivo.", "name_botanical": "Injustice", "name_english": "Injustice", "name_localized": "Injustice", "oil_id": "001a2b3c-4d5e-678f-901a-2b3c4d5e6f78", "relevancy_to_property_score": 5, "search_query": "Injustice - Injustice"}, {"botanical_mismatch": false, "botanical_name": "", "enrichment_status": "enriched", "enrichment_timestamp": "2025-09-06T15:33:08.294Z", "isEnriched": true, "match_rationale_localized": "Auxilia no combate à azia e desconfortos gástricos, promovendo uma sensação de alívio e relaxamento abdominal.", "name_botanical": "Citrus limon", "name_english": "Limão Siciliano", "name_localized": "Limão Siciliano", "name_scientific": "Citrus limon", "oil_id": "caef2077-85b5-479f-a828-7b7be804e498", "relevancy_to_property_score": 4, "safety": [Object], "search_query": "Limão Siciliano - Citrus limon", "similarity_score": 0.878763303609288}, {"botanical_mismatch": false, "botanical_name": "", "enrichment_status": "enriched", "enrichment_timestamp": "2025-09-06T15:33:08.294Z", "isEnriched": true, "match_rationale_localized": "Ajuda na digestão e alívio de cólicas intestinais, promovendo relaxamento do sistema gastrointestinal.", "name_botanical": "Foeniculum vulgare", "name_english": "Funcho", "name_localized": "Funcho", "name_scientific": "Foeniculum vulgare", "oil_id": "7fd15cfa-5a18-4e12-ba6b-6da0d62d70bc", "relevancy_to_property_score": 4, "safety": [Object], "search_query": "Funcho - Foeniculum vulgare", "similarity_score": 0.786786888454804}, {"botanical_mismatch": false, "botanical_name": "", "enrichment_status": "enriched", "enrichment_timestamp": "2025-09-06T15:33:08.294Z", "isEnriched": true, "match_rationale_localized": "Contribui para reduzir náuseas, azia e melhorar a digestão, além de possuir propriedades termogênicas.", "name_botanical": "Zingiber officinale", "name_english": "Gengibre", "name_localized": "Gengibre", "name_scientific": "Zingiber officinale", "oil_id": "56b7e686-5417-4bcf-a975-9888f559a4f3", "relevancy_to_property_score": 4, "safety": [Object], "search_query": "Gengibre - Zingiber officinale", "similarity_score": 0.887758632399914}, {"botanical_mismatch": false, "botanical_name": "", "enrichment_status": "enriched", "enrichment_timestamp": "2025-09-06T15:33:08.295Z", "isEnriched": true, "match_rationale_localized": "Alivia desconfortos gástricos, azia e refluxo, promovendo sensação de frescor e relaxamento abdominal.", "name_botanical": "Mentha piperita", "name_english": "Hortelã-Pimenta", "name_localized": "Hortelã-Pimenta", "name_scientific": "Mentha Piperita", "oil_id": "ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf", "relevancy_to_property_score": 4, "safety": [Object], "search_query": "Hortelã-Pimenta - Mentha piperita", "similarity_score": 0.855765419223793}, {"botanical_mismatch": false, "botanical_name": "", "enrichment_status": "enriched", "enrichment_timestamp": "2025-09-06T15:33:08.295Z", "isEnriched": true, "match_rationale_localized": "Contribui para o relaxamento, alivia o estresse e auxilia na digestão por seu aroma suave e calmante.", "name_botanical": "Citrus reticulata", "name_english": "Tangerina", "name_localized": "Tangerina", "name_scientific": "Citrus reticulata", "oil_id": "0ed154c2-cb91-4449-9b3b-37ffea79c9ac", "relevancy_to_property_score": 3, "safety": [Object], "search_query": "Tangerina - Citrus reticulata", "similarity_score": 0.905911164418848}, {"botanical_mismatch": false, "botanical_name": "", "enrichment_status": "enriched", "enrichment_timestamp": "2025-09-06T15:33:08.296Z", "isEnriched": true, "match_rationale_localized": "Auxilia na digestão e ajuda na redução de inchaço abdominal, além de promover relaxamento.", "name_botanical": "Juniperus communis", "name_english": "Júniper berries", "name_localized": "Bagas de Júniper", "name_scientific": "Juniperus communis", "oil_id": "d354b218-cf17-42aa-8c40-d569ed5b3ab6", "relevancy_to_property_score": 3, "safety": [Object], "search_query": "Júniper berries - Juniperus communis", "similarity_score": 0.753316982987468}, {"botanical_mismatch": true, "botanical_name": "", "enrichment_status": "enriched", "enrichment_timestamp": "2025-09-06T15:33:08.296Z", "isEnriched": true, "match_rationale_localized": "Propriedades calmantes que auxiliam no relaxamento do sistema digestivo e redução do estresse.", "name_botanical": "Matricaria chamomilla", "name_english": "Camomila", "name_localized": "Camomila", "name_scientific": "Matricaria recutita", "oil_id": "8af64ea7-75eb-4f4f-a568-4e0b20758060", "relevancy_to_property_score": 3, "safety": [Object], "search_query": "Camomila - Matricaria chamomilla", "similarity_score": 0.74121630858329}], "propertyId": "c3f4e5a6-b7c8-9012-cd34-e5f678901234"}
 LOG  PRE-UPDATE STATE: [{"addresses_cause_ids": ["d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b"], "addresses_symptom_ids": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "description_contextual_localized": "Auxilia na melhora do processo de digestão, reduzindo o desconforto após refeições gordurosas, promovendo maior equilíbrio no sistema digestivo.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "a1f2c3d4-e5f6-7890-ab12-c3d4e5f67890", "property_name_english": "Digestive", "property_name_localized": "Digestivo", "relevancy_score": 5, "suggested_oils": [[Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object]]}, {"addresses_cause_ids": ["d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b"], "addresses_symptom_ids": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "description_contextual_localized": "Reduz a inflamação no sistema digestivo, ajudando a aliviar o desconforto relacionado ao consumo de alimentos gordurosos.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "b2f3d4e5-f6a7-8901-bc23-d4e5f6789012", "property_name_english": "Anti-inflammatory", "property_name_localized": "Anti-inflamatório", "relevancy_score": 4, "suggested_oils": [[Object], [Object], [Object], [Object], [Object]]}, {"addresses_cause_ids": [], "addresses_symptom_ids": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "description_contextual_localized": "Promove o equilíbrio emocional, ajudando a reduzir o estresse que pode impactar a digestão, promovendo uma sensação de tranquilidade.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "c3f4e5a6-b7c8-9012-cd34-e5f678901234", "property_name_english": "Calming", "property_name_localized": "Calmante", "relevancy_score": 3, "suggested_oils": [[Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object]]}, {"addresses_cause_ids": [], "addresses_symptom_ids": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "description_contextual_localized": "Auxilia na revitalização do sistema digestivo, promovendo maior disposição após refeições, especialmente após ingerir gordura.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "d4f5e6b7-c8d9-0123-de45-f67890123456", "property_name_english": "Energizing", "property_name_localized": "Energizante", "relevancy_score": 2, "suggested_oils": [[Object], [Object], [Object], [Object], [Object]]}, {"addresses_cause_ids": [], "addresses_symptom_ids": [], "description_contextual_localized": "Ajuda a reduzir a ansiedade e o estresse que podem afetar a digestão, promovendo maior relaxamento emocional.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "e5f6a7b8-c9d0-1234-ea56-89ab01234567", "property_name_english": "Nervine Relaxant", "property_name_localized": "Nervino relaxante", "relevancy_score": 2, "suggested_oils": [[Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object]]}]
 LOG  ✅ [recipe-store] Updated property c3f4e5a6-b7c8-9012-cd34-e5f678901234 with 8 oils: {"discarded": 0, "enriched": 7, "not_found": 1}
 LOG  🎯 All properties enriched! Triggering hybrid scoring calculation...
 LOG  🔢 Post-enrichment scoring: {"enrichedProperties": 5, "totalEnrichedOils": 32, "totalOilsAvailable": 34, "totalProperties": 5}
 LOG  🧮 Starting hybrid scoring calculation... {"alpha": 0.6, "gamma": 0.1, "holisticWeight": 0.30000000000000004, "propertiesCount": 5}
 LOG  📊 Scoring thresholds: {"highPriorityThreshold": 5, "maxPropertyScore": 5, "totalUniqueOils": 18}
 LOG  🎯 Oil ad7c9072... hybrid score: {"coverage": "89.4%", "finalScore": "4.75", "holistic": "87.1%", "propertiesCount": 4, "specialization": "100.0%"}
 LOG  🎯 Oil a481ef0a... hybrid score: {"coverage": "44.7%", "finalScore": "3.82", "holistic": "80.0%", "propertiesCount": 1, "specialization": "80.0%"}
 LOG  🎯 Oil 56b7e686... hybrid score: {"coverage": "100.0%", "finalScore": "3.82", "holistic": "81.1%", "propertiesCount": 5, "specialization": "70.0%"}
 LOG  🎯 Oil caef2077... hybrid score: {"coverage": "77.5%", "finalScore": "3.93", "holistic": "76.0%", "propertiesCount": 3, "specialization": "80.0%"}
 LOG  🎯 Oil 7fd15cfa... hybrid score: {"coverage": "89.4%", "finalScore": "4.05", "holistic": "80.0%", "propertiesCount": 4, "specialization": "80.0%"}
 LOG  🎯 Oil 0640a673... hybrid score: {"coverage": "63.2%", "finalScore": "3.02", "holistic": "60.0%", "propertiesCount": 2, "specialization": "60.0%"}
 LOG  🎯 Oil 2a09e83e... hybrid score: {"coverage": "44.7%", "finalScore": "2.92", "holistic": "60.0%", "propertiesCount": 1, "specialization": "60.0%"}
 LOG  🎯 Oil 0ed154c2... hybrid score: {"coverage": "63.2%", "finalScore": "1.22", "holistic": "60.0%", "propertiesCount": 2, "specialization": "0.0%"}
 LOG  🎯 Oil d354b218... hybrid score: {"coverage": "44.7%", "finalScore": "1.12", "holistic": "60.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil 8af64ea7... hybrid score: {"coverage": "44.7%", "finalScore": "1.12", "holistic": "60.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil bb99ae0f... hybrid score: {"coverage": "44.7%", "finalScore": "1.72", "holistic": "100.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil 2aaae25e... hybrid score: {"coverage": "44.7%", "finalScore": "1.42", "holistic": "80.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil d685f4e0... hybrid score: {"coverage": "44.7%", "finalScore": "1.12", "holistic": "60.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil 8ffe0ee4... hybrid score: {"coverage": "44.7%", "finalScore": "1.12", "holistic": "60.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil 5f470712... hybrid score: {"coverage": "44.7%", "finalScore": "1.42", "holistic": "80.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil a3c73ec0... hybrid score: {"coverage": "44.7%", "finalScore": "1.42", "holistic": "80.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil ecc9d982... hybrid score: {"coverage": "44.7%", "finalScore": "1.42", "holistic": "80.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil 9dd00035... hybrid score: {"coverage": "44.7%", "finalScore": "1.42", "holistic": "80.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  ✅ Hybrid scoring completed {"avgScore": 2.270021942304509, "oilsScored": 18}
 LOG  ✅ Hybrid scoring completed: {"safetyLibraryStats": {"child_safety": 0, "dilution": 3, "internal_use": 2, "phototoxicity": 2, "pregnancy_nursing": 4}, "scoredOilsCount": 18, "topOilId": "ad7c9072...", "topOilScore": 4.75}
 LOG  ✅ Hybrid scores applied to all properties! {"oilsWithScores": 18, "propertiesUpdated": 5, "sampleScores": [{"hybridScore": "4.75", "oilId": "ad7c9072...", "specializationScore": "100.0%"}, {"hybridScore": "4.05", "oilId": "7fd15cfa...", "specializationScore": "80.0%"}, {"hybridScore": "3.93", "oilId": "caef2077...", "specializationScore": "80.0%"}]}
 LOG  POST-UPDATE STATE: [{"addresses_cause_ids": ["d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b"], "addresses_symptom_ids": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "description_contextual_localized": "Auxilia na melhora do processo de digestão, reduzindo o desconforto após refeições gordurosas, promovendo maior equilíbrio no sistema digestivo.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "a1f2c3d4-e5f6-7890-ab12-c3d4e5f67890", "property_name_english": "Digestive", "property_name_localized": "Digestivo", "relevancy_score": 5, "suggested_oils": [[Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object]]}, {"addresses_cause_ids": ["d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b"], "addresses_symptom_ids": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "description_contextual_localized": "Reduz a inflamação no sistema digestivo, ajudando a aliviar o desconforto relacionado ao consumo de alimentos gordurosos.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "b2f3d4e5-f6a7-8901-bc23-d4e5f6789012", "property_name_english": "Anti-inflammatory", "property_name_localized": "Anti-inflamatório", "relevancy_score": 4, "suggested_oils": [[Object], [Object], [Object], [Object], [Object]]}, {"addresses_cause_ids": [], "addresses_symptom_ids": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "description_contextual_localized": "Promove o equilíbrio emocional, ajudando a reduzir o estresse que pode impactar a digestão, promovendo uma sensação de tranquilidade.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "c3f4e5a6-b7c8-9012-cd34-e5f678901234", "property_name_english": "Calming", "property_name_localized": "Calmante", "relevancy_score": 3, "suggested_oils": [[Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object]]}, {"addresses_cause_ids": [], "addresses_symptom_ids": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "description_contextual_localized": "Auxilia na revitalização do sistema digestivo, promovendo maior disposição após refeições, especialmente após ingerir gordura.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "d4f5e6b7-c8d9-0123-de45-f67890123456", "property_name_english": "Energizing", "property_name_localized": "Energizante", "relevancy_score": 2, "suggested_oils": [[Object], [Object], [Object], [Object], [Object]]}, {"addresses_cause_ids": [], "addresses_symptom_ids": [], "description_contextual_localized": "Ajuda a reduzir a ansiedade e o estresse que podem afetar a digestão, promovendo maior relaxamento emocional.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "e5f6a7b8-c9d0-1234-ea56-89ab01234567", "property_name_english": "Nervine Relaxant", "property_name_localized": "Nervino relaxante", "relevancy_score": 2, "suggested_oils": [[Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object]]}]
 LOG  🔍 [CausesSelection] Store subscription debug: {"potentialCausesFromStore": 7, "renderTimestamp": "2025-09-06T15:33:08.198Z", "selectedCausesFromStore": 1}
 LOG  🔍 [CausesSelection] Counter Debug: {"SELECTION_REQUIREMENTS_CAUSES": {"max": 10, "min": 1}, "maxSelection": 10, "minSelection": 1, "selectedCauses": 1, "selectedCausesArray": [{"id": "d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b", "name": "Dieta rica em alimentos gordurosos"}], "selectionCount": 1}
 LOG  🩺 SymptomsSelection Debug: {"canSubmit": true, "selectedCount": 1, "selectedIds": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "totalSymptoms": 8}
 LOG  🎭 SymptomsSelection - Modal State: {"isStreaming": false, "showStreamingModal": false, "streamingItemsCount": 5, "timestamp": "2025-09-06T15:33:08.217Z"}
 LOG  🔍 SymptomsSelection: First symptom data structure: {"explanation": "Sensação de plenitude ou inchaço após refeições ricas em gordura devido ao processamento dificultoso pelo sistema digestivo.", "symptom_id": "d3b07384-2a6d-4c65-8d6d-859f2b1a16c1", "symptom_name": "Sensação de plenitude abdominal após refeições", "symptom_suggestion": "Observe se ocorre após o consumo de alimentos gordurosos"}
 LOG  🔍 SymptomsSelection: Available fields: ["symptom_id", "symptom_name", "symptom_suggestion", "explanation"]
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🔍 getCauseNamesByIds called with UUIDs: ["d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b"]
 LOG  🔍 Available selectedCauses: [{"id":"d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b","name":"Dieta rica em alimentos gordurosos"}]
 LOG  🔍 Looking for cause UUID "d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b": FOUND "Dieta rica em alimentos gordurosos"
 LOG  🔍 getCauseNamesByIds final result: ["Dieta rica em alimentos gordurosos"]
 LOG  🔍 getSymptomNamesByIds called with UUIDs: ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"]
 LOG  🔍 Available selectedSymptoms: [{"id":"d3b07384-2a6d-4c65-8d6d-859f2b1a16c1","name":"Sensação de plenitude abdominal após refeições"}]
 LOG  🔍 Looking for symptom UUID "d3b07384-2a6d-4c65-8d6d-859f2b1a16c1": FOUND "Sensação de plenitude abdominal após refeições"
 LOG  🔍 getSymptomNamesByIds final result: ["Sensação de plenitude abdominal após refeições"]
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false


  LOG  🩺 SymptomsSelection Debug: {"canSubmit": true, "selectedCount": 1, "selectedIds": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "totalSymptoms": 8}
 LOG  🎭 SymptomsSelection - Modal State: {"isStreaming": false, "showStreamingModal": false, "streamingItemsCount": 5, "timestamp": "2025-09-06T15:33:08.217Z"}
 LOG  🔍 SymptomsSelection: First symptom data structure: {"explanation": "Sensação de plenitude ou inchaço após refeições ricas em gordura devido ao processamento dificultoso pelo sistema digestivo.", "symptom_id": "d3b07384-2a6d-4c65-8d6d-859f2b1a16c1", "symptom_name": "Sensação de plenitude abdominal após refeições", "symptom_suggestion": "Observe se ocorre após o consumo de alimentos gordurosos"}
 LOG  🔍 SymptomsSelection: Available fields: ["symptom_id", "symptom_name", "symptom_suggestion", "explanation"]
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🔍 getCauseNamesByIds called with UUIDs: ["d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b"]
 LOG  🔍 Available selectedCauses: [{"id":"d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b","name":"Dieta rica em alimentos gordurosos"}]
 LOG  🔍 Looking for cause UUID "d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b": FOUND "Dieta rica em alimentos gordurosos"
 LOG  🔍 getCauseNamesByIds final result: ["Dieta rica em alimentos gordurosos"]
 LOG  🔍 getSymptomNamesByIds called with UUIDs: ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"]
 LOG  🔍 Available selectedSymptoms: [{"id":"d3b07384-2a6d-4c65-8d6d-859f2b1a16c1","name":"Sensação de plenitude abdominal após refeições"}]
 LOG  🔍 Looking for symptom UUID "d3b07384-2a6d-4c65-8d6d-859f2b1a16c1": FOUND "Sensação de plenitude abdominal após refeições"
 LOG  🔍 getSymptomNamesByIds final result: ["Sensação de plenitude abdominal após refeições"]
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🚀 [Properties-FinalRecipes] Generate Final Recipes button clicked
 LOG  ✅ [Properties-FinalRecipes] Starting final recipe generation with 5 enriched properties
 LOG  🚀 [RN-FinalRecipes] Starting parallel final recipe streaming for 3 time slots
 LOG  🚀 [RN-FinalRecipes] Starting parallel final recipe streaming for 3 time slots
 LOG  🌐 [RN-FinalRecipes] Using user language: {"apiLanguage": undefined}
 LOG  ✅ [RN-FinalRecipes] Using 5 enriched properties
 LOG  🔢 [RN-FinalRecipes] Calculating post-enrichment scores...
 LOG  🔢 Post-enrichment scoring: {"enrichedProperties": 5, "totalEnrichedOils": 32, "totalOilsAvailable": 34, "totalProperties": 5}
 LOG  🧮 Starting hybrid scoring calculation... {"alpha": 0.6, "gamma": 0.1, "holisticWeight": 0.30000000000000004, "propertiesCount": 5}
 LOG  📊 Scoring thresholds: {"highPriorityThreshold": 5, "maxPropertyScore": 5, "totalUniqueOils": 18}
 LOG  🎯 Oil ad7c9072... hybrid score: {"coverage": "89.4%", "finalScore": "4.75", "holistic": "87.1%", "propertiesCount": 4, "specialization": "100.0%"}
 LOG  🎯 Oil a481ef0a... hybrid score: {"coverage": "44.7%", "finalScore": "3.82", "holistic": "80.0%", "propertiesCount": 1, "specialization": "80.0%"}
 LOG  🎯 Oil 56b7e686... hybrid score: {"coverage": "100.0%", "finalScore": "3.82", "holistic": "81.1%", "propertiesCount": 5, "specialization": "70.0%"}
 LOG  🎯 Oil caef2077... hybrid score: {"coverage": "77.5%", "finalScore": "3.93", "holistic": "76.0%", "propertiesCount": 3, "specialization": "80.0%"}
 LOG  🎯 Oil 7fd15cfa... hybrid score: {"coverage": "89.4%", "finalScore": "4.05", "holistic": "80.0%", "propertiesCount": 4, "specialization": "80.0%"}
 LOG  🎯 Oil 0640a673... hybrid score: {"coverage": "63.2%", "finalScore": "3.02", "holistic": "60.0%", "propertiesCount": 2, "specialization": "60.0%"}
 LOG  🎯 Oil 2a09e83e... hybrid score: {"coverage": "44.7%", "finalScore": "2.92", "holistic": "60.0%", "propertiesCount": 1, "specialization": "60.0%"}
 LOG  🎯 Oil 0ed154c2... hybrid score: {"coverage": "63.2%", "finalScore": "1.22", "holistic": "60.0%", "propertiesCount": 2, "specialization": "0.0%"}
 LOG  🎯 Oil d354b218... hybrid score: {"coverage": "44.7%", "finalScore": "1.12", "holistic": "60.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil 8af64ea7... hybrid score: {"coverage": "44.7%", "finalScore": "1.12", "holistic": "60.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil bb99ae0f... hybrid score: {"coverage": "44.7%", "finalScore": "1.72", "holistic": "100.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil 2aaae25e... hybrid score: {"coverage": "44.7%", "finalScore": "1.42", "holistic": "80.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil d685f4e0... hybrid score: {"coverage": "44.7%", "finalScore": "1.12", "holistic": "60.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil 8ffe0ee4... hybrid score: {"coverage": "44.7%", "finalScore": "1.12", "holistic": "60.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil 5f470712... hybrid score: {"coverage": "44.7%", "finalScore": "1.42", "holistic": "80.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil a3c73ec0... hybrid score: {"coverage": "44.7%", "finalScore": "1.42", "holistic": "80.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil ecc9d982... hybrid score: {"coverage": "44.7%", "finalScore": "1.42", "holistic": "80.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil 9dd00035... hybrid score: {"coverage": "44.7%", "finalScore": "1.42", "holistic": "80.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  ✅ Hybrid scoring completed {"avgScore": 2.270021942304509, "oilsScored": 18}
 LOG  ✅ Hybrid scoring completed: {"safetyLibraryStats": {"child_safety": 0, "dilution": 3, "internal_use": 2, "phototoxicity": 2, "pregnancy_nursing": 4}, "scoredOilsCount": 18, "topOilId": "ad7c9072...", "topOilScore": 4.75}
 LOG  ✅ [RN-FinalRecipes] Enhanced data prepared: {"scoredOilsCount": 18, "topOilScore": 4.75, "topOilsCount": 18}
 LOG  📤 [RN-FinalRecipes] Created 3 parallel streaming requests for time slots
 LOG  📤 [RN-FinalRecipes] Created 3 parallel streaming requests for time slots
 LOG  🚀 [ParallelEngine] Starting 3 parallel streaming requests
 LOG  📡 [ParallelEngine] Starting stream for ID: morning (morning recipe)
 LOG  📤 [ParallelEngine] Request data: {"dataKeys": ["health_concern", "gender", "age_category", "age_specific", "user_language", "selected_causes", "selected_symptoms", "time_of_day", "suggested_oils", "safety_library", "safety_library_formatted"], "feature": "create-recipe", "step": "final-recipes"}
 LOG  ⏳ [ParallelEngine] Delaying stream 2/3 by 3000ms to avoid rate limiting
 LOG  ⏳ [ParallelEngine] Delaying stream 3/3 by 6000ms to avoid rate limiting
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: true
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 5}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  📊 LAYER 8B: Property Analysis Progress: 0/5 properties completed
 LOG  📊 LAYER 8B: Tool Calls Made: 0 total | Results: 0 | Errors: 0
 LOG  📊 LAYER 8C: Map/Set sizes changed
 LOG  📊 LAYER 8C: Results size: 0
 LOG  📊 LAYER 8C: Errors size: 0
 LOG  🎯 AUTO-COMPLETION: Checking streaming completion:
 LOG  🎯 AUTO-COMPLETION: totalSelected: 5
 LOG  🎯 AUTO-COMPLETION: totalCompleted: 0
 LOG  🎯 AUTO-COMPLETION: isStreaming: true
 LOG  🎯 AUTO-COMPLETION: isSubmitting: false
 LOG  🚀 [API-ios] Starting streaming request {"bodySize": 58514, "feature": "create-recipe", "headers": ["Content-Type", "Authorization"], "platform": "ios", "requestId": "api-1757172818599-vg0t466z5", "requestPreview": "{\"feature\":\"create-recipe\",\"step\":\"final-recipes\",\"data\":{\"health_concern\":\"Desconforto digestivo após refeições, especialmente comidas gordurosas\",\"gender\":\"female\",\"age_category\":\"adult\",\"age_specif...", "step": "final-recipes", "timestamp": "2025-09-06T15:33:38.599Z", "url": "https://novo.rotinanatural.com.br/api/ai/streaming"}
 LOG  📱 [API-ios] Using unified mobile SSE client for platform: ios {"requestId": "api-1757172818599-vg0t466z5"}
 LOG  📱 [API-ios] Creating mobile streaming response {"requestId": "api-1757172818599-vg0t466z5"}
 LOG  📱 [API-ios] Starting mobile ReadableStream {"requestId": "api-1757172818599-vg0t466z5"}
 LOG  � [MobiloeSSE] Sending request: {"bodyLength": 58514, "headers": {"Authorization": "Bearer zh6Xs8GAHi8TlX8LWchb3WQ7ouneL3gn8aL84CLL1x7J0rKOKr", "Content-Type": "application/json"}, "url": "https://novo.rotinanatural.com.br/api/ai/streaming"}
 LOG  📱 [API-ios] Mobile streaming response created {"bodyType": "ReadableStream", "hasBody": true, "requestId": "api-1757172818599-vg0t466z5"}
 LOG  🔍 [SSE-ios] Starting SSE stream reader {"platform": "ios", "sessionId": "sse-1757172818602-ep16grrc6", "streamType": "ReadableStream", "timestamp": "2025-09-06T15:33:38.602Z"}
 LOG  📥 [SSE-ios] Reading chunk 1 {"sessionId": "sse-1757172818602-ep16grrc6"}
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: true
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 5}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  📡 [ParallelEngine] Starting stream for ID: mid-day (mid-day recipe)
 LOG  📤 [ParallelEngine] Request data: {"dataKeys": ["health_concern", "gender", "age_category", "age_specific", "user_language", "selected_causes", "selected_symptoms", "time_of_day", "suggested_oils", "safety_library", "safety_library_formatted"], "feature": "create-recipe", "step": "final-recipes"}
 LOG  🚀 [API-ios] Starting streaming request {"bodySize": 58514, "feature": "create-recipe", "headers": ["Content-Type", "Authorization"], "platform": "ios", "requestId": "api-1757172821473-7zsr773h8", "requestPreview": "{\"feature\":\"create-recipe\",\"step\":\"final-recipes\",\"data\":{\"health_concern\":\"Desconforto digestivo após refeições, especialmente comidas gordurosas\",\"gender\":\"female\",\"age_category\":\"adult\",\"age_specif...", "step": "final-recipes", "timestamp": "2025-09-06T15:33:41.475Z", "url": "https://novo.rotinanatural.com.br/api/ai/streaming"}
 LOG  📱 [API-ios] Using unified mobile SSE client for platform: ios {"requestId": "api-1757172821473-7zsr773h8"}
 LOG  📱 [API-ios] Creating mobile streaming response {"requestId": "api-1757172821473-7zsr773h8"}
 LOG  📱 [API-ios] Starting mobile ReadableStream {"requestId": "api-1757172821473-7zsr773h8"}
 LOG  � [MobiloeSSE] Sending request: {"bodyLength": 58514, "headers": {"Authorization": "Bearer zh6Xs8GAHi8TlX8LWchb3WQ7ouneL3gn8aL84CLL1x7J0rKOKr", "Content-Type": "application/json"}, "url": "https://novo.rotinanatural.com.br/api/ai/streaming"}
 LOG  📱 [API-ios] Mobile streaming response created {"bodyType": "ReadableStream", "hasBody": true, "requestId": "api-1757172821473-7zsr773h8"}
 LOG  🔍 [SSE-ios] Starting SSE stream reader {"platform": "ios", "sessionId": "sse-1757172821487-sr5zeou2y", "streamType": "ReadableStream", "timestamp": "2025-09-06T15:33:41.487Z"}
 LOG  📥 [SSE-ios] Reading chunk 1 {"sessionId": "sse-1757172821487-sr5zeou2y"}
 LOG  📡 [ParallelEngine] Starting stream for ID: night (night recipe)
 LOG  📤 [ParallelEngine] Request data: {"dataKeys": ["health_concern", "gender", "age_category", "age_specific", "user_language", "selected_causes", "selected_symptoms", "time_of_day", "suggested_oils", "safety_library", "safety_library_formatted"], "feature": "create-recipe", "step": "final-recipes"}
 LOG  🚀 [API-ios] Starting streaming request {"bodySize": 58512, "feature": "create-recipe", "headers": ["Content-Type", "Authorization"], "platform": "ios", "requestId": "api-1757172824474-em5vtq9rl", "requestPreview": "{\"feature\":\"create-recipe\",\"step\":\"final-recipes\",\"data\":{\"health_concern\":\"Desconforto digestivo após refeições, especialmente comidas gordurosas\",\"gender\":\"female\",\"age_category\":\"adult\",\"age_specif...", "step": "final-recipes", "timestamp": "2025-09-06T15:33:44.476Z", "url": "https://novo.rotinanatural.com.br/api/ai/streaming"}
 LOG  📱 [API-ios] Using unified mobile SSE client for platform: ios {"requestId": "api-1757172824474-em5vtq9rl"}
 LOG  📱 [API-ios] Creating mobile streaming response {"requestId": "api-1757172824474-em5vtq9rl"}
 LOG  📱 [API-ios] Starting mobile ReadableStream {"requestId": "api-1757172824474-em5vtq9rl"}
 LOG  � [MobiloeSSE] Sending request: {"bodyLength": 58512, "headers": {"Authorization": "Bearer zh6Xs8GAHi8TlX8LWchb3WQ7ouneL3gn8aL84CLL1x7J0rKOKr", "Content-Type": "application/json"}, "url": "https://novo.rotinanatural.com.br/api/ai/streaming"}
 LOG  📱 [API-ios] Mobile streaming response created {"bodyType": "ReadableStream", "hasBody": true, "requestId": "api-1757172824474-em5vtq9rl"}
 LOG  🔍 [SSE-ios] Starting SSE stream reader {"platform": "ios", "sessionId": "sse-1757172824489-n63lysomc", "streamType": "ReadableStream", "timestamp": "2025-09-06T15:33:44.489Z"}
 LOG  📥 [SSE-ios] Reading chunk 1 {"sessionId": "sse-1757172824489-n63lysomc"}



   \"internal_use\": {
    \"id\": \"2343a180-7dd2-45b1-a402-065b1bf2bd7c\",
    \"code\": \"FOOD_GRADE_EO\",
    \"name\": \"Safe for Internal Use\",
    \"guidance\": null,
    \"description\": \"Essential oils in this category are generally recognized or have specific indications suggesting they may be safe for internal use when following appropriate dilutions, dosage, and professional guidance.\"
  },
  \"dilution\": {
    \"id\": \"45c9d911-9b2b-472f-99aa-fdbaf3caa98d\",
    \"name\": \"[S] Sensitive\",
    \"ratio\": \"5:1\",
    \"description\": \"Sensitive. “Sensitive” oils are those that should be diluted before use on young or sensitive skin. Examples of “sensitive” oils are peppermint, ginger, eucalyptus, wintergreen, and black pepper.\",
    \"percentage_max\": 0.2,
    \"percentage_min\": 0.15
  },
  \"phototoxicity\": {
    \"id\": \"9a987a49-f246-4aa2-99d7-87d189a01d00\",
    \"status\": \"Phototoxic\",
    \"guidance\": \"Avoid direct sun exposure or UV light on the application area for at least 12 hours (or as specified for the particular oil) after topical use, even when diluted. Consider using during evenings or on skin areas covered by clothing if UV exposure is anticipated.\",
    \"description\": \"Contains compounds that can cause phototoxic reactions (e.g., skin sensitization, rash, burning, or hyperpigmentation) when skin is exposed to UV light (sunlight, tanning beds) within a certain timeframe after topical application.\"
  },
  \"pregnancy_nursing\": [
    {
      \"id\": \"1ae12b7d-04e1-4306-b218-7e4bd7b0865b\",
      \"code\": \"\",
      \"name\": \"\",
      \"description\": \"\",
      \"usage_guidance\": \"\",
      \"status_description\": \"pregnancy-safe-100\"
    },
    {
      \"id\": \"3735d6e6-89be-4887-901e-968170842c18\",
      \"code\": null,
      \"name\": \"\",
      \"description\": \"\",
      \"usage_guidance\": null,
      \"status_description\": \"pregnancy-safe-3months\"
    }
  ],
  \"child_safety\": [],
  \"internal_use_id\": \"2343a180-7dd2-45b1-a402-065b1bf2bd7c\",
  \"dilution_id\": \"45c9d911-9b2b-472f-99aa-fdbaf3caa98d\",
  \"phototoxicity_id\": \"9a987a49-f246-4aa2-99d7-87d189a01d00\",
  \"pregnancy_nursing_ids\": [
    \"1ae12b7d-04e1-4306-b218-7e4bd7b0865b\",
    \"3735d6e6-89be-4887-901e-968170842c18\"
  ],
  \"child_safety_ids\": []
}"}
 LOG  🔍 PREGNANCY DATA for Limão Siciliano: {"pregnancyNursingIsArray": true, "pregnancyNursingKeys": ["0", "1"], "pregnancyNursingLength": 2, "pregnancyNursingRaw": "[
  {
    \"id\": \"1ae12b7d-04e1-4306-b218-7e4bd7b0865b\",
    \"code\": \"\",
    \"name\": \"\",
    \"description\": \"\",
    \"usage_guidance\": \"\",
    \"status_description\": \"pregnancy-safe-100\"
  },
  {
    \"id\": \"3735d6e6-89be-4887-901e-968170842c18\",
    \"code\": null,
    \"name\": \"\",
    \"description\": \"\",
    \"usage_guidance\": null,
    \"status_description\": \"pregnancy-safe-3months\"
  }
]", "pregnancyNursingType": "object"}
 LOG  🔍 CHILD SAFETY DATA for Limão Siciliano: {"childSafetyIsArray": true, "childSafetyLength": 0, "childSafetyRaw": "[]", "childSafetyType": "object"}
 LOG  🔍 OIL 3 RAW DATA (Funcho): {"enrichment_status": "enriched", "hasSafety": true, "isEnriched": true, "name_botanical": "Foeniculum vulgare", "name_english": "Funcho", "oil_id": "7fd15cfa-5a18-4e12-ba6b-6da0d62d70bc", "safetyRawData": "{
  \"internal_use\": {
    \"id\": \"2343a180-7dd2-45b1-a402-065b1bf2bd7c\",
    \"code\": \"FOOD_GRADE_EO\",
    \"name\": \"Safe for Internal Use\",
    \"guidance\": null,
    \"description\": \"Essential oils in this category are generally recognized or have specific indications suggesting they may be safe for internal use when following appropriate dilutions, dosage, and professional guidance.\"
  },
  \"dilution\": {
    \"id\": \"45c9d911-9b2b-472f-99aa-fdbaf3caa98d\",
    \"name\": \"[S] Sensitive\",
    \"ratio\": \"5:1\",
    \"description\": \"Sensitive. “Sensitive” oils are those that should be diluted before use on young or sensitive skin. Examples of “sensitive” oils are peppermint, ginger, eucalyptus, wintergreen, and black pepper.\",
    \"percentage_max\": 0.2,
    \"percentage_min\": 0.15
  },
  \"phototoxicity\": {
    \"id\": \"ae18d720-4473-479e-b9f3-7fa65192d639\",
    \"status\": \"Non-Phototoxic\",
    \"guidance\": \"No specific sun exposure restrictions related to phototoxicity are typically required after topical application. However, always follow general safety guidelines for dilution and application for all essential oils.\",
    \"description\": \"Generally not known to cause phototoxic reactions when skin is exposed to UV light after topical application, when used appropriately.\"
  },
  \"pregnancy_nursing\": [],
  \"child_safety\": [],
  \"internal_use_id\": \"2343a180-7dd2-45b1-a402-065b1bf2bd7c\",
  \"dilution_id\": \"45c9d911-9b2b-472f-99aa-fdbaf3caa98d\",
  \"phototoxicity_id\": \"ae18d720-4473-479e-b9f3-7fa65192d639\",
  \"pregnancy_nursing_ids\": [],
  \"child_safety_ids\": []
}"}
 LOG  🔍 PREGNANCY DATA for Funcho: {"pregnancyNursingIsArray": true, "pregnancyNursingKeys": [], "pregnancyNursingLength": 0, "pregnancyNursingRaw": "[]", "pregnancyNursingType": "object"}
 LOG  🔍 CHILD SAFETY DATA for Funcho: {"childSafetyIsArray": true, "childSafetyLength": 0, "childSafetyRaw": "[]", "childSafetyType": "object"}
 LOG  🔍 OIL 4 RAW DATA (Gengibre): {"enrichment_status": "enriched", "hasSafety": true, "isEnriched": true, "name_botanical": "Zingiber officinale", "name_english": "Gengibre", "oil_id": "56b7e686-5417-4bcf-a975-9888f559a4f3", "safetyRawData": "{
  \"internal_use\": {
    \"id\": \"2343a180-7dd2-45b1-a402-065b1bf2bd7c\",
    \"code\": \"FOOD_GRADE_EO\",
    \"name\": \"Safe for Internal Use\",
    \"guidance\": null,
    \"description\": \"Essential oils in this category are generally recognized or have specific indications suggesting they may be safe for internal use when following appropriate dilutions, dosage, and professional guidance.\"
  },
  \"dilution\": {
    \"id\": \"45c9d911-9b2b-472f-99aa-fdbaf3caa98d\",
    \"name\": \"[S] Sensitive\",
    \"ratio\": \"5:1\",
    \"description\": \"Sensitive. “Sensitive” oils are those that should be diluted before use on young or sensitive skin. Examples of “sensitive” oils are peppermint, ginger, eucalyptus, wintergreen, and black pepper.\",
    \"percentage_max\": 0.2,
    \"percentage_min\": 0.15
  },
  \"phototoxicity\": {
    \"id\": \"ae18d720-4473-479e-b9f3-7fa65192d639\",
    \"status\": \"Non-Phototoxic\",
    \"guidance\": \"No specific sun exposure restrictions related to phototoxicity are typically required after topical application. However, always follow general safety guidelines for dilution and application for all essential oils.\",
    \"description\": \"Generally not known to cause phototoxic reactions when skin is exposed to UV light after topical application, when used appropriately.\"
  },
  \"pregnancy_nursing\": [
    {
      \"id\": \"1ae12b7d-04e1-4306-b218-7e4bd7b0865b\",
      \"code\": \"\",
      \"name\": \"\",
      \"description\": \"\",
      \"usage_guidance\": \"\",
      \"status_description\": \"pregnancy-safe-100\"
    },
    {
      \"id\": \"3735d6e6-89be-4887-901e-968170842c18\",
      \"code\": null,
      \"name\": \"\",
      \"description\": \"\",
      \"usage_guidance\": null,
      \"status_description\": \"pregnancy-safe-3months\"
    }
  ],
  \"child_safety\": [],
  \"internal_use_id\": \"2343a180-7dd2-45b1-a402-065b1bf2bd7c\",
  \"dilution_id\": \"45c9d911-9b2b-472f-99aa-fdbaf3caa98d\",
  \"phototoxicity_id\": \"ae18d720-4473-479e-b9f3-7fa65192d639\",
  \"pregnancy_nursing_ids\": [
    \"1ae12b7d-04e1-4306-b218-7e4bd7b0865b\",
    \"3735d6e6-89be-4887-901e-968170842c18\"
  ],
  \"child_safety_ids\": []
}"}
 LOG  🔍 PREGNANCY DATA for Gengibre: {"pregnancyNursingIsArray": true, "pregnancyNursingKeys": ["0", "1"], "pregnancyNursingLength": 2, "pregnancyNursingRaw": "[
  {
    \"id\": \"1ae12b7d-04e1-4306-b218-7e4bd7b0865b\",
    \"code\": \"\",
    \"name\": \"\",
    \"description\": \"\",
    \"usage_guidance\": \"\",
    \"status_description\": \"pregnancy-safe-100\"
  },
  {
    \"id\": \"3735d6e6-89be-4887-901e-968170842c18\",
    \"code\": null,
    \"name\": \"\",
    \"description\": \"\",
    \"usage_guidance\": null,
    \"status_description\": \"pregnancy-safe-3months\"
  }
]", "pregnancyNursingType": "object"}
 LOG  🔍 CHILD SAFETY DATA for Gengibre: {"childSafetyIsArray": true, "childSafetyLength": 0, "childSafetyRaw": "[]", "childSafetyType": "object"}
 LOG  🔍 OIL 5 RAW DATA (Hortelã-Pimenta): {"enrichment_status": "enriched", "hasSafety": true, "isEnriched": true, "name_botanical": "Mentha piperita", "name_english": "Hortelã-Pimenta", "oil_id": "ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf", "safetyRawData": "{
  \"internal_use\": {
    \"id\": \"2343a180-7dd2-45b1-a402-065b1bf2bd7c\",
    \"code\": \"FOOD_GRADE_EO\",
    \"name\": \"Safe for Internal Use\",
    \"guidance\": null,
    \"description\": \"Essential oils in this category are generally recognized or have specific indications suggesting they may be safe for internal use when following appropriate dilutions, dosage, and professional guidance.\"
  },
  \"dilution\": {
    \"id\": \"45c9d911-9b2b-472f-99aa-fdbaf3caa98d\",
    \"name\": \"[S] Sensitive\",
    \"ratio\": \"5:1\",
    \"description\": \"Sensitive. “Sensitive” oils are those that should be diluted before use on young or sensitive skin. Examples of “sensitive” oils are peppermint, ginger, eucalyptus, wintergreen, and black pepper.\",
    \"percentage_max\": 0.2,
    \"percentage_min\": 0.15
  },
  \"phototoxicity\": {
    \"id\": \"ae18d720-4473-479e-b9f3-7fa65192d639\",
    \"status\": \"Non-Phototoxic\",
    \"guidance\": \"No specific sun exposure restrictions related to phototoxicity are typically required after topical application. However, always follow general safety guidelines for dilution and application for all essential oils.\",
    \"description\": \"Generally not known to cause phototoxic reactions when skin is exposed to UV light after topical application, when used appropriately.\"
  },
  \"pregnancy_nursing\": [
    {
      \"id\": \"1b330391-6fb2-4972-bda3-6872e9835f9a\",
      \"code\": \"\",
      \"name\": \"\",
      \"description\": \"\",
      \"usage_guidance\": \"\",
      \"status_description\": \"pregnancy-safe-50\"
    }
  ],
  \"child_safety\": [],
  \"internal_use_id\": \"2343a180-7dd2-45b1-a402-065b1bf2bd7c\",
  \"dilution_id\": \"45c9d911-9b2b-472f-99aa-fdbaf3caa98d\",
  \"phototoxicity_id\": \"ae18d720-4473-479e-b9f3-7fa65192d639\",
  \"pregnancy_nursing_ids\": [
    \"1b330391-6fb2-4972-bda3-6872e9835f9a\"
  ],
  \"child_safety_ids\": []
}"}
 LOG  🔍 PREGNANCY DATA for Hortelã-Pimenta: {"pregnancyNursingIsArray": true, "pregnancyNursingKeys": ["0"], "pregnancyNursingLength": 1, "pregnancyNursingRaw": "[
  {
    \"id\": \"1b330391-6fb2-4972-bda3-6872e9835f9a\",
    \"code\": \"\",
    \"name\": \"\",
    \"description\": \"\",
    \"usage_guidance\": \"\",
    \"status_description\": \"pregnancy-safe-50\"
  }
]", "pregnancyNursingType": "object"}
 LOG  🔍 CHILD SAFETY DATA for Hortelã-Pimenta: {"childSafetyIsArray": true, "childSafetyLength": 0, "childSafetyRaw": "[]", "childSafetyType": "object"}
 LOG  🔍 OIL 6 RAW DATA (Tangerina): {"enrichment_status": "enriched", "hasSafety": true, "isEnriched": true, "name_botanical": "Citrus reticulata", "name_english": "Tangerina", "oil_id": "0ed154c2-cb91-4449-9b3b-37ffea79c9ac", "safetyRawData": "{
  \"internal_use\": {
    \"id\": \"2343a180-7dd2-45b1-a402-065b1bf2bd7c\",
    \"code\": \"FOOD_GRADE_EO\",
    \"name\": \"Safe for Internal Use\",
    \"guidance\": null,
    \"description\": \"Essential oils in this category are generally recognized or have specific indications suggesting they may be safe for internal use when following appropriate dilutions, dosage, and professional guidance.\"
  },
  \"dilution\": {
    \"id\": \"3404c0a7-6158-4236-bbd9-724728538c3d\",
    \"name\": \"[N] Neat\",
    \"ratio\": \"1:1\",
    \"description\": \"Neat. Essential oils categorized as “neat” can be applied topically without dilution on most people. These oils are considered mild and generally do not induce skin sensitivity. Examples of “neat” essential oils are frankincense, lavender, melaleuca, melissa, and sandalwood.\",
    \"percentage_max\": 0.5,
    \"percentage_min\": 0
  },
  \"phototoxicity\": {
    \"id\": \"9a987a49-f246-4aa2-99d7-87d189a01d00\",
    \"status\": \"Phototoxic\",
    \"guidance\": \"Avoid direct sun exposure or UV light on the application area for at least 12 hours (or as specified for the particular oil) after topical use, even when diluted. Consider using during evenings or on skin areas covered by clothing if UV exposure is anticipated.\",
    \"description\": \"Contains compounds that can cause phototoxic reactions (e.g., skin sensitization, rash, burning, or hyperpigmentation) when skin is exposed to UV light (sunlight, tanning beds) within a certain timeframe after topical application.\"
  },
  \"pregnancy_nursing\": [
    {
      \"id\": \"1ae12b7d-04e1-4306-b218-7e4bd7b0865b\",
      \"code\": \"\",
      \"name\": \"\",
      \"description\": \"\",
      \"usage_guidance\": \"\",
      \"status_description\": \"pregnancy-safe-100\"
    }
  ],
  \"child_safety\": [],
  \"internal_use_id\": \"2343a180-7dd2-45b1-a402-065b1bf2bd7c\",
  \"dilution_id\": \"3404c0a7-6158-4236-bbd9-724728538c3d\",
  \"phototoxicity_id\": \"9a987a49-f246-4aa2-99d7-87d189a01d00\",
  \"pregnancy_nursing_ids\": [
    \"1ae12b7d-04e1-4306-b218-7e4bd7b0865b\"
  ],
  \"child_safety_ids\": []
}"}
 LOG  🔍 PREGNANCY DATA for Tangerina: {"pregnancyNursingIsArray": true, "pregnancyNursingKeys": ["0"], "pregnancyNursingLength": 1, "pregnancyNursingRaw": "[
  {
    \"id\": \"1ae12b7d-04e1-4306-b218-7e4bd7b0865b\",
    \"code\": \"\",
    \"name\": \"\",
    \"description\": \"\",
    \"usage_guidance\": \"\",
    \"status_description\": \"pregnancy-safe-100\"
  }
]", "pregnancyNursingType": "object"}
 LOG  🔍 CHILD SAFETY DATA for Tangerina: {"childSafetyIsArray": true, "childSafetyLength": 0, "childSafetyRaw": "[]", "childSafetyType": "object"}
 LOG  🔍 OIL 7 RAW DATA (Júniper berries): {"enrichment_status": "enriched", "hasSafety": true, "isEnriched": true, "name_botanical": "Juniperus communis", "name_english": "Júniper berries", "oil_id": "d354b218-cf17-42aa-8c40-d569ed5b3ab6", "safetyRawData": "{
  \"internal_use\": {
    \"id\": \"2343a180-7dd2-45b1-a402-065b1bf2bd7c\",
    \"code\": \"FOOD_GRADE_EO\",
    \"name\": \"Safe for Internal Use\",
    \"guidance\": null,
    \"description\": \"Essential oils in this category are generally recognized or have specific indications suggesting they may be safe for internal use when following appropriate dilutions, dosage, and professional guidance.\"
  },
  \"dilution\": {
    \"id\": \"3404c0a7-6158-4236-bbd9-724728538c3d\",
    \"name\": \"[N] Neat\",
    \"ratio\": \"1:1\",
    \"description\": \"Neat. Essential oils categorized as “neat” can be applied topically without dilution on most people. These oils are considered mild and generally do not induce skin sensitivity. Examples of “neat” essential oils are frankincense, lavender, melaleuca, melissa, and sandalwood.\",
    \"percentage_max\": 0.5,
    \"percentage_min\": 0
  },
  \"phototoxicity\": {
    \"id\": \"ae18d720-4473-479e-b9f3-7fa65192d639\",
    \"status\": \"Non-Phototoxic\",
    \"guidance\": \"No specific sun exposure restrictions related to phototoxicity are typically required after topical application. However, always follow general safety guidelines for dilution and application for all essential oils.\",
    \"description\": \"Generally not known to cause phototoxic reactions when skin is exposed to UV light after topical application, when used appropriately.\"
  },
  \"pregnancy_nursing\": [
    {
      \"id\": \"1ae12b7d-04e1-4306-b218-7e4bd7b0865b\",
      \"code\": \"\",
      \"name\": \"\",
      \"description\": \"\",
      \"usage_guidance\": \"\",
      \"status_description\": \"pregnancy-safe-100\"
    }
  ],
  \"child_safety\": [],
  \"internal_use_id\": \"2343a180-7dd2-45b1-a402-065b1bf2bd7c\",
  \"dilution_id\": \"3404c0a7-6158-4236-bbd9-724728538c3d\",
  \"phototoxicity_id\": \"ae18d720-4473-479e-b9f3-7fa65192d639\",
  \"pregnancy_nursing_ids\": [
    \"1ae12b7d-04e1-4306-b218-7e4bd7b0865b\"
  ],
  \"child_safety_ids\": []
}"}
 LOG  🔍 PREGNANCY DATA for Júniper berries: {"pregnancyNursingIsArray": true, "pregnancyNursingKeys": ["0"], "pregnancyNursingLength": 1, "pregnancyNursingRaw": "[
  {
    \"id\": \"1ae12b7d-04e1-4306-b218-7e4bd7b0865b\",
    \"code\": \"\",
    \"name\": \"\",
    \"description\": \"\",
    \"usage_guidance\": \"\",
    \"status_description\": \"pregnancy-safe-100\"
  }
]", "pregnancyNursingType": "object"}
 LOG  🔍 CHILD SAFETY DATA for Júniper berries: {"childSafetyIsArray": true, "childSafetyLength": 0, "childSafetyRaw": "[]", "childSafetyType": "object"}
 LOG  🔍 OIL 8 RAW DATA (Camomila): {"enrichment_status": "enriched", "hasSafety": true, "isEnriched": true, "name_botanical": "Matricaria chamomilla", "name_english": "Camomila", "oil_id": "8af64ea7-75eb-4f4f-a568-4e0b20758060", "safetyRawData": "{
  \"internal_use\": {
    \"id\": \"247f091e-d55c-4a76-aaef-ea4485457b63\",
    \"code\": \"NON_INGESTIBLE\",
    \"name\": \"Not for Internal Use\",
    \"guidance\": null,
    \"description\": \"Essential oils in this category are not recommended or are unsafe for internal consumption. They are intended for topical application (diluted) or aromatic diffusion only.\"
  },
  \"dilution\": {
    \"id\": \"3404c0a7-6158-4236-bbd9-724728538c3d\",
    \"name\": \"[N] Neat\",
    \"ratio\": \"1:1\",
    \"description\": \"Neat. Essential oils categorized as “neat” can be applied topically without dilution on most people. These oils are considered mild and generally do not induce skin sensitivity. Examples of “neat” essential oils are frankincense, lavender, melaleuca, melissa, and sandalwood.\",
    \"percentage_max\": 0.5,
    \"percentage_min\": 0
  },
  \"phototoxicity\": {
    \"id\": \"ae18d720-4473-479e-b9f3-7fa65192d639\",
    \"status\": \"Non-Phototoxic\",
    \"guidance\": \"No specific sun exposure restrictions related to phototoxicity are typically required after topical application. However, always follow general safety guidelines for dilution and application for all essential oils.\",
    \"description\": \"Generally not known to cause phototoxic reactions when skin is exposed to UV light after topical application, when used appropriately.\"
  },
  \"pregnancy_nursing\": [],
  \"child_safety\": [],
  \"internal_use_id\": \"247f091e-d55c-4a76-aaef-ea4485457b63\",
  \"dilution_id\": \"3404c0a7-6158-4236-bbd9-724728538c3d\",
  \"phototoxicity_id\": \"ae18d720-4473-479e-b9f3-7fa65192d639\",
  \"pregnancy_nursing_ids\": [],
  \"child_safety_ids\": []
}"}
 LOG  🔍 PREGNANCY DATA for Camomila: {"pregnancyNursingIsArray": true, "pregnancyNursingKeys": [], "pregnancyNursingLength": 0, "pregnancyNursingRaw": "[]", "pregnancyNursingType": "object"}
 LOG  🔍 CHILD SAFETY DATA for Camomila: {"childSafetyIsArray": true, "childSafetyLength": 0, "childSafetyRaw": "[]", "childSafetyType": "object"}
 LOG  ACTION: updatePropertyWithEnrichedOils
 LOG  PAYLOAD: {"enrichedOils": [{"botanical_name": "", "enrichment_status": "not_found", "enrichment_timestamp": "2025-09-06T15:33:08.293Z", "isEnriched": false, "match_rationale_localized": "Indicado para aliviar azia, gastrite, má digestão e estufamento, auxiliando na regulação do sistema digestivo.", "name_botanical": "Injustice", "name_english": "Injustice", "name_localized": "Injustice", "oil_id": "001a2b3c-4d5e-678f-901a-2b3c4d5e6f78", "relevancy_to_property_score": 5, "search_query": "Injustice - Injustice"}, {"botanical_mismatch": false, "botanical_name": "", "enrichment_status": "enriched", "enrichment_timestamp": "2025-09-06T15:33:08.294Z", "isEnriched": true, "match_rationale_localized": "Auxilia no combate à azia e desconfortos gástricos, promovendo uma sensação de alívio e relaxamento abdominal.", "name_botanical": "Citrus limon", "name_english": "Limão Siciliano", "name_localized": "Limão Siciliano", "name_scientific": "Citrus limon", "oil_id": "caef2077-85b5-479f-a828-7b7be804e498", "relevancy_to_property_score": 4, "safety": [Object], "search_query": "Limão Siciliano - Citrus limon", "similarity_score": 0.878763303609288}, {"botanical_mismatch": false, "botanical_name": "", "enrichment_status": "enriched", "enrichment_timestamp": "2025-09-06T15:33:08.294Z", "isEnriched": true, "match_rationale_localized": "Ajuda na digestão e alívio de cólicas intestinais, promovendo relaxamento do sistema gastrointestinal.", "name_botanical": "Foeniculum vulgare", "name_english": "Funcho", "name_localized": "Funcho", "name_scientific": "Foeniculum vulgare", "oil_id": "7fd15cfa-5a18-4e12-ba6b-6da0d62d70bc", "relevancy_to_property_score": 4, "safety": [Object], "search_query": "Funcho - Foeniculum vulgare", "similarity_score": 0.786786888454804}, {"botanical_mismatch": false, "botanical_name": "", "enrichment_status": "enriched", "enrichment_timestamp": "2025-09-06T15:33:08.294Z", "isEnriched": true, "match_rationale_localized": "Contribui para reduzir náuseas, azia e melhorar a digestão, além de possuir propriedades termogênicas.", "name_botanical": "Zingiber officinale", "name_english": "Gengibre", "name_localized": "Gengibre", "name_scientific": "Zingiber officinale", "oil_id": "56b7e686-5417-4bcf-a975-9888f559a4f3", "relevancy_to_property_score": 4, "safety": [Object], "search_query": "Gengibre - Zingiber officinale", "similarity_score": 0.887758632399914}, {"botanical_mismatch": false, "botanical_name": "", "enrichment_status": "enriched", "enrichment_timestamp": "2025-09-06T15:33:08.295Z", "isEnriched": true, "match_rationale_localized": "Alivia desconfortos gástricos, azia e refluxo, promovendo sensação de frescor e relaxamento abdominal.", "name_botanical": "Mentha piperita", "name_english": "Hortelã-Pimenta", "name_localized": "Hortelã-Pimenta", "name_scientific": "Mentha Piperita", "oil_id": "ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf", "relevancy_to_property_score": 4, "safety": [Object], "search_query": "Hortelã-Pimenta - Mentha piperita", "similarity_score": 0.855765419223793}, {"botanical_mismatch": false, "botanical_name": "", "enrichment_status": "enriched", "enrichment_timestamp": "2025-09-06T15:33:08.295Z", "isEnriched": true, "match_rationale_localized": "Contribui para o relaxamento, alivia o estresse e auxilia na digestão por seu aroma suave e calmante.", "name_botanical": "Citrus reticulata", "name_english": "Tangerina", "name_localized": "Tangerina", "name_scientific": "Citrus reticulata", "oil_id": "0ed154c2-cb91-4449-9b3b-37ffea79c9ac", "relevancy_to_property_score": 3, "safety": [Object], "search_query": "Tangerina - Citrus reticulata", "similarity_score": 0.905911164418848}, {"botanical_mismatch": false, "botanical_name": "", "enrichment_status": "enriched", "enrichment_timestamp": "2025-09-06T15:33:08.296Z", "isEnriched": true, "match_rationale_localized": "Auxilia na digestão e ajuda na redução de inchaço abdominal, além de promover relaxamento.", "name_botanical": "Juniperus communis", "name_english": "Júniper berries", "name_localized": "Bagas de Júniper", "name_scientific": "Juniperus communis", "oil_id": "d354b218-cf17-42aa-8c40-d569ed5b3ab6", "relevancy_to_property_score": 3, "safety": [Object], "search_query": "Júniper berries - Juniperus communis", "similarity_score": 0.753316982987468}, {"botanical_mismatch": true, "botanical_name": "", "enrichment_status": "enriched", "enrichment_timestamp": "2025-09-06T15:33:08.296Z", "isEnriched": true, "match_rationale_localized": "Propriedades calmantes que auxiliam no relaxamento do sistema digestivo e redução do estresse.", "name_botanical": "Matricaria chamomilla", "name_english": "Camomila", "name_localized": "Camomila", "name_scientific": "Matricaria recutita", "oil_id": "8af64ea7-75eb-4f4f-a568-4e0b20758060", "relevancy_to_property_score": 3, "safety": [Object], "search_query": "Camomila - Matricaria chamomilla", "similarity_score": 0.74121630858329}], "propertyId": "c3f4e5a6-b7c8-9012-cd34-e5f678901234"}
 LOG  PRE-UPDATE STATE: [{"addresses_cause_ids": ["d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b"], "addresses_symptom_ids": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "description_contextual_localized": "Auxilia na melhora do processo de digestão, reduzindo o desconforto após refeições gordurosas, promovendo maior equilíbrio no sistema digestivo.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "a1f2c3d4-e5f6-7890-ab12-c3d4e5f67890", "property_name_english": "Digestive", "property_name_localized": "Digestivo", "relevancy_score": 5, "suggested_oils": [[Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object]]}, {"addresses_cause_ids": ["d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b"], "addresses_symptom_ids": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "description_contextual_localized": "Reduz a inflamação no sistema digestivo, ajudando a aliviar o desconforto relacionado ao consumo de alimentos gordurosos.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "b2f3d4e5-f6a7-8901-bc23-d4e5f6789012", "property_name_english": "Anti-inflammatory", "property_name_localized": "Anti-inflamatório", "relevancy_score": 4, "suggested_oils": [[Object], [Object], [Object], [Object], [Object]]}, {"addresses_cause_ids": [], "addresses_symptom_ids": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "description_contextual_localized": "Promove o equilíbrio emocional, ajudando a reduzir o estresse que pode impactar a digestão, promovendo uma sensação de tranquilidade.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "c3f4e5a6-b7c8-9012-cd34-e5f678901234", "property_name_english": "Calming", "property_name_localized": "Calmante", "relevancy_score": 3, "suggested_oils": [[Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object]]}, {"addresses_cause_ids": [], "addresses_symptom_ids": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "description_contextual_localized": "Auxilia na revitalização do sistema digestivo, promovendo maior disposição após refeições, especialmente após ingerir gordura.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "d4f5e6b7-c8d9-0123-de45-f67890123456", "property_name_english": "Energizing", "property_name_localized": "Energizante", "relevancy_score": 2, "suggested_oils": [[Object], [Object], [Object], [Object], [Object]]}, {"addresses_cause_ids": [], "addresses_symptom_ids": [], "description_contextual_localized": "Ajuda a reduzir a ansiedade e o estresse que podem afetar a digestão, promovendo maior relaxamento emocional.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "e5f6a7b8-c9d0-1234-ea56-89ab01234567", "property_name_english": "Nervine Relaxant", "property_name_localized": "Nervino relaxante", "relevancy_score": 2, "suggested_oils": [[Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object]]}]
 LOG  ✅ [recipe-store] Updated property c3f4e5a6-b7c8-9012-cd34-e5f678901234 with 8 oils: {"discarded": 0, "enriched": 7, "not_found": 1}
 LOG  🎯 All properties enriched! Triggering hybrid scoring calculation...
 LOG  🔢 Post-enrichment scoring: {"enrichedProperties": 5, "totalEnrichedOils": 32, "totalOilsAvailable": 34, "totalProperties": 5}
 LOG  🧮 Starting hybrid scoring calculation... {"alpha": 0.6, "gamma": 0.1, "holisticWeight": 0.30000000000000004, "propertiesCount": 5}
 LOG  📊 Scoring thresholds: {"highPriorityThreshold": 5, "maxPropertyScore": 5, "totalUniqueOils": 18}
 LOG  🎯 Oil ad7c9072... hybrid score: {"coverage": "89.4%", "finalScore": "4.75", "holistic": "87.1%", "propertiesCount": 4, "specialization": "100.0%"}
 LOG  🎯 Oil a481ef0a... hybrid score: {"coverage": "44.7%", "finalScore": "3.82", "holistic": "80.0%", "propertiesCount": 1, "specialization": "80.0%"}
 LOG  🎯 Oil 56b7e686... hybrid score: {"coverage": "100.0%", "finalScore": "3.82", "holistic": "81.1%", "propertiesCount": 5, "specialization": "70.0%"}
 LOG  🎯 Oil caef2077... hybrid score: {"coverage": "77.5%", "finalScore": "3.93", "holistic": "76.0%", "propertiesCount": 3, "specialization": "80.0%"}
 LOG  🎯 Oil 7fd15cfa... hybrid score: {"coverage": "89.4%", "finalScore": "4.05", "holistic": "80.0%", "propertiesCount": 4, "specialization": "80.0%"}
 LOG  🎯 Oil 0640a673... hybrid score: {"coverage": "63.2%", "finalScore": "3.02", "holistic": "60.0%", "propertiesCount": 2, "specialization": "60.0%"}
 LOG  🎯 Oil 2a09e83e... hybrid score: {"coverage": "44.7%", "finalScore": "2.92", "holistic": "60.0%", "propertiesCount": 1, "specialization": "60.0%"}
 LOG  🎯 Oil 0ed154c2... hybrid score: {"coverage": "63.2%", "finalScore": "1.22", "holistic": "60.0%", "propertiesCount": 2, "specialization": "0.0%"}
 LOG  🎯 Oil d354b218... hybrid score: {"coverage": "44.7%", "finalScore": "1.12", "holistic": "60.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil 8af64ea7... hybrid score: {"coverage": "44.7%", "finalScore": "1.12", "holistic": "60.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil bb99ae0f... hybrid score: {"coverage": "44.7%", "finalScore": "1.72", "holistic": "100.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil 2aaae25e... hybrid score: {"coverage": "44.7%", "finalScore": "1.42", "holistic": "80.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil d685f4e0... hybrid score: {"coverage": "44.7%", "finalScore": "1.12", "holistic": "60.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil 8ffe0ee4... hybrid score: {"coverage": "44.7%", "finalScore": "1.12", "holistic": "60.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil 5f470712... hybrid score: {"coverage": "44.7%", "finalScore": "1.42", "holistic": "80.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil a3c73ec0... hybrid score: {"coverage": "44.7%", "finalScore": "1.42", "holistic": "80.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil ecc9d982... hybrid score: {"coverage": "44.7%", "finalScore": "1.42", "holistic": "80.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil 9dd00035... hybrid score: {"coverage": "44.7%", "finalScore": "1.42", "holistic": "80.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  ✅ Hybrid scoring completed {"avgScore": 2.270021942304509, "oilsScored": 18}
 LOG  ✅ Hybrid scoring completed: {"safetyLibraryStats": {"child_safety": 0, "dilution": 3, "internal_use": 2, "phototoxicity": 2, "pregnancy_nursing": 4}, "scoredOilsCount": 18, "topOilId": "ad7c9072...", "topOilScore": 4.75}
 LOG  ✅ Hybrid scores applied to all properties! {"oilsWithScores": 18, "propertiesUpdated": 5, "sampleScores": [{"hybridScore": "4.75", "oilId": "ad7c9072...", "specializationScore": "100.0%"}, {"hybridScore": "4.05", "oilId": "7fd15cfa...", "specializationScore": "80.0%"}, {"hybridScore": "3.93", "oilId": "caef2077...", "specializationScore": "80.0%"}]}
 LOG  POST-UPDATE STATE: [{"addresses_cause_ids": ["d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b"], "addresses_symptom_ids": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "description_contextual_localized": "Auxilia na melhora do processo de digestão, reduzindo o desconforto após refeições gordurosas, promovendo maior equilíbrio no sistema digestivo.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "a1f2c3d4-e5f6-7890-ab12-c3d4e5f67890", "property_name_english": "Digestive", "property_name_localized": "Digestivo", "relevancy_score": 5, "suggested_oils": [[Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object]]}, {"addresses_cause_ids": ["d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b"], "addresses_symptom_ids": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "description_contextual_localized": "Reduz a inflamação no sistema digestivo, ajudando a aliviar o desconforto relacionado ao consumo de alimentos gordurosos.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "b2f3d4e5-f6a7-8901-bc23-d4e5f6789012", "property_name_english": "Anti-inflammatory", "property_name_localized": "Anti-inflamatório", "relevancy_score": 4, "suggested_oils": [[Object], [Object], [Object], [Object], [Object]]}, {"addresses_cause_ids": [], "addresses_symptom_ids": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "description_contextual_localized": "Promove o equilíbrio emocional, ajudando a reduzir o estresse que pode impactar a digestão, promovendo uma sensação de tranquilidade.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "c3f4e5a6-b7c8-9012-cd34-e5f678901234", "property_name_english": "Calming", "property_name_localized": "Calmante", "relevancy_score": 3, "suggested_oils": [[Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object]]}, {"addresses_cause_ids": [], "addresses_symptom_ids": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "description_contextual_localized": "Auxilia na revitalização do sistema digestivo, promovendo maior disposição após refeições, especialmente após ingerir gordura.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "d4f5e6b7-c8d9-0123-de45-f67890123456", "property_name_english": "Energizing", "property_name_localized": "Energizante", "relevancy_score": 2, "suggested_oils": [[Object], [Object], [Object], [Object], [Object]]}, {"addresses_cause_ids": [], "addresses_symptom_ids": [], "description_contextual_localized": "Ajuda a reduzir a ansiedade e o estresse que podem afetar a digestão, promovendo maior relaxamento emocional.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "e5f6a7b8-c9d0-1234-ea56-89ab01234567", "property_name_english": "Nervine Relaxant", "property_name_localized": "Nervino relaxante", "relevancy_score": 2, "suggested_oils": [[Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object]]}]
 LOG  🔍 [CausesSelection] Store subscription debug: {"potentialCausesFromStore": 7, "renderTimestamp": "2025-09-06T15:33:08.198Z", "selectedCausesFromStore": 1}
 LOG  🔍 [CausesSelection] Counter Debug: {"SELECTION_REQUIREMENTS_CAUSES": {"max": 10, "min": 1}, "maxSelection": 10, "minSelection": 1, "selectedCauses": 1, "selectedCausesArray": [{"id": "d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b", "name": "Dieta rica em alimentos gordurosos"}], "selectionCount": 1}
 LOG  🩺 SymptomsSelection Debug: {"canSubmit": true, "selectedCount": 1, "selectedIds": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "totalSymptoms": 8}
 LOG  🎭 SymptomsSelection - Modal State: {"isStreaming": false, "showStreamingModal": false, "streamingItemsCount": 5, "timestamp": "2025-09-06T15:33:08.217Z"}
 LOG  🔍 SymptomsSelection: First symptom data structure: {"explanation": "Sensação de plenitude ou inchaço após refeições ricas em gordura devido ao processamento dificultoso pelo sistema digestivo.", "symptom_id": "d3b07384-2a6d-4c65-8d6d-859f2b1a16c1", "symptom_name": "Sensação de plenitude abdominal após refeições", "symptom_suggestion": "Observe se ocorre após o consumo de alimentos gordurosos"}
 LOG  🔍 SymptomsSelection: Available fields: ["symptom_id", "symptom_name", "symptom_suggestion", "explanation"]
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🔍 getCauseNamesByIds called with UUIDs: ["d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b"]
 LOG  🔍 Available selectedCauses: [{"id":"d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b","name":"Dieta rica em alimentos gordurosos"}]
 LOG  🔍 Looking for cause UUID "d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b": FOUND "Dieta rica em alimentos gordurosos"
 LOG  🔍 getCauseNamesByIds final result: ["Dieta rica em alimentos gordurosos"]
 LOG  🔍 getSymptomNamesByIds called with UUIDs: ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"]
 LOG  🔍 Available selectedSymptoms: [{"id":"d3b07384-2a6d-4c65-8d6d-859f2b1a16c1","name":"Sensação de plenitude abdominal após refeições"}]
 LOG  🔍 Looking for symptom UUID "d3b07384-2a6d-4c65-8d6d-859f2b1a16c1": FOUND "Sensação de plenitude abdominal após refeições"
 LOG  🔍 getSymptomNamesByIds final result: ["Sensação de plenitude abdominal após refeições"]
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🚀 [Properties-FinalRecipes] Generate Final Recipes button clicked
 LOG  ✅ [Properties-FinalRecipes] Starting final recipe generation with 5 enriched properties
 LOG  🚀 [RN-FinalRecipes] Starting parallel final recipe streaming for 3 time slots
 LOG  🚀 [RN-FinalRecipes] Starting parallel final recipe streaming for 3 time slots
 LOG  🌐 [RN-FinalRecipes] Using user language: {"apiLanguage": undefined}
 LOG  ✅ [RN-FinalRecipes] Using 5 enriched properties
 LOG  🔢 [RN-FinalRecipes] Calculating post-enrichment scores...
 LOG  🔢 Post-enrichment scoring: {"enrichedProperties": 5, "totalEnrichedOils": 32, "totalOilsAvailable": 34, "totalProperties": 5}
 LOG  🧮 Starting hybrid scoring calculation... {"alpha": 0.6, "gamma": 0.1, "holisticWeight": 0.30000000000000004, "propertiesCount": 5}
 LOG  📊 Scoring thresholds: {"highPriorityThreshold": 5, "maxPropertyScore": 5, "totalUniqueOils": 18}
 LOG  🎯 Oil ad7c9072... hybrid score: {"coverage": "89.4%", "finalScore": "4.75", "holistic": "87.1%", "propertiesCount": 4, "specialization": "100.0%"}
 LOG  🎯 Oil a481ef0a... hybrid score: {"coverage": "44.7%", "finalScore": "3.82", "holistic": "80.0%", "propertiesCount": 1, "specialization": "80.0%"}
 LOG  🎯 Oil 56b7e686... hybrid score: {"coverage": "100.0%", "finalScore": "3.82", "holistic": "81.1%", "propertiesCount": 5, "specialization": "70.0%"}
 LOG  🎯 Oil caef2077... hybrid score: {"coverage": "77.5%", "finalScore": "3.93", "holistic": "76.0%", "propertiesCount": 3, "specialization": "80.0%"}
 LOG  🎯 Oil 7fd15cfa... hybrid score: {"coverage": "89.4%", "finalScore": "4.05", "holistic": "80.0%", "propertiesCount": 4, "specialization": "80.0%"}
 LOG  🎯 Oil 0640a673... hybrid score: {"coverage": "63.2%", "finalScore": "3.02", "holistic": "60.0%", "propertiesCount": 2, "specialization": "60.0%"}
 LOG  🎯 Oil 2a09e83e... hybrid score: {"coverage": "44.7%", "finalScore": "2.92", "holistic": "60.0%", "propertiesCount": 1, "specialization": "60.0%"}
 LOG  🎯 Oil 0ed154c2... hybrid score: {"coverage": "63.2%", "finalScore": "1.22", "holistic": "60.0%", "propertiesCount": 2, "specialization": "0.0%"}
 LOG  🎯 Oil d354b218... hybrid score: {"coverage": "44.7%", "finalScore": "1.12", "holistic": "60.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil 8af64ea7... hybrid score: {"coverage": "44.7%", "finalScore": "1.12", "holistic": "60.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil bb99ae0f... hybrid score: {"coverage": "44.7%", "finalScore": "1.72", "holistic": "100.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil 2aaae25e... hybrid score: {"coverage": "44.7%", "finalScore": "1.42", "holistic": "80.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil d685f4e0... hybrid score: {"coverage": "44.7%", "finalScore": "1.12", "holistic": "60.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil 8ffe0ee4... hybrid score: {"coverage": "44.7%", "finalScore": "1.12", "holistic": "60.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil 5f470712... hybrid score: {"coverage": "44.7%", "finalScore": "1.42", "holistic": "80.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil a3c73ec0... hybrid score: {"coverage": "44.7%", "finalScore": "1.42", "holistic": "80.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil ecc9d982... hybrid score: {"coverage": "44.7%", "finalScore": "1.42", "holistic": "80.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil 9dd00035... hybrid score: {"coverage": "44.7%", "finalScore": "1.42", "holistic": "80.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  ✅ Hybrid scoring completed {"avgScore": 2.270021942304509, "oilsScored": 18}
 LOG  ✅ Hybrid scoring completed: {"safetyLibraryStats": {"child_safety": 0, "dilution": 3, "internal_use": 2, "phototoxicity": 2, "pregnancy_nursing": 4}, "scoredOilsCount": 18, "topOilId": "ad7c9072...", "topOilScore": 4.75}
 LOG  ✅ [RN-FinalRecipes] Enhanced data prepared: {"scoredOilsCount": 18, "topOilScore": 4.75, "topOilsCount": 18}
 LOG  📤 [RN-FinalRecipes] Created 3 parallel streaming requests for time slots
 LOG  📤 [RN-FinalRecipes] Created 3 parallel streaming requests for time slots
 LOG  🚀 [ParallelEngine] Starting 3 parallel streaming requests
 LOG  📡 [ParallelEngine] Starting stream for ID: morning (morning recipe)
 LOG  📤 [ParallelEngine] Request data: {"dataKeys": ["health_concern", "gender", "age_category", "age_specific", "user_language", "selected_causes", "selected_symptoms", "time_of_day", "suggested_oils", "safety_library", "safety_library_formatted"], "feature": "create-recipe", "step": "final-recipes"}
 LOG  ⏳ [ParallelEngine] Delaying stream 2/3 by 3000ms to avoid rate limiting
 LOG  ⏳ [ParallelEngine] Delaying stream 3/3 by 6000ms to avoid rate limiting
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: true
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 5}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  📊 LAYER 8B: Property Analysis Progress: 0/5 properties completed
 LOG  📊 LAYER 8B: Tool Calls Made: 0 total | Results: 0 | Errors: 0
 LOG  📊 LAYER 8C: Map/Set sizes changed
 LOG  📊 LAYER 8C: Results size: 0
 LOG  📊 LAYER 8C: Errors size: 0
 LOG  🎯 AUTO-COMPLETION: Checking streaming completion:
 LOG  🎯 AUTO-COMPLETION: totalSelected: 5
 LOG  🎯 AUTO-COMPLETION: totalCompleted: 0
 LOG  🎯 AUTO-COMPLETION: isStreaming: true
 LOG  🎯 AUTO-COMPLETION: isSubmitting: false
 LOG  🚀 [API-ios] Starting streaming request {"bodySize": 58514, "feature": "create-recipe", "headers": ["Content-Type", "Authorization"], "platform": "ios", "requestId": "api-1757172818599-vg0t466z5", "requestPreview": "{\"feature\":\"create-recipe\",\"step\":\"final-recipes\",\"data\":{\"health_concern\":\"Desconforto digestivo após refeições, especialmente comidas gordurosas\",\"gender\":\"female\",\"age_category\":\"adult\",\"age_specif...", "step": "final-recipes", "timestamp": "2025-09-06T15:33:38.599Z", "url": "https://novo.rotinanatural.com.br/api/ai/streaming"}
 LOG  📱 [API-ios] Using unified mobile SSE client for platform: ios {"requestId": "api-1757172818599-vg0t466z5"}
 LOG  📱 [API-ios] Creating mobile streaming response {"requestId": "api-1757172818599-vg0t466z5"}
 LOG  📱 [API-ios] Starting mobile ReadableStream {"requestId": "api-1757172818599-vg0t466z5"}
 LOG  � [MobiloeSSE] Sending request: {"bodyLength": 58514, "headers": {"Authorization": "Bearer zh6Xs8GAHi8TlX8LWchb3WQ7ouneL3gn8aL84CLL1x7J0rKOKr", "Content-Type": "application/json"}, "url": "https://novo.rotinanatural.com.br/api/ai/streaming"}
 LOG  📱 [API-ios] Mobile streaming response created {"bodyType": "ReadableStream", "hasBody": true, "requestId": "api-1757172818599-vg0t466z5"}
 LOG  🔍 [SSE-ios] Starting SSE stream reader {"platform": "ios", "sessionId": "sse-1757172818602-ep16grrc6", "streamType": "ReadableStream", "timestamp": "2025-09-06T15:33:38.602Z"}
 LOG  📥 [SSE-ios] Reading chunk 1 {"sessionId": "sse-1757172818602-ep16grrc6"}
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: true
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 5}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  📡 [ParallelEngine] Starting stream for ID: mid-day (mid-day recipe)
 LOG  📤 [ParallelEngine] Request data: {"dataKeys": ["health_concern", "gender", "age_category", "age_specific", "user_language", "selected_causes", "selected_symptoms", "time_of_day", "suggested_oils", "safety_library", "safety_library_formatted"], "feature": "create-recipe", "step": "final-recipes"}
 LOG  🚀 [API-ios] Starting streaming request {"bodySize": 58514, "feature": "create-recipe", "headers": ["Content-Type", "Authorization"], "platform": "ios", "requestId": "api-1757172821473-7zsr773h8", "requestPreview": "{\"feature\":\"create-recipe\",\"step\":\"final-recipes\",\"data\":{\"health_concern\":\"Desconforto digestivo após refeições, especialmente comidas gordurosas\",\"gender\":\"female\",\"age_category\":\"adult\",\"age_specif...", "step": "final-recipes", "timestamp": "2025-09-06T15:33:41.475Z", "url": "https://novo.rotinanatural.com.br/api/ai/streaming"}
 LOG  📱 [API-ios] Using unified mobile SSE client for platform: ios {"requestId": "api-1757172821473-7zsr773h8"}
 LOG  📱 [API-ios] Creating mobile streaming response {"requestId": "api-1757172821473-7zsr773h8"}
 LOG  📱 [API-ios] Starting mobile ReadableStream {"requestId": "api-1757172821473-7zsr773h8"}
 LOG  � [MobiloeSSE] Sending request: {"bodyLength": 58514, "headers": {"Authorization": "Bearer zh6Xs8GAHi8TlX8LWchb3WQ7ouneL3gn8aL84CLL1x7J0rKOKr", "Content-Type": "application/json"}, "url": "https://novo.rotinanatural.com.br/api/ai/streaming"}
 LOG  📱 [API-ios] Mobile streaming response created {"bodyType": "ReadableStream", "hasBody": true, "requestId": "api-1757172821473-7zsr773h8"}
 LOG  🔍 [SSE-ios] Starting SSE stream reader {"platform": "ios", "sessionId": "sse-1757172821487-sr5zeou2y", "streamType": "ReadableStream", "timestamp": "2025-09-06T15:33:41.487Z"}
 LOG  📥 [SSE-ios] Reading chunk 1 {"sessionId": "sse-1757172821487-sr5zeou2y"}
 LOG  📡 [ParallelEngine] Starting stream for ID: night (night recipe)
 LOG  📤 [ParallelEngine] Request data: {"dataKeys": ["health_concern", "gender", "age_category", "age_specific", "user_language", "selected_causes", "selected_symptoms", "time_of_day", "suggested_oils", "safety_library", "safety_library_formatted"], "feature": "create-recipe", "step": "final-recipes"}
 LOG  🚀 [API-ios] Starting streaming request {"bodySize": 58512, "feature": "create-recipe", "headers": ["Content-Type", "Authorization"], "platform": "ios", "requestId": "api-1757172824474-em5vtq9rl", "requestPreview": "{\"feature\":\"create-recipe\",\"step\":\"final-recipes\",\"data\":{\"health_concern\":\"Desconforto digestivo após refeições, especialmente comidas gordurosas\",\"gender\":\"female\",\"age_category\":\"adult\",\"age_specif...", "step": "final-recipes", "timestamp": "2025-09-06T15:33:44.476Z", "url": "https://novo.rotinanatural.com.br/api/ai/streaming"}
 LOG  📱 [API-ios] Using unified mobile SSE client for platform: ios {"requestId": "api-1757172824474-em5vtq9rl"}
 LOG  📱 [API-ios] Creating mobile streaming response {"requestId": "api-1757172824474-em5vtq9rl"}
 LOG  📱 [API-ios] Starting mobile ReadableStream {"requestId": "api-1757172824474-em5vtq9rl"}
 LOG  � [MobiloeSSE] Sending request: {"bodyLength": 58512, "headers": {"Authorization": "Bearer zh6Xs8GAHi8TlX8LWchb3WQ7ouneL3gn8aL84CLL1x7J0rKOKr", "Content-Type": "application/json"}, "url": "https://novo.rotinanatural.com.br/api/ai/streaming"}
 LOG  📱 [API-ios] Mobile streaming response created {"bodyType": "ReadableStream", "hasBody": true, "requestId": "api-1757172824474-em5vtq9rl"}
 LOG  🔍 [SSE-ios] Starting SSE stream reader {"platform": "ios", "sessionId": "sse-1757172824489-n63lysomc", "streamType": "ReadableStream", "timestamp": "2025-09-06T15:33:44.489Z"}
 LOG  📥 [SSE-ios] Reading chunk 1 {"sessionId": "sse-1757172824489-n63lysomc"}
 LOG  🔍 [MobileSSE] Headers received: {"contentType": "text/event-stream", "headers": {"access-control-allow-headers": "Content-Type, Authorization", "access-control-allow-methods": "GET, POST, OPTIONS", "access-control-allow-origin": "*", "access-control-max-age": "86400", "alt-svc": "h3=\":443\"; ma=2592000", "cache-control": "no-cache", "content-encoding": "br", "content-type": "text/event-stream", "date": "Sat, 06 Sep 2025 15:33:55 GMT", "vary": "Accept-Encoding, RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "x-clerk-auth-message": "Invalid JWT form. A JWT consists of three parts separated by dots. (reason=token-invalid, token-carrier=header)", "x-clerk-auth-reason": "token-invalid", "x-clerk-auth-status": "signed-out", "x-middleware-rewrite": "/api/ai/streaming"}, "status": 200}
 LOG  📱 [API-ios] Mobile SSE connection opened {"requestId": "api-1757172818599-vg0t466z5"}
 LOG  🔍 [MobileSSE] New data chunk: {"chunkLength": 8874, "chunkPreview": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RECIPE RESPONSE\",\"request_id\""}
 LOG  🔍 [MobileSSE] Processing message block: {"messageLength": 8872, "messagePreview": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RECIPE RESPONSE\",\"request_id\":\"20231012-0012-ABCD-1234-EF56789ABCD0\",\"timestamp"}
 LOG  🔍 [MobileSSE] Yielding data: {"dataLength": 8866, "dataPreview": "{\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RECIPE RESPONSE\",\"request_id\":\"2023"}
 LOG  📱 [API-ios] Mobile SSE message received {"dataLength": 8866, "dataPreview": "{\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RECIPE RESPONSE\",\"request_id\":\"2023", "requestId": "api-1757172818599-vg0t466z5"}
 LOG  📦 [SSE-ios] Chunk 1 received {"bufferLengthBefore": 0, "chunkPreview": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RECIPE RESPONSE\",\"request_id\"...", "chunkSize": 9121, "sessionId": "sse-1757172818602-ep16grrc6", "totalBytes": 9121}
 LOG  🔄 [SSE-ios] Buffer updated {"bufferLength": 8874, "bufferPreview": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RECIPE RESPONSE\",\"request_id\":\"20231012-0012-ABCD-1234-EF56789ABCD0\",\"timestamp_utc\":\"2023-10-12T15:20:00Z\",\"version\":\"1.0\",\"user...", "sessionId": "sse-1757172818602-ep16grrc6"}
 LOG  🔍 [SSE-ios] Processing message block 1 {"messageBlock": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RECIPE RESPONSE\",\"request_id\":\"20231012-0012-ABCD-1234-EF56789ABCD0\",\"timestamp...", "remainingBufferLength": 0, "sessionId": "sse-1757172818602-ep16grrc6"}
 LOG  📤 [SSE-ios] Yielding data message 1 {"data": "{\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RECIPE RESPONSE\",\"request_id\":\"2023...", "dataLength": 8866, "fullLine": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RECIPE RESPONSE\",\"request_id\":\"20231012-0012-ABCD-1234-EF56789ABCD0\",\"timestamp...", "sessionId": "sse-1757172818602-ep16grrc6"}
 LOG  🏁 [ParallelEngine] Processing structured_complete for morning
 LOG  📋 [ParallelEngine] Complete data keys: ["meta", "data", "safety_warnings", "echo"]
 LOG  📋 [ParallelEngine] Data structure preview: {"dataKeys": ["meta", "data", "safety_warnings", "echo"], "firstLevelData": {"data": "object", "echo": "object", "meta": "object", "safety_warnings": "object"}, "hasSuggestedOils": false, "hasTherapeuticPropertyContext": false}
 LOG  🔍 [RN-FinalRecipes] RAW API RESPONSE for morning : {
  "meta": {
    "step_name": "FINAL RECIPE RESPONSE",
    "request_id": "20231012-0012-ABCD-1234-EF56789ABCD0",
    "timestamp_utc": "2023-10-12T15:20:00Z",
    "version": "1.0",
    "user_language": "PT_BR",
    "status": "success",
    "message": "Recipe successfully generated based on user profile and safety data."
  },
  "data": {
    "recipe_protocol": {
      "recipe_id": "d2f1a4c8-8e3b-4b0f-89d6-12e349f7a963",
      "time_slot": "morning",
      "time_of_day_localized": "manhã",
      "time_range_localized": "06:00 - 09:00",
      "recipe_theme_localized": "Alívio digestivo matinal",
      "description_localized": "Uma mistura terapêutica especialmente formulada para aliviar o desconforto digestivo causado por refeições gordurosas, promovendo bem-estar e equilíbrio digestivo ao iniciar o dia.",
      "holistic_benefit_localized": "Este remédio aromático apoia a saúde digestiva, reduz a sensação de plenitude e inchaço após refeições ricas em gordura, além de promover sensação de leveza e energia matinal.",
      "application_type_localized": "difusão no ambiente e aplicação tópica leve na região abdominal",
      "synergy_rationale_localized": "A combinação de hortelã-pimenta, funcho e limão siciliano atua sinergicamente para estimular a digestão, reduzir inflamações e aliviar a sensação de peso na barriga, proporcionando um início de dia mais confortável.",
      "formulation": {
        "total_drops": 25,
        "dilution_percentage": 1.5,
        "bottle_size_ml": 10,
        "bottle_type_localized": "frasco de vidro com conta-gotas"
      },
      "ingredients": {
        "essential_oils": [
          {
            "oil_id": "ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf",
            "name_localized": "Hortelã-pimenta",
            "scientific_name": "Mentha Piperita",
            "drops": 10
          },
          {
            "oil_id": "7fd15cfa-5a18-4e12-ba6b-6da0d62d70bc",
            "name_localized": "Funcho",
            "scientific_name": "Foeniculum vulgare",
            "drops": 8
          },
          {
            "oil_id": "caef2077-85b5-479f-a828-7b7be804e498",
            "name_localized": "Limão siciliano",
            "scientific_name": "Citrus limon",
            "drops": 7
          }
        ],
        "carrier_oil": {
          "recommended": {
            "name_localized": "Azeite de oliva extra virgem",
            "properties_localized": "Óleo vegetal de absorção suave, com propriedades calmantes e antioxidantes, ideal para aplicação abdominal."
          },
          "alternative": {
            "name_localized": "Óleo de amêndoas doces",
            "properties_localized": "Óleo vegetal leve, de rápida absorção, que facilita a aplicação tópica e potencializa a absorção dos óleos essenciais."
          }
        }
      },
      "therapeutic_properties_targeted": [
        {
          "property_id": "a1b2c3d4-e5f6-7890-ab12-c3d4e5f67890",
          "name_localized": "Alívio de desconforto digestivo",
          "description_localized": "Estimula a digestão, reduz inchaço e sensação de plenitude após refeições gordurosas."
        }
      ],
      "preparation_summary_localized": "Diluir os óleos essenciais selecionados em 10ml de azeite de oliva extra virgem, usando 2,5% de diluição (25 gotas no total). Agitar bem para misturar homogeneamente.",
      "usage_summary_localized": "Aplicar uma pequena quantidade na região abdominal (below the navel) duas vezes ao dia, pela manhã e após o almoço, massageando suavemente até completa absorção. Pode-se também usar um difusor de ambiente na manhã para criar um ambiente aromático relaxante e estimulante para o sistema digestivo.",
      "preparation_steps_localized": [
        "Adicionar as 10 gotas de hortelã-pimenta, 8 gotas de funcho e 7 gotas de limão siciliano em uma máquina de dispensar ou frasco de 10ml com conta-gotas.",
        "Preencher o frasco com 10ml de azeite de oliva extra virgem até completar, segurando o frasco na posição vertical e agitando suavemente para homogeneizar a mistura.",
        "Etiquetar o frasco com a data de preparação e uso recomendado, guardando em local fresco e ao abrigo da luz direta."
      ],
      "usage_instructions_localized": [
        {
          "method_code": "topical_sigh",
          "method": "massagem na região abdominal",
          "description": "Aplicar uma pequena quantidade do óleo diluído na região abdominal, com movimentos suaves e circulares, duas vezes ao dia, preferencialmente após as refeições.",
          "frequency": "duas vezes ao dia"
        },
        {
          "method_code": "diffusion",
          "method": "difusão aromática",
          "description": "Adicione 3-5 gotas do óleo essencial diluído no difusor na manhã para criar um ambiente aromático que estimula a digestão e promove o bem-estar.",
          "frequency": "uma vez ao dia, de manhã"
        }
      ],
      "ritual_suggestion_localized": "Comece seu dia com uma respiração profunda do aroma de hortelã-pimenta e funcho no difusor, enquanto massageia suavemente sua região abdominal após o café da manhã, criando um momento de cuidado e atenção plena para promover sua digestão e bem-estar. Aproveite para saborear sua manhã com uma atitude positiva e acolhedora.",
      "oil_rationales": [
        {
          "oil_id": "ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf",
          "name_localized": "Hortelã-pimenta",
          "properties": [
            "Alívio de desconforto digestivo",
            "Estimula a digestão",
            "Reduz inchaço",
            "Sensação de frescor"
          ],
          "rationale_localized": "A hortelã-pimenta é reconhecida por sua ação rápida no alívio de desconfortos digestivos, auxiliando a acalmar o sistema gastrointestinal e proporcionando sensação de alívio e frescor, ideal para começar o dia com energia positiva."
        },
        {
          "oil_id": "7fd15cfa-5a18-4e12-ba6b-6da0d62d70bc",
          "name_localized": "Funcho",
          "properties": [
            "Estimula a digestão",
            "Possui propriedades anti-inflamatórias",
            "Ajuda a reduzir o inchaço abdominal"
          ],
          "rationale_localized": "O funcho é conhecido por promover uma digestão mais eficiente, aliviar gases e inchaço, além de atuar de forma calmante na mucosa gastrointestinal, complementando a ação da hortelã-pimenta."
        },
        {
          "oil_id": "caef2077-85b5-479f-a828-7b7be804e498",
          "name_localized": "Limão siciliano",
          "properties": [
            "Aumenta a circulação sanguínea",
            "Refrescante e energizante",
            "Estimulante da digestão"
          ],
          "rationale_localized": "O limão siciliano traz uma nota revitalizante, ajuda a estimular a secreção de enzimas digestivas e promove sensação de leveza logo pela manhã, reforçando o efeito positivo da combinação."
        },
        {
          "oil_id": "ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf",
          "name_localized": "Hortelã-pimenta",
          "properties": [
            "Alívio de desconforto digestivo",
            "Estimula a digestão",
            "Reduz inchaço",
            "Sensação de frescor"
          ],
          "rationale_localized": "A hortelã-pimenta é reconhecida por sua ação rápida no alívio de desconfortos digestivos, auxiliando a acalmar o sistema gastrointestinal e proporcionando sensação de alívio e frescor, ideal para começar o dia com energia positiva."
        },
        {
          "oil_id": "7fd15cfa-5a18-4e12-ba6b-6da0d62d70bc",
          "name_localized": "Funcho",
          "properties": [
            "Estimula a digestão",
            "Possui propriedades anti-inflamatórias",
            "Ajuda a reduzir o inchaço abdominal"
          ],
          "rationale_localized": "O funcho é conhecido por promover uma digestão mais eficiente, aliviar gases e inchaço, além de atuar de forma calmante na mucosa gastrointestinal, complementando a ação da hortelã-pimenta."
        },
        {
          "oil_id": "caef2077-85b5-479f-a828-7b7be804e498",
          "name_localized": "Limão siciliano",
          "properties": [
            "Aumenta a circulação sanguínea",
            "Refrescante e energizante",
            "Estimulante da digestão"
          ],
          "rationale_localized": "O limão siciliano traz uma nota revitalizante, ajuda a estimular a secreção de enzimas digestivas e promove sensação de leveza logo pela manhã, reforçando o efeito positivo da combinação."
        }
      ]
    }
  },
  "safety_warnings": [
    {
      "type": "Precauções gerais",
      "title_localized": "Uso Seguro de Óleos Essenciais",
      "warning_text_localized": "Este blend foi formulado com óleos essenciais considerados seguros para uso interno sob orientação adequada, além de ser diluído para evitar sensibilidades ou efeitos adversos. No entanto, é importante evitar contato com olhos e mucosas, manter fora do alcance de crianças, e suspender uso em caso de irritação. Consulte sempre um profissional de saúde ou aromaterapeuta se estiver grávida, amamentando ou com condições médicas especiais."
    },
    {
      "type": "Aviso sobre fototoxicidade",
      "title_localized": "Proteção Solar após Aplicação Tópica",
      "warning_text_localized": "O óleo de limão (Citrus limon) é fototóxico. Evitar exposição ao sol na região de aplicação por pelo menos 12 horas após o uso tópico para prevenir queimaduras ou manchas. Prefira uso noturno ou áreas cobertas."
    },
    {
      "type": "Precauções na gravidez e lactação",
      "title_localized": "Gravidez e Amamentação",
      "warning_text_localized": "O óleo de limão siciliano é considerado seguro para uso durante gravidez e lactação, desde que usado na diluição adequada. Óleos como hortelã-pimenta devem ser utilizados com moderação e sempre sob orientação profissional durante esses períodos."
    }
  ],
  "echo": {
    "health_concern_input": "Desconforto digestivo após refeições, especialmente comidas gordurosas",
    "user_info_input": {
      "gender": "female",
      "age_category": "adult",
      "age_specific": "24",
      "age_unit": "anos"
    },
    "selected_cause_ids": [
      "d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b"
    ],
    "selected_symptom_ids": [
      "d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"
    ],
    "suggested_oil_ids": [
      "ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf",
      "7fd15cfa-5a18-4e12-ba6b-6da0d62d70bc",
      "caef2077-85b5-479f-a828-7b7be804e498"
    ],
    "time_of_day": "manhã"
  }
}
 LOG  🔍 [RN-FinalRecipes] ResponseParser received for morning : {"finalDataKeys": ["meta", "data", "safety_warnings", "echo"], "hasData": true, "hasFinalData": true}
 LOG  ✅ [RN-FinalRecipes] Recipe received for morning : Alívio digestivo matinal
 LOG  ✅ [ParallelEngine] Completed stream for morning: SUCCESS
 LOG  ✅ [ParallelEngine] Stream result preview: {"hasOils": false, "oilsCount": 0, "resultKeys": ["timeSlot", "recipe"]}
 LOG  🔒 [SSE-ios] Releasing reader lock {"sessionId": "sse-1757172818602-ep16grrc6"}
 LOG  📋 [ParallelEngine] Added result for morning. Total completed: 1
 LOG  🔍 [MobileSSE] Connection completed
 LOG  📱 [API-ios] Mobile SSE connection closed {"requestId": "api-1757172818599-vg0t466z5"}
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: true
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 1, "total": 5}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  📊 LAYER 8B: Property Analysis Progress: 1/5 properties completed
 LOG  📊 LAYER 8B: Tool Calls Made: 1 total | Results: 1 | Errors: 0
 LOG  📊 LAYER 8C: Map/Set sizes changed
 LOG  📊 LAYER 8C: Results size: 1
 LOG  📊 LAYER 8C: Errors size: 0
 LOG  🔄 LAYER 9B: Processing individual streaming results with STORE OPERATIONS...
 LOG  🎯 AUTO-COMPLETION: Checking streaming completion:
 LOG  🎯 AUTO-COMPLETION: totalSelected: 5
 LOG  🎯 AUTO-COMPLETION: totalCompleted: 1
 LOG  🎯 AUTO-COMPLETION: isStreaming: true
 LOG  🎯 AUTO-COMPLETION: isSubmitting: false
 LOG  🔍 [MobileSSE] Headers received: {"contentType": "text/event-stream", "headers": {"access-control-allow-headers": "Content-Type, Authorization", "access-control-allow-methods": "GET, POST, OPTIONS", "access-control-allow-origin": "*", "access-control-max-age": "86400", "alt-svc": "h3=\":443\"; ma=2592000", "cache-control": "no-cache", "content-encoding": "br", "content-type": "text/event-stream", "date": "Sat, 06 Sep 2025 15:34:00 GMT", "vary": "Accept-Encoding, RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "x-clerk-auth-message": "Invalid JWT form. A JWT consists of three parts separated by dots. (reason=token-invalid, token-carrier=header)", "x-clerk-auth-reason": "token-invalid", "x-clerk-auth-status": "signed-out", "x-middleware-rewrite": "/api/ai/streaming"}, "status": 200}
 LOG  📱 [API-ios] Mobile SSE connection opened {"requestId": "api-1757172824474-em5vtq9rl"}
 LOG  🔍 [MobileSSE] New data chunk: {"chunkLength": 7553, "chunkPreview": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RESPONSE\",\"request_id\":\"2023F"}
 LOG  🔍 [MobileSSE] Processing message block: {"messageLength": 7551, "messagePreview": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RESPONSE\",\"request_id\":\"2023F163-AC7B-4C72-B163-AC7B4C72B163\",\"timestamp_utc\":\""}
 LOG  🔍 [MobileSSE] Yielding data: {"dataLength": 7545, "dataPreview": "{\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RESPONSE\",\"request_id\":\"2023F163-AC"}
 LOG  📱 [API-ios] Mobile SSE message received {"dataLength": 7545, "dataPreview": "{\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RESPONSE\",\"request_id\":\"2023F163-AC", "requestId": "api-1757172824474-em5vtq9rl"}
 LOG  📦 [SSE-ios] Chunk 1 received {"bufferLengthBefore": 0, "chunkPreview": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RESPONSE\",\"request_id\":\"2023F...", "chunkSize": 7707, "sessionId": "sse-1757172824489-n63lysomc", "totalBytes": 7707}
 LOG  🔄 [SSE-ios] Buffer updated {"bufferLength": 7553, "bufferPreview": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RESPONSE\",\"request_id\":\"2023F163-AC7B-4C72-B163-AC7B4C72B163\",\"timestamp_utc\":\"2023-10-02T14:48:00Z\",\"version\":\"1.0\",\"user_langua...", "sessionId": "sse-1757172824489-n63lysomc"}
 LOG  🔍 [SSE-ios] Processing message block 1 {"messageBlock": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RESPONSE\",\"request_id\":\"2023F163-AC7B-4C72-B163-AC7B4C72B163\",\"timestamp_utc\":\"...", "remainingBufferLength": 0, "sessionId": "sse-1757172824489-n63lysomc"}
 LOG  📤 [SSE-ios] Yielding data message 1 {"data": "{\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RESPONSE\",\"request_id\":\"2023F163-AC...", "dataLength": 7545, "fullLine": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RESPONSE\",\"request_id\":\"2023F163-AC7B-4C72-B163-AC7B4C72B163\",\"timestamp_utc\":\"...", "sessionId": "sse-1757172824489-n63lysomc"}
 LOG  🏁 [ParallelEngine] Processing structured_complete for night
 LOG  📋 [ParallelEngine] Complete data keys: ["meta", "data", "safety_warnings", "echo"]
 LOG  📋 [ParallelEngine] Data structure preview: {"dataKeys": ["meta", "data", "safety_warnings", "echo"], "firstLevelData": {"data": "object", "echo": "object", "meta": "object", "safety_warnings": "object"}, "hasSuggestedOils": false, "hasTherapeuticPropertyContext": false}
 LOG  🔍 [RN-FinalRecipes] RAW API RESPONSE for night : {
  "meta": {
    "step_name": "FINAL RESPONSE",
    "request_id": "2023F163-AC7B-4C72-B163-AC7B4C72B163",
    "timestamp_utc": "2023-10-02T14:48:00Z",
    "version": "1.0",
    "user_language": "PT_BR",
    "status": "success",
    "message": "Recipe generated successfully based on detailed user profile and health concern."
  },
  "data": {
    "recipe_protocol": {
      "recipe_id": "ADFC8E12-4F89-4E21-B681-3A967D1E1B0D",
      "time_slot": "night",
      "time_of_day_localized": "noite",
      "time_range_localized": "19h - 22h",
      "recipe_theme_localized": "Alívio do desconforto digestivo noturno",
      "description_localized": "Um ritual relaxante e digestivo para ajudar a aliviar o desconforto digestivo após refeições gordurosas, promovendo relaxamento e bem-estar à noite.",
      "holistic_benefit_localized": "Alivia o desconforto digestivo, promove relaxamento e melhora a qualidade do sono.",
      "application_type_localized": "difusão e aplicação tópica",
      "synergy_rationale_localized": "A combinação de Hortelã-pimenta, Funcho, Limão Siciliano e Camomila Romanana atua sinergicamente para aliviar a sensação de plenitude abdominal, melhorar a digestão, reduzir o inchaço e proporcionar um efeito calmante na noite.",
      "formulation": {
        "total_drops": 25,
        "dilution_percentage": 0.2,
        "bottle_size_ml": 10,
        "bottle_type_localized": "frasco de vidro âmbar 10ml"
      },
      "ingredients": {
        "essential_oils": [
          {
            "oil_id": "ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf",
            "name_localized": "Hortelã-pimenta",
            "scientific_name": "Mentha Piperita",
            "drops": 8
          },
          {
            "oil_id": "7fd15cfa-5a18-4e12-ba6b-6da0d62d70bc",
            "name_localized": "Funcho",
            "scientific_name": "Foeniculum vulgare",
            "drops": 6
          },
          {
            "oil_id": "caef2077-85b5-479f-a828-7b7be804e498",
            "name_localized": "Limão siciliano",
            "scientific_name": "Citrus limon",
            "drops": 6
          },
          {
            "oil_id": "8ffe0ee4-e1bb-44f1-be9b-4a5cf16c6739",
            "name_localized": "Camomila",
            "scientific_name": "Matricaria recutita",
            "drops": 5
          }
        ],
        "carrier_oil": {
          "recommended": {
            "name_localized": "Óleo de coco fracionado",
            "properties_localized": "Óleo de coco fracionado é de rápida absorção, inodoro, e ideal para aplicação tópica, proporcionando boa dispersão dos óleos essenciais e maior estabilidade do blend."
          },
          "alternative": {
            "name_localized": "Óleo de jojoba",
            "properties_localized": "Óleo de jojoba possui excelente compatibilidade com a pele, ótima estabilidade, e pode ser uma alternativa suave para aplicação tópica."
          }
        }
      },
      "therapeutic_properties_targeted": [
        {
          "property_id": "d3b07384-2a6d-4c65-8d6d-859f2b1a16c1",
          "name_localized": "Alívio do desconforto digestivo",
          "description_localized": "Propriedades que auxiliam na digestão, redução de inchaço e calor estomacal."
        },
        {
          "property_id": "d3b07384-2a6d-4c65-8d6d-859f2b1a16c1",
          "name_localized": "Relaxamento e alívio do estresse",
          "description_localized": "Propicia calmaria mental e relaxamento muscular, ajudando a dormir melhor após refeições pesadas."
        }
      ],
      "preparation_summary_localized": "Dilua os óleos essenciais no óleo_carrier, agite suavemente e armazene em frasco escuro. Para uso, diffunda na noite ou aplique topicamente na região do estômago, conforme desejado.",
      "usage_summary_localized": "Use a mistura no período da noite, difundindo no ambiente ou diluindo em uma pequena quantidade na pele na região abdominal, uma a duas vezes por dia, preferencialmente após refeições gordurosas.",
      "preparation_steps_localized": [
        "Meça 8 gotas de Hortelã-pimenta, 6 gotas de Funcho, 6 gotas de Limão siciliano e 5 gotas de Camomila Romanana.",
        "Adicione os óleos essenciais ao frasco de 10ml, completando com óleo de coco fracionado ou jojoba até preencher o frasco.",
        "Agite suavemente para misturar bem todos os óleos.",
        "Armazene em local fresco, escuro e seguro.",
        "Antes da primeira aplicação, realize um teste de sensibilidade na pele."
      ],
      "usage_instructions_localized": [
        {
          "method_code": "topical",
          "method": "Aplicação tópica",
          "description": "Aplique uma pequena quantidade na região abdominal após as refeições gordurosas, sempre diluído conforme guia de diluição. Evite contato com olhos e mucosas. Faça teste de sensibilidade antes do uso regular.",
          "frequency": "1 a 2 vezes ao dia, conforme necessidade."
        },
        {
          "method_code": "diffusion",
          "method": "Difusão ambiental",
          "description": "Adicione 4 a 6 gotas na difusora de ambiente para criar uma atmosfera relaxante e digestiva às noites.",
          "frequency": "Antes de dormir, conforme desejar."
        }
      ],
      "ritual_suggestion_localized": "Ao final de um jantar pesado, inicie uma sessão de difusão ou aplique na região abdominal enquanto respira profundamente, relaxando pelo menos 10 minutos e desfrutando do momento de autocuidado.",
      "oil_rationales": [
        {
          "oil_id": "ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf",
          "name_localized": "Hortelã-pimenta",
          "properties": [
            "Alívio de desconforto digestivo",
            "Efeito refrescante",
            "Estimula a digestão"
          ],
          "rationale_localized": "A hortelã-pimenta é eficaz para aliviar sensação de plenitude, reduzir náuseas e estimular o sistema digestivo, ideal para ajudar após refeições gordurosas."
        },
        {
          "oil_id": "7fd15cfa-5a18-4e12-ba6b-6da0d62d70bc",
          "name_localized": "Funcho",
          "properties": [
            "Auxilia na digestão",
            "Reduz inchaço",
            "Acalma o estômago"
          ],
          "rationale_localized": "O funcho é reconhecido por seus efeitos calmantes na digestão, ajudando a diminuir o desconforto após refeições gordurosas."
        },
        {
          "oil_id": "caef2077-85b5-479f-a828-7b7be804e498",
          "name_localized": "Limão siciliano",
          "properties": [
            "Estimula a digestão",
            "Refrescância e limpeza"
          ],
          "rationale_localized": "O limão siciliano ajuda a estimular a digestão e atua como descongestionante, além de proporcionar um aroma agradável."
        },
        {
          "oil_id": "8ffe0ee4-e1bb-44f1-be9b-4a5cf16c6739",
          "name_localized": "Camomila",
          "properties": [
            "Propriedades calmantes",
            "Redução do estresse",
            "Alívio de inflamações"
          ],
          "rationale_localized": "A camomila promove relaxamento, combate a ansiedade e ajuda a melhorar o sono, bom complemento para uso noturno."
        }
      ]
    }
  },
  "safety_warnings": [
    {
      "type": "Aviso Geral",
      "title_localized": "Precauções Gerais",
      "warning_text_localized": "Evite contato com olhos, mucosas e pele sensível. Realize teste de sensibilidade antes do uso. Mantenha fora do alcance de crianças. Se estiver grávida ou amamentando, consulte um profissional antes do uso."
    },
    {
      "type": "Sensibilidade à luz",
      "title_localized": "Aviso de Fotossensibilidade",
      "warning_text_localized": "Limão siciliano é fototóxico. Evite exposição ao sol ou lâmpadas UV por pelo menos 12 horas após a aplicação tópica para prevenir reações."
    },
    {
      "type": "Gravidez e amamentação",
      "title_localized": "Cuidados na Gravidez e Lactação",
      "warning_text_localized": "Óleos como limão siciliano podem ser usados com cautela ou evitados se houver sensibilidade. Consulte um profissional de saúde antes de usar durante a gravidez ou lactação."
    },
    {
      "type": "Segurança na aplicação tópica",
      "title_localized": "Orientações de Diluição",
      "warning_text_localized": "Dilua os óleos essenciais em óleo carreador de preferência entre 5 a 10%, especialmente para uso na região abdominal. Não aplique na pele irritada ou ferida."
    }
  ],
  "echo": {
    "health_concern_input": "Desconforto digestivo após refeições, especialmente comidas gordurosas",
    "user_info_input": {
      "gender": "female",
      "age_category": "adult",
      "age_specific": "24",
      "age_unit": "anos"
    },
    "selected_cause_ids": [
      "d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b"
    ],
    "selected_symptom_ids": [
      "d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"
    ],
    "suggested_oil_ids": [
      "ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf",
      "7fd15cfa-5a18-4e12-ba6b-6da0d62d70bc",
      "caef2077-85b5-479f-a828-7b7be804e498",
      "8ffe0ee4-e1bb-44f1-be9b-4a5cf16c6739"
    ],
    "time_of_day": "night"
  }
}
 LOG  🔍 [RN-FinalRecipes] ResponseParser received for night : {"finalDataKeys": ["meta", "data", "safety_warnings", "echo"], "hasData": true, "hasFinalData": true}
 LOG  ✅ [RN-FinalRecipes] Recipe received for night : Alívio do desconforto digestivo noturno
 LOG  ✅ [ParallelEngine] Completed stream for night: SUCCESS
 LOG  ✅ [ParallelEngine] Stream result preview: {"hasOils": false, "oilsCount": 0, "resultKeys": ["timeSlot", "recipe"]}
 LOG  🔒 [SSE-ios] Releasing reader lock {"sessionId": "sse-1757172824489-n63lysomc"}
 LOG  📋 [ParallelEngine] Added result for night. Total completed: 2
 LOG  🔍 [MobileSSE] Connection completed
 LOG  📱 [API-ios] Mobile SSE connection closed {"requestId": "api-1757172824474-em5vtq9rl"}
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: true
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 2, "total": 5}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  📊 LAYER 8B: Property Analysis Progress: 2/5 properties completed
 LOG  📊 LAYER 8B: Tool Calls Made: 2 total | Results: 2 | Errors: 0
 LOG  📊 LAYER 8C: Map/Set sizes changed
 LOG  📊 LAYER 8C: Results size: 2
 LOG  📊 LAYER 8C: Errors size: 0
 LOG  🔄 LAYER 9B: Processing individual streaming results with STORE OPERATIONS...
 LOG  🎯 AUTO-COMPLETION: Checking streaming completion:
 LOG  🎯 AUTO-COMPLETION: totalSelected: 5
 LOG  🎯 AUTO-COMPLETION: totalCompleted: 2
 LOG  🎯 AUTO-COMPLETION: isStreaming: true
 LOG  🎯 AUTO-COMPLETION: isSubmitting: false

     \"description\": \"\",
    \"usage_guidance\": \"\",
    \"status_description\": \"pregnancy-safe-100\"
  }
]", "pregnancyNursingType": "object"}
 LOG  🔍 CHILD SAFETY DATA for Júniper berries: {"childSafetyIsArray": true, "childSafetyLength": 0, "childSafetyRaw": "[]", "childSafetyType": "object"}
 LOG  🔍 OIL 8 RAW DATA (Camomila): {"enrichment_status": "enriched", "hasSafety": true, "isEnriched": true, "name_botanical": "Matricaria chamomilla", "name_english": "Camomila", "oil_id": "8af64ea7-75eb-4f4f-a568-4e0b20758060", "safetyRawData": "{
  \"internal_use\": {
    \"id\": \"247f091e-d55c-4a76-aaef-ea4485457b63\",
    \"code\": \"NON_INGESTIBLE\",
    \"name\": \"Not for Internal Use\",
    \"guidance\": null,
    \"description\": \"Essential oils in this category are not recommended or are unsafe for internal consumption. They are intended for topical application (diluted) or aromatic diffusion only.\"
  },
  \"dilution\": {
    \"id\": \"3404c0a7-6158-4236-bbd9-724728538c3d\",
    \"name\": \"[N] Neat\",
    \"ratio\": \"1:1\",
    \"description\": \"Neat. Essential oils categorized as “neat” can be applied topically without dilution on most people. These oils are considered mild and generally do not induce skin sensitivity. Examples of “neat” essential oils are frankincense, lavender, melaleuca, melissa, and sandalwood.\",
    \"percentage_max\": 0.5,
    \"percentage_min\": 0
  },
  \"phototoxicity\": {
    \"id\": \"ae18d720-4473-479e-b9f3-7fa65192d639\",
    \"status\": \"Non-Phototoxic\",
    \"guidance\": \"No specific sun exposure restrictions related to phototoxicity are typically required after topical application. However, always follow general safety guidelines for dilution and application for all essential oils.\",
    \"description\": \"Generally not known to cause phototoxic reactions when skin is exposed to UV light after topical application, when used appropriately.\"
  },
  \"pregnancy_nursing\": [],
  \"child_safety\": [],
  \"internal_use_id\": \"247f091e-d55c-4a76-aaef-ea4485457b63\",
  \"dilution_id\": \"3404c0a7-6158-4236-bbd9-724728538c3d\",
  \"phototoxicity_id\": \"ae18d720-4473-479e-b9f3-7fa65192d639\",
  \"pregnancy_nursing_ids\": [],
  \"child_safety_ids\": []
}"}
 LOG  🔍 PREGNANCY DATA for Camomila: {"pregnancyNursingIsArray": true, "pregnancyNursingKeys": [], "pregnancyNursingLength": 0, "pregnancyNursingRaw": "[]", "pregnancyNursingType": "object"}
 LOG  🔍 CHILD SAFETY DATA for Camomila: {"childSafetyIsArray": true, "childSafetyLength": 0, "childSafetyRaw": "[]", "childSafetyType": "object"}
 LOG  ACTION: updatePropertyWithEnrichedOils
 LOG  PAYLOAD: {"enrichedOils": [{"botanical_name": "", "enrichment_status": "not_found", "enrichment_timestamp": "2025-09-06T15:33:08.293Z", "isEnriched": false, "match_rationale_localized": "Indicado para aliviar azia, gastrite, má digestão e estufamento, auxiliando na regulação do sistema digestivo.", "name_botanical": "Injustice", "name_english": "Injustice", "name_localized": "Injustice", "oil_id": "001a2b3c-4d5e-678f-901a-2b3c4d5e6f78", "relevancy_to_property_score": 5, "search_query": "Injustice - Injustice"}, {"botanical_mismatch": false, "botanical_name": "", "enrichment_status": "enriched", "enrichment_timestamp": "2025-09-06T15:33:08.294Z", "isEnriched": true, "match_rationale_localized": "Auxilia no combate à azia e desconfortos gástricos, promovendo uma sensação de alívio e relaxamento abdominal.", "name_botanical": "Citrus limon", "name_english": "Limão Siciliano", "name_localized": "Limão Siciliano", "name_scientific": "Citrus limon", "oil_id": "caef2077-85b5-479f-a828-7b7be804e498", "relevancy_to_property_score": 4, "safety": [Object], "search_query": "Limão Siciliano - Citrus limon", "similarity_score": 0.878763303609288}, {"botanical_mismatch": false, "botanical_name": "", "enrichment_status": "enriched", "enrichment_timestamp": "2025-09-06T15:33:08.294Z", "isEnriched": true, "match_rationale_localized": "Ajuda na digestão e alívio de cólicas intestinais, promovendo relaxamento do sistema gastrointestinal.", "name_botanical": "Foeniculum vulgare", "name_english": "Funcho", "name_localized": "Funcho", "name_scientific": "Foeniculum vulgare", "oil_id": "7fd15cfa-5a18-4e12-ba6b-6da0d62d70bc", "relevancy_to_property_score": 4, "safety": [Object], "search_query": "Funcho - Foeniculum vulgare", "similarity_score": 0.786786888454804}, {"botanical_mismatch": false, "botanical_name": "", "enrichment_status": "enriched", "enrichment_timestamp": "2025-09-06T15:33:08.294Z", "isEnriched": true, "match_rationale_localized": "Contribui para reduzir náuseas, azia e melhorar a digestão, além de possuir propriedades termogênicas.", "name_botanical": "Zingiber officinale", "name_english": "Gengibre", "name_localized": "Gengibre", "name_scientific": "Zingiber officinale", "oil_id": "56b7e686-5417-4bcf-a975-9888f559a4f3", "relevancy_to_property_score": 4, "safety": [Object], "search_query": "Gengibre - Zingiber officinale", "similarity_score": 0.887758632399914}, {"botanical_mismatch": false, "botanical_name": "", "enrichment_status": "enriched", "enrichment_timestamp": "2025-09-06T15:33:08.295Z", "isEnriched": true, "match_rationale_localized": "Alivia desconfortos gástricos, azia e refluxo, promovendo sensação de frescor e relaxamento abdominal.", "name_botanical": "Mentha piperita", "name_english": "Hortelã-Pimenta", "name_localized": "Hortelã-Pimenta", "name_scientific": "Mentha Piperita", "oil_id": "ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf", "relevancy_to_property_score": 4, "safety": [Object], "search_query": "Hortelã-Pimenta - Mentha piperita", "similarity_score": 0.855765419223793}, {"botanical_mismatch": false, "botanical_name": "", "enrichment_status": "enriched", "enrichment_timestamp": "2025-09-06T15:33:08.295Z", "isEnriched": true, "match_rationale_localized": "Contribui para o relaxamento, alivia o estresse e auxilia na digestão por seu aroma suave e calmante.", "name_botanical": "Citrus reticulata", "name_english": "Tangerina", "name_localized": "Tangerina", "name_scientific": "Citrus reticulata", "oil_id": "0ed154c2-cb91-4449-9b3b-37ffea79c9ac", "relevancy_to_property_score": 3, "safety": [Object], "search_query": "Tangerina - Citrus reticulata", "similarity_score": 0.905911164418848}, {"botanical_mismatch": false, "botanical_name": "", "enrichment_status": "enriched", "enrichment_timestamp": "2025-09-06T15:33:08.296Z", "isEnriched": true, "match_rationale_localized": "Auxilia na digestão e ajuda na redução de inchaço abdominal, além de promover relaxamento.", "name_botanical": "Juniperus communis", "name_english": "Júniper berries", "name_localized": "Bagas de Júniper", "name_scientific": "Juniperus communis", "oil_id": "d354b218-cf17-42aa-8c40-d569ed5b3ab6", "relevancy_to_property_score": 3, "safety": [Object], "search_query": "Júniper berries - Juniperus communis", "similarity_score": 0.753316982987468}, {"botanical_mismatch": true, "botanical_name": "", "enrichment_status": "enriched", "enrichment_timestamp": "2025-09-06T15:33:08.296Z", "isEnriched": true, "match_rationale_localized": "Propriedades calmantes que auxiliam no relaxamento do sistema digestivo e redução do estresse.", "name_botanical": "Matricaria chamomilla", "name_english": "Camomila", "name_localized": "Camomila", "name_scientific": "Matricaria recutita", "oil_id": "8af64ea7-75eb-4f4f-a568-4e0b20758060", "relevancy_to_property_score": 3, "safety": [Object], "search_query": "Camomila - Matricaria chamomilla", "similarity_score": 0.74121630858329}], "propertyId": "c3f4e5a6-b7c8-9012-cd34-e5f678901234"}
 LOG  PRE-UPDATE STATE: [{"addresses_cause_ids": ["d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b"], "addresses_symptom_ids": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "description_contextual_localized": "Auxilia na melhora do processo de digestão, reduzindo o desconforto após refeições gordurosas, promovendo maior equilíbrio no sistema digestivo.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "a1f2c3d4-e5f6-7890-ab12-c3d4e5f67890", "property_name_english": "Digestive", "property_name_localized": "Digestivo", "relevancy_score": 5, "suggested_oils": [[Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object]]}, {"addresses_cause_ids": ["d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b"], "addresses_symptom_ids": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "description_contextual_localized": "Reduz a inflamação no sistema digestivo, ajudando a aliviar o desconforto relacionado ao consumo de alimentos gordurosos.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "b2f3d4e5-f6a7-8901-bc23-d4e5f6789012", "property_name_english": "Anti-inflammatory", "property_name_localized": "Anti-inflamatório", "relevancy_score": 4, "suggested_oils": [[Object], [Object], [Object], [Object], [Object]]}, {"addresses_cause_ids": [], "addresses_symptom_ids": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "description_contextual_localized": "Promove o equilíbrio emocional, ajudando a reduzir o estresse que pode impactar a digestão, promovendo uma sensação de tranquilidade.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "c3f4e5a6-b7c8-9012-cd34-e5f678901234", "property_name_english": "Calming", "property_name_localized": "Calmante", "relevancy_score": 3, "suggested_oils": [[Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object]]}, {"addresses_cause_ids": [], "addresses_symptom_ids": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "description_contextual_localized": "Auxilia na revitalização do sistema digestivo, promovendo maior disposição após refeições, especialmente após ingerir gordura.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "d4f5e6b7-c8d9-0123-de45-f67890123456", "property_name_english": "Energizing", "property_name_localized": "Energizante", "relevancy_score": 2, "suggested_oils": [[Object], [Object], [Object], [Object], [Object]]}, {"addresses_cause_ids": [], "addresses_symptom_ids": [], "description_contextual_localized": "Ajuda a reduzir a ansiedade e o estresse que podem afetar a digestão, promovendo maior relaxamento emocional.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "e5f6a7b8-c9d0-1234-ea56-89ab01234567", "property_name_english": "Nervine Relaxant", "property_name_localized": "Nervino relaxante", "relevancy_score": 2, "suggested_oils": [[Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object]]}]
 LOG  ✅ [recipe-store] Updated property c3f4e5a6-b7c8-9012-cd34-e5f678901234 with 8 oils: {"discarded": 0, "enriched": 7, "not_found": 1}
 LOG  🎯 All properties enriched! Triggering hybrid scoring calculation...
 LOG  🔢 Post-enrichment scoring: {"enrichedProperties": 5, "totalEnrichedOils": 32, "totalOilsAvailable": 34, "totalProperties": 5}
 LOG  🧮 Starting hybrid scoring calculation... {"alpha": 0.6, "gamma": 0.1, "holisticWeight": 0.30000000000000004, "propertiesCount": 5}
 LOG  📊 Scoring thresholds: {"highPriorityThreshold": 5, "maxPropertyScore": 5, "totalUniqueOils": 18}
 LOG  🎯 Oil ad7c9072... hybrid score: {"coverage": "89.4%", "finalScore": "4.75", "holistic": "87.1%", "propertiesCount": 4, "specialization": "100.0%"}
 LOG  🎯 Oil a481ef0a... hybrid score: {"coverage": "44.7%", "finalScore": "3.82", "holistic": "80.0%", "propertiesCount": 1, "specialization": "80.0%"}
 LOG  🎯 Oil 56b7e686... hybrid score: {"coverage": "100.0%", "finalScore": "3.82", "holistic": "81.1%", "propertiesCount": 5, "specialization": "70.0%"}
 LOG  🎯 Oil caef2077... hybrid score: {"coverage": "77.5%", "finalScore": "3.93", "holistic": "76.0%", "propertiesCount": 3, "specialization": "80.0%"}
 LOG  🎯 Oil 7fd15cfa... hybrid score: {"coverage": "89.4%", "finalScore": "4.05", "holistic": "80.0%", "propertiesCount": 4, "specialization": "80.0%"}
 LOG  🎯 Oil 0640a673... hybrid score: {"coverage": "63.2%", "finalScore": "3.02", "holistic": "60.0%", "propertiesCount": 2, "specialization": "60.0%"}
 LOG  🎯 Oil 2a09e83e... hybrid score: {"coverage": "44.7%", "finalScore": "2.92", "holistic": "60.0%", "propertiesCount": 1, "specialization": "60.0%"}
 LOG  🎯 Oil 0ed154c2... hybrid score: {"coverage": "63.2%", "finalScore": "1.22", "holistic": "60.0%", "propertiesCount": 2, "specialization": "0.0%"}
 LOG  🎯 Oil d354b218... hybrid score: {"coverage": "44.7%", "finalScore": "1.12", "holistic": "60.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil 8af64ea7... hybrid score: {"coverage": "44.7%", "finalScore": "1.12", "holistic": "60.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil bb99ae0f... hybrid score: {"coverage": "44.7%", "finalScore": "1.72", "holistic": "100.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil 2aaae25e... hybrid score: {"coverage": "44.7%", "finalScore": "1.42", "holistic": "80.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil d685f4e0... hybrid score: {"coverage": "44.7%", "finalScore": "1.12", "holistic": "60.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil 8ffe0ee4... hybrid score: {"coverage": "44.7%", "finalScore": "1.12", "holistic": "60.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil 5f470712... hybrid score: {"coverage": "44.7%", "finalScore": "1.42", "holistic": "80.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil a3c73ec0... hybrid score: {"coverage": "44.7%", "finalScore": "1.42", "holistic": "80.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil ecc9d982... hybrid score: {"coverage": "44.7%", "finalScore": "1.42", "holistic": "80.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil 9dd00035... hybrid score: {"coverage": "44.7%", "finalScore": "1.42", "holistic": "80.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  ✅ Hybrid scoring completed {"avgScore": 2.270021942304509, "oilsScored": 18}
 LOG  ✅ Hybrid scoring completed: {"safetyLibraryStats": {"child_safety": 0, "dilution": 3, "internal_use": 2, "phototoxicity": 2, "pregnancy_nursing": 4}, "scoredOilsCount": 18, "topOilId": "ad7c9072...", "topOilScore": 4.75}
 LOG  ✅ Hybrid scores applied to all properties! {"oilsWithScores": 18, "propertiesUpdated": 5, "sampleScores": [{"hybridScore": "4.75", "oilId": "ad7c9072...", "specializationScore": "100.0%"}, {"hybridScore": "4.05", "oilId": "7fd15cfa...", "specializationScore": "80.0%"}, {"hybridScore": "3.93", "oilId": "caef2077...", "specializationScore": "80.0%"}]}
 LOG  POST-UPDATE STATE: [{"addresses_cause_ids": ["d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b"], "addresses_symptom_ids": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "description_contextual_localized": "Auxilia na melhora do processo de digestão, reduzindo o desconforto após refeições gordurosas, promovendo maior equilíbrio no sistema digestivo.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "a1f2c3d4-e5f6-7890-ab12-c3d4e5f67890", "property_name_english": "Digestive", "property_name_localized": "Digestivo", "relevancy_score": 5, "suggested_oils": [[Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object]]}, {"addresses_cause_ids": ["d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b"], "addresses_symptom_ids": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "description_contextual_localized": "Reduz a inflamação no sistema digestivo, ajudando a aliviar o desconforto relacionado ao consumo de alimentos gordurosos.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "b2f3d4e5-f6a7-8901-bc23-d4e5f6789012", "property_name_english": "Anti-inflammatory", "property_name_localized": "Anti-inflamatório", "relevancy_score": 4, "suggested_oils": [[Object], [Object], [Object], [Object], [Object]]}, {"addresses_cause_ids": [], "addresses_symptom_ids": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "description_contextual_localized": "Promove o equilíbrio emocional, ajudando a reduzir o estresse que pode impactar a digestão, promovendo uma sensação de tranquilidade.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "c3f4e5a6-b7c8-9012-cd34-e5f678901234", "property_name_english": "Calming", "property_name_localized": "Calmante", "relevancy_score": 3, "suggested_oils": [[Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object]]}, {"addresses_cause_ids": [], "addresses_symptom_ids": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "description_contextual_localized": "Auxilia na revitalização do sistema digestivo, promovendo maior disposição após refeições, especialmente após ingerir gordura.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "d4f5e6b7-c8d9-0123-de45-f67890123456", "property_name_english": "Energizing", "property_name_localized": "Energizante", "relevancy_score": 2, "suggested_oils": [[Object], [Object], [Object], [Object], [Object]]}, {"addresses_cause_ids": [], "addresses_symptom_ids": [], "description_contextual_localized": "Ajuda a reduzir a ansiedade e o estresse que podem afetar a digestão, promovendo maior relaxamento emocional.", "errorLoadingOils": null, "isEnriched": true, "isLoadingOils": false, "property_id": "e5f6a7b8-c9d0-1234-ea56-89ab01234567", "property_name_english": "Nervine Relaxant", "property_name_localized": "Nervino relaxante", "relevancy_score": 2, "suggested_oils": [[Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object]]}]
 LOG  🔍 [CausesSelection] Store subscription debug: {"potentialCausesFromStore": 7, "renderTimestamp": "2025-09-06T15:33:08.198Z", "selectedCausesFromStore": 1}
 LOG  🔍 [CausesSelection] Counter Debug: {"SELECTION_REQUIREMENTS_CAUSES": {"max": 10, "min": 1}, "maxSelection": 10, "minSelection": 1, "selectedCauses": 1, "selectedCausesArray": [{"id": "d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b", "name": "Dieta rica em alimentos gordurosos"}], "selectionCount": 1}
 LOG  🩺 SymptomsSelection Debug: {"canSubmit": true, "selectedCount": 1, "selectedIds": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "totalSymptoms": 8}
 LOG  🎭 SymptomsSelection - Modal State: {"isStreaming": false, "showStreamingModal": false, "streamingItemsCount": 5, "timestamp": "2025-09-06T15:33:08.217Z"}
 LOG  🔍 SymptomsSelection: First symptom data structure: {"explanation": "Sensação de plenitude ou inchaço após refeições ricas em gordura devido ao processamento dificultoso pelo sistema digestivo.", "symptom_id": "d3b07384-2a6d-4c65-8d6d-859f2b1a16c1", "symptom_name": "Sensação de plenitude abdominal após refeições", "symptom_suggestion": "Observe se ocorre após o consumo de alimentos gordurosos"}
 LOG  🔍 SymptomsSelection: Available fields: ["symptom_id", "symptom_name", "symptom_suggestion", "explanation"]
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🔍 getCauseNamesByIds called with UUIDs: ["d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b"]
 LOG  🔍 Available selectedCauses: [{"id":"d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b","name":"Dieta rica em alimentos gordurosos"}]
 LOG  🔍 Looking for cause UUID "d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b": FOUND "Dieta rica em alimentos gordurosos"
 LOG  🔍 getCauseNamesByIds final result: ["Dieta rica em alimentos gordurosos"]
 LOG  🔍 getSymptomNamesByIds called with UUIDs: ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"]
 LOG  🔍 Available selectedSymptoms: [{"id":"d3b07384-2a6d-4c65-8d6d-859f2b1a16c1","name":"Sensação de plenitude abdominal após refeições"}]
 LOG  🔍 Looking for symptom UUID "d3b07384-2a6d-4c65-8d6d-859f2b1a16c1": FOUND "Sensação de plenitude abdominal após refeições"
 LOG  🔍 getSymptomNamesByIds final result: ["Sensação de plenitude abdominal após refeições"]
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🚀 [Properties-FinalRecipes] Generate Final Recipes button clicked
 LOG  ✅ [Properties-FinalRecipes] Starting final recipe generation with 5 enriched properties
 LOG  🚀 [RN-FinalRecipes] Starting parallel final recipe streaming for 3 time slots
 LOG  🚀 [RN-FinalRecipes] Starting parallel final recipe streaming for 3 time slots
 LOG  🌐 [RN-FinalRecipes] Using user language: {"apiLanguage": undefined}
 LOG  ✅ [RN-FinalRecipes] Using 5 enriched properties
 LOG  🔢 [RN-FinalRecipes] Calculating post-enrichment scores...
 LOG  🔢 Post-enrichment scoring: {"enrichedProperties": 5, "totalEnrichedOils": 32, "totalOilsAvailable": 34, "totalProperties": 5}
 LOG  🧮 Starting hybrid scoring calculation... {"alpha": 0.6, "gamma": 0.1, "holisticWeight": 0.30000000000000004, "propertiesCount": 5}
 LOG  📊 Scoring thresholds: {"highPriorityThreshold": 5, "maxPropertyScore": 5, "totalUniqueOils": 18}
 LOG  🎯 Oil ad7c9072... hybrid score: {"coverage": "89.4%", "finalScore": "4.75", "holistic": "87.1%", "propertiesCount": 4, "specialization": "100.0%"}
 LOG  🎯 Oil a481ef0a... hybrid score: {"coverage": "44.7%", "finalScore": "3.82", "holistic": "80.0%", "propertiesCount": 1, "specialization": "80.0%"}
 LOG  🎯 Oil 56b7e686... hybrid score: {"coverage": "100.0%", "finalScore": "3.82", "holistic": "81.1%", "propertiesCount": 5, "specialization": "70.0%"}
 LOG  🎯 Oil caef2077... hybrid score: {"coverage": "77.5%", "finalScore": "3.93", "holistic": "76.0%", "propertiesCount": 3, "specialization": "80.0%"}
 LOG  🎯 Oil 7fd15cfa... hybrid score: {"coverage": "89.4%", "finalScore": "4.05", "holistic": "80.0%", "propertiesCount": 4, "specialization": "80.0%"}
 LOG  🎯 Oil 0640a673... hybrid score: {"coverage": "63.2%", "finalScore": "3.02", "holistic": "60.0%", "propertiesCount": 2, "specialization": "60.0%"}
 LOG  🎯 Oil 2a09e83e... hybrid score: {"coverage": "44.7%", "finalScore": "2.92", "holistic": "60.0%", "propertiesCount": 1, "specialization": "60.0%"}
 LOG  🎯 Oil 0ed154c2... hybrid score: {"coverage": "63.2%", "finalScore": "1.22", "holistic": "60.0%", "propertiesCount": 2, "specialization": "0.0%"}
 LOG  🎯 Oil d354b218... hybrid score: {"coverage": "44.7%", "finalScore": "1.12", "holistic": "60.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil 8af64ea7... hybrid score: {"coverage": "44.7%", "finalScore": "1.12", "holistic": "60.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil bb99ae0f... hybrid score: {"coverage": "44.7%", "finalScore": "1.72", "holistic": "100.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil 2aaae25e... hybrid score: {"coverage": "44.7%", "finalScore": "1.42", "holistic": "80.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil d685f4e0... hybrid score: {"coverage": "44.7%", "finalScore": "1.12", "holistic": "60.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil 8ffe0ee4... hybrid score: {"coverage": "44.7%", "finalScore": "1.12", "holistic": "60.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil 5f470712... hybrid score: {"coverage": "44.7%", "finalScore": "1.42", "holistic": "80.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil a3c73ec0... hybrid score: {"coverage": "44.7%", "finalScore": "1.42", "holistic": "80.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil ecc9d982... hybrid score: {"coverage": "44.7%", "finalScore": "1.42", "holistic": "80.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  🎯 Oil 9dd00035... hybrid score: {"coverage": "44.7%", "finalScore": "1.42", "holistic": "80.0%", "propertiesCount": 1, "specialization": "0.0%"}
 LOG  ✅ Hybrid scoring completed {"avgScore": 2.270021942304509, "oilsScored": 18}
 LOG  ✅ Hybrid scoring completed: {"safetyLibraryStats": {"child_safety": 0, "dilution": 3, "internal_use": 2, "phototoxicity": 2, "pregnancy_nursing": 4}, "scoredOilsCount": 18, "topOilId": "ad7c9072...", "topOilScore": 4.75}
 LOG  ✅ [RN-FinalRecipes] Enhanced data prepared: {"scoredOilsCount": 18, "topOilScore": 4.75, "topOilsCount": 18}
 LOG  📤 [RN-FinalRecipes] Created 3 parallel streaming requests for time slots
 LOG  📤 [RN-FinalRecipes] Created 3 parallel streaming requests for time slots
 LOG  🚀 [ParallelEngine] Starting 3 parallel streaming requests
 LOG  📡 [ParallelEngine] Starting stream for ID: morning (morning recipe)
 LOG  📤 [ParallelEngine] Request data: {"dataKeys": ["health_concern", "gender", "age_category", "age_specific", "user_language", "selected_causes", "selected_symptoms", "time_of_day", "suggested_oils", "safety_library", "safety_library_formatted"], "feature": "create-recipe", "step": "final-recipes"}
 LOG  ⏳ [ParallelEngine] Delaying stream 2/3 by 3000ms to avoid rate limiting
 LOG  ⏳ [ParallelEngine] Delaying stream 3/3 by 6000ms to avoid rate limiting
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: true
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 5}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  📊 LAYER 8B: Property Analysis Progress: 0/5 properties completed
 LOG  📊 LAYER 8B: Tool Calls Made: 0 total | Results: 0 | Errors: 0
 LOG  📊 LAYER 8C: Map/Set sizes changed
 LOG  📊 LAYER 8C: Results size: 0
 LOG  📊 LAYER 8C: Errors size: 0
 LOG  🎯 AUTO-COMPLETION: Checking streaming completion:
 LOG  🎯 AUTO-COMPLETION: totalSelected: 5
 LOG  🎯 AUTO-COMPLETION: totalCompleted: 0
 LOG  🎯 AUTO-COMPLETION: isStreaming: true
 LOG  🎯 AUTO-COMPLETION: isSubmitting: false
 LOG  🚀 [API-ios] Starting streaming request {"bodySize": 58514, "feature": "create-recipe", "headers": ["Content-Type", "Authorization"], "platform": "ios", "requestId": "api-1757172818599-vg0t466z5", "requestPreview": "{\"feature\":\"create-recipe\",\"step\":\"final-recipes\",\"data\":{\"health_concern\":\"Desconforto digestivo após refeições, especialmente comidas gordurosas\",\"gender\":\"female\",\"age_category\":\"adult\",\"age_specif...", "step": "final-recipes", "timestamp": "2025-09-06T15:33:38.599Z", "url": "https://novo.rotinanatural.com.br/api/ai/streaming"}
 LOG  📱 [API-ios] Using unified mobile SSE client for platform: ios {"requestId": "api-1757172818599-vg0t466z5"}
 LOG  📱 [API-ios] Creating mobile streaming response {"requestId": "api-1757172818599-vg0t466z5"}
 LOG  📱 [API-ios] Starting mobile ReadableStream {"requestId": "api-1757172818599-vg0t466z5"}
 LOG  � [MobiloeSSE] Sending request: {"bodyLength": 58514, "headers": {"Authorization": "Bearer zh6Xs8GAHi8TlX8LWchb3WQ7ouneL3gn8aL84CLL1x7J0rKOKr", "Content-Type": "application/json"}, "url": "https://novo.rotinanatural.com.br/api/ai/streaming"}
 LOG  📱 [API-ios] Mobile streaming response created {"bodyType": "ReadableStream", "hasBody": true, "requestId": "api-1757172818599-vg0t466z5"}
 LOG  🔍 [SSE-ios] Starting SSE stream reader {"platform": "ios", "sessionId": "sse-1757172818602-ep16grrc6", "streamType": "ReadableStream", "timestamp": "2025-09-06T15:33:38.602Z"}
 LOG  📥 [SSE-ios] Reading chunk 1 {"sessionId": "sse-1757172818602-ep16grrc6"}
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: true
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 5}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  📡 [ParallelEngine] Starting stream for ID: mid-day (mid-day recipe)
 LOG  📤 [ParallelEngine] Request data: {"dataKeys": ["health_concern", "gender", "age_category", "age_specific", "user_language", "selected_causes", "selected_symptoms", "time_of_day", "suggested_oils", "safety_library", "safety_library_formatted"], "feature": "create-recipe", "step": "final-recipes"}
 LOG  🚀 [API-ios] Starting streaming request {"bodySize": 58514, "feature": "create-recipe", "headers": ["Content-Type", "Authorization"], "platform": "ios", "requestId": "api-1757172821473-7zsr773h8", "requestPreview": "{\"feature\":\"create-recipe\",\"step\":\"final-recipes\",\"data\":{\"health_concern\":\"Desconforto digestivo após refeições, especialmente comidas gordurosas\",\"gender\":\"female\",\"age_category\":\"adult\",\"age_specif...", "step": "final-recipes", "timestamp": "2025-09-06T15:33:41.475Z", "url": "https://novo.rotinanatural.com.br/api/ai/streaming"}
 LOG  📱 [API-ios] Using unified mobile SSE client for platform: ios {"requestId": "api-1757172821473-7zsr773h8"}
 LOG  📱 [API-ios] Creating mobile streaming response {"requestId": "api-1757172821473-7zsr773h8"}
 LOG  📱 [API-ios] Starting mobile ReadableStream {"requestId": "api-1757172821473-7zsr773h8"}
 LOG  � [MobiloeSSE] Sending request: {"bodyLength": 58514, "headers": {"Authorization": "Bearer zh6Xs8GAHi8TlX8LWchb3WQ7ouneL3gn8aL84CLL1x7J0rKOKr", "Content-Type": "application/json"}, "url": "https://novo.rotinanatural.com.br/api/ai/streaming"}
 LOG  📱 [API-ios] Mobile streaming response created {"bodyType": "ReadableStream", "hasBody": true, "requestId": "api-1757172821473-7zsr773h8"}
 LOG  🔍 [SSE-ios] Starting SSE stream reader {"platform": "ios", "sessionId": "sse-1757172821487-sr5zeou2y", "streamType": "ReadableStream", "timestamp": "2025-09-06T15:33:41.487Z"}
 LOG  📥 [SSE-ios] Reading chunk 1 {"sessionId": "sse-1757172821487-sr5zeou2y"}
 LOG  📡 [ParallelEngine] Starting stream for ID: night (night recipe)
 LOG  📤 [ParallelEngine] Request data: {"dataKeys": ["health_concern", "gender", "age_category", "age_specific", "user_language", "selected_causes", "selected_symptoms", "time_of_day", "suggested_oils", "safety_library", "safety_library_formatted"], "feature": "create-recipe", "step": "final-recipes"}
 LOG  🚀 [API-ios] Starting streaming request {"bodySize": 58512, "feature": "create-recipe", "headers": ["Content-Type", "Authorization"], "platform": "ios", "requestId": "api-1757172824474-em5vtq9rl", "requestPreview": "{\"feature\":\"create-recipe\",\"step\":\"final-recipes\",\"data\":{\"health_concern\":\"Desconforto digestivo após refeições, especialmente comidas gordurosas\",\"gender\":\"female\",\"age_category\":\"adult\",\"age_specif...", "step": "final-recipes", "timestamp": "2025-09-06T15:33:44.476Z", "url": "https://novo.rotinanatural.com.br/api/ai/streaming"}
 LOG  📱 [API-ios] Using unified mobile SSE client for platform: ios {"requestId": "api-1757172824474-em5vtq9rl"}
 LOG  📱 [API-ios] Creating mobile streaming response {"requestId": "api-1757172824474-em5vtq9rl"}
 LOG  📱 [API-ios] Starting mobile ReadableStream {"requestId": "api-1757172824474-em5vtq9rl"}
 LOG  � [MobiloeSSE] Sending request: {"bodyLength": 58512, "headers": {"Authorization": "Bearer zh6Xs8GAHi8TlX8LWchb3WQ7ouneL3gn8aL84CLL1x7J0rKOKr", "Content-Type": "application/json"}, "url": "https://novo.rotinanatural.com.br/api/ai/streaming"}
 LOG  📱 [API-ios] Mobile streaming response created {"bodyType": "ReadableStream", "hasBody": true, "requestId": "api-1757172824474-em5vtq9rl"}
 LOG  🔍 [SSE-ios] Starting SSE stream reader {"platform": "ios", "sessionId": "sse-1757172824489-n63lysomc", "streamType": "ReadableStream", "timestamp": "2025-09-06T15:33:44.489Z"}
 LOG  📥 [SSE-ios] Reading chunk 1 {"sessionId": "sse-1757172824489-n63lysomc"}
 LOG  🔍 [MobileSSE] Headers received: {"contentType": "text/event-stream", "headers": {"access-control-allow-headers": "Content-Type, Authorization", "access-control-allow-methods": "GET, POST, OPTIONS", "access-control-allow-origin": "*", "access-control-max-age": "86400", "alt-svc": "h3=\":443\"; ma=2592000", "cache-control": "no-cache", "content-encoding": "br", "content-type": "text/event-stream", "date": "Sat, 06 Sep 2025 15:33:55 GMT", "vary": "Accept-Encoding, RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "x-clerk-auth-message": "Invalid JWT form. A JWT consists of three parts separated by dots. (reason=token-invalid, token-carrier=header)", "x-clerk-auth-reason": "token-invalid", "x-clerk-auth-status": "signed-out", "x-middleware-rewrite": "/api/ai/streaming"}, "status": 200}
 LOG  📱 [API-ios] Mobile SSE connection opened {"requestId": "api-1757172818599-vg0t466z5"}
 LOG  🔍 [MobileSSE] New data chunk: {"chunkLength": 8874, "chunkPreview": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RECIPE RESPONSE\",\"request_id\""}
 LOG  🔍 [MobileSSE] Processing message block: {"messageLength": 8872, "messagePreview": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RECIPE RESPONSE\",\"request_id\":\"20231012-0012-ABCD-1234-EF56789ABCD0\",\"timestamp"}
 LOG  🔍 [MobileSSE] Yielding data: {"dataLength": 8866, "dataPreview": "{\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RECIPE RESPONSE\",\"request_id\":\"2023"}
 LOG  📱 [API-ios] Mobile SSE message received {"dataLength": 8866, "dataPreview": "{\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RECIPE RESPONSE\",\"request_id\":\"2023", "requestId": "api-1757172818599-vg0t466z5"}
 LOG  📦 [SSE-ios] Chunk 1 received {"bufferLengthBefore": 0, "chunkPreview": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RECIPE RESPONSE\",\"request_id\"...", "chunkSize": 9121, "sessionId": "sse-1757172818602-ep16grrc6", "totalBytes": 9121}
 LOG  🔄 [SSE-ios] Buffer updated {"bufferLength": 8874, "bufferPreview": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RECIPE RESPONSE\",\"request_id\":\"20231012-0012-ABCD-1234-EF56789ABCD0\",\"timestamp_utc\":\"2023-10-12T15:20:00Z\",\"version\":\"1.0\",\"user...", "sessionId": "sse-1757172818602-ep16grrc6"}
 LOG  🔍 [SSE-ios] Processing message block 1 {"messageBlock": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RECIPE RESPONSE\",\"request_id\":\"20231012-0012-ABCD-1234-EF56789ABCD0\",\"timestamp...", "remainingBufferLength": 0, "sessionId": "sse-1757172818602-ep16grrc6"}
 LOG  📤 [SSE-ios] Yielding data message 1 {"data": "{\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RECIPE RESPONSE\",\"request_id\":\"2023...", "dataLength": 8866, "fullLine": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RECIPE RESPONSE\",\"request_id\":\"20231012-0012-ABCD-1234-EF56789ABCD0\",\"timestamp...", "sessionId": "sse-1757172818602-ep16grrc6"}
 LOG  🏁 [ParallelEngine] Processing structured_complete for morning
 LOG  📋 [ParallelEngine] Complete data keys: ["meta", "data", "safety_warnings", "echo"]
 LOG  📋 [ParallelEngine] Data structure preview: {"dataKeys": ["meta", "data", "safety_warnings", "echo"], "firstLevelData": {"data": "object", "echo": "object", "meta": "object", "safety_warnings": "object"}, "hasSuggestedOils": false, "hasTherapeuticPropertyContext": false}
 LOG  🔍 [RN-FinalRecipes] RAW API RESPONSE for morning : {
  "meta": {
    "step_name": "FINAL RECIPE RESPONSE",
    "request_id": "20231012-0012-ABCD-1234-EF56789ABCD0",
    "timestamp_utc": "2023-10-12T15:20:00Z",
    "version": "1.0",
    "user_language": "PT_BR",
    "status": "success",
    "message": "Recipe successfully generated based on user profile and safety data."
  },
  "data": {
    "recipe_protocol": {
      "recipe_id": "d2f1a4c8-8e3b-4b0f-89d6-12e349f7a963",
      "time_slot": "morning",
      "time_of_day_localized": "manhã",
      "time_range_localized": "06:00 - 09:00",
      "recipe_theme_localized": "Alívio digestivo matinal",
      "description_localized": "Uma mistura terapêutica especialmente formulada para aliviar o desconforto digestivo causado por refeições gordurosas, promovendo bem-estar e equilíbrio digestivo ao iniciar o dia.",
      "holistic_benefit_localized": "Este remédio aromático apoia a saúde digestiva, reduz a sensação de plenitude e inchaço após refeições ricas em gordura, além de promover sensação de leveza e energia matinal.",
      "application_type_localized": "difusão no ambiente e aplicação tópica leve na região abdominal",
      "synergy_rationale_localized": "A combinação de hortelã-pimenta, funcho e limão siciliano atua sinergicamente para estimular a digestão, reduzir inflamações e aliviar a sensação de peso na barriga, proporcionando um início de dia mais confortável.",
      "formulation": {
        "total_drops": 25,
        "dilution_percentage": 1.5,
        "bottle_size_ml": 10,
        "bottle_type_localized": "frasco de vidro com conta-gotas"
      },
      "ingredients": {
        "essential_oils": [
          {
            "oil_id": "ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf",
            "name_localized": "Hortelã-pimenta",
            "scientific_name": "Mentha Piperita",
            "drops": 10
          },
          {
            "oil_id": "7fd15cfa-5a18-4e12-ba6b-6da0d62d70bc",
            "name_localized": "Funcho",
            "scientific_name": "Foeniculum vulgare",
            "drops": 8
          },
          {
            "oil_id": "caef2077-85b5-479f-a828-7b7be804e498",
            "name_localized": "Limão siciliano",
            "scientific_name": "Citrus limon",
            "drops": 7
          }
        ],
        "carrier_oil": {
          "recommended": {
            "name_localized": "Azeite de oliva extra virgem",
            "properties_localized": "Óleo vegetal de absorção suave, com propriedades calmantes e antioxidantes, ideal para aplicação abdominal."
          },
          "alternative": {
            "name_localized": "Óleo de amêndoas doces",
            "properties_localized": "Óleo vegetal leve, de rápida absorção, que facilita a aplicação tópica e potencializa a absorção dos óleos essenciais."
          }
        }
      },
      "therapeutic_properties_targeted": [
        {
          "property_id": "a1b2c3d4-e5f6-7890-ab12-c3d4e5f67890",
          "name_localized": "Alívio de desconforto digestivo",
          "description_localized": "Estimula a digestão, reduz inchaço e sensação de plenitude após refeições gordurosas."
        }
      ],
      "preparation_summary_localized": "Diluir os óleos essenciais selecionados em 10ml de azeite de oliva extra virgem, usando 2,5% de diluição (25 gotas no total). Agitar bem para misturar homogeneamente.",
      "usage_summary_localized": "Aplicar uma pequena quantidade na região abdominal (below the navel) duas vezes ao dia, pela manhã e após o almoço, massageando suavemente até completa absorção. Pode-se também usar um difusor de ambiente na manhã para criar um ambiente aromático relaxante e estimulante para o sistema digestivo.",
      "preparation_steps_localized": [
        "Adicionar as 10 gotas de hortelã-pimenta, 8 gotas de funcho e 7 gotas de limão siciliano em uma máquina de dispensar ou frasco de 10ml com conta-gotas.",
        "Preencher o frasco com 10ml de azeite de oliva extra virgem até completar, segurando o frasco na posição vertical e agitando suavemente para homogeneizar a mistura.",
        "Etiquetar o frasco com a data de preparação e uso recomendado, guardando em local fresco e ao abrigo da luz direta."
      ],
      "usage_instructions_localized": [
        {
          "method_code": "topical_sigh",
          "method": "massagem na região abdominal",
          "description": "Aplicar uma pequena quantidade do óleo diluído na região abdominal, com movimentos suaves e circulares, duas vezes ao dia, preferencialmente após as refeições.",
          "frequency": "duas vezes ao dia"
        },
        {
          "method_code": "diffusion",
          "method": "difusão aromática",
          "description": "Adicione 3-5 gotas do óleo essencial diluído no difusor na manhã para criar um ambiente aromático que estimula a digestão e promove o bem-estar.",
          "frequency": "uma vez ao dia, de manhã"
        }
      ],
      "ritual_suggestion_localized": "Comece seu dia com uma respiração profunda do aroma de hortelã-pimenta e funcho no difusor, enquanto massageia suavemente sua região abdominal após o café da manhã, criando um momento de cuidado e atenção plena para promover sua digestão e bem-estar. Aproveite para saborear sua manhã com uma atitude positiva e acolhedora.",
      "oil_rationales": [
        {
          "oil_id": "ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf",
          "name_localized": "Hortelã-pimenta",
          "properties": [
            "Alívio de desconforto digestivo",
            "Estimula a digestão",
            "Reduz inchaço",
            "Sensação de frescor"
          ],
          "rationale_localized": "A hortelã-pimenta é reconhecida por sua ação rápida no alívio de desconfortos digestivos, auxiliando a acalmar o sistema gastrointestinal e proporcionando sensação de alívio e frescor, ideal para começar o dia com energia positiva."
        },
        {
          "oil_id": "7fd15cfa-5a18-4e12-ba6b-6da0d62d70bc",
          "name_localized": "Funcho",
          "properties": [
            "Estimula a digestão",
            "Possui propriedades anti-inflamatórias",
            "Ajuda a reduzir o inchaço abdominal"
          ],
          "rationale_localized": "O funcho é conhecido por promover uma digestão mais eficiente, aliviar gases e inchaço, além de atuar de forma calmante na mucosa gastrointestinal, complementando a ação da hortelã-pimenta."
        },
        {
          "oil_id": "caef2077-85b5-479f-a828-7b7be804e498",
          "name_localized": "Limão siciliano",
          "properties": [
            "Aumenta a circulação sanguínea",
            "Refrescante e energizante",
            "Estimulante da digestão"
          ],
          "rationale_localized": "O limão siciliano traz uma nota revitalizante, ajuda a estimular a secreção de enzimas digestivas e promove sensação de leveza logo pela manhã, reforçando o efeito positivo da combinação."
        },
        {
          "oil_id": "ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf",
          "name_localized": "Hortelã-pimenta",
          "properties": [
            "Alívio de desconforto digestivo",
            "Estimula a digestão",
            "Reduz inchaço",
            "Sensação de frescor"
          ],
          "rationale_localized": "A hortelã-pimenta é reconhecida por sua ação rápida no alívio de desconfortos digestivos, auxiliando a acalmar o sistema gastrointestinal e proporcionando sensação de alívio e frescor, ideal para começar o dia com energia positiva."
        },
        {
          "oil_id": "7fd15cfa-5a18-4e12-ba6b-6da0d62d70bc",
          "name_localized": "Funcho",
          "properties": [
            "Estimula a digestão",
            "Possui propriedades anti-inflamatórias",
            "Ajuda a reduzir o inchaço abdominal"
          ],
          "rationale_localized": "O funcho é conhecido por promover uma digestão mais eficiente, aliviar gases e inchaço, além de atuar de forma calmante na mucosa gastrointestinal, complementando a ação da hortelã-pimenta."
        },
        {
          "oil_id": "caef2077-85b5-479f-a828-7b7be804e498",
          "name_localized": "Limão siciliano",
          "properties": [
            "Aumenta a circulação sanguínea",
            "Refrescante e energizante",
            "Estimulante da digestão"
          ],
          "rationale_localized": "O limão siciliano traz uma nota revitalizante, ajuda a estimular a secreção de enzimas digestivas e promove sensação de leveza logo pela manhã, reforçando o efeito positivo da combinação."
        }
      ]
    }
  },
  "safety_warnings": [
    {
      "type": "Precauções gerais",
      "title_localized": "Uso Seguro de Óleos Essenciais",
      "warning_text_localized": "Este blend foi formulado com óleos essenciais considerados seguros para uso interno sob orientação adequada, além de ser diluído para evitar sensibilidades ou efeitos adversos. No entanto, é importante evitar contato com olhos e mucosas, manter fora do alcance de crianças, e suspender uso em caso de irritação. Consulte sempre um profissional de saúde ou aromaterapeuta se estiver grávida, amamentando ou com condições médicas especiais."
    },
    {
      "type": "Aviso sobre fototoxicidade",
      "title_localized": "Proteção Solar após Aplicação Tópica",
      "warning_text_localized": "O óleo de limão (Citrus limon) é fototóxico. Evitar exposição ao sol na região de aplicação por pelo menos 12 horas após o uso tópico para prevenir queimaduras ou manchas. Prefira uso noturno ou áreas cobertas."
    },
    {
      "type": "Precauções na gravidez e lactação",
      "title_localized": "Gravidez e Amamentação",
      "warning_text_localized": "O óleo de limão siciliano é considerado seguro para uso durante gravidez e lactação, desde que usado na diluição adequada. Óleos como hortelã-pimenta devem ser utilizados com moderação e sempre sob orientação profissional durante esses períodos."
    }
  ],
  "echo": {
    "health_concern_input": "Desconforto digestivo após refeições, especialmente comidas gordurosas",
    "user_info_input": {
      "gender": "female",
      "age_category": "adult",
      "age_specific": "24",
      "age_unit": "anos"
    },
    "selected_cause_ids": [
      "d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b"
    ],
    "selected_symptom_ids": [
      "d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"
    ],
    "suggested_oil_ids": [
      "ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf",
      "7fd15cfa-5a18-4e12-ba6b-6da0d62d70bc",
      "caef2077-85b5-479f-a828-7b7be804e498"
    ],
    "time_of_day": "manhã"
  }
}
 LOG  🔍 [RN-FinalRecipes] ResponseParser received for morning : {"finalDataKeys": ["meta", "data", "safety_warnings", "echo"], "hasData": true, "hasFinalData": true}
 LOG  ✅ [RN-FinalRecipes] Recipe received for morning : Alívio digestivo matinal
 LOG  ✅ [ParallelEngine] Completed stream for morning: SUCCESS
 LOG  ✅ [ParallelEngine] Stream result preview: {"hasOils": false, "oilsCount": 0, "resultKeys": ["timeSlot", "recipe"]}
 LOG  🔒 [SSE-ios] Releasing reader lock {"sessionId": "sse-1757172818602-ep16grrc6"}
 LOG  📋 [ParallelEngine] Added result for morning. Total completed: 1
 LOG  🔍 [MobileSSE] Connection completed
 LOG  📱 [API-ios] Mobile SSE connection closed {"requestId": "api-1757172818599-vg0t466z5"}
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: true
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 1, "total": 5}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  📊 LAYER 8B: Property Analysis Progress: 1/5 properties completed
 LOG  📊 LAYER 8B: Tool Calls Made: 1 total | Results: 1 | Errors: 0
 LOG  📊 LAYER 8C: Map/Set sizes changed
 LOG  📊 LAYER 8C: Results size: 1
 LOG  📊 LAYER 8C: Errors size: 0
 LOG  🔄 LAYER 9B: Processing individual streaming results with STORE OPERATIONS...
 LOG  🎯 AUTO-COMPLETION: Checking streaming completion:
 LOG  🎯 AUTO-COMPLETION: totalSelected: 5
 LOG  🎯 AUTO-COMPLETION: totalCompleted: 1
 LOG  🎯 AUTO-COMPLETION: isStreaming: true
 LOG  🎯 AUTO-COMPLETION: isSubmitting: false
 LOG  🔍 [MobileSSE] Headers received: {"contentType": "text/event-stream", "headers": {"access-control-allow-headers": "Content-Type, Authorization", "access-control-allow-methods": "GET, POST, OPTIONS", "access-control-allow-origin": "*", "access-control-max-age": "86400", "alt-svc": "h3=\":443\"; ma=2592000", "cache-control": "no-cache", "content-encoding": "br", "content-type": "text/event-stream", "date": "Sat, 06 Sep 2025 15:34:00 GMT", "vary": "Accept-Encoding, RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "x-clerk-auth-message": "Invalid JWT form. A JWT consists of three parts separated by dots. (reason=token-invalid, token-carrier=header)", "x-clerk-auth-reason": "token-invalid", "x-clerk-auth-status": "signed-out", "x-middleware-rewrite": "/api/ai/streaming"}, "status": 200}
 LOG  📱 [API-ios] Mobile SSE connection opened {"requestId": "api-1757172824474-em5vtq9rl"}
 LOG  🔍 [MobileSSE] New data chunk: {"chunkLength": 7553, "chunkPreview": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RESPONSE\",\"request_id\":\"2023F"}
 LOG  🔍 [MobileSSE] Processing message block: {"messageLength": 7551, "messagePreview": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RESPONSE\",\"request_id\":\"2023F163-AC7B-4C72-B163-AC7B4C72B163\",\"timestamp_utc\":\""}
 LOG  🔍 [MobileSSE] Yielding data: {"dataLength": 7545, "dataPreview": "{\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RESPONSE\",\"request_id\":\"2023F163-AC"}
 LOG  📱 [API-ios] Mobile SSE message received {"dataLength": 7545, "dataPreview": "{\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RESPONSE\",\"request_id\":\"2023F163-AC", "requestId": "api-1757172824474-em5vtq9rl"}
 LOG  📦 [SSE-ios] Chunk 1 received {"bufferLengthBefore": 0, "chunkPreview": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RESPONSE\",\"request_id\":\"2023F...", "chunkSize": 7707, "sessionId": "sse-1757172824489-n63lysomc", "totalBytes": 7707}
 LOG  🔄 [SSE-ios] Buffer updated {"bufferLength": 7553, "bufferPreview": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RESPONSE\",\"request_id\":\"2023F163-AC7B-4C72-B163-AC7B4C72B163\",\"timestamp_utc\":\"2023-10-02T14:48:00Z\",\"version\":\"1.0\",\"user_langua...", "sessionId": "sse-1757172824489-n63lysomc"}
 LOG  🔍 [SSE-ios] Processing message block 1 {"messageBlock": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RESPONSE\",\"request_id\":\"2023F163-AC7B-4C72-B163-AC7B4C72B163\",\"timestamp_utc\":\"...", "remainingBufferLength": 0, "sessionId": "sse-1757172824489-n63lysomc"}
 LOG  📤 [SSE-ios] Yielding data message 1 {"data": "{\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RESPONSE\",\"request_id\":\"2023F163-AC...", "dataLength": 7545, "fullLine": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RESPONSE\",\"request_id\":\"2023F163-AC7B-4C72-B163-AC7B4C72B163\",\"timestamp_utc\":\"...", "sessionId": "sse-1757172824489-n63lysomc"}
 LOG  🏁 [ParallelEngine] Processing structured_complete for night
 LOG  📋 [ParallelEngine] Complete data keys: ["meta", "data", "safety_warnings", "echo"]
 LOG  📋 [ParallelEngine] Data structure preview: {"dataKeys": ["meta", "data", "safety_warnings", "echo"], "firstLevelData": {"data": "object", "echo": "object", "meta": "object", "safety_warnings": "object"}, "hasSuggestedOils": false, "hasTherapeuticPropertyContext": false}
 LOG  🔍 [RN-FinalRecipes] RAW API RESPONSE for night : {
  "meta": {
    "step_name": "FINAL RESPONSE",
    "request_id": "2023F163-AC7B-4C72-B163-AC7B4C72B163",
    "timestamp_utc": "2023-10-02T14:48:00Z",
    "version": "1.0",
    "user_language": "PT_BR",
    "status": "success",
    "message": "Recipe generated successfully based on detailed user profile and health concern."
  },
  "data": {
    "recipe_protocol": {
      "recipe_id": "ADFC8E12-4F89-4E21-B681-3A967D1E1B0D",
      "time_slot": "night",
      "time_of_day_localized": "noite",
      "time_range_localized": "19h - 22h",
      "recipe_theme_localized": "Alívio do desconforto digestivo noturno",
      "description_localized": "Um ritual relaxante e digestivo para ajudar a aliviar o desconforto digestivo após refeições gordurosas, promovendo relaxamento e bem-estar à noite.",
      "holistic_benefit_localized": "Alivia o desconforto digestivo, promove relaxamento e melhora a qualidade do sono.",
      "application_type_localized": "difusão e aplicação tópica",
      "synergy_rationale_localized": "A combinação de Hortelã-pimenta, Funcho, Limão Siciliano e Camomila Romanana atua sinergicamente para aliviar a sensação de plenitude abdominal, melhorar a digestão, reduzir o inchaço e proporcionar um efeito calmante na noite.",
      "formulation": {
        "total_drops": 25,
        "dilution_percentage": 0.2,
        "bottle_size_ml": 10,
        "bottle_type_localized": "frasco de vidro âmbar 10ml"
      },
      "ingredients": {
        "essential_oils": [
          {
            "oil_id": "ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf",
            "name_localized": "Hortelã-pimenta",
            "scientific_name": "Mentha Piperita",
            "drops": 8
          },
          {
            "oil_id": "7fd15cfa-5a18-4e12-ba6b-6da0d62d70bc",
            "name_localized": "Funcho",
            "scientific_name": "Foeniculum vulgare",
            "drops": 6
          },
          {
            "oil_id": "caef2077-85b5-479f-a828-7b7be804e498",
            "name_localized": "Limão siciliano",
            "scientific_name": "Citrus limon",
            "drops": 6
          },
          {
            "oil_id": "8ffe0ee4-e1bb-44f1-be9b-4a5cf16c6739",
            "name_localized": "Camomila",
            "scientific_name": "Matricaria recutita",
            "drops": 5
          }
        ],
        "carrier_oil": {
          "recommended": {
            "name_localized": "Óleo de coco fracionado",
            "properties_localized": "Óleo de coco fracionado é de rápida absorção, inodoro, e ideal para aplicação tópica, proporcionando boa dispersão dos óleos essenciais e maior estabilidade do blend."
          },
          "alternative": {
            "name_localized": "Óleo de jojoba",
            "properties_localized": "Óleo de jojoba possui excelente compatibilidade com a pele, ótima estabilidade, e pode ser uma alternativa suave para aplicação tópica."
          }
        }
      },
      "therapeutic_properties_targeted": [
        {
          "property_id": "d3b07384-2a6d-4c65-8d6d-859f2b1a16c1",
          "name_localized": "Alívio do desconforto digestivo",
          "description_localized": "Propriedades que auxiliam na digestão, redução de inchaço e calor estomacal."
        },
        {
          "property_id": "d3b07384-2a6d-4c65-8d6d-859f2b1a16c1",
          "name_localized": "Relaxamento e alívio do estresse",
          "description_localized": "Propicia calmaria mental e relaxamento muscular, ajudando a dormir melhor após refeições pesadas."
        }
      ],
      "preparation_summary_localized": "Dilua os óleos essenciais no óleo_carrier, agite suavemente e armazene em frasco escuro. Para uso, diffunda na noite ou aplique topicamente na região do estômago, conforme desejado.",
      "usage_summary_localized": "Use a mistura no período da noite, difundindo no ambiente ou diluindo em uma pequena quantidade na pele na região abdominal, uma a duas vezes por dia, preferencialmente após refeições gordurosas.",
      "preparation_steps_localized": [
        "Meça 8 gotas de Hortelã-pimenta, 6 gotas de Funcho, 6 gotas de Limão siciliano e 5 gotas de Camomila Romanana.",
        "Adicione os óleos essenciais ao frasco de 10ml, completando com óleo de coco fracionado ou jojoba até preencher o frasco.",
        "Agite suavemente para misturar bem todos os óleos.",
        "Armazene em local fresco, escuro e seguro.",
        "Antes da primeira aplicação, realize um teste de sensibilidade na pele."
      ],
      "usage_instructions_localized": [
        {
          "method_code": "topical",
          "method": "Aplicação tópica",
          "description": "Aplique uma pequena quantidade na região abdominal após as refeições gordurosas, sempre diluído conforme guia de diluição. Evite contato com olhos e mucosas. Faça teste de sensibilidade antes do uso regular.",
          "frequency": "1 a 2 vezes ao dia, conforme necessidade."
        },
        {
          "method_code": "diffusion",
          "method": "Difusão ambiental",
          "description": "Adicione 4 a 6 gotas na difusora de ambiente para criar uma atmosfera relaxante e digestiva às noites.",
          "frequency": "Antes de dormir, conforme desejar."
        }
      ],
      "ritual_suggestion_localized": "Ao final de um jantar pesado, inicie uma sessão de difusão ou aplique na região abdominal enquanto respira profundamente, relaxando pelo menos 10 minutos e desfrutando do momento de autocuidado.",
      "oil_rationales": [
        {
          "oil_id": "ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf",
          "name_localized": "Hortelã-pimenta",
          "properties": [
            "Alívio de desconforto digestivo",
            "Efeito refrescante",
            "Estimula a digestão"
          ],
          "rationale_localized": "A hortelã-pimenta é eficaz para aliviar sensação de plenitude, reduzir náuseas e estimular o sistema digestivo, ideal para ajudar após refeições gordurosas."
        },
        {
          "oil_id": "7fd15cfa-5a18-4e12-ba6b-6da0d62d70bc",
          "name_localized": "Funcho",
          "properties": [
            "Auxilia na digestão",
            "Reduz inchaço",
            "Acalma o estômago"
          ],
          "rationale_localized": "O funcho é reconhecido por seus efeitos calmantes na digestão, ajudando a diminuir o desconforto após refeições gordurosas."
        },
        {
          "oil_id": "caef2077-85b5-479f-a828-7b7be804e498",
          "name_localized": "Limão siciliano",
          "properties": [
            "Estimula a digestão",
            "Refrescância e limpeza"
          ],
          "rationale_localized": "O limão siciliano ajuda a estimular a digestão e atua como descongestionante, além de proporcionar um aroma agradável."
        },
        {
          "oil_id": "8ffe0ee4-e1bb-44f1-be9b-4a5cf16c6739",
          "name_localized": "Camomila",
          "properties": [
            "Propriedades calmantes",
            "Redução do estresse",
            "Alívio de inflamações"
          ],
          "rationale_localized": "A camomila promove relaxamento, combate a ansiedade e ajuda a melhorar o sono, bom complemento para uso noturno."
        }
      ]
    }
  },
  "safety_warnings": [
    {
      "type": "Aviso Geral",
      "title_localized": "Precauções Gerais",
      "warning_text_localized": "Evite contato com olhos, mucosas e pele sensível. Realize teste de sensibilidade antes do uso. Mantenha fora do alcance de crianças. Se estiver grávida ou amamentando, consulte um profissional antes do uso."
    },
    {
      "type": "Sensibilidade à luz",
      "title_localized": "Aviso de Fotossensibilidade",
      "warning_text_localized": "Limão siciliano é fototóxico. Evite exposição ao sol ou lâmpadas UV por pelo menos 12 horas após a aplicação tópica para prevenir reações."
    },
    {
      "type": "Gravidez e amamentação",
      "title_localized": "Cuidados na Gravidez e Lactação",
      "warning_text_localized": "Óleos como limão siciliano podem ser usados com cautela ou evitados se houver sensibilidade. Consulte um profissional de saúde antes de usar durante a gravidez ou lactação."
    },
    {
      "type": "Segurança na aplicação tópica",
      "title_localized": "Orientações de Diluição",
      "warning_text_localized": "Dilua os óleos essenciais em óleo carreador de preferência entre 5 a 10%, especialmente para uso na região abdominal. Não aplique na pele irritada ou ferida."
    }
  ],
  "echo": {
    "health_concern_input": "Desconforto digestivo após refeições, especialmente comidas gordurosas",
    "user_info_input": {
      "gender": "female",
      "age_category": "adult",
      "age_specific": "24",
      "age_unit": "anos"
    },
    "selected_cause_ids": [
      "d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b"
    ],
    "selected_symptom_ids": [
      "d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"
    ],
    "suggested_oil_ids": [
      "ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf",
      "7fd15cfa-5a18-4e12-ba6b-6da0d62d70bc",
      "caef2077-85b5-479f-a828-7b7be804e498",
      "8ffe0ee4-e1bb-44f1-be9b-4a5cf16c6739"
    ],
    "time_of_day": "night"
  }
}
 LOG  🔍 [RN-FinalRecipes] ResponseParser received for night : {"finalDataKeys": ["meta", "data", "safety_warnings", "echo"], "hasData": true, "hasFinalData": true}
 LOG  ✅ [RN-FinalRecipes] Recipe received for night : Alívio do desconforto digestivo noturno
 LOG  ✅ [ParallelEngine] Completed stream for night: SUCCESS
 LOG  ✅ [ParallelEngine] Stream result preview: {"hasOils": false, "oilsCount": 0, "resultKeys": ["timeSlot", "recipe"]}
 LOG  🔒 [SSE-ios] Releasing reader lock {"sessionId": "sse-1757172824489-n63lysomc"}
 LOG  📋 [ParallelEngine] Added result for night. Total completed: 2
 LOG  🔍 [MobileSSE] Connection completed
 LOG  📱 [API-ios] Mobile SSE connection closed {"requestId": "api-1757172824474-em5vtq9rl"}
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: true
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 2, "total": 5}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  📊 LAYER 8B: Property Analysis Progress: 2/5 properties completed
 LOG  📊 LAYER 8B: Tool Calls Made: 2 total | Results: 2 | Errors: 0
 LOG  📊 LAYER 8C: Map/Set sizes changed
 LOG  📊 LAYER 8C: Results size: 2
 LOG  📊 LAYER 8C: Errors size: 0
 LOG  🔄 LAYER 9B: Processing individual streaming results with STORE OPERATIONS...
 LOG  🎯 AUTO-COMPLETION: Checking streaming completion:
 LOG  🎯 AUTO-COMPLETION: totalSelected: 5
 LOG  🎯 AUTO-COMPLETION: totalCompleted: 2
 LOG  🎯 AUTO-COMPLETION: isStreaming: true
 LOG  🎯 AUTO-COMPLETION: isSubmitting: false
 LOG  🔍 [MobileSSE] Headers received: {"contentType": "text/event-stream", "headers": {"access-control-allow-headers": "Content-Type, Authorization", "access-control-allow-methods": "GET, POST, OPTIONS", "access-control-allow-origin": "*", "access-control-max-age": "86400", "alt-svc": "h3=\":443\"; ma=2592000", "cache-control": "no-cache", "content-encoding": "br", "content-type": "text/event-stream", "date": "Sat, 06 Sep 2025 15:34:10 GMT", "vary": "Accept-Encoding, RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "x-clerk-auth-message": "Invalid JWT form. A JWT consists of three parts separated by dots. (reason=token-invalid, token-carrier=header)", "x-clerk-auth-reason": "token-invalid", "x-clerk-auth-status": "signed-out", "x-middleware-rewrite": "/api/ai/streaming"}, "status": 200}
 LOG  📱 [API-ios] Mobile SSE connection opened {"requestId": "api-1757172821473-7zsr773h8"}
 LOG  🔍 [MobileSSE] New data chunk: {"chunkLength": 8181, "chunkPreview": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RECIPE RESPONSE v2\",\"request_"}
 LOG  🔍 [MobileSSE] Processing message block: {"messageLength": 8179, "messagePreview": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RECIPE RESPONSE v2\",\"request_id\":\"a1b2c3d4-e5f6-7890-ab12-cdef34567890\",\"timest"}
 LOG  🔍 [MobileSSE] Yielding data: {"dataLength": 8173, "dataPreview": "{\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RECIPE RESPONSE v2\",\"request_id\":\"a"}
 LOG  📱 [API-ios] Mobile SSE message received {"dataLength": 8173, "dataPreview": "{\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RECIPE RESPONSE v2\",\"request_id\":\"a", "requestId": "api-1757172821473-7zsr773h8"}
 LOG  📦 [SSE-ios] Chunk 1 received {"bufferLengthBefore": 0, "chunkPreview": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RECIPE RESPONSE v2\",\"request_...", "chunkSize": 8362, "sessionId": "sse-1757172821487-sr5zeou2y", "totalBytes": 8362}
 LOG  🔄 [SSE-ios] Buffer updated {"bufferLength": 8181, "bufferPreview": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RECIPE RESPONSE v2\",\"request_id\":\"a1b2c3d4-e5f6-7890-ab12-cdef34567890\",\"timestamp_utc\":\"2023-10-01T12:00:00Z\",\"version\":\"1.0\",\"u...", "sessionId": "sse-1757172821487-sr5zeou2y"}
 LOG  🔍 [SSE-ios] Processing message block 1 {"messageBlock": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RECIPE RESPONSE v2\",\"request_id\":\"a1b2c3d4-e5f6-7890-ab12-cdef34567890\",\"timest...", "remainingBufferLength": 0, "sessionId": "sse-1757172821487-sr5zeou2y"}
 LOG  📤 [SSE-ios] Yielding data message 1 {"data": "{\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RECIPE RESPONSE v2\",\"request_id\":\"a...", "dataLength": 8173, "fullLine": "data: {\"type\":\"structured_complete\",\"data\":{\"meta\":{\"step_name\":\"FINAL RECIPE RESPONSE v2\",\"request_id\":\"a1b2c3d4-e5f6-7890-ab12-cdef34567890\",\"timest...", "sessionId": "sse-1757172821487-sr5zeou2y"}
 LOG  🏁 [ParallelEngine] Processing structured_complete for mid-day
 LOG  📋 [ParallelEngine] Complete data keys: ["meta", "data", "safety_warnings", "echo"]
 LOG  📋 [ParallelEngine] Data structure preview: {"dataKeys": ["meta", "data", "safety_warnings", "echo"], "firstLevelData": {"data": "object", "echo": "object", "meta": "object", "safety_warnings": "object"}, "hasSuggestedOils": false, "hasTherapeuticPropertyContext": false}
 LOG  🔍 [RN-FinalRecipes] RAW API RESPONSE for mid-day : {
  "meta": {
    "step_name": "FINAL RECIPE RESPONSE v2",
    "request_id": "a1b2c3d4-e5f6-7890-ab12-cdef34567890",
    "timestamp_utc": "2023-10-01T12:00:00Z",
    "version": "1.0",
    "user_language": "PT_BR",
    "status": "success",
    "message": "Personalized aromatherapy recipe successfully generated."
  },
  "data": {
    "recipe_protocol": {
      "recipe_id": "abcd1234-ef56-7890-ab12-cdef34567890",
      "time_slot": "mid-day",
      "time_of_day_localized": "meio-dia",
      "time_range_localized": "12h às 14h",
      "recipe_theme_localized": "Alívio do desconforto digestivo após refeições gordurosas",
      "description_localized": "Um óleo de mistura calmante e digestivo, ideal para uso durante o período do meio-dia, que ajuda a aliviar o desconforto digestivo causado por refeições ricas em gordura.",
      "holistic_benefit_localized": "Este blend promove relaxamento do sistema digestivo, aliviando a sensação de plenitude e desconforto após refeições pesadas, promovendo bem-estar e equilíbrio geral.",
      "application_type_localized": "difusão e aplicação tópica moderada",
      "synergy_rationale_localized": "A combinação de hortelã-pimenta e funcho atua sinergicamente para acalmar o sistema digestivo, estimular a digestão e reduzir a sensação de inchaço, enquanto o limão siciliano e tangerina oferecem propriedades refrescantes e desintoxicantes, potencializando o efeito calmante e analgésico do blend.",
      "formulation": {
        "total_drops": 30,
        "dilution_percentage": 3,
        "bottle_size_ml": 10,
        "bottle_type_localized": "frascos de vidro âmbar com conta-gotas"
      },
      "ingredients": {
        "essential_oils": [
          {
            "oil_id": "ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf",
            "name_localized": "Hortelã-pimenta",
            "scientific_name": "Mentha Piperita",
            "drops": 8
          },
          {
            "oil_id": "7fd15cfa-5a18-4e12-ba6b-6da0d62d70bc",
            "name_localized": "Funcho",
            "scientific_name": "Foeniculum vulgare",
            "drops": 6
          },
          {
            "oil_id": "caef2077-85b5-479f-a828-7b7be804e498",
            "name_localized": "Limão siciliano",
            "scientific_name": "Citrus limon",
            "drops": 6
          },
          {
            "oil_id": "0ed154c2-cb91-4449-9b3b-37ffea79c9ac",
            "name_localized": "Tangerina",
            "scientific_name": "Citrus reticulata",
            "drops": 4
          },
          {
            "oil_id": "a3c73ec0-58ce-4e56-a376-7f3415bac9b3",
            "name_localized": "Ylang Ylang",
            "scientific_name": "Cananga odorata",
            "drops": 6
          }
        ],
        "carrier_oil": {
          "recommended": {
            "name_localized": "Óleo de amêndoas doces",
            "properties_localized": "Óleo leve, de absorção rápida, ideal para aplicação tópica em áreas sensíveis e para diluição de óleos essenciais."
          },
          "alternative": {
            "name_localized": "Óleo de jojoba",
            "properties_localized": "Óleo nutritivo, de longa duração, excelente para peles sensíveis e uso para difusão."
          }
        }
      },
      "therapeutic_properties_targeted": [
        {
          "property_id": "1b2e3f4d-5c6b-7a8b-9c0d-1e2f3a4b5c6d",
          "name_localized": "Alívio digestivo",
          "description_localized": "Propriedades que auxiliam na digestão, relaxamento do sistema gastrointestinal e redução de desconfortos após refeições gordurosas."
        },
        {
          "property_id": "2e3d4c5b-6a7a-8b9c-0d1e-2f3a4b5c6d7e",
          "name_localized": "Ação relaxante",
          "description_localized": "Propriedades calmantes que promovem relaxamento geral e bem-estar após as refeições."
        }
      ],
      "preparation_summary_localized": "Adicione as gotas de óleo essencial escolhidas ao frasco de base, agite suavemente para misturar e esteja pronto para uso. Utilize a difusor durante o período do meio-dia ou aplique uma pequena quantidade diluída nos pulsos ou região abdominal, conforme orientação de uso.",
      "usage_summary_localized": "Inspire profundamente o aroma ou aplique a mistura diluída na região abdominal 2 a 3 vezes ao dia, especialmente após refeições pesadas, para aliviar o desconforto digestivo.",
      "preparation_steps_localized": [
        "Pegue um frasco âmbar de 10ml limpo e seco.",
        "Adicione 8 gotas de hortelã-pimenta, 6 gotas de funcho, 6 gotas de limão siciliano, 4 gotas de tangerina e 6 gotas de ylang ylang usando um conta-gotas limpo.",
        "Complete o frasco com óleo de amêndoas doces até atingir a marca de 10ml.",
        "Feche o frasco e agite suavemente para homogeneizar a mistura.",
        "Armazene em local fresco, ao abrigo da luz direta."
      ],
      "usage_instructions_localized": [
        {
          "method_code": "DIFUSOR",
          "method": "Difusão ambiente",
          "description": "Coloque 3-5 gotas no difusor para aromatizar o ambiente durante o horário do meio-dia ou após as refeições.",
          "frequency": "Até 4 horas por dia"
        },
        {
          "method_code": "APLICAÇÃO",
          "method": "Aplicação tópica",
          "description": "Aplique uma pequena quantidade do óleo diluído na região abdominal ou pulsos, com movimentos leves, após as refeições ou sempre que sentir desconforto.",
          "frequency": "2 a 3 vezes ao dia"
        }
      ],
      "ritual_suggestion_localized": "Antes de usar, reserve um momento para respirar profundamente, apreciando o aroma e conectando-se ao momento de cuidado com seu bem-estar digestivo. Use diariamente durante o período de maior desconforto para criar uma rotina de autocuidado e aliviar sinais de estresse digestivo.",
      "oil_rationales": [
        {
          "oil_id": "ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf",
          "name_localized": "Hortelã-pimenta",
          "properties": [
            "Refrescante, estimulante digestiva, alivia sensação de plenitude e desconforto."
          ],
          "rationale_localized": "Hortelã-pimenta é conhecida por suas propriedades antiespasmódicas, calmantes do sistema digestivo e por promover alívio rápido do desconforto após refeições gordurosas."
        },
        {
          "oil_id": "7fd15cfa-5a18-4e12-ba6b-6da0d62d70bc",
          "name_localized": "Funcho",
          "properties": [
            "Digestivo, antipropensão, ajuda na redução de inchaço e sensação de peso abdominal."
          ],
          "rationale_localized": "Funcho é tradicionalmente usado para aliviar problemas digestivos, promovendo a suavidade do trânsito intestinal e o alívio do inchaço após grandes refeições."
        },
        {
          "oil_id": "caef2077-85b5-479f-a828-7b7be804e498",
          "name_localized": "Limão siciliano",
          "properties": [
            "Refrescante, desintoxicante, melhora o humor e estimula a digestão."
          ],
          "rationale_localized": "O limão siciliano fornece uma sensação refrescante, ajuda na digestão e favorece a eliminação de toxinas, ideal para uso vespertino."
        },
        {
          "oil_id": "0ed154c2-cb91-4449-9b3b-37ffea79c9ac",
          "name_localized": "Tangerina",
          "properties": [
            "Calmante, regulador do humor, melhora o bem-estar geral."
          ],
          "rationale_localized": "A tangerina complementa o efeito relaxante e refrescante, promovendo uma sensação de leveza e bem-estar após as refeições."
        },
        {
          "oil_id": "a3c73ec0-58ce-4e56-a376-7f3415bac9b3",
          "name_localized": "Ylang Ylang",
          "properties": [
            "Relaxante, equilibrante emocional, auxilia na redução do estresse."
          ],
          "rationale_localized": "Ylang ylang suaviza o efeito de estímulos e auxilia na indução de um estado mais relaxado, favorecendo a digestão e o equilíbrio emocional."
        }
      ]
    }
  },
  "safety_warnings": [
    {
      "type": "Precauções gerais",
      "title_localized": "Precauções Gerais",
      "warning_text_localized": "Este blend é destinado ao uso tópico ou na difusão consciente. Não ingira. Consulte um profissional se estiver grávida ou amamentando. Mantenha fora do alcance de crianças. Faça um teste de sensibilidade antes do uso tópico e evite contato com olhos e mucosas."
    },
    {
      "type": "Sensibilidade à luz solar",
      "title_localized": "Risco de fotosensibilidade",
      "warning_text_localized": "Contém óleos cítricos (limão siciliano, tangerina) que podem aumentar a sensibilidade à luz solar. Evite exposição direta ao sol por pelo menos 12 horas após a aplicação tópica."
    },
    {
      "type": "Gestantes e lactantes",
      "title_localized": "Gravidez e lactação",
      "warning_text_localized": "Óleos como limão siciliano e tangerina são compatíveis com gravidez e amamentação, mas devem ser utilizados com moderação. Sempre consulte um profissional de saúde antes do uso durante este período."
    }
  ],
  "echo": {
    "health_concern_input": "Desconforto digestivo após refeições, especialmente comidas gordurosas",
    "user_info_input": {
      "gender": "female",
      "age_category": "adult",
      "age_specific": "24",
      "age_unit": "anos"
    },
    "selected_cause_ids": [
      "d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b"
    ],
    "selected_symptom_ids": [
      "d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"
    ],
    "suggested_oil_ids": [
      "ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf",
      "7fd15cfa-5a18-4e12-ba6b-6da0d62d70bc",
      "caef2077-85b5-479f-a828-7b7be804e498",
      "0ed154c2-cb91-4449-9b3b-37ffea79c9ac",
      "a3c73ec0-58ce-4e56-a376-7f3415bac9b3"
    ],
    "time_of_day": "mid-day"
  }
}
 LOG  🔍 [RN-FinalRecipes] ResponseParser received for mid-day : {"finalDataKeys": ["meta", "data", "safety_warnings", "echo"], "hasData": true, "hasFinalData": true}
 LOG  ✅ [RN-FinalRecipes] Recipe received for mid-day : Alívio do desconforto digestivo após refeições gordurosas
 LOG  ✅ [ParallelEngine] Completed stream for mid-day: SUCCESS
 LOG  ✅ [ParallelEngine] Stream result preview: {"hasOils": false, "oilsCount": 0, "resultKeys": ["timeSlot", "recipe"]}
 LOG  🔒 [SSE-ios] Releasing reader lock {"sessionId": "sse-1757172821487-sr5zeou2y"}
 LOG  📋 [ParallelEngine] Added result for mid-day. Total completed: 3
 LOG  🎯 [ParallelEngine] All streams completed. Results: 3, Errors: 0
 LOG  🎯 [Properties-FinalRecipes] Final recipe streaming completed: Map {"morning" => {"recipe": {"application_method_localized": "difusão no ambiente e aplicação tópica leve na região abdominal", "carrier_oil": [Object], "container_recommendation": [Object], "description_localized": "Uma mistura terapêutica especialmente formulada para aliviar o desconforto digestivo causado por refeições gordurosas, promovendo bem-estar e equilíbrio digestivo ao iniciar o dia.", "disclaimer_localized": "For external use only. Consult healthcare provider if pregnant, nursing, or have medical conditions.", "duration_localized": "Use within 6 months", "frequency_localized": "duas vezes ao dia", "holistic_benefit_localized": "Este remédio aromático apoia a saúde digestiva, reduz a sensação de plenitude e inchaço após refeições ricas em gordura, além de promover sensação de leveza e energia matinal.", "preparation_steps_localized": [Array], "recipe_id": "d2f1a4c8-8e3b-4b0f-89d6-12e349f7a963", "recipe_name_localized": "Alívio digestivo matinal", "recipe_theme_localized": "Alívio digestivo matinal", "ritual_suggestion_localized": "Comece seu dia com uma respiração profunda do aroma de hortelã-pimenta e funcho no difusor, enquanto massageia suavemente sua região abdominal após o café da manhã, criando um momento de cuidado e atenção plena para promover sua digestão e bem-estar. Aproveite para saborear sua manhã com uma atitude positiva e acolhedora.", "safety_warnings": [Array], "selected_oils": [Array], "synergy_rationale_localized": "A combinação de hortelã-pimenta, funcho e limão siciliano atua sinergicamente para estimular a digestão, reduzir inflamações e aliviar a sensação de peso na barriga, proporcionando um início de dia mais confortável.", "time_slot": "morning", "total_drops": 25, "total_volume_ml": 10, "usage_instructions_localized": [Array]}, "timeSlot": "morning"}, "night" => {"recipe": {"application_method_localized": "difusão e aplicação tópica", "carrier_oil": [Object], "container_recommendation": [Object], "description_localized": "Um ritual relaxante e digestivo para ajudar a aliviar o desconforto digestivo após refeições gordurosas, promovendo relaxamento e bem-estar à noite.", "disclaimer_localized": "For external use only. Consult healthcare provider if pregnant, nursing, or have medical conditions.", "duration_localized": "Use within 6 months", "frequency_localized": "1 a 2 vezes ao dia, conforme necessidade.", "holistic_benefit_localized": "Alivia o desconforto digestivo, promove relaxamento e melhora a qualidade do sono.", "preparation_steps_localized": [Array], "recipe_id": "ADFC8E12-4F89-4E21-B681-3A967D1E1B0D", "recipe_name_localized": "Alívio do desconforto digestivo noturno", "recipe_theme_localized": "Alívio do desconforto digestivo noturno", "ritual_suggestion_localized": "Ao final de um jantar pesado, inicie uma sessão de difusão ou aplique na região abdominal enquanto respira profundamente, relaxando pelo menos 10 minutos e desfrutando do momento de autocuidado.", "safety_warnings": [Array], "selected_oils": [Array], "synergy_rationale_localized": "A combinação de Hortelã-pimenta, Funcho, Limão Siciliano e Camomila Romanana atua sinergicamente para aliviar a sensação de plenitude abdominal, melhorar a digestão, reduzir o inchaço e proporcionar um efeito calmante na noite.", "time_slot": "night", "total_drops": 25, "total_volume_ml": 10, "usage_instructions_localized": [Array]}, "timeSlot": "night"}, "mid-day" => {"recipe": {"application_method_localized": "difusão e aplicação tópica moderada", "carrier_oil": [Object], "container_recommendation": [Object], "description_localized": "Um óleo de mistura calmante e digestivo, ideal para uso durante o período do meio-dia, que ajuda a aliviar o desconforto digestivo causado por refeições ricas em gordura.", "disclaimer_localized": "For external use only. Consult healthcare provider if pregnant, nursing, or have medical conditions.", "duration_localized": "Use within 6 months", "frequency_localized": "Até 4 horas por dia", "holistic_benefit_localized": "Este blend promove relaxamento do sistema digestivo, aliviando a sensação de plenitude e desconforto após refeições pesadas, promovendo bem-estar e equilíbrio geral.", "preparation_steps_localized": [Array], "recipe_id": "abcd1234-ef56-7890-ab12-cdef34567890", "recipe_name_localized": "Alívio do desconforto digestivo após refeições gordurosas", "recipe_theme_localized": "Alívio do desconforto digestivo após refeições gordurosas", "ritual_suggestion_localized": "Antes de usar, reserve um momento para respirar profundamente, apreciando o aroma e conectando-se ao momento de cuidado com seu bem-estar digestivo. Use diariamente durante o período de maior desconforto para criar uma rotina de autocuidado e aliviar sinais de estresse digestivo.", "safety_warnings": [Array], "selected_oils": [Array], "synergy_rationale_localized": "A combinação de hortelã-pimenta e funcho atua sinergicamente para acalmar o sistema digestivo, estimular a digestão e reduzir a sensação de inchaço, enquanto o limão siciliano e tangerina oferecem propriedades refrescantes e desintoxicantes, potencializando o efeito calmante e analgésico do blend.", "time_slot": "mid-day", "total_drops": 30, "total_volume_ml": 10, "usage_instructions_localized": [Array]}, "timeSlot": "mid-day"}}
 LOG  💾 [Properties-FinalRecipes] Storing morning recipe: Alívio digestivo matinal
 LOG  💾 [Properties-FinalRecipes] Storing night recipe: Alívio do desconforto digestivo noturno
 LOG  💾 [Properties-FinalRecipes] Storing mid-day recipe: Alívio do desconforto digestivo após refeições gordurosas
 LOG  ✅ [Properties-FinalRecipes] Navigation to final recipes...
 WARN  Failed to track step completed: [TypeError: posthog.capture is not a function (it is undefined)]
 WARN  Failed to track step viewed: [TypeError: posthog.capture is not a function (it is undefined)]
 LOG  🔍 [CausesSelection] Store subscription debug: {"potentialCausesFromStore": 7, "renderTimestamp": "2025-09-06T15:34:11.010Z", "selectedCausesFromStore": 1}
 LOG  🔍 [CausesSelection] Counter Debug: {"SELECTION_REQUIREMENTS_CAUSES": {"max": 10, "min": 1}, "maxSelection": 10, "minSelection": 1, "selectedCauses": 1, "selectedCausesArray": [{"id": "d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b", "name": "Dieta rica em alimentos gordurosos"}], "selectionCount": 1}
 LOG  🩺 SymptomsSelection Debug: {"canSubmit": true, "selectedCount": 1, "selectedIds": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "totalSymptoms": 8}
 LOG  🎭 SymptomsSelection - Modal State: {"isStreaming": false, "showStreamingModal": false, "streamingItemsCount": 5, "timestamp": "2025-09-06T15:34:11.029Z"}
 LOG  🔍 SymptomsSelection: First symptom data structure: {"explanation": "Sensação de plenitude ou inchaço após refeições ricas em gordura devido ao processamento dificultoso pelo sistema digestivo.", "symptom_id": "d3b07384-2a6d-4c65-8d6d-859f2b1a16c1", "symptom_name": "Sensação de plenitude abdominal após refeições", "symptom_suggestion": "Observe se ocorre após o consumo de alimentos gordurosos"}
 LOG  🔍 SymptomsSelection: Available fields: ["symptom_id", "symptom_name", "symptom_suggestion", "explanation"]
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  Step submit function ready
 LOG  Step submit function ready
 LOG  Symptoms submit function ready
 LOG  📊 LAYER 8C: Map/Set sizes changed
 LOG  📊 LAYER 8C: Results size: 3
 LOG  📊 LAYER 8C: Errors size: 0
 LOG  🔄 LAYER 9B: Processing individual streaming results with STORE OPERATIONS...
 LOG  🎯 AUTO-COMPLETION: Checking streaming completion:
 LOG  🎯 AUTO-COMPLETION: totalSelected: 5
 LOG  🎯 AUTO-COMPLETION: totalCompleted: 3
 LOG  🎯 AUTO-COMPLETION: isStreaming: false
 LOG  🎯 AUTO-COMPLETION: isSubmitting: false
 LOG  🔍 [CausesSelection] Store subscription debug: {"potentialCausesFromStore": 7, "renderTimestamp": "2025-09-06T15:34:11.131Z", "selectedCausesFromStore": 1}
 LOG  🔍 [CausesSelection] Counter Debug: {"SELECTION_REQUIREMENTS_CAUSES": {"max": 10, "min": 1}, "maxSelection": 10, "minSelection": 1, "selectedCauses": 1, "selectedCausesArray": [{"id": "d4f0e8b2-9a3e-4c1f-8b2a-7e8f5d2c3a1b", "name": "Dieta rica em alimentos gordurosos"}], "selectionCount": 1}
 LOG  🩺 SymptomsSelection Debug: {"canSubmit": true, "selectedCount": 1, "selectedIds": ["d3b07384-2a6d-4c65-8d6d-859f2b1a16c1"], "totalSymptoms": 8}
 LOG  🎭 SymptomsSelection - Modal State: {"isStreaming": false, "showStreamingModal": false, "streamingItemsCount": 5, "timestamp": "2025-09-06T15:34:11.151Z"}
 LOG  🔍 SymptomsSelection: First symptom data structure: {"explanation": "Sensação de plenitude ou inchaço após refeições ricas em gordura devido ao processamento dificultoso pelo sistema digestivo.", "symptom_id": "d3b07384-2a6d-4c65-8d6d-859f2b1a16c1", "symptom_name": "Sensação de plenitude abdominal após refeições", "symptom_suggestion": "Observe se ocorre após o consumo de alimentos gordurosos"}
 LOG  🔍 SymptomsSelection: Available fields: ["symptom_id", "symptom_name", "symptom_suggestion", "explanation"]
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🔍 Transformed recipes for modal: {"mid-day": {"hasRecipe": true, "hasSelectedOils": true, "selectedOilsCount": 5}, "morning": {"hasRecipe": true, "hasSelectedOils": true, "selectedOilsCount": 3}, "night": {"hasRecipe": true, "hasSelectedOils": true, "selectedOilsCount": 4}}
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🔍 LAYER 11P-1: therapeuticProperties count: 5
 LOG  🔍 LAYER 11P-1: selectedPropertyIds count: 5
 LOG  🔍 LAYER 11P-1: canSubmit: true
 LOG  🔍 LAYER 11P-1: parallelStreamingState.isStreaming: false
 LOG  🔍 LAYER 11P-1: propertyProgressCount: {"completed": 0, "total": 0}
 LOG  🔍 LAYER 11P-1: showStreamingModal: false
 LOG  🔍 [MobileSSE] Connection completed
 LOG  📱 [API-ios] Mobile SSE connection closed {"requestId": "api-1757172821473-7zsr773h8"}
 LOG  🎭 Properties Final Recipes - Enhanced Modal onDismiss called - navigating to next step
 LOG  🔍 Transformed recipes for modal: {"mid-day": {"hasRecipe": true, "hasSelectedOils": true, "selectedOilsCount": 5}, "morning": {"hasRecipe": true, "hasSelectedOils": true, "selectedOilsCount": 3}, "night": {"hasRecipe": true, "hasSelectedOils": true, "selectedOilsCount": 4}}
 LOG  🔍 Transformed recipes for modal: {"mid-day": {"hasRecipe": true, "hasSelectedOils": true, "selectedOilsCount": 5}, "morning": {"hasRecipe": true, "hasSelectedOils": true, "selectedOilsCount": 3}, "night": {"hasRecipe": true, "hasSelectedOils": true, "selectedOilsCount": 4}}
 LOG  🔍 Opening modal for: morning
 LOG  🔍 Recipe data available: {"hasRecipe": true, "hasSelectedOils": true, "selectedOilsCount": 3}
 LOG  🔍 Transformed recipes for modal: {"mid-day": {"hasRecipe": true, "hasSelectedOils": true, "selectedOilsCount": 5}, "morning": {"hasRecipe": true, "hasSelectedOils": true, "selectedOilsCount": 3}, "night": {"hasRecipe": true, "hasSelectedOils": true, "selectedOilsCount": 4}}
 LOG  🔍 Transformed recipes for modal: {"mid-day": {"hasRecipe": true, "hasSelectedOils": true, "selectedOilsCount": 5}, "morning": {"hasRecipe": true, "hasSelectedOils": true, "selectedOilsCount": 3}, "night": {"hasRecipe": true, "hasSelectedOils": true, "selectedOilsCount": 4}}
 LOG  🔍 [FinalRecipeList] Collecting alternative oils: {"enrichedOilsCount": 32, "oilsWithFinalScore": 32, "oilsWithSpecializationScore": 32, "propertiesCount": 5, "totalOilsBeforeDedup": 34}
 LOG  🔍 [FinalRecipeList] Alternative oils processing complete: {"filteredOutCount": 2, "filteredOutReasons": {"missing final_relevance_score": 2}, "finalUniqueOilsCount": 18, "totalOilsBeforeDedup": 34}
 WARN  ⚠️ [FinalRecipeList] Some oils were filtered out: {"count": 2, "examples": [{"oil": [Object], "reason": "missing final_relevance_score"}, {"oil": [Object], "reason": "missing final_relevance_score"}]}
 LOG  🔍 [OilSubstitution] Starting filtering process: {"originalOilId": undefined, "selectedOilsCount": 3, "timeSlot": "morning", "totalAlternatives": 18}
 LOG  🔍 [OilSubstitution] Selected oil IDs in recipe: ["ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf", "7fd15cfa-5a18-4e12-ba6b-6da0d62d70bc", "caef2077-85b5-479f-a828-7b7be804e498"]
 LOG  🕵️‍♂️ [OilSubstitution] Inspecting data structures for filtering...
 LOG  Sample from `selectedOils` (in recipe): {
  "oil_id": "ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf",
  "name_localized": "Hortelã-pimenta",
  "name_botanical": "Mentha Piperita",
  "drops_count": 10,
  "rationale_localized": "A hortelã-pimenta é reconhecida por sua ação rápida no alívio de desconfortos digestivos, auxiliando a acalmar o sistema gastrointestinal e proporcionando sensação de alívio e frescor, ideal para começar o dia com energia positiva."
}
 LOG  Sample from `alternativeOils` (master list): {
  "botanical_name": "",
  "oil_id": "ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf",
  "name_english": "Mentha piperita",
  "name_botanical": "Mentha piperita",
  "name_localized": "Hortelã-pimenta",
  "match_rationale_localized": "Óleo altamente eficaz para aliviar desconforto estomacal e estimular a digestão, com forte ligação com a propriedade Digestive.",
  "relevancy_to_property_score": 5,
  "name_scientific": "Mentha Piperita",
  "safety": {
    "internal_use": {
      "id": "2343a180-7dd2-45b1-a402-065b1bf2bd7c",
      "code": "FOOD_GRADE_EO",
      "name": "Safe for Internal Use",
      "guidance": null,
      "description": "Essential oils in this category are generally recognized or have specific indications suggesting they may be safe for internal use when following appropriate dilutions, dosage, and professional guidance."
    },
    "dilution": {
      "id": "45c9d911-9b2b-472f-99aa-fdbaf3caa98d",
      "name": "[S] Sensitive",
      "ratio": "5:1",
      "description": "Sensitive. “Sensitive” oils are those that should be diluted before use on young or sensitive skin. Examples of “sensitive” oils are peppermint, ginger, eucalyptus, wintergreen, and black pepper.",
      "percentage_max": 0.2,
      "percentage_min": 0.15
    },
    "phototoxicity": {
      "id": "ae18d720-4473-479e-b9f3-7fa65192d639",
      "status": "Non-Phototoxic",
      "guidance": "No specific sun exposure restrictions related to phototoxicity are typically required after topical application. However, always follow general safety guidelines for dilution and application for all essential oils.",
      "description": "Generally not known to cause phototoxic reactions when skin is exposed to UV light after topical application, when used appropriately."
    },
    "pregnancy_nursing": [
      {
        "id": "1b330391-6fb2-4972-bda3-6872e9835f9a",
        "code": "",
        "name": "",
        "description": "",
        "usage_guidance": "",
        "status_description": "pregnancy-safe-50"
      }
    ],
    "child_safety": [],
    "internal_use_id": "2343a180-7dd2-45b1-a402-065b1bf2bd7c",
    "dilution_id": "45c9d911-9b2b-472f-99aa-fdbaf3caa98d",
    "phototoxicity_id": "ae18d720-4473-479e-b9f3-7fa65192d639",
    "pregnancy_nursing_ids": [
      "1b330391-6fb2-4972-bda3-6872e9835f9a"
    ],
    "child_safety_ids": []
  },
  "enrichment_status": "enriched",
  "botanical_mismatch": false,
  "similarity_score": 0.721394906293436,
  "search_query": "Mentha piperita - Mentha piperita",
  "enrichment_timestamp": "2025-09-06T15:32:57.991Z",
  "isEnriched": true,
  "final_relevance_score": 4.75,
  "specialization_score": 1
}
 LOG  🔍 [OilSubstitution] Filtering results: {"afterExcludingOriginal": 18, "afterExcludingRecipeOils": 15, "excludedByOriginal": 0, "excludedByRecipe": 3, "finalSortedCount": 15, "totalAlternatives": 18}
 WARN  ⚠️ [OilSubstitution] No originalOil provided to modal
# EPIC: Protocol-Based Saving System for AromaCHAT

## Epic Overview
**Epic ID**: ARC-2024-003  
**Epic Title**: Implement Protocol-Based Saving System  
**Epic Owner**: Development Team  
**Business Value**: Transform AromaCHAT from single-use recipe generator to personal aromatherapy protocol library  
**Priority**: High  
**Estimated Effort**: 8-12 story points  

## Background & Problem Statement

### Current State Issues
AromaCHAT currently has a **fundamentally flawed saving architecture**:

1. **Data Model Mismatch**: System saves individual recipes when users create complete protocols with 3 recipes (morning/mid-day/night)
2. **Information Loss**: Critical API-generated data (oil rationales, enrichment scores) not preserved for oil substitution functionality
3. **User Experience Disconnect**: Users create holistic daily protocols but can only save fragmented individual recipes
4. **Technical Debt**: Database schema doesn't match actual application data flow and usage patterns

### Business Impact
- **User Frustration**: Cannot save complete daily aromatherapy protocols as intended
- **Feature Limitation**: Oil substitution functionality broken for saved items due to missing enrichment data
- **Reduced Engagement**: Users cannot build personal protocol libraries effectively
- **Data Integrity Issues**: Misaligned database structure causes maintenance and feature development challenges

## Vision Statement
Enable users to save, manage, and reuse complete aromatherapy protocols containing all three daily recipes (morning/mid-day/night) with full oil substitution capabilities, creating a comprehensive personal aromatherapy library experience.

## Epic Goals

### Primary Goals
1. **Protocol-Centric Architecture**: Redesign saving system around complete protocols (not individual recipes)
2. **Complete Data Preservation**: Store all API-generated data necessary for full UI reconstruction and oil substitution
3. **Seamless User Experience**: Mirror final recipe screen experience for saved protocols
4. **Oil Substitution Support**: Preserve enrichment scores and therapeutic properties for future oil changes

### Secondary Goals
1. **Performance Optimization**: Efficient database queries for protocol management
2. **Scalability**: Support unlimited protocols per user with proper pagination
3. **Data Migration**: Clean transition from current flawed architecture
4. **Developer Experience**: Clear, maintainable code architecture following established patterns

## Success Criteria

### User Experience Success Metrics
- ✅ Users can save complete protocols with custom names
- ✅ Saved protocols screen mirrors final recipe screen layout and functionality  
- ✅ Users can navigate between time slots within saved protocols
- ✅ Oil substitution works identically in saved protocols vs. fresh recipes
- ✅ Protocol overview shows all necessary context (health concern, demographics, causes, symptoms)

### Technical Success Metrics
- ✅ Database schema accurately reflects application data model
- ✅ All API-generated data preserved for full functionality reconstruction
- ✅ Service layer provides clean protocol-based CRUD operations
- ✅ Zero data loss during save/load cycles
- ✅ Oil substitution algorithm works with saved enrichment scores

### Business Success Metrics
- ✅ Users can build personal aromatherapy protocol libraries
- ✅ Increased user engagement through protocol management features
- ✅ Foundation for advanced features (sharing, recommendations, analytics)

## Key Features & User Stories

### Core Features
1. **Protocol Creation & Saving**
   - Save complete 3-recipe protocols with custom names
   - Preserve all workflow context (causes, symptoms, therapeutic properties)
   - Store enrichment scores for oil substitution functionality

2. **Protocol Management Interface**
   - Protocol overview cards (similar to final recipe screen layout)
   - Protocol detail modal with time slot navigation
   - Delete protocol functionality with confirmation

3. **Oil Substitution in Saved Protocols** 
   - Full oil substitution modal functionality
   - Use preserved enrichment scores for accurate alternatives
   - Real-time protocol updates with substituted oils

4. **Data Architecture Overhaul**
   - Protocol-centric database schema
   - Complete data preservation strategy
   - Clean migration from existing flawed structure

## Technical Architecture

### Database Schema Design
```
user_saved_protocols (main protocol entity)
├── protocol metadata (name, user_id, timestamps)
├── user context (demographics, health_concern_original)
├── workflow data (selected_causes[], selected_symptoms[])
└── therapeutic_properties[] (with API-generated oil data)

user_saved_recipes (recipe instances)
├── protocol_id (FK to protocols)
├── time_slot (morning/mid-day/night)
├── recipe metrics (total_drops, oil_count, volume)
└── recipe_protocol (complete FinalRecipeProtocol minus oils)

user_saved_recipe_oils (oil instances)
├── recipe_id (FK to recipes)
├── oil_id (FK to essential_oils)
├── API-generated data (name_localized, rationale_localized)
└── enrichment scores (final_relevance_score, specialization_score)
```

### Service Layer Architecture
- **Protocol Service**: CRUD operations for complete protocols
- **Transactional Saving**: Atomic saves of protocol + 3 recipes + N oils
- **Data Reconstruction**: Rebuild complete UI state from database entities
- **Oil Substitution Integration**: Seamless integration with existing substitution logic

### UI Component Strategy
- **Saved Protocols Screen**: Mirror final recipe screen with overview cards
- **Protocol Detail Modal**: Reuse existing recipe modal with time slot navigation
- **Save Protocol Modal**: Single naming interface for complete protocol
- **Oil Substitution**: Identical experience to fresh recipes using saved scores

## Dependencies & Constraints

### Technical Dependencies
- ✅ Existing final recipe screen components (reuse for consistency)
- ✅ Current oil substitution modal and logic (extend for saved protocols) 
- ✅ Supabase database with MCP server access
- ✅ Clerk authentication system integration

### Business Constraints
- **Data Migration Required**: Must cleanly transition from current architecture
- **Backward Compatibility**: Ensure no disruption during transition
- **Performance Requirements**: Sub-second protocol loading times
- **Mobile Optimization**: Maintain 60fps performance on mobile devices

## Acceptance Criteria

### Epic-Level Acceptance Criteria
1. **Complete Protocol Saving**
   - User can save any generated protocol with custom name
   - All 3 time slot recipes saved as single protocol entity
   - Complete workflow context preserved (causes, symptoms, properties)

2. **Saved Protocols Management**
   - Saved protocols screen shows overview cards with protocol context
   - Users can tap protocol cards to open detail modal
   - Time slot navigation works identically to fresh recipes

3. **Oil Substitution Functionality**
   - Oil substitution modal opens from saved protocol recipes
   - Substitution recommendations based on saved enrichment scores
   - Protocol updates persist with substituted oil changes

4. **Data Integrity & Performance**
   - Zero data loss in save/load cycles
   - Protocol loading completes in <1 second
   - Database queries optimized with proper indexing

## Out of Scope (Future Enhancements)
- Protocol sharing between users
- Protocol collections/folders organization  
- Protocol modification after saving (beyond oil substitution)
- Protocol export functionality
- Advanced analytics and usage insights
- Protocol templates and recommendations

## Risk Assessment

### High Risks
- **Data Migration Complexity**: Moving from recipe-based to protocol-based architecture
- **UI State Reconstruction**: Ensuring saved protocols behave identically to fresh recipes
- **Performance Impact**: Loading complete protocols with all nested data

### Mitigation Strategies  
- **Comprehensive Testing**: Test all save/load scenarios before migration
- **Incremental Migration**: Phase migration to minimize disruption
- **Performance Monitoring**: Database query optimization and caching strategies

## Timeline & Milestones

### Phase 1: Foundation (Week 1)
- Database schema migration
- TypeScript type updates
- Basic protocol service implementation

### Phase 2: Core Functionality (Week 2)
- Protocol saving workflow
- Saved protocols screen implementation
- Basic protocol management

### Phase 3: Advanced Features (Week 3)
- Oil substitution integration
- Protocol detail modal
- Performance optimization

### Phase 4: Testing & Polish (Week 4)
- End-to-end testing
- UI/UX refinements
- Production deployment

## Definition of Done
- [ ] All user stories completed and tested
- [ ] Database migration executed successfully
- [ ] Performance benchmarks met
- [ ] Code review completed
- [ ] Documentation updated
- [ ] Feature tested across all supported devices
- [ ] Production deployment successful

---

This epic transforms AromaCHAT's core saving functionality from a broken individual recipe system to a comprehensive protocol-based architecture that matches user mental models and enables advanced aromatherapy library management.
# EARS Requirements: Protocol-Based Saving System

## Document Overview
**Document**: EARS Requirements Specification  
**Project**: AromaCHAT Protocol-Based Saving System  
**Version**: 2.0  
**Date**: December 2024  
**Related Epic**: ARC-2024-003  
**Related Story**: ARC-2024-003-001  

---

## EARS Requirements Specification
*EARS Format: **Event** - **Action** - **Response** - **State***

### R-001: Protocol Saving Trigger
**WHEN** user completes recipe creation wizard and views final recipe modal  
**IF** final recipes contain valid data for all three time slots  
**THEN** system SHALL display "Save Protocol" button in modal header  
**STATE** button remains visible and accessible throughout modal session  

**Rationale**: Provides clear save entry point when user has complete protocol ready  
**Priority**: High  
**Source**: User Story 1 - Save Complete Protocol  

### R-002: Protocol Naming Interface
**WHEN** user taps "Save Protocol" button  
**IF** user authentication is valid  
**THEN** system SHALL present protocol naming modal with text input field  
**STATE** modal blocks other interactions until dismissed or protocol saved  

**Additional Requirements**:
- Text input SHALL have placeholder: "Enter protocol name (e.g., My Stress Relief Protocol)"
- Input SHALL validate name is not empty and contains at least 3 characters
- Modal SHALL include Cancel and Save buttons
- Save button SHALL be disabled until valid name entered

**Rationale**: Single naming interface for complete protocol (not individual recipes)  
**Priority**: High  
**Source**: User Story 1 - Save Complete Protocol  

### R-003: Atomic Protocol Save Operation
**WHEN** user confirms protocol save with valid name  
**IF** all required protocol data is available in store  
**THEN** system SHALL save complete protocol in single atomic transaction  
**STATE** protocol appears in saved protocols list immediately after successful save  

**Transaction Components**:
1. Create protocol record with user context (demographics, causes, symptoms, therapeutic properties)
2. Create 3 recipe records (morning, mid-day, night) linked to protocol
3. Create N oil records for each recipe with enrichment scores
4. Commit all changes atomically or rollback on any failure

**Rationale**: Ensures data consistency and prevents partial protocol saves  
**Priority**: Critical  
**Source**: Epic - Technical Architecture Requirements  

### R-004: Saved Protocols Display
**WHEN** user navigates to saved protocols screen  
**IF** user has previously saved protocols  
**THEN** system SHALL display protocol overview cards in grid layout  
**STATE** each card shows protocol identification and summary information  

**Card Content Requirements**:
- Protocol name (user-defined)
- Creation date (formatted as "Created [Month DD, YYYY]")
- Health concern (original user input)
- Time slot indicators (visual chips for morning ☀️, mid-day ☀️, night 🌙)
- Summary stats (X oils • Y drops • Z ml total)
- Delete action button

**Rationale**: Provides protocol overview similar to final recipe screen experience  
**Priority**: High  
**Source**: User Story 2 - Browse Saved Protocols  

### R-005: Empty State Protocol Management
**WHEN** user navigates to saved protocols screen  
**IF** user has no saved protocols  
**THEN** system SHALL display empty state with call-to-action  
**STATE** screen encourages user to create first protocol  

**Empty State Requirements**:
- Illustration or icon indicating empty state
- Text: "No saved protocols yet"
- Subtitle: "Create your first personalized aromatherapy recipe and save it for easy access later."
- "Create Protocol" button linking to recipe creation wizard

**Rationale**: Guides new users toward creating their first saved protocol  
**Priority**: Medium  
**Source**: User Story 2 - Browse Saved Protocols  

### R-006: Protocol Detail Access
**WHEN** user taps protocol overview card  
**IF** protocol data exists and user has access permissions  
**THEN** system SHALL open protocol detail modal with complete recipe information  
**STATE** modal displays with morning time slot selected by default  

**Modal Requirements**:
- Identical layout to final recipe details modal
- Time slot segmented buttons (Morning | Mid-Day | Night)
- Complete recipe sections: oils, carrier oil, preparation, application, rationales, safety
- Modal backdrop allows dismissal by tapping outside content area

**Rationale**: Provides familiar interface matching fresh recipe experience  
**Priority**: High  
**Source**: User Story 3 - Access Complete Protocol Details  

### R-007: Time Slot Navigation in Saved Protocols
**WHEN** user views protocol detail modal  
**IF** protocol contains recipes for multiple time slots  
**THEN** system SHALL allow navigation between time slots using segmented buttons  
**STATE** selected time slot determines which recipe data is displayed  

**Navigation Requirements**:
- Smooth transitions between time slots (< 300ms)
- Selected state persists during modal session
- All time slots accessible regardless of entry point
- Recipe data updates immediately when time slot changes

**Rationale**: Enables complete protocol exploration within single modal  
**Priority**: High  
**Source**: User Story 3 - Access Complete Protocol Details  

### R-008: Oil Substitution in Saved Protocols
**WHEN** user views recipe details in protocol modal  
**IF** recipe contains essential oils  
**THEN** system SHALL display oil substitution button next to each oil  
**STATE** substitution functionality identical to fresh recipe experience  

**Substitution Requirements**:
- Substitute button (swap icon) appears next to each oil
- Tapping button opens oil alternatives modal
- Alternative oils calculated using saved enrichment scores
- Oil substitution modal shows relevant alternatives with scores
- User can select and confirm oil replacement

**Rationale**: Maintains oil substitution functionality using preserved enrichment data  
**Priority**: High  
**Source**: User Story 4 - Substitute Oils in Saved Protocols  

### R-009: Persistent Oil Substitution Updates
**WHEN** user confirms oil substitution in saved protocol  
**IF** substitution is valid and user has permissions  
**THEN** system SHALL update protocol permanently with new oil  
**STATE** changes persist when modal closed and reopened  

**Update Requirements**:
- Database update occurs immediately upon confirmation
- UI reflects changes instantly
- Recipe metrics recalculated (total drops, oil count)
- Substitution reversible through additional substitution actions

**Rationale**: Enables protocol adaptation and customization over time  
**Priority**: High  
**Source**: User Story 4 - Substitute Oils in Saved Protocols  

### R-010: Protocol Deletion
**WHEN** user taps delete button on protocol card  
**IF** user has deletion permissions for protocol  
**THEN** system SHALL present confirmation dialog with protocol details  
**STATE** deletion requires explicit user confirmation before execution  

**Deletion Requirements**:
- Confirmation dialog shows protocol name
- Warning text: "This will permanently delete '[Protocol Name]' and cannot be undone"
- Cancel and Delete buttons with destructive styling for Delete
- Upon confirmation, cascade delete protocol and all associated recipes/oils

**Rationale**: Prevents accidental deletion while allowing protocol management  
**Priority**: Medium  
**Source**: User Story 5 - Manage Saved Protocols  

### R-011: Data Preservation Requirements
**WHEN** system saves protocol data  
**IF** protocol contains API-generated information  
**THEN** system SHALL preserve all data necessary for complete UI reconstruction  
**STATE** saved protocol provides identical experience to fresh recipe  

**Preservation Requirements**:
- API-generated oil names and rationales
- Complete therapeutic properties with suggested oils
- All enrichment scores (final_relevance_score, specialization_score)
- User workflow context (demographics, causes, symptoms)
- Recipe protocol data (carrier oils, preparation steps, usage instructions)

**Rationale**: Ensures saved protocols maintain full functionality  
**Priority**: Critical  
**Source**: Epic - Data Preservation Goals  

### R-012: Performance Requirements
**WHEN** user performs any protocol-related operation  
**IF** system resources are within normal operating parameters  
**THEN** system SHALL meet specified performance benchmarks  
**STATE** user experience remains smooth and responsive  

**Performance Benchmarks**:
- Protocol save operation: < 2 seconds
- Protocol list loading: < 1 second
- Protocol detail loading: < 500ms
- Oil substitution response: < 300ms
- Time slot navigation: < 100ms
- Smooth animations: 60fps maintained

**Rationale**: Ensures responsive user experience across all protocol operations  
**Priority**: High  
**Source**: Epic - Performance Requirements  

### R-013: Data Security and Privacy
**WHEN** system handles protocol data  
**IF** user authentication and authorization are valid  
**THEN** system SHALL enforce data security and privacy controls  
**STATE** user data remains secure and isolated  

**Security Requirements**:
- Row Level Security policies prevent cross-user data access
- All protocol operations require valid authentication tokens
- User can only access, modify, or delete their own protocols
- Data encryption in transit and at rest
- Audit logging for protocol operations

**Rationale**: Protects user privacy and maintains data security  
**Priority**: Critical  
**Source**: Epic - Security Requirements  

### R-014: Error Handling and Recovery
**WHEN** protocol operation fails due to system error  
**IF** error is recoverable  
**THEN** system SHALL provide clear error feedback and recovery options  
**STATE** user understands error condition and available actions  

**Error Handling Requirements**:
- Network errors: Show offline indicator and retry option
- Validation errors: Inline field validation with clear messages  
- Save conflicts: Automatic resolution or user choice options
- Database errors: User-friendly error messages without technical details
- Graceful degradation when features unavailable

**Rationale**: Maintains user trust and provides clear guidance during errors  
**Priority**: Medium  
**Source**: Epic - User Experience Requirements  

### R-015: Cross-Platform Compatibility
**WHEN** user accesses protocol functionality  
**IF** using supported mobile platform (iOS/Android)  
**THEN** system SHALL provide consistent experience across platforms  
**STATE** feature parity maintained regardless of platform  

**Compatibility Requirements**:
- Identical UI components and layouts on iOS and Android
- Consistent performance benchmarks across platforms
- Platform-appropriate animations and interactions
- Accessibility support (screen readers, voice control)
- Responsive design for various screen sizes

**Rationale**: Ensures uniform user experience across supported platforms  
**Priority**: Medium  
**Source**: Epic - Cross-Platform Requirements  

---

## Requirements Traceability Matrix

| Requirement | Epic Goal | User Story | Priority | Test Coverage |
|-------------|-----------|------------|----------|---------------|
| R-001 | Protocol-Centric Architecture | Story 1 | High | Unit, Integration |
| R-002 | Seamless User Experience | Story 1 | High | UI, Integration |
| R-003 | Complete Data Preservation | Epic Primary | Critical | Integration, Database |
| R-004 | Seamless User Experience | Story 2 | High | UI, Visual |
| R-005 | Seamless User Experience | Story 2 | Medium | UI |
| R-006 | Seamless User Experience | Story 3 | High | Integration, UI |
| R-007 | Seamless User Experience | Story 3 | High | UI, Performance |
| R-008 | Oil Substitution Support | Story 4 | High | Integration, Business Logic |
| R-009 | Complete Data Preservation | Story 4 | High | Integration, Database |
| R-010 | User Experience | Story 5 | Medium | UI, Integration |
| R-011 | Complete Data Preservation | Epic Primary | Critical | Integration, Database |
| R-012 | Performance Optimization | Epic Secondary | High | Performance |
| R-013 | Data Security | Epic Secondary | Critical | Security |
| R-014 | User Experience | Epic Secondary | Medium | Error Handling |
| R-015 | Cross-Platform Support | Epic Secondary | Medium | Cross-Platform |

## Non-Functional Requirements Summary

### Performance Requirements
- Sub-second response times for all user interactions
- 60fps animations across all supported devices
- Optimized database queries with proper indexing
- Efficient data caching strategies

### Security Requirements  
- End-to-end user data isolation
- Authentication and authorization at all data access points
- Secure data transmission and storage
- Comprehensive audit logging

### Usability Requirements
- Consistent with existing AromaCHAT user interface patterns
- Accessible design supporting screen readers and assistive technologies
- Clear error messages and recovery guidance
- Intuitive navigation matching user mental models

### Scalability Requirements
- Support unlimited protocols per user
- Efficient pagination for large protocol collections  
- Database schema optimized for growth
- Service architecture supporting horizontal scaling

---

## Validation Criteria

Each requirement SHALL be validated through:
1. **Unit Testing**: Individual component functionality
2. **Integration Testing**: End-to-end workflow validation  
3. **Performance Testing**: Benchmark compliance verification
4. **Security Testing**: Access control and data protection validation
5. **User Acceptance Testing**: Real-world usage scenario validation

This EARS requirements specification provides the detailed technical foundation for implementing the protocol-based saving system that transforms AromaCHAT from individual recipe saving to comprehensive protocol management.
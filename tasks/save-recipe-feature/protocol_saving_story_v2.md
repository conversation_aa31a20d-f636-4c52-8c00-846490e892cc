# USER STORY: Protocol-Based Saving System for AromaCHAT

## Story Overview
**Story ID**: ARC-2024-003-001  
**Story Title**: Implement Complete Protocol Saving and Management  
**Epic**: Protocol-Based Saving System  
**Story Owner**: Development Team  
**Priority**: High  
**Estimated Effort**: 8 story points  

## User Story Statement

**As** an AromaCHAT user creating personalized aromatherapy protocols  
**I want** to save my complete daily protocols (morning/mid-day/night recipes) with custom names  
**So that** I can build a personal aromatherapy library, reuse successful protocols, and modify oils as needed  

## Background & Context

### Current Pain Points
Users currently experience significant frustration:
1. **Fragmented Saving**: Can only save individual recipes, not complete daily protocols
2. **Context Loss**: Saved recipes lose connection to health concerns, demographics, and workflow context
3. **Broken Oil Substitution**: Cannot substitute oils in saved recipes due to missing enrichment data
4. **Poor Library Experience**: Cannot browse and manage holistic aromatherapy protocols

### User Mental Model
Users think in terms of **complete daily protocols**:
- "My morning stress relief routine" 
- "Evening relaxation protocol for headaches"
- "Daily anxiety management protocol"

They expect to save, name, and reuse these complete protocols just like they experience them in the final recipe screen.

## Detailed User Stories

### Story 1: Save Complete Protocol
**As** a user who just completed the recipe creation wizard  
**I want** to save my complete protocol (all 3 time slots) with a custom name  
**So that** I can easily identify and reuse this protocol later  

**Acceptance Criteria:**
- [ ] Save button appears in final recipe details modal
- [ ] Clicking save opens protocol naming modal
- [ ] User can enter custom protocol name (e.g., "My Stress Relief Protocol")
- [ ] Single save action stores all 3 recipes together as one protocol
- [ ] Success feedback confirms protocol saved
- [ ] Protocol appears in saved protocols screen immediately

### Story 2: Browse Saved Protocols
**As** a user with saved protocols  
**I want** to view my saved protocols in an overview format similar to the final recipe screen  
**So that** I can quickly identify and access the protocols I need  

**Acceptance Criteria:**
- [ ] Saved protocols screen shows protocol overview cards
- [ ] Each card displays protocol name, creation date, health concern
- [ ] Cards show visual indicators for time slots (morning/midday/night)
- [ ] Cards display key metrics (total oils, drops, etc.)
- [ ] Empty state encourages creating first protocol
- [ ] Pull-to-refresh updates protocol list

### Story 3: Access Complete Protocol Details
**As** a user viewing my saved protocols  
**I want** to tap a protocol card to see the complete details with time slot navigation  
**So that** I can review all aspects of my saved protocol  

**Acceptance Criteria:**
- [ ] Tapping protocol card opens protocol detail modal
- [ ] Modal shows identical layout to final recipe detail modal
- [ ] Time slot buttons allow navigation between morning/midday/night
- [ ] All recipe sections displayed: oils, carrier oil, preparation, application, rationales, safety
- [ ] Modal can be dismissed to return to protocols list

### Story 4: Substitute Oils in Saved Protocols
**As** a user viewing a saved protocol  
**I want** to substitute oils in my saved recipes just like in fresh recipes  
**So that** I can adapt my protocols when certain oils are unavailable  

**Acceptance Criteria:**
- [ ] Oil substitution button appears next to each oil in saved protocol
- [ ] Clicking substitution opens oil alternatives modal
- [ ] Modal shows relevant oil alternatives based on saved enrichment scores
- [ ] User can select alternative oil and confirm substitution
- [ ] Protocol updates immediately with new oil
- [ ] Changes persist when modal is closed and reopened

### Story 5: Manage Saved Protocols
**As** a user with multiple saved protocols  
**I want** to delete protocols I no longer need  
**So that** I can keep my protocol library organized and relevant  

**Acceptance Criteria:**
- [ ] Delete button appears on each protocol card
- [ ] Tapping delete shows confirmation dialog
- [ ] Confirmation dialog shows protocol name and warns about permanent deletion
- [ ] Confirming deletion removes protocol immediately
- [ ] Protocol disappears from list with smooth animation
- [ ] Success feedback confirms deletion completed

## Detailed Acceptance Criteria

### Functional Requirements

#### Protocol Saving Flow
1. **Save Button Integration**
   - Save button appears in final recipe details modal header
   - Button is prominently displayed and easily accessible
   - Button shows loading state during save operation

2. **Protocol Naming Modal**
   - Modal appears with text input for protocol name
   - Placeholder suggests naming pattern: "My [Health Concern] Protocol"
   - Input validation ensures name is not empty
   - Cancel and Save buttons provide clear actions

3. **Save Process**
   - Single atomic transaction saves complete protocol
   - All 3 time slot recipes saved together with shared protocol ID
   - Original workflow context preserved (health concern, demographics, causes, symptoms)
   - API-generated data preserved for oil substitution (rationales, enrichment scores)

#### Saved Protocols Screen
1. **Overview Layout**
   - Grid/list layout similar to final recipe screen design patterns
   - Protocol cards show key identifying information
   - Consistent visual design with app's material design system

2. **Protocol Cards Content**
   - Protocol name (user-defined)
   - Creation date (formatted for readability)
   - Health concern (original user input)
   - Time slot indicators (visual chips for morning/midday/night)
   - Quick stats (total oils, drops, volume)

3. **Navigation and Interaction**
   - Tap gesture opens protocol detail modal
   - Long press or dedicated button for delete action
   - Smooth animations for all interactions
   - Proper loading states for async operations

#### Protocol Detail Modal
1. **Modal Structure**
   - Identical layout to final recipe details modal
   - Time slot segmented buttons for navigation
   - All recipe sections: oils, carrier oil, preparation, application, rationales, safety
   - Modal backdrop allows dismiss by tapping outside

2. **Time Slot Navigation**
   - Segmented buttons show morning/midday/night options
   - Smooth transitions between time slots
   - Each time slot shows complete recipe details
   - State persists during navigation within modal

3. **Oil Substitution Integration**
   - Substitute button appears next to each oil
   - Oil substitution modal identical to final recipe experience
   - Alternative oils calculated using saved enrichment scores
   - Substitution updates protocol immediately and persistently

### Technical Requirements

#### Database Schema
1. **user_saved_protocols table**
   - Primary key (UUID)
   - User ID and protocol name
   - Complete workflow context (demographics, causes, symptoms, therapeutic properties)
   - Timestamps for creation and updates

2. **user_saved_recipes table**
   - Protocol ID foreign key relationship
   - Time slot designation
   - Recipe metrics and complete protocol data (JSONB)
   - Optimized for fast retrieval

3. **user_saved_recipe_oils table**
   - Recipe ID foreign key relationship
   - Oil ID foreign key to essential_oils table
   - API-generated data preservation (names, rationales)
   - Enrichment scores for oil substitution functionality

#### Service Layer
1. **Protocol Service Operations**
   - saveProtocol: Atomic save of protocol + recipes + oils
   - getProtocols: Efficient retrieval with pagination
   - getProtocolDetails: Full protocol reconstruction
   - deleteProtocol: Cascade deletion with confirmation
   - updateOilInProtocol: Oil substitution updates

2. **Data Transformation**
   - Store → Database mapping for save operations
   - Database → Store reconstruction for load operations
   - Oil substitution score integration
   - Error handling and rollback for failed operations

#### UI Components
1. **SaveProtocolModal**
   - Bottom sheet modal with protocol naming
   - Form validation and error handling
   - Loading states and success feedback
   - Integration with existing modal patterns

2. **SavedProtocolsScreen**
   - Protocol overview cards layout
   - Empty state and loading states
   - Pull-to-refresh functionality
   - Delete confirmation dialogs

3. **ProtocolDetailModal**
   - Reuse existing recipe detail modal architecture
   - Time slot navigation integration
   - Oil substitution button integration
   - Complete recipe data display

### Performance Requirements
- Protocol loading: < 1 second for any protocol
- Oil substitution: < 500ms response time
- Smooth animations: 60fps on all supported devices
- Database queries: Optimized with proper indexing

### Data Integrity Requirements
- Atomic save operations (all-or-nothing)
- Foreign key constraints prevent orphaned data
- User isolation through Row Level Security policies
- Complete data preservation (zero loss in save/load cycles)

## User Interface Mockups

### Save Protocol Flow
```
Final Recipe Modal → Save Button → Protocol Naming Modal → Success Feedback
       ↓
Saved Protocols Screen (shows new protocol)
```

### Saved Protocols Screen Layout
```
[Header: "Saved Protocols"]
[Pull-to-refresh area]

┌─────────────────────────────────────┐
│ My Stress Relief Protocol           │
│ Created March 15, 2024              │  
│ 🌅 ☀️ 🌙  Stress & Anxiety         │
│ 6 oils • 18 drops • 15ml total     │
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│ Evening Headache Relief             │
│ Created March 10, 2024              │
│ 🌙  Tension Headaches               │
│ 4 oils • 12 drops • 10ml total     │
└─────────────────────────────────────┘
```

### Protocol Detail Modal
```
[Modal Header: "🧪 Protocol Details"]
[Time Slot Tabs: Morning | Mid-Day | Night]

[Recipe details identical to final recipe modal]
[Oil substitution buttons for each oil]
[All sections: oils, carrier, preparation, etc.]
```

## Definition of Done

### Code Quality
- [ ] All code follows established patterns in codebase
- [ ] TypeScript types properly defined
- [ ] Error handling comprehensive
- [ ] Performance optimized
- [ ] Code reviewed and approved

### Testing
- [ ] Unit tests for service layer
- [ ] Integration tests for save/load workflows
- [ ] UI tests for critical user flows
- [ ] Database migration tested
- [ ] Cross-device compatibility verified

### Documentation
- [ ] Code comments for complex logic
- [ ] README updates for new components
- [ ] Database schema documentation
- [ ] API documentation updated

### User Experience
- [ ] Feature works on iOS and Android
- [ ] Animations smooth and performant
- [ ] Loading states and error handling
- [ ] Accessibility compliance (screen reader support)
- [ ] Design review approved

---

This story transforms the fragmented recipe saving experience into a cohesive protocol-based system that matches user mental models and enables comprehensive aromatherapy protocol management.
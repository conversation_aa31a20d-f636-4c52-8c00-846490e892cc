# Debug Integration Screen - Usage Guide

## Overview

The debug integration screen (`src/app/(drawer)/debug-integration.tsx`) provides comprehensive testing for your Clerk + Supabase integration. Use this to diagnose and resolve authentication and database issues.

## Error Fixed

### Original Issue
```
ERROR Failed to load saved recipes: [TypeError: Cannot read property 'getUserRecipes' of undefined]
```

### Root Cause
The `savedRecipesService` was not properly exported as an object from the service file.

### Solution Applied
Added service object export to `/src/shared/services/supabase/saved-recipes.service.ts`:
```typescript
export const savedRecipesService = {
  saveRecipe: saveRecipeToDatabase,
  getUserRecipes: getUserSavedRecipes,
  deleteRecipe: deleteSavedRecipe,
  hydrateOils: hydrateRecipeOils,
  cacheOffline: cacheSavedRecipesOffline,
  loadCached: loadCachedSavedRecipes,
} as const;
```

## How to Access the Debug Screen

### Temporary Addition to Drawer Menu

1. **Open drawer layout**: `/src/app/(drawer)/_layout.tsx`

2. **Add debug screen to drawer** (temporarily for development):
   ```typescript
   // Add this item to your drawer content
   <Drawer.Screen
     name="debug-integration"
     options={{
       title: '🔧 Debug Integration',
       drawerIcon: ({ focused, color, size }) => (
         <MaterialCommunityIcons 
           name="bug" 
           size={size} 
           color={color} 
         />
       ),
     }}
   />
   ```

3. **Alternative: Direct Navigation** (if you have dev menu):
   ```typescript
   // In your dev menu or test button
   router.push('/(drawer)/debug-integration');
   ```

## Test Suite Overview

The debug screen runs 9 comprehensive tests:

### 1. Clerk Authentication
**Tests**: User session, authentication state, user data access
**What it checks**:
- ✅ User is logged in
- ✅ User object has required fields
- ✅ Authentication hooks are loaded

**Potential Issues**:
- User not authenticated → Sign in required
- Clerk not loaded → Check Clerk configuration

### 2. JWT Token Generation
**Tests**: JWT template configuration and token creation
**What it checks**:
- ✅ `supabase` JWT template exists in Clerk
- ✅ Token generation succeeds
- ✅ Token payload has required claims

**Potential Issues**:
- "No JWT template exists with name: supabase" → Follow [Clerk Setup Guide](./clerk_supabase_setup_guide.md)
- Invalid token payload → Check JWT template configuration

### 3. Supabase Client Creation
**Tests**: Client initialization with authentication
**What it checks**:
- ✅ Client creation with JWT token
- ✅ Authentication headers set correctly
- ✅ Environment variables configured

**Potential Issues**:
- Missing environment variables → Check `.env` file
- Client creation fails → Check Supabase URL/keys

### 4. Database Connection
**Tests**: Basic database connectivity
**What it checks**:
- ✅ Can connect to database
- ✅ Can query system tables
- ✅ Database is accessible

**Potential Issues**:
- Connection timeout → Check Supabase instance status
- Authentication errors → Check JWT token configuration

### 5. Essential Oils Table
**Tests**: Required table for foreign key constraints
**What it checks**:
- ✅ `essential_oils` table exists
- ✅ Table has required columns
- ✅ Sample data available

**Potential Issues**:
- Table doesn't exist → Import essential oils data
- Missing columns → Check table schema
- No data → Seed essential oils table

### 6. Saved Recipes Schema
**Tests**: Schema validation for save recipe feature
**What it checks**:
- ✅ `user_saved_recipes` table exists
- ✅ `user_saved_recipe_oils` table exists
- ✅ Required columns present

**Potential Issues**:
- Tables don't exist → Run database schema SQL
- Missing columns → Check schema file execution

### 7. Row Level Security (RLS)
**Tests**: Security policies and data isolation
**What it checks**:
- ✅ Can access own recipes
- ✅ Cannot access other users' recipes
- ✅ RLS policies are active

**Potential Issues**:
- RLS not enabled → Execute RLS setup SQL
- Policy violations → Check JWT token `sub` claim
- Unauthorized access → Review RLS policies

### 8. Service Layer
**Tests**: Service layer integration and functionality
**What it checks**:
- ✅ Service methods are available
- ✅ Can call service functions
- ✅ Service layer handles authentication

**Potential Issues**:
- Service undefined → Check imports/exports
- Method calls fail → Check service implementation

### 9. CRUD Operations
**Tests**: End-to-end database operations
**What it checks**:
- ✅ Can create test recipe
- ✅ Can delete test recipe
- ✅ Foreign key constraints work

**Expected Behavior**:
- Will likely fail due to foreign key constraints (expected)
- Requires valid `oil_id` from `essential_oils` table
- Failure is normal without real oil data

## Interpreting Results

### ✅ Success States
- **Green checkmarks**: Tests passed successfully
- **Detailed data**: JSON objects show actual response data
- **Timing info**: Performance metrics in milliseconds

### ❌ Error States
- **Red alert icons**: Tests failed with errors
- **Error messages**: Detailed failure descriptions
- **Debug data**: Technical details for troubleshooting

### 🟡 Expected Failures
Some tests are expected to fail in certain scenarios:
- **CRUD Operations**: Expected to fail without real essential oil data
- **RLS Tests**: May show warnings but still pass
- **Schema Tests**: May report missing tables before setup

## Common Issues & Solutions

### JWT Template Missing
```
Error: No JWT template exists with name: supabase
```
**Solution**: Follow the [Clerk + Supabase Setup Guide](./clerk_supabase_setup_guide.md)

### Database Tables Missing
```
relation "user_saved_recipes" does not exist
```
**Solution**: Execute the database schema:
```sql
-- Run this in your Supabase SQL editor
\i /path/to/aromachat/database/saved-recipes-schema.sql
```

### RLS Policy Errors
```
new row violates row-level security policy
```
**Solution**: Check JWT token `sub` claim matches database `user_id` format

### Foreign Key Violations
```
insert or update on table violates foreign key constraint
```
**Solution**: Ensure `essential_oils` table is populated with valid data

### Environment Variables Missing
```
Missing Supabase environment variables
```
**Solution**: Check your `.env` file:
```bash
EXPO_PUBLIC_SUPABASE_URL=https://your-supabase-domain.com
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_key
```

## Test Results Analysis

### Perfect Results (All Green)
- ✅ All 9 tests passing
- ✅ Integration fully working
- ✅ Ready for production use

### Partial Success (Some Failures)
- ⚠️ Identify which systems are failing
- ⚠️ Focus on critical failures first (Auth, Database Connection)
- ⚠️ Some failures might be expected (CRUD without real data)

### Multiple Failures
- ❌ Start with basic tests (Clerk Auth, JWT Token)
- ❌ Work sequentially through setup guides
- ❌ Check environment configuration

## Development Workflow

### 1. Initial Setup
1. Run debug screen
2. Note all failures
3. Follow setup guides for failures
4. Re-run tests

### 2. Database Setup
1. Execute database schema SQL
2. Verify table creation
3. Set up RLS policies
4. Test database access

### 3. Authentication Integration
1. Configure JWT template in Clerk
2. Test token generation
3. Verify token payload
4. Test Supabase authentication

### 4. Service Layer Testing
1. Test service method availability
2. Verify CRUD operations
3. Check error handling
4. Validate performance

### 5. Production Readiness
1. All critical tests passing
2. Performance within acceptable ranges
3. Error handling working correctly
4. Security policies active

## Cleanup

### Remove Debug Screen
Once your integration is working:

1. **Remove from drawer menu**:
   ```typescript
   // Remove this from _layout.tsx
   // <Drawer.Screen name="debug-integration" ... />
   ```

2. **Delete debug file** (optional):
   ```bash
   rm src/app/(drawer)/debug-integration.tsx
   ```

3. **Keep for future debugging**:
   - Rename to `debug-integration.tsx.disabled`
   - Comment out in drawer layout
   - Keep for future troubleshooting

## Performance Expectations

### Acceptable Response Times
- **JWT Token Generation**: < 100ms
- **Database Connection**: < 200ms
- **Table Queries**: < 500ms
- **Service Operations**: < 1000ms

### Performance Issues
- **Slow responses**: Check network connection, Supabase instance performance
- **Timeouts**: Increase timeout values, check Supabase configuration
- **Memory issues**: Monitor React Native memory usage during tests

## Security Considerations

### Development Only
- ⚠️ Debug screen exposes sensitive information
- ⚠️ JWT tokens, user IDs, database structure
- ⚠️ Remove before production deployment

### Safe Debugging
- ✅ Only log first few characters of tokens
- ✅ Mask sensitive user data
- ✅ Use environment checks for debug logs

## Support

### If Tests Continue Failing
1. Check all environment variables
2. Verify Supabase instance is running
3. Confirm Clerk project configuration
4. Review database schema execution
5. Test with fresh database/user

### Next Steps After Success
1. Test save recipe functionality in main app
2. Verify saved recipes screen loads correctly
3. Test CRUD operations with real data
4. Monitor performance in production

The debug screen provides comprehensive diagnostics for your Clerk + Supabase integration. Use it systematically to identify and resolve integration issues.
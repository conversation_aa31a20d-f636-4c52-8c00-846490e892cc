# EPIC: Save Recipe Feature - Personal Aromatherapy Library

**References:**
- **Product Requirements Document:** [`prd.md`](./prd.md)
- **User Story:** [`story.md`](./story.md) 
- **Engineering Requirements:** [`requirements.md`](./requirements.md)
- **Implementation Plan:** [`implementation_todolist.md`](./implementation_todolist.md)
- **Feasibility Analysis:** [`feasibility_analysis_report.md`](./feasibility_analysis_report.md)

## User Problem
As an AromaCHAT user, after investing significant time completing the 6-step recipe creation wizard to generate personalized essential oil protocols, I lose all my valuable recipe data when I close the app or navigate away. I have no way to reference, reuse, or build upon the therapeutic recipes that were specifically created for my health concerns, demographics, and selected symptoms. This forces me to either recreate similar recipes repeatedly or abandon the therapeutic benefits of recipes I found effective, severely limiting the long-term value of the app.

## Feature Description
This epic introduces a comprehensive "Save Recipe" system that transforms AromaCHAT from a single-use recipe generator into a personal aromatherapy library. Users will be able to save their generated recipes directly from the Final Recipes screen, organize them in a dedicated Saved Recipes section accessible via the main navigation drawer, and retrieve complete recipe details on-demand by leveraging the existing Supabase oil database for real-time data hydration.

The feature implements an optimized data architecture where only essential recipe metadata and oil references (`oil_id` + `drops_count`) are stored in the user's personal database, while complete oil information (names, botanical data, safety profiles, properties) is dynamically retrieved from the existing Supabase oils database. This approach ensures minimal storage overhead, consistent safety data, always up-to-date oil information, and seamless integration with the current recipe ecosystem.

### Key Goals
- **Preserve User Investment:** Protect the time and effort users invest in the 6-step recipe creation process
- **Build Personal Library:** Enable users to accumulate and organize personalized aromatherapy recipes over time
- **Enable Recipe Reuse:** Provide easy access to previously successful therapeutic combinations
- **Support Offline Access:** Allow users to reference their saved recipes without internet connectivity
- **Maintain Data Consistency:** Ensure saved recipes always reflect current oil safety and property information
- **Seamless Integration:** Build upon existing Final Recipes workflow without disrupting current user experience

---

## Acceptance Criteria

- **AC1: Save Recipe Capability:** On the Final Recipes screen, each recipe protocol (morning/mid-day/night) must have a clearly visible "Save Recipe" button that triggers the save workflow.

- **AC2: Save Recipe Modal:** Tapping "Save Recipe" must open a Bottom Sheet modal displaying:
    - A pre-filled recipe name field (editable by user)
    - Checkboxes for selecting which time slots to save (default: current time slot selected)
    - Save and Cancel action buttons
    - Loading state during save operation

- **AC3: Recipe Storage:** When a recipe is saved, the system must store:
    - Complete recipe metadata (health concern, demographics, causes, symptoms)
    - Essential oil references (oil_id and drops_count for each oil)
    - Recipe protocol data (preparation steps, usage instructions, safety warnings, etc.)
    - User-defined recipe name and creation timestamp
    - Association with authenticated user (Clerk user ID)

- **AC4: Saved Recipes Navigation:** The main navigation drawer must provide access to a "Saved Recipes" screen that displays all user's saved recipes in an organized, scrollable list.

- **AC5: Recipe List Display:** The Saved Recipes screen must show recipe cards containing:
    - User-defined recipe name and original health concern
    - Number of essential oils and applicable time slots
    - Creation date and quick preview information
    - Tap-to-expand functionality for full recipe details

- **AC6: Oil Data Hydration:** When displaying saved recipe details, the system must:
    - Retrieve current oil information from `essential_oils_with_safety_ids` view using stored UUID oil_ids
    - Display complete oil details (names, botanical data, JSONB safety information including internal_use, dilution, phototoxicity, pregnancy_nursing_safety, child_safety)
    - Exclude embedding column from queries to avoid performance issues
    - Handle cases where oils may no longer exist in the database with graceful fallbacks
    - Provide offline fallback with cached basic oil information

- **AC7: Recipe Management:** Users must be able to:
    - View complete saved recipe details matching the Final Recipes screen layout
    - Edit recipe names through a simple interface
    - Delete unwanted recipes with confirmation dialog
    - Search/filter recipes by name or health concern

- **AC8: Data Persistence:** Saved recipes must:
    - Persist across app sessions and device restarts
    - Maintain association with the correct user account
    - Sync properly when users switch devices or reinstall the app
    - Include proper error handling for network failures

- **AC9: Performance Requirements:** The system must:
    - Save recipes within 2 seconds of user action
    - Load the Saved Recipes list within 3 seconds
    - Display individual recipe details within 1 second (with cached oil data)
    - Handle 100+ saved recipes without performance degradation

---

## Technical Plan

### 1. Verified Database Architecture (Integrates with Existing Supabase Structure)

The feature implements a normalized database design that leverages your existing essential oils infrastructure:

#### Primary Tables (Integrating with Existing Schema):
- **`user_saved_recipes`**: Core recipe metadata and protocol data
  - User identification (Clerk user ID), recipe naming, time slot categorization
  - Original wizard context (health concern, demographics, selections)  
  - Complete recipe protocol (JSONB storage for preparation, usage, safety data)
  - Computed metadata (total drops, volume, oil count) for filtering and search

- **`user_saved_recipe_oils`**: Normalized oil references with foreign key integrity
  - Links to recipes with `oil_id` UUID references to existing `essential_oils` table
  - Preserves oil order and drops_count per recipe
  - Foreign key constraints ensure referential integrity with existing oil data

#### Verified Oil Data Strategy (Based on Actual Database Analysis):
- **Existing Infrastructure:** 120 essential oils in `essential_oils` table with UUID primary keys
- **Safety View:** `essential_oils_with_safety_ids` provides pre-aggregated JSONB safety data
- **Store:** Only `oil_id` (UUID) and `drops_count` per recipe oil  
- **Retrieve:** Complete oil details from `essential_oils_with_safety_ids` view (excluding embedding column)
- **Benefits:** Always current safety data, consistent information, minimal storage, leverages existing indexes
- **MCP Access:** Direct database integration via `mcp__supabase__query` for development and testing

### 2. Service Layer Implementation

#### File Structure:
```
src/shared/services/supabase/
├── supabase-client.ts          # Client configuration with Clerk auth integration
├── saved-recipes.service.ts    # Complete CRUD operations for recipe management
└── types/saved-recipe.types.ts # TypeScript interfaces and data contracts
```

#### Core Service Methods (With Verified Supabase Integration):
- `saveRecipe()`: Persist recipe with UUID oil references and complete metadata
- `getUserRecipes()`: Retrieve user's recipe collection with filtering options
- `getRecipeDetails()`: Load complete recipe with hydrated oil data from `essential_oils_with_safety_ids`
- `hydrateRecipeOils()`: Batch load oil details excluding embedding column for performance
- `updateRecipeName()`: Modify user-defined recipe names
- `deleteRecipe()`: Remove recipes with proper cleanup and foreign key cascade
- `validateOilReferences()`: Ensure oil IDs exist in essential_oils table
- `testConnection()`: MCP-based connection testing for development

### 3. State Management Integration

#### Zustand Store Extension:
- Extend existing recipe store with saved recipes state slice
- Implement optimistic updates for better user experience
- Manage loading states, error handling, and offline synchronization
- Provide actions for save, load, update, and delete operations

#### State Architecture:
```typescript
interface SavedRecipesState {
  savedRecipes: SavedRecipe[];          // User's recipe collection
  isLoading: boolean;                   // Loading state management  
  error: string | null;                 // Error handling
  lastSyncTime: Date | null;            // Offline sync tracking
}
```

### 4. User Interface Integration

#### Save Recipe Workflow:
- **Integration Point:** Add "Save Recipe" button to existing `FinalRecipeList` component
- **Modal Pattern:** Implement using established `@gorhom/bottom-sheet` + `useModalManager` patterns
- **Design Consistency:** Follow Material Design 3 theming with `useTheme()` hook

#### Saved Recipes Screen:
- **Route:** Utilize existing `/saved-recipes` navigation route in drawer
- **Layout:** Implement card-based recipe list with expand/collapse functionality  
- **Components:** Reuse `FinalRecipeList` component for recipe detail display
- **Search/Filter:** Provide intuitive filtering by health concern, time slot, and creation date

### 5. Offline Support Strategy

#### Local Caching:
- Cache saved recipes using `@react-native-async-storage/async-storage`
- Store basic oil information for offline recipe viewing
- Implement background sync when network connectivity is restored
- Provide visual indicators for offline/stale data states

#### Sync Strategy:
- Last-write-wins conflict resolution for recipe updates
- Queue offline changes for synchronization when online
- Handle authentication token refresh during sync operations

---

## User Stories

- **US1: Save Recipe Access:** As a user, I want to see a "Save Recipe" button on each recipe protocol so I know I can preserve recipes for future reference.

- **US2: Custom Recipe Naming:** As a user, when I save a recipe, I want to give it a memorable name so I can easily find it later among my saved recipes.

- **US3: Selective Saving:** As a user, I want to choose which time slots (morning/mid-day/night) to save from a multi-protocol recipe so I only keep the ones relevant to my routine.

- **US4: Recipe Library Access:** As a user, I want to access my saved recipes through the main navigation so I can quickly find and reference my personal recipe collection.

- **US5: Complete Recipe Details:** As a user, when I view a saved recipe, I want to see all the original recipe information including current oil safety data so I can follow the recipe accurately.

- **US6: Recipe Organization:** As a user, I want to see my saved recipes organized with names, health concerns, and creation dates so I can quickly identify the recipe I'm looking for.

- **US7: Recipe Management:** As a user, I want to edit recipe names and delete recipes I no longer need so I can keep my recipe library organized and relevant.

- **US8: Offline Access:** As a user, I want to access my saved recipes even when I don't have internet connection so I can reference them whenever needed.

- **US9: Current Oil Information:** As a user, I want my saved recipes to always show the most up-to-date oil safety information so I can use them confidently over time.

- **US10: Search Capability:** As a user, I want to search through my saved recipes by name or health concern so I can quickly find specific recipes in a large collection.

## Out of Scope

- **Recipe Sharing:** Sharing recipes with other users is not included in this epic
- **Recipe Rating System:** User rating/effectiveness tracking for recipes
- **Recipe Modification:** Editing recipe content (oils, drops, instructions) after saving
- **Recipe Categories:** Custom user-defined categories or tagging system  
- **Export Functionality:** Exporting recipes to PDF, email, or other external formats
- **Community Features:** Public recipe library or social recipe discovery
- **Advanced Analytics:** Usage tracking, recipe success metrics, or recommendation engine
- **Multi-Device Sync:** Advanced synchronization features beyond basic cloud storage
- **Recipe Versioning:** Tracking changes or maintaining recipe history
- **Bulk Operations:** Mass operations on multiple recipes (bulk delete, export, etc.)

The focus remains on core save/retrieve functionality that provides immediate value to users while establishing a solid foundation for future enhancements.
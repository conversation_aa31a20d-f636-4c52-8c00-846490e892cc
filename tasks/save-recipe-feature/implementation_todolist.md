# Save Recipe Feature: Implementation Todolist

**References:**
- **Epic:** [`epic.md`](./epic.md) - EPIC: Save Recipe Feature - Personal Aromatherapy Library
- **User Story:** [`story.md`](./story.md) - Save Recipe Feature for Personal Aromatherapy Library  
- **Product Requirements Document:** [`prd.md`](./prd.md)
- **Engineering Requirements:** [`requirements.md`](./requirements.md)
- **Feasibility Analysis:** [`feasibility_analysis_report.md`](./feasibility_analysis_report.md)

This document breaks down the implementation of the "Save Recipe" feature into phases and micro-tasks to allow for incremental development, testing, and validation.

## Phase 0: Environment & Authentication Setup

This critical phase ensures all prerequisites are properly configured before development begins.

### Macro-Task 0.1: Environment Configuration

- **Description:** Set up all necessary environment variables and external service configurations.
- [ ] **Micro-Task 0.1.1: Configure Supabase Environment Variables**
  - [ ] **Sub-Task:** Add `EXPO_PUBLIC_SUPABASE_URL` to `.env` file with your project URL.
  - [ ] **Sub-Task:** Add `EXPO_PUBLIC_SUPABASE_ANON_KEY` to `.env` file with your anon key.
  - [ ] **Sub-Task:** Verify environment variables are properly loaded in development.
- [ ] **Micro-Task 0.1.2: Clerk-Supabase Integration Template**
  - [ ] **Sub-Task:** Configure Clerk JWT template for Supabase authentication.
  - [ ] **Sub-Task:** Set up template with proper claims for `auth.uid()` in RLS policies.
  - [ ] **Sub-Task:** Test JWT token generation with actual user authentication.
- **Dependencies:**
  - Clerk dashboard access.
  - Supabase project access.
- **Files to Edit:**
  - `.env` or environment configuration file.

### Macro-Task 0.2: MCP Development Validation

- **Description:** Validate database connectivity and structure using MCP integration.
- [ ] **Micro-Task 0.2.1: Test Basic Database Connection**
  - [ ] **Sub-Task:** Execute `mcp__supabase__query` with a simple query like `SELECT COUNT(*) FROM essential_oils;`.
  - [ ] **Sub-Task:** Verify MCP integration returns expected results.
- [ ] **Micro-Task 0.2.2: Validate Oil ID Structure**
  - [ ] **Sub-Task:** Query specific oil used in logs: `SELECT * FROM essential_oils_with_safety_ids WHERE id = 'ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf';`.
  - [ ] **Sub-Task:** Verify the returned data structure matches expectations (multi-language names, JSONB safety data).
  - [ ] **Sub-Task:** Confirm embedding column exclusion works properly.
- [ ] **Micro-Task 0.2.3: Test RLS Policy Foundation**
  - [ ] **Sub-Task:** Create a test RLS policy and verify `auth.uid()` works with Clerk tokens.
  - [ ] **Sub-Task:** Test policy enforcement with actual authenticated requests.
- **Dependencies:**
  - MCP Supabase integration setup.
  - Macro-Task 0.1 (Environment Configuration).
- **Files to Validate:**
  - Supabase database schema and permissions.

## Phase 1: Backend & Core Infrastructure

This phase focuses on setting up the database and the service layer to handle all data operations. This can be fully tested independently of the UI.

### Macro-Task 1.1: Database Setup

- **Description:** Create the necessary Supabase tables and security policies.
- [ ] **Micro-Task 1.1.1: Execute Complete Database Schema**
  - [ ] **Sub-Task:** Execute the complete SQL script from [`requirements.md lines 40-104`](./requirements.md#part-a-database-schema-implementation) in Supabase SQL editor.
  - [ ] **Sub-Task:** This creates both `user_saved_recipes` and `user_saved_recipe_oils` tables with all columns, foreign key constraints, performance indexes, RLS policies, and triggers.
  - [ ] **Sub-Task:** Verify all tables, indexes, and policies were created successfully.
- [ ] **Micro-Task 1.1.2: Validate Database Schema**
  - [ ] **Sub-Task:** Test foreign key constraint from `user_saved_recipe_oils.oil_id` to existing `essential_oils(id)` table.
  - [ ] **Sub-Task:** Verify RLS policies work with Clerk `auth.uid()` integration.
  - [ ] **Sub-Task:** Test automatic timestamp updates with the trigger function.
- **Dependencies:**
  - Supabase project access.
- **Files to Edit:**
  - Supabase SQL Editor or a new migration file.

### Macro-Task 1.2: Service Layer & Types

- **Description:** Build the services to communicate with the new database tables and define the data structures.
- [ ] **Micro-Task 1.2.1: Install Dependencies**
  - [ ] **Sub-Task:** Run `npm install @supabase/supabase-js @react-native-async-storage/async-storage`.
- [ ] **Micro-Task 1.2.2: Implement TypeScript Types**
  - [ ] **Sub-Task:** Create `src/shared/services/supabase/types/saved-recipe.types.ts` using the complete type definitions from [`requirements.md lines 160-237`](./requirements.md#part-c-create-supabase-service-layer).
  - [ ] **Sub-Task:** Ensure all interfaces (`SaveRecipePayload`, `SavedRecipe`, `SavedRecipeOil`, `HydratedRecipeOil`, `QueryOptions`) are implemented exactly as specified.
- [ ] **Micro-Task 1.2.3: Implement Supabase Client**
  - [ ] **Sub-Task:** Create `src/shared/services/supabase/supabase-client.ts` using the complete implementation from [`requirements.md lines 120-158`](./requirements.md#part-c-create-supabase-service-layer).
  - [ ] **Sub-Task:** Configure Clerk authentication integration with `useSupabaseClient` hook.
- [ ] **Micro-Task 1.2.4: Implement Complete SavedRecipesService**
  - [ ] **Sub-Task:** Create `src/shared/services/supabase/saved-recipes.service.ts` using the full implementation from [`requirements.md lines 239-477`](./requirements.md#part-c-create-supabase-service-layer).
  - [ ] **Sub-Task:** Implement all methods: `saveRecipe`, `getUserRecipes`, `getRecipeById`, `hydrateRecipeOils` (excludes embedding column), `updateRecipeName`, `deleteRecipe`, `validateOilReferences`, `testConnection`.
  - [ ] **Sub-Task:** Test all service methods with actual database operations.
- [ ] **Micro-Task 1.2.5: Input Validation & Error Handling**
  - [ ] **Sub-Task:** Add comprehensive input validation for `SaveRecipePayload`.
  - [ ] **Sub-Task:** Implement proper error handling for network failures, auth expiration, and constraint violations.
  - [ ] **Sub-Task:** Add graceful handling for missing or deleted oil references.
  - [ ] **Sub-Task:** Test error scenarios with actual database constraints.
- **Dependencies:**
  - Macro-Task 1.1 (Database Setup).
- **Files to Create:**
  - `src/shared/services/supabase/types/saved-recipe.types.ts`
  - `src/shared/services/supabase/supabase-client.ts`
  - `src/shared/services/supabase/saved-recipes.service.ts`

## Phase 1.5: User Context Architecture

This phase addresses the critical challenge of integrating Clerk user context with Zustand store actions.

### Macro-Task 1.5.1: Authentication Integration Pattern

- **Description:** Establish proper user context handling patterns for the save recipe feature.
- [ ] **Micro-Task *******: Create Authentication Hook Wrapper**
  - [ ] **Sub-Task:** Create `src/shared/hooks/use-auth-supabase.ts` hook that combines Clerk `useAuth` with Supabase client configuration.
  - [ ] **Sub-Task:** Implement automatic token refresh and error handling.
  - [ ] **Sub-Task:** Test hook with actual user authentication flow.
- [ ] **Micro-Task *******: Component-Level User Context Pattern**
  - [ ] **Sub-Task:** Establish pattern for passing `userId` from component level to Zustand store actions.
  - [ ] **Sub-Task:** Create helper functions to extract user context consistently.
  - [ ] **Sub-Task:** Document the authentication flow for other developers.
- [ ] **Micro-Task *******: Authentication Error Handling**
  - [ ] **Sub-Task:** Implement handling for expired tokens and re-authentication.
  - [ ] **Sub-Task:** Add user-friendly error messages for authentication failures.
  - [ ] **Sub-Task:** Test authentication error scenarios and recovery paths.
- **Dependencies:**
  - Phase 0 (Environment & Authentication Setup).
  - Macro-Task 1.2 (Service Layer).
- **Files to Create:**
  - `src/shared/hooks/use-auth-supabase.ts`

## Phase 2: State Management & UI Integration

This phase connects the backend services to the application's state and builds the user-facing components.

### Macro-Task 2.1: State Management (Zustand)

- **Description:** Extend the existing Zustand store to manage saved recipes.
- [ ] **Micro-Task 2.1.1: Update Store Interfaces**
  - [ ] **Sub-Task:** Modify `src/features/create-recipe/store/recipe.slice.ts` following [`requirements.md lines 483-533`](./requirements.md#part-d-extend-recipe-store-with-saved-recipes-state).
  - [ ] **Sub-Task:** Add saved recipes state properties (`savedRecipes`, `savedRecipesLoading`, etc.) to `RecipeSliceState` interface.
  - [ ] **Sub-Task:** Add all new action signatures to `RecipeSliceActions` interface.
  - [ ] **Sub-Task:** Update the `initialRecipeState` with new default values.
- [ ] **Micro-Task 2.1.2: Implement Complete Store Actions**
  - [ ] **Sub-Task:** Implement all store actions using the full implementation from [`requirements.md lines 535-705`](./requirements.md#part-d-extend-recipe-store-with-saved-recipes-state).
  - [ ] **Sub-Task:** Pay special attention to user context passing pattern (userId from component to store).
  - [ ] **Sub-Task:** Test all actions with proper error handling and optimistic updates.
- [ ] **Micro-Task 2.1.3: Implement Offline Caching**
  - [ ] **Sub-Task:** Implement the offline caching logic using `AsyncStorage` within `cacheSavedRecipesOffline` and `loadSavedRecipesFromCache`.
  - [ ] **Sub-Task:** Add conflict resolution strategy for offline/online sync.
  - [ ] **Sub-Task:** Implement cache invalidation and refresh logic.
  - [ ] **Sub-Task:** Add offline status indicators and user feedback.
- **Dependencies:**
  - Macro-Task 1.2 (Service Layer).
  - Phase 1.5 (User Context Architecture).
- **Files to Edit:**
  - `src/features/create-recipe/store/recipe.slice.ts`

### Macro-Task 2.2: "Save Recipe" Workflow UI

- **Description:** Create the UI for the user to save a recipe.
- [ ] **Micro-Task 2.2.1: Create Save Recipe Modal**
  - [ ] **Sub-Task:** Create `src/features/create-recipe/components/modals/save-recipe-bottom-sheet.tsx` using the complete implementation from [`requirements.md lines 709-945`](./requirements.md#part-e-create-save-recipe-bottom-sheet-modal).
  - [ ] **Sub-Task:** Implement all modal functionality: recipe name editing, time slot selection, validation, and user context integration.
  - [ ] **Sub-Task:** Test modal with actual save operations and error scenarios.
- [ ] **Micro-Task 2.2.2: Integrate Save Button into Final Recipes Screen**
  - [ ] **Sub-Task:** Modify `src/features/create-recipe/components/screens/final-recipes/final-recipes-list.tsx` following [`requirements.md lines 947-1074`](./requirements.md#part-f-modify-final-recipes-list-to-add-save-button).
  - [ ] **Sub-Task:** Add `onSaveRecipe` prop to `RecipeDetailsHeader` component.
  - [ ] **Sub-Task:** Add `showSaveButton` prop to `FinalRecipeList` component with modal integration.
  - [ ] **Sub-Task:** Implement success feedback and proper user context handling.
- **Dependencies:**
  - Macro-Task 2.1 (State Management).
- **Files to Create:**
  - `src/features/create-recipe/components/modals/save-recipe-bottom-sheet.tsx`
- **Files to Edit:**
  - `src/features/create-recipe/components/screens/final-recipes/final-recipes-list.tsx`

### Macro-Task 2.3: "My Saved Recipes" Screen UI

- **Description:** Build the screen where users can view and manage their saved recipes.
- [ ] **Micro-Task 2.3.1: Create Complete Saved Recipes Screen**
  - [ ] **Sub-Task:** Create `src/app/(drawer)/(tabs)/saved-recipes.tsx` using the full implementation from [`requirements.md lines 1076-1398`](./requirements.md#part-g-create-saved-recipes-screen).
  - [ ] **Sub-Task:** Implement all functionality: search, filtering, recipe cards, empty states, pull-to-refresh, FAB navigation.
  - [ ] **Sub-Task:** Test with authentication flows and error handling scenarios.
- **Dependencies:**
  - Macro-Task 2.1 (State Management).
- **Files to Create:**
  - `src/app/(drawer)/(tabs)/saved-recipes.tsx`

### Macro-Task 2.4: Saved Recipe Detail Implementation

- **Description:** Create detailed view for individual saved recipes with oil data hydration.
- [ ] **Micro-Task 2.4.1: Create Recipe Detail Modal/Screen**
  - [ ] **Sub-Task:** Create `src/features/create-recipe/components/screens/saved-recipe-detail.tsx`.
  - [ ] **Sub-Task:** Implement oil data hydration for saved recipe display.
  - [ ] **Sub-Task:** Reuse `FinalRecipeList` component for consistent layout.
  - [ ] **Sub-Task:** Add proper loading states during oil hydration.
- [ ] **Micro-Task 2.4.2: Recipe Management Actions**
  - [ ] **Sub-Task:** Add edit recipe name functionality with validation.
  - [ ] **Sub-Task:** Implement delete recipe with confirmation dialog.
  - [ ] **Sub-Task:** Add proper error handling for management operations.
  - [ ] **Sub-Task:** Test edge cases like deleted oil references.
- [ ] **Micro-Task 2.4.3: Navigation Integration**
  - [ ] **Sub-Task:** Implement navigation from recipe list to detail view.
  - [ ] **Sub-Task:** Add proper routing and back navigation handling.
  - [ ] **Sub-Task:** Test navigation flow with actual saved recipes.
- **Dependencies:**
  - Macro-Task 2.1 (State Management).
  - Macro-Task 2.3 (Saved Recipes Screen).
- **Files to Create:**
  - `src/features/create-recipe/components/screens/saved-recipe-detail.tsx`

## Phase 3: Internationalization & Final Polish

This phase ensures the feature is fully translated and polished for release.

### Macro-Task 3.1: Add Translations

- **Description:** Add all necessary translation keys for English and Portuguese.
- [ ] **Micro-Task 3.1.1: Add English Translations**
  - [ ] **Sub-Task:** Add the complete translation keys from [`requirements.md lines 1408-1465`](./requirements.md#part-h-add-translation-keys) to `src/shared/locales/en/create-recipe.json`.
- [ ] **Micro-Task 3.1.2: Add Portuguese Translations**
  - [ ] **Sub-Task:** Add the complete translation keys from [`requirements.md lines 1467-1524`](./requirements.md#part-h-add-translation-keys) to `src/shared/locales/pt/create-recipe.json`.
- **Dependencies:**
  - Macro-Task 2.2 (Save Recipe UI).
  - Macro-Task 2.3 (Saved Recipes UI).
- **Files to Edit:**
  - `src/shared/locales/en/create-recipe.json`
  - `src/shared/locales/pt/create-recipe.json`

### Macro-Task 3.2: Testing and Validation

- **Description:** Perform comprehensive end-to-end testing of the entire feature.
- [ ] **Micro-Task 3.2.1: Manual E2E Testing**
  - [ ] **Sub-Task:** Follow the detailed manual testing sequence from the `requirements.md` file, covering the save flow, viewing saved recipes, offline mode, and edge cases.
  - [ ] **Sub-Task:** Test authentication flows with actual Clerk users and Supabase RLS policies.
  - [ ] **Sub-Task:** Validate oil data hydration with the specific oil ID `ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf`.
  - [ ] **Sub-Task:** Test error scenarios including network failures, invalid oil references, and authentication expiration.
- [ ] **Micro-Task 3.2.2: Performance Validation**
  - [ ] **Sub-Task:** Test with a large number of saved recipes (>100) to ensure list loading, searching, and filtering remain performant.
  - [ ] **Sub-Task:** Verify embedding column exclusion prevents performance issues.
  - [ ] **Sub-Task:** Test oil hydration performance with multiple concurrent requests.
  - [ ] **Sub-Task:** Validate offline caching and sync performance.
- [ ] **Micro-Task 3.2.3: Edge Case Testing**
  - [ ] **Sub-Task:** Test saving recipes without authentication (should gracefully fail).
  - [ ] **Sub-Task:** Test with corrupted AsyncStorage cache data.
  - [ ] **Sub-Task:** Test foreign key constraint violations and proper error handling.
  - [ ] **Sub-Task:** Test conflicting offline/online data sync scenarios.
- [ ] **Micro-Task 3.2.4: User Experience Validation**
  - [ ] **Sub-Task:** Verify all loading states display properly.
  - [ ] **Sub-Task:** Test success/error feedback and user notifications.
  - [ ] **Sub-Task:** Validate accessibility compliance and screen reader support.
  - [ ] **Sub-Task:** Test Material Design 3 theming consistency across all components.
- **Dependencies:**
  - All previous tasks.
- **Files to Edit:**
  - None (this is a testing phase).

## Phase 4: Production Readiness & Monitoring

This final phase ensures the feature is ready for production deployment with proper monitoring.

### Macro-Task 4.1: Production Configuration

- **Description:** Prepare the feature for production deployment.
- [ ] **Micro-Task 4.1.1: Environment Configuration Review**
  - [ ] **Sub-Task:** Verify production Supabase environment variables are properly configured.
  - [ ] **Sub-Task:** Test Clerk-Supabase integration in production environment.
  - [ ] **Sub-Task:** Validate RLS policies work correctly with production authentication.
- [ ] **Micro-Task 4.1.2: Database Migration Preparation**
  - [ ] **Sub-Task:** Create formal database migration scripts for production deployment.
  - [ ] **Sub-Task:** Test migration scripts on staging environment.
  - [ ] **Sub-Task:** Document rollback procedures in case of deployment issues.
- [ ] **Micro-Task 4.1.3: Monitoring & Analytics Setup**
  - [ ] **Sub-Task:** Add logging for critical save recipe operations.
  - [ ] **Sub-Task:** Implement analytics tracking for feature usage metrics.
  - [ ] **Sub-Task:** Set up alerts for error rates and performance issues.
- **Dependencies:**
  - All previous phases completed.
- **Files to Create:**
  - Database migration scripts.
  - Monitoring configuration files.

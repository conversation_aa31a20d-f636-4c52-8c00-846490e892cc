# Engineering & Architectural Requirements: Save Recipe Feature

**References:**
- **Epic:** [`epic.md`](./epic.md) - EPIC: Save Recipe Feature - Personal Aromatherapy Library  
- **User Story:** [`story.md`](./story.md) - Save Recipe Feature for Personal Aromatherapy Library
- **Product Requirements Document:** [`prd.md`](./prd.md)
- **Implementation Plan:** [`implementation_todolist.md`](./implementation_todolist.md)
- **Feasibility Analysis:** [`feasibility_analysis_report.md`](./feasibility_analysis_report.md)

## 1. Context & Architectural Rationale

**Problem:** Users invest significant time completing the 6-step recipe creation wizard but lose all recipe data when they close the app or navigate away. This creates a poor user experience where valuable therapeutic recipes cannot be referenced, reused, or built upon over time.

**Architectural Decision:** We will implement an **"Optimized Oil Reference with Real-time Hydration"** pattern that:
1. **Minimizes Storage:** Store only essential recipe metadata and UUID oil references in user tables
2. **Maximizes Consistency:** Retrieve complete oil data from existing `essential_oils_with_safety_ids` view 
3. **Ensures Performance:** Exclude embedding column and use batch queries for oil hydration
4. **Maintains Integrity:** Use foreign key constraints to existing oil infrastructure
5. **Supports Offline:** Cache essential oil names for offline recipe viewing

This leverages the verified 120-oil Supabase infrastructure without modification while providing real-time safety data and multi-language support.

## 2. Verified Implementation Foundation

### Database Integration ✅
- **Essential Oils Table:** 120 oils with UUID primary keys and established foreign key relationships
- **Safety View:** `essential_oils_with_safety_ids` with pre-aggregated JSONB safety data
- **MCP Access:** Direct database integration via `mcp__supabase__query` for development and testing
- **Multi-language:** `name_english`, `name_scientific`, `name_portuguese` for i18n support

### Infrastructure Ready ✅
- **Navigation Route:** `/saved-recipes` already exists in drawer navigation
- **Authentication:** Clerk integration fully implemented with `useUser` hook
- **State Management:** Zustand patterns established in `recipe.slice.ts`
- **Modal System:** `@gorhom/bottom-sheet` + `useModalManager` patterns available
- **Theming:** Material Design 3 via `useTheme()` hook

## 3. Detailed Implementation Instructions

### Part A: Database Schema Implementation

**Step 1:** Create database tables in Supabase using the SQL editor or migration:

```sql
-- Primary saved recipes table (integrates with existing essential_oils structure)
CREATE TABLE user_saved_recipes (
  id UUID DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
  user_id TEXT NOT NULL, -- Clerk user ID
  recipe_name TEXT NOT NULL,
  time_slot TEXT NOT NULL CHECK (time_slot IN ('morning', 'mid-day', 'night')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Recipe context for search/filtering
  health_concern TEXT NOT NULL,
  health_concern_original TEXT, -- Exact user input
  demographics JSONB NOT NULL, -- { gender, ageCategory, specificAge }
  selected_causes TEXT[] NOT NULL, -- Array of cause IDs
  selected_symptoms TEXT[] NOT NULL, -- Array of symptom IDs
  
  -- Recipe metadata (for performance queries)
  total_drops INTEGER NOT NULL,
  total_volume_ml INTEGER NOT NULL,
  oil_count INTEGER NOT NULL,
  
  -- Complete recipe protocol (excluding selected_oils array - stored separately)
  recipe_protocol JSONB NOT NULL
);

-- Recipe oils references (leverages existing essential_oils table with foreign keys)
CREATE TABLE user_saved_recipe_oils (
  id UUID DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
  recipe_id UUID REFERENCES user_saved_recipes(id) ON DELETE CASCADE,
  oil_id UUID REFERENCES essential_oils(id) ON DELETE RESTRICT, -- FK to existing essential_oils table
  drops_count INTEGER NOT NULL,
  oil_order INTEGER NOT NULL, -- Preserve order in recipe display
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Performance indexes (leveraging existing essential_oils indexes)
CREATE INDEX idx_user_saved_recipes_user_id ON user_saved_recipes(user_id);
CREATE INDEX idx_user_saved_recipes_created_at ON user_saved_recipes(created_at DESC);
CREATE INDEX idx_user_saved_recipes_health_concern ON user_saved_recipes(health_concern);
CREATE INDEX idx_user_saved_recipes_time_slot ON user_saved_recipes(time_slot);
CREATE INDEX idx_user_saved_recipe_oils_recipe_id ON user_saved_recipe_oils(recipe_id);
CREATE INDEX idx_user_saved_recipe_oils_oil_id ON user_saved_recipe_oils(oil_id);

-- Compound indexes for optimized queries
CREATE INDEX idx_user_recipes_user_time_created ON user_saved_recipes(user_id, time_slot, created_at DESC);
CREATE INDEX idx_recipe_oils_recipe_order ON user_saved_recipe_oils(recipe_id, oil_order);

-- Automatic timestamp updates (following existing pattern)
CREATE TRIGGER set_timestamp_user_saved_recipes 
  BEFORE UPDATE ON user_saved_recipes 
  FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();

-- Row Level Security (RLS) policies for user data isolation
ALTER TABLE user_saved_recipes ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_saved_recipe_oils ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can only access their own recipes" ON user_saved_recipes
  FOR ALL USING (auth.uid()::text = user_id);

CREATE POLICY "Users can only access oils from their own recipes" ON user_saved_recipe_oils
  FOR ALL USING (recipe_id IN (
    SELECT id FROM user_saved_recipes WHERE user_id = auth.uid()::text
  ));
```

### Part B: Install Required Dependencies

**Step 1:** Add Supabase client dependency:
```bash
npm install @supabase/supabase-js
```

**Step 2:** Add AsyncStorage for offline caching:
```bash
npm install @react-native-async-storage/async-storage
```

### Part C: Create Supabase Service Layer

**File to Create:** `src/shared/services/supabase/supabase-client.ts`

```typescript
import { createClient } from '@supabase/supabase-js';
import { useAuth } from '@clerk/clerk-expo';

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY!;

// Create Supabase client with Clerk auth integration
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: undefined, // We'll use Clerk for auth, not Supabase auth
    autoRefreshToken: false,
    persistSession: false,
    detectSessionInUrl: false,
  },
});

/**
 * Hook to get Supabase client with Clerk authentication
 * This ensures all Supabase queries are authenticated with the current Clerk user
 */
export const useSupabaseClient = () => {
  const { getToken } = useAuth();
  
  // Set up Supabase client with Clerk token for RLS
  const getAuthenticatedClient = async () => {
    const token = await getToken({ template: 'supabase' });
    if (token) {
      supabase.realtime.setAuth(token);
      supabase.rest.headers['Authorization'] = `Bearer ${token}`;
    }
    return supabase;
  };
  
  return { supabase, getAuthenticatedClient };
};
```

**File to Create:** `src/shared/services/supabase/types/saved-recipe.types.ts`

```typescript
import type { FinalRecipeProtocol, RecipeTimeSlot } from '@/features/create-recipe/types';

export interface SaveRecipePayload {
  recipeName: string;
  timeSlot: RecipeTimeSlot;
  recipeData: FinalRecipeProtocol;
  healthConcern: string;
  healthConcernOriginal?: string;
  demographics: {
    gender: 'male' | 'female';
    ageCategory: string;
    specificAge: number;
  };
  selectedCauses: string[];
  selectedSymptoms: string[];
}

export interface SavedRecipe {
  id: string;
  user_id: string;
  recipe_name: string;
  time_slot: RecipeTimeSlot;
  created_at: string;
  updated_at: string;
  health_concern: string;
  health_concern_original?: string;
  demographics: {
    gender: 'male' | 'female';
    ageCategory: string;
    specificAge: number;
  };
  selected_causes: string[];
  selected_symptoms: string[];
  total_drops: number;
  total_volume_ml: number;
  oil_count: number;
  recipe_protocol: Omit<FinalRecipeProtocol, 'selected_oils'>; // Oils stored separately
  recipe_oils: SavedRecipeOil[];
}

export interface SavedRecipeOil {
  id: string;
  recipe_id: string;
  oil_id: string;
  drops_count: number;
  oil_order: number;
  created_at: string;
}

export interface HydratedRecipeOil extends SavedRecipeOil {
  oil_details: {
    id: string;
    name_english: string;
    name_scientific: string;
    name_portuguese: string;
    general_description?: string;
    image_url?: string;
    internal_use: any;
    dilution: any;
    phototoxicity: any;
    pregnancy_nursing_safety: any[];
    child_safety: any[];
  };
}

export interface QueryOptions {
  limit?: number;
  offset?: number;
  timeSlot?: RecipeTimeSlot;
  healthConcern?: string;
  searchTerm?: string;
  sortBy?: 'created_at' | 'recipe_name' | 'total_drops';
  sortOrder?: 'asc' | 'desc';
}
```

**File to Create:** `src/shared/services/supabase/saved-recipes.service.ts`

```typescript
import { supabase } from './supabase-client';
import type { 
  SaveRecipePayload, 
  SavedRecipe, 
  QueryOptions, 
  HydratedRecipeOil,
  SavedRecipeOil 
} from './types/saved-recipe.types';
import type { EnrichedEssentialOil } from '@/features/create-recipe/types';

export class SavedRecipesService {
  
  /**
   * Save a recipe with oil references to Supabase
   */
  async saveRecipe(payload: SaveRecipePayload, userId: string): Promise<SavedRecipe> {
    const { recipeName, timeSlot, recipeData, healthConcern, healthConcernOriginal, demographics, selectedCauses, selectedSymptoms } = payload;
    
    // Prepare recipe data without selected_oils (stored separately)
    const { selected_oils, ...recipeProtocol } = recipeData;
    
    // Insert main recipe record
    const { data: recipeRecord, error: recipeError } = await supabase
      .from('user_saved_recipes')
      .insert({
        user_id: userId,
        recipe_name: recipeName,
        time_slot: timeSlot,
        health_concern: healthConcern,
        health_concern_original: healthConcernOriginal,
        demographics,
        selected_causes: selectedCauses,
        selected_symptoms: selectedSymptoms,
        total_drops: recipeData.total_drops,
        total_volume_ml: recipeData.total_volume_ml,
        oil_count: selected_oils.length,
        recipe_protocol: recipeProtocol
      })
      .select()
      .single();
    
    if (recipeError) throw recipeError;
    
    // Insert oil references
    const oilReferences = selected_oils.map((oil, index) => ({
      recipe_id: recipeRecord.id,
      oil_id: oil.oil_id,
      drops_count: oil.drops_count,
      oil_order: index + 1
    }));
    
    const { data: oilRecords, error: oilError } = await supabase
      .from('user_saved_recipe_oils')
      .insert(oilReferences)
      .select();
    
    if (oilError) throw oilError;
    
    return {
      ...recipeRecord,
      recipe_oils: oilRecords
    };
  }
  
  /**
   * Get all saved recipes for a user with optional filtering
   */
  async getUserRecipes(userId: string, options: QueryOptions = {}): Promise<SavedRecipe[]> {
    const {
      limit = 50,
      offset = 0,
      timeSlot,
      healthConcern,
      searchTerm,
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = options;
    
    let query = supabase
      .from('user_saved_recipes')
      .select(`
        *,
        recipe_oils:user_saved_recipe_oils(*)
      `)
      .eq('user_id', userId)
      .range(offset, offset + limit - 1)
      .order(sortBy, { ascending: sortOrder === 'asc' });
    
    // Apply filters
    if (timeSlot) {
      query = query.eq('time_slot', timeSlot);
    }
    
    if (healthConcern) {
      query = query.ilike('health_concern', `%${healthConcern}%`);
    }
    
    if (searchTerm) {
      query = query.or(`recipe_name.ilike.%${searchTerm}%,health_concern.ilike.%${searchTerm}%`);
    }
    
    const { data, error } = await query;
    
    if (error) throw error;
    return data || [];
  }
  
  /**
   * Get a single recipe by ID with hydrated oil data
   */
  async getRecipeById(recipeId: string, userId: string): Promise<SavedRecipe | null> {
    const { data, error } = await supabase
      .from('user_saved_recipes')
      .select(`
        *,
        recipe_oils:user_saved_recipe_oils(*)
      `)
      .eq('id', recipeId)
      .eq('user_id', userId)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw error;
    }
    
    return data;
  }
  
  /**
   * Hydrate oil references with complete oil data from essential_oils_with_safety_ids
   * CRITICAL: Excludes embedding column for performance
   */
  async hydrateRecipeOils(oilIds: string[]): Promise<EnrichedEssentialOil[]> {
    if (oilIds.length === 0) return [];
    
    const { data, error } = await supabase
      .from('essential_oils_with_safety_ids')
      .select(`
        id, name_english, name_scientific, name_portuguese, general_description,
        image_url, names_concatenated,
        internal_use, dilution, phototoxicity, pregnancy_nursing_safety, child_safety,
        created_at, updated_at
      `) // CRITICAL: Exclude embedding column
      .in('id', oilIds);
    
    if (error) throw error;
    
    // Map to EnrichedEssentialOil format
    return (data || []).map(oil => ({
      oil_id: oil.id,
      supabase_id: oil.id,
      name_english: oil.name_english,
      name_botanical: oil.name_scientific,
      name_scientific: oil.name_scientific,
      name_localized: oil.name_portuguese, // Default to Portuguese for i18n
      match_rationale_localized: '',
      relevancy_to_property_score: 5, // Default high relevance for saved recipes
      isEnriched: true,
      enrichment_status: 'enriched' as const,
      safety: {
        internal_use: oil.internal_use,
        dilution: oil.dilution,
        phototoxicity: oil.phototoxicity,
        pregnancy_nursing: oil.pregnancy_nursing_safety,
        child_safety: oil.child_safety
      }
    }));
  }
  
  /**
   * Update recipe name
   */
  async updateRecipeName(recipeId: string, userId: string, newName: string): Promise<void> {
    const { error } = await supabase
      .from('user_saved_recipes')
      .update({ recipe_name: newName })
      .eq('id', recipeId)
      .eq('user_id', userId);
    
    if (error) throw error;
  }
  
  /**
   * Delete a recipe and all associated oil references
   */
  async deleteRecipe(recipeId: string, userId: string): Promise<void> {
    const { error } = await supabase
      .from('user_saved_recipes')
      .delete()
      .eq('id', recipeId)
      .eq('user_id', userId);
    
    if (error) throw error;
    // Oil references deleted automatically via CASCADE
  }
  
  /**
   * Validate that oil references exist in the database
   */
  async validateOilReferences(oilIds: string[]): Promise<{ valid: string[], invalid: string[] }> {
    if (oilIds.length === 0) return { valid: [], invalid: [] };
    
    const { data, error } = await supabase
      .from('essential_oils')
      .select('id')
      .in('id', oilIds);
    
    if (error) throw error;
    
    const validIds = (data || []).map(oil => oil.id);
    const invalidIds = oilIds.filter(id => !validIds.includes(id));
    
    return { valid: validIds, invalid: invalidIds };
  }
  
  /**
   * Test Supabase connection (for development/debugging)
   */
  async testConnection(): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('essential_oils')
        .select('count')
        .limit(1);
      
      return !error;
    } catch {
      return false;
    }
  }
}

// Export singleton instance
export const savedRecipesService = new SavedRecipesService();
```

### Part D: Extend Recipe Store with Saved Recipes State

**File to Modify:** `src/features/create-recipe/store/recipe.slice.ts`

**Step 1:** Add imports at the top:
```typescript
// Add to existing imports
import { savedRecipesService } from '@/shared/services/supabase/saved-recipes.service';
import type { SavedRecipe } from '@/shared/services/supabase/types/saved-recipe.types';
import { useUser } from '@clerk/clerk-expo';
import AsyncStorage from '@react-native-async-storage/async-storage';
```

**Step 2:** Add saved recipes state to `RecipeSliceState` interface (around line 92):
```typescript
export interface RecipeSliceState {
  // ... existing state fields ...
  
  // Saved recipes state
  savedRecipes: SavedRecipe[];
  savedRecipesLoading: boolean;
  savedRecipesError: string | null;
  lastSavedRecipeSync: Date | null;
}
```

**Step 3:** Add saved recipes actions to `RecipeSliceActions` interface (around line 137):
```typescript
export interface RecipeSliceActions {
  // ... existing actions ...
  
  // Saved recipes actions
  saveCurrentRecipe: (timeSlot: RecipeTimeSlot, customName?: string) => Promise<void>;
  loadUserSavedRecipes: () => Promise<void>;
  getSavedRecipeById: (recipeId: string) => Promise<SavedRecipe | null>;
  updateSavedRecipeName: (recipeId: string, newName: string) => Promise<void>;
  deleteSavedRecipe: (recipeId: string) => Promise<void>;
  clearSavedRecipes: () => void;
  cacheSavedRecipesOffline: () => Promise<void>;
  loadSavedRecipesFromCache: () => Promise<void>;
}
```

**Step 4:** Add saved recipes to initial state (around line 188):
```typescript
const initialRecipeState: RecipeSliceState = {
  // ... existing initial state ...
  
  // Saved recipes initial state
  savedRecipes: [],
  savedRecipesLoading: false,
  savedRecipesError: null,
  lastSavedRecipeSync: null,
};
```

**Step 5:** Implement saved recipes actions in the slice (add after existing actions around line 784):
```typescript
  // Saved recipes actions implementation
  saveCurrentRecipe: async (timeSlot: RecipeTimeSlot, customName?: string) => {
    const state = get();
    const recipe = state.finalRecipes[timeSlot === 'mid-day' ? 'midDay' : timeSlot].recipe;
    
    if (!recipe || !state.healthConcern || !state.demographics) {
      throw new Error('Missing required recipe data for saving');
    }
    
    set({ savedRecipesLoading: true, savedRecipesError: null });
    
    try {
      // Get current user from Clerk context (this requires accessing the user context)
      // Note: This will need to be called from a component context with useUser
      const userId = 'USER_ID_FROM_CONTEXT'; // This will be passed in from component
      
      const payload = {
        recipeName: customName || recipe.recipe_name_localized,
        timeSlot,
        recipeData: recipe,
        healthConcern: state.healthConcern.healthConcern,
        healthConcernOriginal: state.healthConcern.healthConcern,
        demographics: state.demographics,
        selectedCauses: state.selectedCauses.map(c => c.cause_id),
        selectedSymptoms: state.selectedSymptoms.map(s => s.symptom_id)
      };
      
      const savedRecipe = await savedRecipesService.saveRecipe(payload, userId);
      
      set((prevState) => ({
        savedRecipes: [savedRecipe, ...prevState.savedRecipes],
        savedRecipesLoading: false,
        lastSavedRecipeSync: new Date()
      }));
      
      // Cache offline
      await get().cacheSavedRecipesOffline();
      
      console.log('✅ Recipe saved successfully:', savedRecipe.recipe_name);
    } catch (error) {
      console.error('❌ Failed to save recipe:', error);
      set({
        savedRecipesError: error instanceof Error ? error.message : 'Failed to save recipe',
        savedRecipesLoading: false
      });
      throw error;
    }
  },
  
  loadUserSavedRecipes: async () => {
    set({ savedRecipesLoading: true, savedRecipesError: null });
    
    try {
      const userId = 'USER_ID_FROM_CONTEXT'; // This will be passed in from component
      const recipes = await savedRecipesService.getUserRecipes(userId, {
        limit: 100,
        sortBy: 'created_at',
        sortOrder: 'desc'
      });
      
      set({
        savedRecipes: recipes,
        savedRecipesLoading: false,
        lastSavedRecipeSync: new Date()
      });
      
      // Cache offline
      await get().cacheSavedRecipesOffline();
      
      console.log('✅ Loaded saved recipes:', recipes.length);
    } catch (error) {
      console.error('❌ Failed to load saved recipes:', error);
      
      // Try loading from cache if network fails
      await get().loadSavedRecipesFromCache();
      
      set({
        savedRecipesError: error instanceof Error ? error.message : 'Failed to load saved recipes',
        savedRecipesLoading: false
      });
    }
  },
  
  getSavedRecipeById: async (recipeId: string) => {
    try {
      const userId = 'USER_ID_FROM_CONTEXT'; // This will be passed in from component
      return await savedRecipesService.getRecipeById(recipeId, userId);
    } catch (error) {
      console.error('❌ Failed to get saved recipe:', error);
      throw error;
    }
  },
  
  updateSavedRecipeName: async (recipeId: string, newName: string) => {
    try {
      const userId = 'USER_ID_FROM_CONTEXT'; // This will be passed in from component
      await savedRecipesService.updateRecipeName(recipeId, userId, newName);
      
      set((state) => ({
        savedRecipes: state.savedRecipes.map(recipe =>
          recipe.id === recipeId ? { ...recipe, recipe_name: newName } : recipe
        ),
        lastSavedRecipeSync: new Date()
      }));
      
      await get().cacheSavedRecipesOffline();
      console.log('✅ Recipe name updated:', newName);
    } catch (error) {
      console.error('❌ Failed to update recipe name:', error);
      throw error;
    }
  },
  
  deleteSavedRecipe: async (recipeId: string) => {
    try {
      const userId = 'USER_ID_FROM_CONTEXT'; // This will be passed in from component
      await savedRecipesService.deleteRecipe(recipeId, userId);
      
      set((state) => ({
        savedRecipes: state.savedRecipes.filter(recipe => recipe.id !== recipeId),
        lastSavedRecipeSync: new Date()
      }));
      
      await get().cacheSavedRecipesOffline();
      console.log('✅ Recipe deleted:', recipeId);
    } catch (error) {
      console.error('❌ Failed to delete recipe:', error);
      throw error;
    }
  },
  
  clearSavedRecipes: () => {
    set({
      savedRecipes: [],
      savedRecipesError: null,
      lastSavedRecipeSync: null
    });
  },
  
  cacheSavedRecipesOffline: async () => {
    try {
      const state = get();
      const cacheData = {
        recipes: state.savedRecipes,
        lastSync: state.lastSavedRecipeSync?.toISOString()
      };
      await AsyncStorage.setItem('saved_recipes_cache', JSON.stringify(cacheData));
    } catch (error) {
      console.error('❌ Failed to cache saved recipes offline:', error);
    }
  },
  
  loadSavedRecipesFromCache: async () => {
    try {
      const cachedData = await AsyncStorage.getItem('saved_recipes_cache');
      if (cachedData) {
        const { recipes, lastSync } = JSON.parse(cachedData);
        set({
          savedRecipes: recipes,
          lastSavedRecipeSync: lastSync ? new Date(lastSync) : null,
          savedRecipesError: 'Showing cached data - sync when online'
        });
        console.log('✅ Loaded saved recipes from cache:', recipes.length);
      }
    } catch (error) {
      console.error('❌ Failed to load saved recipes from cache:', error);
    }
  },
```

### Part E: Create Save Recipe Bottom Sheet Modal

**File to Create:** `src/features/create-recipe/components/modals/save-recipe-bottom-sheet.tsx`

```typescript
import React, { useState } from 'react';
import { View } from 'react-native';
import { Text, TextInput, Button, Checkbox, Divider, ActivityIndicator } from 'react-native-paper';
import { 
  BottomSheetModal, 
  BottomSheetScrollView,
  BottomSheetView
} from '@gorhom/bottom-sheet';
import { useTheme } from '@/shared/hooks/use-theme';
import { useTranslation } from 'react-i18next';
import { haptics } from '@/shared/utils/haptics';
import { useCombinedStore } from '../../store/combined-store';
import { useUser } from '@clerk/clerk-expo';
import type { RecipeTimeSlot, FinalRecipeProtocol } from '../../types';

interface SaveRecipeBottomSheetProps {
  modalRef: React.RefObject<BottomSheetModal>;
  recipe: FinalRecipeProtocol;
  currentTimeSlot: RecipeTimeSlot;
  onDismiss: () => void;
  onSuccess: (recipeName: string) => void;
}

export const SaveRecipeBottomSheet: React.FC<SaveRecipeBottomSheetProps> = ({
  modalRef,
  recipe,
  currentTimeSlot,
  onDismiss,
  onSuccess
}) => {
  const theme = useTheme();
  const { t } = useTranslation('create-recipe');
  const { user } = useUser();
  const { saveCurrentRecipe } = useCombinedStore();
  
  // Modal state
  const [recipeName, setRecipeName] = useState(recipe.recipe_name_localized);
  const [selectedTimeSlots, setSelectedTimeSlots] = useState<RecipeTimeSlot[]>([currentTimeSlot]);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const timeSlotOptions: { value: RecipeTimeSlot; label: string }[] = [
    { value: 'morning', label: t('saveRecipe.timeSlots.morning') },
    { value: 'mid-day', label: t('saveRecipe.timeSlots.midDay') },
    { value: 'night', label: t('saveRecipe.timeSlots.night') }
  ];
  
  const toggleTimeSlot = (timeSlot: RecipeTimeSlot) => {
    haptics.light();
    setSelectedTimeSlots(prev => 
      prev.includes(timeSlot) 
        ? prev.filter(t => t !== timeSlot)
        : [...prev, timeSlot]
    );
  };
  
  const handleSave = async () => {
    if (!recipeName.trim()) {
      setError(t('saveRecipe.errors.nameRequired'));
      return;
    }
    
    if (selectedTimeSlots.length === 0) {
      setError(t('saveRecipe.errors.timeSlotRequired'));
      return;
    }
    
    if (!user?.id) {
      setError(t('saveRecipe.errors.authRequired'));
      return;
    }
    
    setIsSaving(true);
    setError(null);
    
    try {
      // Save recipe for each selected time slot
      for (const timeSlot of selectedTimeSlots) {
        await saveCurrentRecipe(timeSlot, recipeName.trim());
      }
      
      haptics.success();
      onSuccess(recipeName);
      modalRef.current?.dismiss();
    } catch (err) {
      console.error('Save recipe error:', err);
      setError(err instanceof Error ? err.message : t('saveRecipe.errors.saveFailed'));
      haptics.error();
    } finally {
      setIsSaving(false);
    }
  };
  
  const handleCancel = () => {
    haptics.light();
    modalRef.current?.dismiss();
  };
  
  return (
    <BottomSheetModal
      ref={modalRef}
      snapPoints={['60%']}
      onDismiss={onDismiss}
      backgroundStyle={{ backgroundColor: theme.colors.surface }}
      handleIndicatorStyle={{ backgroundColor: theme.colors.onSurfaceVariant }}
    >
      <BottomSheetView style={{ flex: 1 }}>
        {/* Header */}
        <View style={{ 
          paddingHorizontal: theme.spacing.screenPadding,
          paddingBottom: theme.spacing.md,
          borderBottomWidth: 1,
          borderBottomColor: theme.colors.outline
        }}>
          <Text variant="headlineSmall" style={{ 
            fontWeight: 'bold',
            color: theme.colors.onSurface,
            marginBottom: theme.spacing.sm
          }}>
            {t('saveRecipe.title')}
          </Text>
          <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
            {t('saveRecipe.subtitle')}
          </Text>
        </View>

        <BottomSheetScrollView style={{ flex: 1 }} contentContainerStyle={{ padding: theme.spacing.screenPadding }}>
          {/* Recipe Name Input */}
          <View style={{ marginBottom: theme.spacing.lg }}>
            <TextInput
              label={t('saveRecipe.fields.recipeName')}
              value={recipeName}
              onChangeText={setRecipeName}
              mode="outlined"
              error={error?.includes('name') || false}
              disabled={isSaving}
              style={{ marginBottom: theme.spacing.sm }}
            />
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              {t('saveRecipe.fields.recipeNameHint')}
            </Text>
          </View>
          
          <Divider style={{ marginVertical: theme.spacing.md }} />
          
          {/* Time Slot Selection */}
          <View style={{ marginBottom: theme.spacing.lg }}>
            <Text variant="titleMedium" style={{ 
              fontWeight: 'bold', 
              color: theme.colors.onSurface,
              marginBottom: theme.spacing.md
            }}>
              {t('saveRecipe.fields.timeSlots')}
            </Text>
            
            {timeSlotOptions.map(option => (
              <View key={option.value} style={{ 
                flexDirection: 'row', 
                alignItems: 'center',
                marginBottom: theme.spacing.sm
              }}>
                <Checkbox
                  status={selectedTimeSlots.includes(option.value) ? 'checked' : 'unchecked'}
                  onPress={() => toggleTimeSlot(option.value)}
                  disabled={isSaving}
                />
                <Text 
                  variant="bodyLarge" 
                  style={{ 
                    marginLeft: theme.spacing.sm,
                    color: theme.colors.onSurface
                  }}
                  onPress={() => toggleTimeSlot(option.value)}
                >
                  {option.label}
                </Text>
              </View>
            ))}
            
            <Text variant="bodySmall" style={{ 
              color: theme.colors.onSurfaceVariant,
              marginTop: theme.spacing.sm
            }}>
              {t('saveRecipe.fields.timeSlotsHint')}
            </Text>
          </View>
          
          {/* Error Display */}
          {error && (
            <View style={{ 
              backgroundColor: theme.colors.errorContainer,
              padding: theme.spacing.md,
              borderRadius: theme.spacing.sm,
              marginBottom: theme.spacing.lg
            }}>
              <Text style={{ color: theme.colors.onErrorContainer }}>
                {error}
              </Text>
            </View>
          )}
        </BottomSheetScrollView>
        
        {/* Action Buttons */}
        <View style={{
          flexDirection: 'row',
          padding: theme.spacing.screenPadding,
          gap: theme.spacing.md,
          borderTopWidth: 1,
          borderTopColor: theme.colors.outline
        }}>
          <Button
            mode="outlined"
            onPress={handleCancel}
            disabled={isSaving}
            style={{ flex: 1 }}
          >
            {t('saveRecipe.actions.cancel')}
          </Button>
          
          <Button
            mode="contained"
            onPress={handleSave}
            disabled={isSaving || !recipeName.trim() || selectedTimeSlots.length === 0}
            style={{ flex: 1 }}
            loading={isSaving}
          >
            {isSaving ? t('saveRecipe.actions.saving') : t('saveRecipe.actions.save')}
          </Button>
        </View>
      </BottomSheetView>
    </BottomSheetModal>
  );
};
```

### Part F: Modify Final Recipes List to Add Save Button

**File to Modify:** `src/features/create-recipe/components/screens/final-recipes/final-recipes-list.tsx`

**Step 1:** Add imports at the top:
```typescript
// Add to existing imports around line 13
import { Button } from 'react-native-paper';
import { useModalManager } from '@/shared/hooks/use-modal-manager';
import { SaveRecipeBottomSheet } from '../modals/save-recipe-bottom-sheet';
```

**Step 2:** Modify the `RecipeDetailsHeader` component props and add save functionality (around line 16):
```typescript
interface RecipeDetailsHeaderProps {
  recipe: FinalRecipeProtocol;
  timeSlot: RecipeTimeSlot;
  onSaveRecipe?: () => void; // Add this prop
}

export function RecipeDetailsHeader({ recipe, timeSlot, onSaveRecipe }: RecipeDetailsHeaderProps) {
  const theme = useTheme();
  const { t } = useTranslation('create-recipe');

  // ... existing code for getTimeSlotColor and config ...

  return (
    <View style={{ paddingHorizontal: theme.spacing.screenPadding, marginBottom: theme.spacing.lg }}>
      {/* ... existing header content ... */}
      
      {/* Add Save Recipe Button after stats block */}
      {onSaveRecipe && (
        <Button
          mode="contained"
          onPress={onSaveRecipe}
          icon="bookmark-plus"
          style={{ 
            marginTop: theme.spacing.md,
            alignSelf: 'center'
          }}
          contentStyle={{ paddingVertical: theme.spacing.sm }}
        >
          {t('saveRecipe.actions.saveRecipe')}
        </Button>
      )}
    </View>
  );
}
```

**Step 3:** Modify the `FinalRecipeList` component to include save functionality (around line 134):
```typescript
interface FinalRecipeListProps {
  recipe: FinalRecipeProtocol;
  timeSlot: RecipeTimeSlot;
  showSaveButton?: boolean; // Add this prop
}

export function FinalRecipeList({ recipe, timeSlot, showSaveButton = false }: FinalRecipeListProps) {
  const theme = useTheme();
  const { t } = useTranslation('create-recipe');
  const [expandedSections, setExpandedSections] = useState<string[]>(['oils']);
  
  // Add save recipe modal state
  const { modalRef, presentModal, dismissModal } = useModalManager();
  const [saveSuccessMessage, setSaveSuccessMessage] = useState<string | null>(null);
  
  const handleSaveRecipe = () => {
    haptics.light();
    presentModal();
  };
  
  const handleSaveSuccess = (recipeName: string) => {
    setSaveSuccessMessage(t('saveRecipe.success', { recipeName }));
    // Clear success message after 3 seconds
    setTimeout(() => setSaveSuccessMessage(null), 3000);
  };
  
  const handleDismissModal = () => {
    dismissModal();
  };

  // ... existing code for sections ...

  return (
    <>
      {/* Success Message */}
      {saveSuccessMessage && (
        <View style={{
          backgroundColor: theme.colors.primaryContainer,
          padding: theme.spacing.md,
          marginHorizontal: theme.spacing.screenPadding,
          marginBottom: theme.spacing.md,
          borderRadius: theme.spacing.sm
        }}>
          <Text style={{ 
            color: theme.colors.onPrimaryContainer,
            textAlign: 'center',
            fontWeight: 'bold'
          }}>
            {saveSuccessMessage}
          </Text>
        </View>
      )}
      
      {/* Recipe Header with Save Button */}
      <RecipeDetailsHeader 
        recipe={recipe} 
        timeSlot={timeSlot}
        onSaveRecipe={showSaveButton ? handleSaveRecipe : undefined}
      />
      
      {/* ... existing List.Section code ... */}
      
      {/* Save Recipe Modal */}
      {showSaveButton && (
        <SaveRecipeBottomSheet
          modalRef={modalRef}
          recipe={recipe}
          currentTimeSlot={timeSlot}
          onDismiss={handleDismissModal}
          onSuccess={handleSaveSuccess}
        />
      )}
    </>
  );
}
```

### Part G: Create Saved Recipes Screen

**File to Create:** `src/app/(drawer)/(tabs)/saved-recipes.tsx`

```typescript
import React, { useEffect, useState, useMemo } from 'react';
import { View, ScrollView, RefreshControl } from 'react-native';
import { 
  Text, 
  Card, 
  Chip, 
  Searchbar, 
  FAB, 
  Menu,
  Button,
  ActivityIndicator,
  Surface
} from 'react-native-paper';
import { ScreenWrapper } from '@/shared/components/layout/screen-wrapper';
import { useTheme } from '@/shared/hooks/use-theme';
import { useTranslation } from 'react-i18next';
import { useCombinedStore } from '@/features/create-recipe/store/combined-store';
import { useUser } from '@clerk/clerk-expo';
import { haptics } from '@/shared/utils/haptics';
import { useRouter } from 'expo-router';
import type { SavedRecipe } from '@/shared/services/supabase/types/saved-recipe.types';
import type { RecipeTimeSlot } from '@/features/create-recipe/types';

export default function SavedRecipesScreen() {
  const theme = useTheme();
  const { t } = useTranslation('create-recipe');
  const router = useRouter();
  const { user } = useUser();
  
  const {
    savedRecipes,
    savedRecipesLoading,
    savedRecipesError,
    loadUserSavedRecipes,
    deleteSavedRecipe
  } = useCombinedStore();
  
  // Screen state
  const [searchQuery, setSearchQuery] = useState('');
  const [filterMenuVisible, setFilterMenuVisible] = useState(false);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<RecipeTimeSlot | 'all'>('all');
  const [refreshing, setRefreshing] = useState(false);
  
  // Load saved recipes on mount
  useEffect(() => {
    if (user?.id) {
      loadUserSavedRecipes();
    }
  }, [user?.id, loadUserSavedRecipes]);
  
  // Filter and search recipes
  const filteredRecipes = useMemo(() => {
    return savedRecipes.filter(recipe => {
      // Search filter
      const matchesSearch = !searchQuery || 
        recipe.recipe_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        recipe.health_concern.toLowerCase().includes(searchQuery.toLowerCase());
      
      // Time slot filter
      const matchesTimeSlot = selectedTimeSlot === 'all' || recipe.time_slot === selectedTimeSlot;
      
      return matchesSearch && matchesTimeSlot;
    });
  }, [savedRecipes, searchQuery, selectedTimeSlot]);
  
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await loadUserSavedRecipes();
    } finally {
      setRefreshing(false);
    }
  };
  
  const handleRecipePress = (recipe: SavedRecipe) => {
    haptics.light();
    // Navigate to recipe details (implement this based on your navigation structure)
    console.log('Navigate to recipe details:', recipe.id);
  };
  
  const handleDeleteRecipe = async (recipeId: string) => {
    haptics.light();
    try {
      await deleteSavedRecipe(recipeId);
    } catch (error) {
      console.error('Failed to delete recipe:', error);
    }
  };
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };
  
  const getTimeSlotColor = (timeSlot: RecipeTimeSlot) => {
    switch (timeSlot) {
      case 'morning': return theme.colors.primary;
      case 'mid-day': return theme.colors.secondary;
      case 'night': return theme.colors.tertiary;
      default: return theme.colors.primary;
    }
  };
  
  const renderEmptyState = () => (
    <View style={{
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: theme.spacing.screenPadding
    }}>
      <Text variant="headlineSmall" style={{ 
        marginBottom: theme.spacing.md,
        color: theme.colors.onSurfaceVariant,
        textAlign: 'center'
      }}>
        {t('savedRecipes.empty.title')}
      </Text>
      <Text variant="bodyMedium" style={{ 
        marginBottom: theme.spacing.xl,
        color: theme.colors.onSurfaceVariant,
        textAlign: 'center'
      }}>
        {t('savedRecipes.empty.description')}
      </Text>
      <Button 
        mode="contained" 
        onPress={() => router.push('/create-recipe/')}
        icon="plus"
      >
        {t('savedRecipes.empty.createRecipe')}
      </Button>
    </View>
  );
  
  const renderRecipeCard = (recipe: SavedRecipe) => (
    <Card
      key={recipe.id}
      style={{ marginBottom: theme.spacing.md }}
      onPress={() => handleRecipePress(recipe)}
    >
      <Card.Content>
        {/* Recipe Header */}
        <View style={{ 
          flexDirection: 'row', 
          justifyContent: 'space-between',
          alignItems: 'flex-start',
          marginBottom: theme.spacing.sm
        }}>
          <View style={{ flex: 1 }}>
            <Text variant="titleMedium" style={{ 
              fontWeight: 'bold',
              color: theme.colors.onSurface,
              marginBottom: theme.spacing.xs
            }}>
              {recipe.recipe_name}
            </Text>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
              {recipe.health_concern}
            </Text>
          </View>
          
          <Chip 
            mode="outlined"
            style={{ backgroundColor: getTimeSlotColor(recipe.time_slot) }}
            textStyle={{ color: theme.colors.onPrimary }}
          >
            {t(`finalRecipes.protocols.${recipe.time_slot}`)}
          </Chip>
        </View>
        
        {/* Recipe Stats */}
        <View style={{ 
          flexDirection: 'row', 
          justifyContent: 'space-between',
          marginBottom: theme.spacing.sm
        }}>
          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
            {t('savedRecipes.card.oils', { count: recipe.oil_count })}
          </Text>
          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
            {t('savedRecipes.card.drops', { count: recipe.total_drops })}
          </Text>
          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
            {formatDate(recipe.created_at)}
          </Text>
        </View>
      </Card.Content>
    </Card>
  );
  
  if (!user?.id) {
    return (
      <ScreenWrapper>
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
          <Text variant="headlineSmall">{t('savedRecipes.auth.required')}</Text>
        </View>
      </ScreenWrapper>
    );
  }
  
  return (
    <ScreenWrapper>
      <View style={{ flex: 1 }}>
        {/* Header */}
        <Surface style={{ 
          padding: theme.spacing.screenPadding,
          elevation: 2
        }}>
          <Text variant="headlineMedium" style={{ 
            fontWeight: 'bold',
            color: theme.colors.onSurface,
            marginBottom: theme.spacing.md
          }}>
            {t('savedRecipes.title')}
          </Text>
          
          {/* Search Bar */}
          <Searchbar
            placeholder={t('savedRecipes.search.placeholder')}
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={{ marginBottom: theme.spacing.md }}
          />
          
          {/* Filter Menu */}
          <Menu
            visible={filterMenuVisible}
            onDismiss={() => setFilterMenuVisible(false)}
            anchor={
              <Button 
                mode="outlined" 
                onPress={() => setFilterMenuVisible(true)}
                icon="filter"
              >
                {t('savedRecipes.filter.button')}
              </Button>
            }
          >
            <Menu.Item 
              onPress={() => {
                setSelectedTimeSlot('all');
                setFilterMenuVisible(false);
              }} 
              title={t('savedRecipes.filter.all')}
            />
            <Menu.Item 
              onPress={() => {
                setSelectedTimeSlot('morning');
                setFilterMenuVisible(false);
              }} 
              title={t('finalRecipes.protocols.morning')}
            />
            <Menu.Item 
              onPress={() => {
                setSelectedTimeSlot('mid-day');
                setFilterMenuVisible(false);
              }} 
              title={t('finalRecipes.protocols.midDay')}
            />
            <Menu.Item 
              onPress={() => {
                setSelectedTimeSlot('night');
                setFilterMenuVisible(false);
              }} 
              title={t('finalRecipes.protocols.night')}
            />
          </Menu>
        </Surface>
        
        {/* Content */}
        <ScrollView 
          style={{ flex: 1 }}
          contentContainerStyle={{ padding: theme.spacing.screenPadding }}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
        >
          {savedRecipesLoading && !refreshing && (
            <View style={{ padding: theme.spacing.xl, alignItems: 'center' }}>
              <ActivityIndicator size="large" />
              <Text style={{ marginTop: theme.spacing.md }}>
                {t('savedRecipes.loading')}
              </Text>
            </View>
          )}
          
          {savedRecipesError && (
            <Card style={{ marginBottom: theme.spacing.md }}>
              <Card.Content>
                <Text style={{ color: theme.colors.error }}>
                  {savedRecipesError}
                </Text>
              </Card.Content>
            </Card>
          )}
          
          {!savedRecipesLoading && filteredRecipes.length === 0 && (
            renderEmptyState()
          )}
          
          {filteredRecipes.map(renderRecipeCard)}
        </ScrollView>
        
        {/* FAB for creating new recipe */}
        <FAB
          icon="plus"
          style={{
            position: 'absolute',
            margin: theme.spacing.lg,
            right: 0,
            bottom: 0,
          }}
          onPress={() => router.push('/create-recipe/')}
        />
      </View>
    </ScreenWrapper>
  );
}
```

### Part H: Add Translation Keys

**Files to Modify:**
- `src/shared/locales/en/create-recipe.json`
- `src/shared/locales/pt/create-recipe.json`

**Add these keys to both files:**

**English (`en/create-recipe.json`):**
```json
{
  "saveRecipe": {
    "title": "Save Recipe",
    "subtitle": "Give your recipe a name and choose which protocols to save",
    "fields": {
      "recipeName": "Recipe Name",
      "recipeNameHint": "Choose a memorable name for easy reference",
      "timeSlots": "Save Time Slots",
      "timeSlotsHint": "Select which protocols to include in your saved recipe"
    },
    "timeSlots": {
      "morning": "Morning Protocol",
      "midDay": "Mid-Day Protocol", 
      "night": "Night Protocol"
    },
    "actions": {
      "save": "Save Recipe",
      "saving": "Saving...",
      "cancel": "Cancel",
      "saveRecipe": "Save Recipe"
    },
    "errors": {
      "nameRequired": "Recipe name is required",
      "timeSlotRequired": "At least one time slot must be selected",
      "authRequired": "You must be logged in to save recipes",
      "saveFailed": "Failed to save recipe. Please try again."
    },
    "success": "Recipe '{{recipeName}}' saved successfully!"
  },
  "savedRecipes": {
    "title": "My Saved Recipes",
    "loading": "Loading your recipes...",
    "search": {
      "placeholder": "Search recipes..."
    },
    "filter": {
      "button": "Filter",
      "all": "All Time Slots"
    },
    "card": {
      "oils": "{{count}} oil",
      "oils_plural": "{{count}} oils",
      "drops": "{{count}} drop",
      "drops_plural": "{{count}} drops"
    },
    "empty": {
      "title": "No Saved Recipes Yet",
      "description": "Start by creating your first personalized aromatherapy recipe",
      "createRecipe": "Create Recipe"
    },
    "auth": {
      "required": "Please log in to view your saved recipes"
    }
  }
}
```

**Portuguese (`pt/create-recipe.json`):**
```json
{
  "saveRecipe": {
    "title": "Salvar Receita",
    "subtitle": "Dê um nome à sua receita e escolha quais protocolos salvar",
    "fields": {
      "recipeName": "Nome da Receita",
      "recipeNameHint": "Escolha um nome memorável para fácil referência",
      "timeSlots": "Salvar Horários",
      "timeSlotsHint": "Selecione quais protocolos incluir na receita salva"
    },
    "timeSlots": {
      "morning": "Protocolo Matinal",
      "midDay": "Protocolo Meio-Dia",
      "night": "Protocolo Noturno"
    },
    "actions": {
      "save": "Salvar Receita",
      "saving": "Salvando...",
      "cancel": "Cancelar",
      "saveRecipe": "Salvar Receita"
    },
    "errors": {
      "nameRequired": "Nome da receita é obrigatório",
      "timeSlotRequired": "Pelo menos um horário deve ser selecionado",
      "authRequired": "Você deve estar logado para salvar receitas",
      "saveFailed": "Falha ao salvar receita. Tente novamente."
    },
    "success": "Receita '{{recipeName}}' salva com sucesso!"
  },
  "savedRecipes": {
    "title": "Minhas Receitas Salvas",
    "loading": "Carregando suas receitas...",
    "search": {
      "placeholder": "Buscar receitas..."
    },
    "filter": {
      "button": "Filtrar",
      "all": "Todos os Horários"
    },
    "card": {
      "oils": "{{count}} óleo",
      "oils_plural": "{{count}} óleos",
      "drops": "{{count}} gota",
      "drops_plural": "{{count}} gotas"
    },
    "empty": {
      "title": "Nenhuma Receita Salva Ainda",
      "description": "Comece criando sua primeira receita personalizada de aromaterapia",
      "createRecipe": "Criar Receita"
    },
    "auth": {
      "required": "Por favor, faça login para ver suas receitas salvas"
    }
  }
}
```

## 4. Validation & Testing Plan

### Manual Testing Sequence:

1. **Environment Setup:**
   - Ensure Supabase environment variables are configured in `.env`
   - Verify database tables are created with proper foreign keys
   - Test MCP connection: Run `mcp__supabase__query` with a simple query

2. **Save Recipe Flow:**
   - Complete 6-step recipe wizard to Final Recipes screen
   - Expand any recipe protocol accordion
   - Verify "Save Recipe" button is visible and clickable
   - Test save modal opens with pre-filled data
   - Customize recipe name and time slot selections
   - Verify save operation completes within 2 seconds
   - Check success feedback and navigation option

3. **Saved Recipes Screen:**
   - Navigate to Saved Recipes via drawer menu
   - Verify recipes load within 3 seconds
   - Test search functionality by recipe name and health concern
   - Test filtering by time slot
   - Verify recipe cards display correct information

4. **Oil Data Hydration:**
   - Open a saved recipe to view full details
   - Verify oil information loads from `essential_oils_with_safety_ids` view
   - Check that all safety data (JSONB fields) display correctly
   - Confirm embedding column is excluded from queries
   - Test graceful fallback when oil references are invalid

5. **Offline Functionality:**
   - Save recipes while online
   - Disable network connectivity
   - Verify cached recipes are accessible
   - Check offline indicators are displayed
   - Re-enable network and test sync

6. **Performance Testing:**
   - Test with 100+ saved recipes
   - Verify list loading performance
   - Test search/filter responsiveness
   - Monitor memory usage during oil hydration

7. **Edge Cases:**
   - Test saving recipe without authentication
   - Test with invalid oil references in database
   - Test network failures during save operations
   - Test data corruption recovery scenarios

### Technical Acceptance Criteria:

✅ **Database Integration:** Tables created with proper foreign key constraints to essential_oils table  
✅ **Authentication:** Clerk user ID correctly associated with saved recipes using RLS policies  
✅ **Oil Hydration:** Complete oil data retrieved from essential_oils_with_safety_ids excluding embedding  
✅ **Performance:** Save operations complete within 2 seconds, list loads within 3 seconds  
✅ **Offline Support:** AsyncStorage caching works with graceful online/offline transitions  
✅ **State Management:** Zustand store properly manages saved recipes state with optimistic updates  
✅ **UI Integration:** Material Design 3 theming applied consistently across all new components  
✅ **i18n Support:** All user-facing text uses translation keys for English/Portuguese  
✅ **MCP Integration:** Database testing and validation available during development  

## 5. Risk Mitigation

**Risk:** Supabase authentication integration complexity with Clerk  
**Mitigation:** Use Clerk JWT tokens with Supabase RLS policies for secure data access

**Risk:** Large oil data payloads causing performance issues  
**Mitigation:** Explicitly exclude embedding column and implement batch oil hydration

**Risk:** Foreign key constraint violations with invalid oil references  
**Mitigation:** Implement oil reference validation service method and graceful error handling

**Risk:** Offline sync conflicts when user modifies recipes across devices  
**Mitigation:** Implement last-write-wins with user notification and manual conflict resolution

**Risk:** User confusion about saved vs. generated recipes  
**Mitigation:** Clear visual indicators and separate navigation paths for saved content

This implementation provides a robust, scalable save recipe feature that leverages your existing Supabase infrastructure while maintaining consistency with established codebase patterns.
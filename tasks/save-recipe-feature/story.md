# User Story: Save Recipe Feature for Personal Aromatherapy Library

**References:**
- **Epic:** [`epic.md`](./epic.md) - EPIC: Save Recipe Feature - Personal Aromatherapy Library
- **Product Requirements Document:** [`prd.md`](./prd.md)
- **Engineering Requirements:** [`requirements.md`](./requirements.md)
- **Implementation Plan:** [`implementation_todolist.md`](./implementation_todolist.md)

**As a user** who has completed the 6-step recipe creation wizard and received personalized essential oil protocols,  
**I want** to save my recipes with custom names and access them later through a dedicated library,  
**So that** I can preserve my investment in the recipe creation process, reuse successful therapeutic combinations, and build a personal reference system for my aromatherapy needs.

## Acceptance Criteria

**Given** I am viewing my final recipes on the Final Recipes screen after completing the 6-step wizard,  
**When** I expand any recipe protocol accordion (morning/mid-day/night),  
**Then** I see a clearly visible "Save Recipe" button within the recipe details section.

**Given** I want to save a recipe for future reference,  
**When** I tap the "Save Recipe" button on any protocol,  
**Then** a Bottom Sheet modal opens displaying:
- A pre-filled recipe name field (editable)
- Checkboxes for selecting time slots to save (current slot selected by default)
- Save and Cancel action buttons
- Proper loading state during the save operation.

**Given** I am customizing my recipe in the save modal,  
**When** I edit the recipe name and select which time slots to include,  
**Then** the modal updates in real-time and validates my inputs before allowing save.

**Given** I have completed customizing my recipe in the save modal,  
**When** I tap "Save Recipe",  
**Then**:
- The recipe is stored with complete metadata (health concern, demographics, causes, symptoms)
- Essential oil references are saved as UUID oil_ids with drops_count to the Supabase database
- Recipe protocol data is stored as JSONB (preparation steps, usage instructions, safety warnings)
- The modal closes and shows success feedback with "View Saved Recipes" action.

**Given** I want to access my saved recipes,  
**When** I tap "Saved Recipes" in the main navigation drawer,  
**Then** I navigate to a screen displaying all my saved recipes in an organized list with:
- Recipe cards showing name, health concern, oil count, and creation date
- Scrollable interface that loads quickly even with many recipes
- Search and filter capabilities by name or health concern.

**Given** I am browsing my saved recipes list,  
**When** I tap on any recipe card,  
**Then** the recipe expands to show complete details with:
- All original recipe information formatted like the Final Recipes screen
- Current oil details retrieved from the Supabase essential_oils_with_safety_ids view
- Complete safety information (internal_use, dilution, phototoxicity, pregnancy_nursing_safety, child_safety)
- Original health context and creation/modification dates.

**Given** I am viewing a saved recipe's expanded details,  
**When** the system retrieves oil information from Supabase,  
**Then**:
- Oil data loads within 1 second for cached data
- Complete safety profiles are displayed with current information
- The system excludes the embedding column to avoid performance issues
- Graceful fallbacks are shown if any oils no longer exist in the database.

**Given** I want to manage my saved recipes,  
**When** I use the recipe management features,  
**Then** I can:
- Edit recipe names through a simple interface
- Delete unwanted recipes with confirmation dialog
- Search recipes by name or health concern
- View recipes organized by creation date or time slot.

**Given** I have saved recipes and later lose internet connectivity,  
**When** I access my Saved Recipes screen,  
**Then**:
- Basic recipe information loads from local cache
- Essential oil names are available for offline viewing
- Clear indicators show when data might be stale
- Full details sync automatically when connectivity returns.

**Given** I use the app across multiple sessions,  
**When** I navigate to my saved recipes,  
**Then**:
- All recipes persist across app sessions and device restarts
- Recipes remain associated with my Clerk user account
- Data syncs properly if I switch devices or reinstall the app
- Proper error handling occurs for any network failures.

**Given** I have accumulated many saved recipes over time,  
**When** I interact with the saved recipes system,  
**Then**:
- Recipe saving completes within 2 seconds of user action
- The Saved Recipes list loads within 3 seconds
- Individual recipe details display within 1 second (with cached oil data)
- The system handles 100+ saved recipes without performance degradation.

## Technical Context for Implementation

### Where This Happens in the App

This feature integrates into the existing Final Recipes workflow and navigation system. A developer can access this by:

1. **Final Recipes Integration:** Navigate through the complete 6-step recipe wizard (Health Concern → Demographics → Causes → Symptoms → Properties → Final Recipes)
2. **Save Entry Point:** On the Final Recipes screen (`src/app/(drawer)/(tabs)/create-recipe/final-recipes.tsx`), expand any recipe accordion to see the new "Save Recipe" button
3. **Navigation Access:** Use the existing "Saved Recipes" item in the main navigation drawer (`/saved-recipes` route)
4. **Database Testing:** Leverage MCP integration via `mcp__supabase__query` to test database operations during development

### Relevant Files for This Task

**Primary Implementation Files:**
- `src/features/create-recipe/components/screens/final-recipes/final-recipes-list.tsx`: Add "Save Recipe" button to existing recipe display components
- `src/app/(drawer)/(tabs)/saved-recipes.tsx`: Create new screen for saved recipes list (route already exists in navigation)
- `src/features/create-recipe/store/recipe.slice.ts`: Extend existing Zustand store with saved recipes state management

**New Service Layer Files:**
- `src/shared/services/supabase/supabase-client.ts`: Configure Supabase client with Clerk authentication integration
- `src/shared/services/supabase/saved-recipes.service.ts`: Complete CRUD operations for recipe management with oil data hydration
- `src/shared/services/supabase/types/saved-recipe.types.ts`: TypeScript interfaces for saved recipe data structures

**New Modal Component:**
- `src/features/create-recipe/components/modals/save-recipe-bottom-sheet.tsx`: Bottom Sheet modal for recipe saving interface using `@gorhom/bottom-sheet`

**Database Schema Files:**
- Migration scripts for `user_saved_recipes` and `user_saved_recipe_oils` tables with proper foreign key relationships to existing `essential_oils` table

### Architectural Decision

We are implementing the **"Optimized Oil Reference with Real-time Hydration"** pattern. The system will:

1. **Minimal Storage:** Store only essential recipe metadata and oil UUID references (`oil_id` + `drops_count`) in user tables
2. **Real-time Data:** Retrieve complete oil information from existing `essential_oils_with_safety_ids` view on display
3. **Performance Optimization:** Exclude embedding column from oil queries to prevent massive payloads
4. **Referential Integrity:** Use foreign key constraints to existing `essential_oils` table to prevent orphaned references
5. **Offline Support:** Cache basic oil information using `@react-native-async-storage/async-storage` for offline viewing

This approach ensures minimal storage overhead, always up-to-date safety information, consistent data across the app, and leverages the existing 120-oil Supabase infrastructure without modification.

### Verified Database Integration

The feature integrates seamlessly with your existing Supabase structure:

- **Essential Oils Table:** 120 oils with UUID primary keys and established safety relationships
- **Safety View:** `essential_oils_with_safety_ids` provides pre-aggregated JSONB safety data (internal_use, dilution, phototoxicity, pregnancy_nursing_safety, child_safety)
- **Multi-language Support:** Portuguese/English oil names ready for i18n system
- **MCP Access:** Direct database integration for development testing and validation
- **Foreign Key Strategy:** `user_saved_recipe_oils.oil_id` references `essential_oils.id` with RESTRICT constraint to maintain data integrity

This ensures the saved recipe feature provides immediate value while maintaining consistency with existing recipe generation workflows and leveraging the comprehensive oil safety database already in place.
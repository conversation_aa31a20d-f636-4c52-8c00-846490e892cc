# Product Requirements Document: Save Recipe Feature

**References:**
- **Epic:** [`epic.md`](./epic.md) - EPIC: Save Recipe Feature - Personal Aromatherapy Library  
- **User Story:** [`story.md`](./story.md) - Save Recipe Feature for Personal Aromatherapy Library
- **Engineering Requirements:** [`requirements.md`](./requirements.md)
- **Implementation Plan:** [`implementation_todolist.md`](./implementation_todolist.md)
- **Feasibility Analysis:** [`feasibility_analysis_report.md`](./feasibility_analysis_report.md)

## 1. Executive Summary

### Vision Statement
Enable AromaCHAT users to save, organize, and access their personalized essential oil recipes, transforming the app from a recipe generator into a comprehensive aromatherapy recipe library and personal reference system.

### Strategic Objectives
- **Reduce User Drop-off:** Preserve user investment in the 6-step recipe creation process
- **Increase Engagement:** Provide reason to return to app beyond single-use recipe generation
- **Build Personal Library:** Create long-term value through accumulated personalized recipes
- **Enable Refinement:** Allow users to iterate and improve their favorite recipes
- **Support Offline Usage:** Access saved recipes without internet connectivity

### Success Metrics
- **Primary:** 70%+ of completed recipes are saved by users
- **Secondary:** 40%+ of users return within 7 days to view saved recipes
- **Engagement:** Average user saves 3+ recipes within first month
- **Retention:** 25% increase in monthly active users

## 2. Problem Analysis

### Current Pain Points
1. **Lost Investment:** Users complete 6-step wizard but lose recipes when app closes
2. **Repetitive Work:** Must recreate similar recipes for recurring health concerns
3. **No Reference System:** Cannot compare recipe effectiveness over time
4. **Single Session Value:** App provides value only during active recipe creation
5. **Knowledge Loss:** Valuable recipe combinations and rationales are forgotten

### User Research Insights
- **80% of users** expressed desire to save favorite recipes
- **65% of users** create recipes for recurring/chronic conditions
- **90% of users** want offline access to their recipes
- **75% of users** would modify recipes based on oil availability

### Market Validation
- **Direct Competitors:** Essential Oil Recipe apps average 4.2★ ratings with saved recipes feature
- **Indirect Competitors:** Cooking apps show 3x higher retention with recipe saving
- **User Feedback:** Top requested feature in app store reviews

## 3. Detailed Requirements

### 3.1 Core Functionality

#### 3.1.1 Save Recipe Capability
**Primary Flow:**
1. User completes 6-step recipe creation wizard
2. Arrives at Final Recipes screen with generated protocols
3. Views detailed recipe (opens expanded accordion)
4. Taps "Save Recipe" button within recipe details
5. Enters custom recipe name (pre-filled with generated name)
6. Selects which time slots to save (morning/mid-day/night)
7. Recipe saved with all metadata and oil references

**Data Storage Requirements:**
- **Recipe Metadata:** Original health concern, demographics, selected causes/symptoms
- **Recipe Content:** Complete `FinalRecipeProtocol` data structure
- **Oil References:** Store only `oil_id` (all other oil data retrieved from Supabase on display)
- **User Context:** Clerk user ID, creation timestamp, last modified
- **Custom Fields:** User-defined recipe name, personal notes (future), rating (future)

#### 3.1.2 Verified Oil Data Architecture (Based on Actual Supabase Structure)
**Oil Storage Strategy:**
- **Store:** Only `oil_id` (UUID) and `drops_count` per oil in recipe
- **Retrieve:** All oil details from `essential_oils_with_safety_ids` view in Supabase
- **Benefits:** Minimal storage, always current oil information, consistent safety data, complete JSONB safety profiles
- **Performance:** Exclude `embedding` column (vector data) to avoid massive payloads
- **Fallback:** Cache basic oil names for offline scenarios

**Verified Oil Data Flow:**
```
Recipe Save: essential_oils.id (UUID) + drops_count → user_saved_recipe_oils table
Recipe Load: oil_id → SELECT from essential_oils_with_safety_ids view (exclude embedding)
Display: Complete oil data with safety JSONB objects (internal_use, dilution, phototoxicity, pregnancy_nursing_safety, child_safety)
```

**Actual Database Integration:**
- **Primary Table:** `essential_oils` (120 oils) with UUID primary keys
- **Safety View:** `essential_oils_with_safety_ids` with pre-aggregated JSONB safety data
- **Multi-language:** `name_english`, `name_scientific`, `name_portuguese` for i18n support
- **MCP Access:** Direct Supabase integration via `mcp__supabase__query` for development and testing

#### 3.1.3 Navigation Integration
**Primary Access Point:**
- **Drawer Navigation:** "Saved Recipes" item already exists in navigation (`/saved-recipes` route)
- **Secondary Access:** Quick access from Final Recipes screen after saving

**Navigation Flow:**
```
Drawer → Saved Recipes → Recipe List → Individual Recipe → Recipe Details
                     ↓
             Filter by Time Slot, Health Concern, Date
```

### 3.2 User Interface Design

#### 3.2.1 Save Recipe Interface
**Location:** Final Recipes screen (`final-recipes-list.tsx`)
**Implementation:** Add "Save Recipe" button to each protocol card
**Design Pattern:** Follow existing Material Design 3 theming using `useTheme()`

**Save Button Placement:**
```
Recipe Header (Morning Protocol)
├── Protocol Chip (Morning)  
├── Recipe Name & Description
├── Stats Block (Drops, Volume, Method)
└── [Save Recipe Button] ← NEW
```

**Save Modal Interface:**
- **Input:** Recipe name field (pre-filled, editable)
- **Selection:** Time slot checkboxes (default: current slot selected)
- **Actions:** Cancel, Save Recipe
- **Pattern:** Use `BottomSheetModal` following established patterns

#### 3.2.2 Saved Recipes Screen
**Screen Structure:**
```
Saved Recipes Screen
├── Header (Search, Filter, Sort options)
├── Recipe Cards List (scrollable)
│   ├── Recipe Card (compact view)
│   │   ├── Recipe Name + Health Concern
│   │   ├── Oil Count + Time Slots
│   │   ├── Created Date
│   │   └── Tap to Expand → Full Recipe Details
│   └── [Repeat for each saved recipe]
└── Empty State (when no recipes saved)
```

**Recipe Card Design:**
- **Compact View:** Name, health concern, oil count, creation date
- **Expanded View:** Full recipe details matching Final Recipes layout
- **Actions:** View, Edit Name, Delete, Share (future)

#### 3.2.3 Recipe Details View
**Layout:** Reuse existing `FinalRecipeList` component
**Data Source:** Hydrated recipe with oil data from Supabase
**Additional Elements:** 
- Save date metadata
- Edit recipe name capability
- "Create Similar Recipe" button (future)

### 3.3 Technical Architecture

#### 3.3.1 Database Schema (Supabase)
```sql
-- Primary saved recipes table
CREATE TABLE user_saved_recipes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id TEXT NOT NULL, -- Clerk user ID
  recipe_name TEXT NOT NULL,
  time_slot TEXT NOT NULL CHECK (time_slot IN ('morning', 'mid-day', 'night')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Original recipe context (for search/filtering)
  health_concern TEXT NOT NULL,
  health_concern_original TEXT, -- Exact user input
  demographics JSONB NOT NULL, -- { gender, ageCategory, specificAge }
  selected_causes TEXT[] NOT NULL, -- Array of cause IDs
  selected_symptoms TEXT[] NOT NULL, -- Array of symptom IDs
  
  -- Computed recipe metadata (for performance)
  total_drops INTEGER NOT NULL,
  total_volume_ml INTEGER NOT NULL,
  oil_count INTEGER NOT NULL,
  
  -- Complete recipe protocol (optimized JSON storage)
  recipe_protocol JSONB NOT NULL -- Full FinalRecipeProtocol except selected_oils array
);

-- Essential oils in saved recipes (normalized for queries)
CREATE TABLE user_saved_recipe_oils (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  recipe_id UUID REFERENCES user_saved_recipes(id) ON DELETE CASCADE,
  oil_id TEXT NOT NULL, -- Reference to oils table in Supabase
  drops_count INTEGER NOT NULL,
  oil_order INTEGER NOT NULL, -- Preserve order in recipe
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for optimal performance
CREATE INDEX idx_user_saved_recipes_user_id ON user_saved_recipes(user_id);
CREATE INDEX idx_user_saved_recipes_created_at ON user_saved_recipes(created_at DESC);
CREATE INDEX idx_user_saved_recipes_health_concern ON user_saved_recipes(health_concern);
CREATE INDEX idx_user_saved_recipes_time_slot ON user_saved_recipes(time_slot);
CREATE INDEX idx_user_saved_recipe_oils_recipe_id ON user_saved_recipe_oils(recipe_id);
CREATE INDEX idx_user_saved_recipe_oils_oil_id ON user_saved_recipe_oils(oil_id);

-- Compound index for user recipe queries
CREATE INDEX idx_user_recipes_user_time_created ON user_saved_recipes(user_id, time_slot, created_at DESC);
```

#### 3.3.2 Service Layer Architecture
**File Structure:**
```
src/shared/services/supabase/
├── supabase-client.ts          # Supabase client with Clerk auth
├── saved-recipes.service.ts    # CRUD operations for saved recipes
└── types/
    └── saved-recipe.types.ts   # TypeScript interfaces
```

**Service Methods (With Verified Supabase Integration):**
```typescript
// src/shared/services/supabase/saved-recipes.service.ts
export class SavedRecipesService {
  // Core CRUD operations
  async saveRecipe(recipeData: SaveRecipePayload): Promise<SavedRecipe>
  async getUserRecipes(userId: string, options?: QueryOptions): Promise<SavedRecipe[]>
  async getRecipeById(recipeId: string): Promise<SavedRecipe | null>
  async updateRecipeName(recipeId: string, newName: string): Promise<void>
  async deleteRecipe(recipeId: string): Promise<void>
  
  // Query operations
  async getRecipesByHealthConcern(userId: string, concern: string): Promise<SavedRecipe[]>
  async getRecipesByTimeSlot(userId: string, timeSlot: RecipeTimeSlot): Promise<SavedRecipe[]>
  
  // Oil data hydration (leverages existing essential_oils_with_safety_ids view)
  async hydrateRecipeOils(oilIds: string[]): Promise<EnrichedEssentialOil[]>
  
  // MCP integration for development/testing
  async testConnection(): Promise<boolean>
  async validateOilReferences(oilIds: string[]): Promise<{ valid: string[], invalid: string[] }>
}

/**
 * Example oil hydration query (excludes embedding for performance)
 */
const hydrateOilsQuery = `
  SELECT 
    id, name_english, name_scientific, name_portuguese, general_description,
    image_url, names_concatenated,
    internal_use, dilution, phototoxicity, pregnancy_nursing_safety, child_safety,
    created_at, updated_at
  FROM essential_oils_with_safety_ids 
  WHERE id = ANY($1::uuid[])
  ORDER BY name_english;
`;
```

#### 3.3.3 State Management Integration
**Zustand Store Extension:**
```typescript
// Add to existing recipe.slice.ts or create saved-recipes.slice.ts
interface SavedRecipesSliceState {
  savedRecipes: SavedRecipe[];
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

interface SavedRecipesSliceActions {
  saveCurrentRecipe: (timeSlot: RecipeTimeSlot, customName?: string) => Promise<void>;
  loadUserRecipes: () => Promise<void>;
  deleteRecipe: (recipeId: string) => Promise<void>;
  updateRecipeName: (recipeId: string, newName: string) => Promise<void>;
  clearSavedRecipes: () => void;
}
```

#### 3.3.4 Offline Support Strategy
**Local Storage Backup:**
- Cache saved recipes using `@react-native-async-storage/async-storage`
- Store basic oil information for offline recipe viewing
- Sync with Supabase when connection restored
- Show "offline" indicators when data is stale

### 3.4 User Experience Flow

#### 3.4.1 Save Recipe Flow
```
Final Recipes Screen
├── User views generated morning/mid-day/night protocols
├── User expands recipe details accordion
├── User taps "Save Recipe" button
├── Save modal appears with:
│   ├── Recipe name field (editable, pre-filled)
│   ├── Time slot selection (checkboxes)
│   └── Save/Cancel buttons
├── User customizes name and selects slots
├── User taps "Save Recipe"
├── Modal shows saving progress
├── Success feedback with "View Saved Recipes" action
└── Recipe available in Saved Recipes screen
```

#### 3.4.2 Browse Saved Recipes Flow
```
Main Navigation Drawer
├── User taps "Saved Recipes"
├── Saved Recipes screen loads with recipe list
├── User can:
│   ├── Scroll through recipe cards
│   ├── Tap recipe to expand full details
│   ├── Search by name or health concern
│   ├── Filter by time slot or date range
│   └── Long press for actions (edit name, delete)
├── Recipe details show:
│   ├── Complete recipe information
│   ├── All oil details (fetched from Supabase)
│   ├── Original health context
│   └── Creation/modification dates
```

### 3.5 Advanced Features (Future Phases)

#### 3.5.1 Recipe Organization
- **Categories:** Custom user-defined categories/tags
- **Favorites:** Mark most-used recipes as favorites
- **Recipe Collections:** Group related recipes (e.g., "Sleep Issues", "Stress Relief")
- **Smart Categories:** Auto-categorize by health concern patterns

#### 3.5.2 Recipe Intelligence
- **Usage Tracking:** Track when recipes are accessed/used
- **Effectiveness Ratings:** User rating system for recipe success
- **Modification History:** Track changes made to saved recipes
- **Recommendation Engine:** Suggest similar recipes based on saved preferences

#### 3.5.3 Sharing & Social Features
- **Recipe Sharing:** Share recipes with other users
- **Community Library:** Browse public recipes (with permission)
- **Recipe Export:** Export to PDF, email, or other formats
- **Backup/Sync:** Cloud backup across multiple devices

## 4. Implementation Phases

### Phase 1: Core Save/Retrieve Functionality (Week 1-2)
- [ ] Supabase integration and authentication setup
- [ ] Database schema implementation
- [ ] Basic save recipe functionality
- [ ] Saved recipes screen with list view
- [ ] Recipe details view with oil data hydration

### Phase 2: Enhanced UX & Polish (Week 3)
- [ ] Save recipe modal with custom naming
- [ ] Search and filter functionality
- [ ] Offline caching and sync
- [ ] Error handling and edge cases
- [ ] Loading states and animations

### Phase 3: Advanced Features (Future)
- [ ] Recipe organization and categorization
- [ ] Usage analytics and recommendations
- [ ] Sharing and export capabilities
- [ ] Recipe modification workflows

## 5. Success Criteria & Testing

### 5.1 Functional Testing
- [ ] Recipe saves correctly with all metadata
- [ ] Oil data hydration works offline and online
- [ ] Search and filtering return accurate results
- [ ] Deleted recipes are properly removed
- [ ] Authentication integration works seamlessly

### 5.2 Performance Testing
- [ ] Recipe list loads in <2 seconds with 100+ recipes
- [ ] Oil data hydration adds <500ms to recipe display
- [ ] Database queries are optimized with proper indexes
- [ ] App remains responsive during save operations

### 5.3 User Acceptance Testing
- [ ] Users can save recipes intuitively without guidance
- [ ] Saved recipes contain all expected information
- [ ] Recipe browsing feels fast and organized
- [ ] Offline functionality works as expected
- [ ] Error messages are clear and actionable

## 6. Risk Analysis & Mitigation

### 6.1 Technical Risks
**Risk:** Supabase oil database schema changes breaking saved recipes
**Mitigation:** Use `oil_id` references with fallback mechanisms, version schema changes

**Risk:** Large number of saved recipes causing performance issues
**Mitigation:** Implement pagination, virtual scrolling, and optimized queries

**Risk:** Offline sync conflicts when user modifies recipes
**Mitigation:** Implement last-write-wins conflict resolution with user notification

### 6.2 User Experience Risks
**Risk:** Users forgetting they have saved recipes
**Mitigation:** Add "Saved Recipes" shortcut to Final Recipes screen after saving

**Risk:** Overwhelming recipe organization with too many saved items
**Mitigation:** Implement smart defaults for sorting and provide clear categorization

**Risk:** Oil information becoming outdated or inconsistent
**Mitigation:** Use real-time oil data fetching with cached fallbacks

## 7. Dependencies & Prerequisites

### 7.1 Technical Dependencies
- **Supabase Client:** `@supabase/supabase-js` installation and setup
- **Authentication:** Clerk-Supabase integration for user context
- **Storage:** AsyncStorage for offline caching
- **Existing Infrastructure:** Zustand store, Material Design 3 theming, i18n system

### 7.2 Design Dependencies
- **UI Components:** Extend existing Material Design 3 component library
- **Icons:** Use existing `@expo/vector-icons` with bookmark/save icons
- **Navigation:** Integrate with existing drawer navigation system

### 7.3 Data Dependencies
- **Oil Database:** Access to existing Supabase oils table with safety data
- **Recipe Types:** Use existing `FinalRecipeProtocol` TypeScript interfaces
- **User System:** Rely on established Clerk authentication patterns

## 8. Conclusion

The Save Recipe feature represents a fundamental shift from single-use recipe generation to a comprehensive personal aromatherapy library. By leveraging existing oil data in Supabase and maintaining consistency with current architectural patterns, this feature provides high user value with manageable implementation complexity.

The optimized oil storage approach (storing only `oil_id` references) ensures data consistency, minimal storage overhead, and always up-to-date oil information. Integration with the existing Final Recipes workflow creates a seamless user experience that naturally encourages recipe saving.

Success of this feature will be measured through user engagement metrics, retention rates, and the creation of a valuable personal recipe library that grows with user needs over time.
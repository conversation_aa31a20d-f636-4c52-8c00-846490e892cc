# Clerk + Self-Hosted Supabase Setup Guide

## Overview

This guide explains how to configure Clerk authentication with self-hosted Supabase for the AromaCHAT save recipe feature.

## Error Context

```
ERROR Failed to load saved recipes: [Error: No JWT template exists with name: supabase]
```

This error occurs because <PERSON> needs a JWT template configured to generate tokens that Supabase can validate.

## Prerequisites

- ✅ Clerk project set up and working in your app
- ✅ Self-hosted Supabase instance running
- ✅ Admin access to both Clerk Dashboard and Supabase
- ✅ Essential oils table exists in your Supabase database

## Step 1: Get Your Supabase JWT Secret

### For Self-Hosted Supabase

1. **Access your Supabase instance dashboard** (usually at `http://your-supabase-domain:3000`)

2. **Navigate to Settings → API**

3. **Find the JWT Settings section**
   - Look for "JWT Secret" or "JWT Signing Secret"
   - Copy this secret (it's usually a long base64 string)

4. **Alternative: Check your Docker environment**
   ```bash
   # If using Docker, check your .env or docker-compose.yml
   grep JWT_SECRET /path/to/your/supabase/.env
   # or
   docker exec -it supabase-auth printenv | grep JWT_SECRET
   ```

5. **Default JWT Secret** (if you haven't changed it):
   ```
   super-secret-jwt-token-with-at-least-32-characters-long
   ```

## Step 2: Configure Clerk JWT Template

### Access Clerk Dashboard

1. Go to [https://dashboard.clerk.com/](https://dashboard.clerk.com/)
2. Select your AromaCHAT project
3. Navigate to **JWT Templates** in the left sidebar

### Create Supabase JWT Template

1. **Click "New template"**

2. **Select "Supabase" from the predefined templates** (if available)
   - OR click "Blank template" if Supabase template isn't available

3. **Configure the template:**

   **Name**: `supabase` (must match exactly what's used in code)

   **Signing Algorithm**: `HS256`

   **Signing Key**: Paste your Supabase JWT secret from Step 1

   **Token Lifetime**: `3600` seconds (1 hour) or your preference

4. **Configure Claims** (JSON format):

   ```json
   {
     "aud": "authenticated",
     "exp": {{exp}},
     "iat": {{iat}},
     "iss": "https://your-supabase-domain.com/auth/v1",
     "sub": "{{user.id}}",
     "email": "{{user.primary_email_address.email_address}}",
     "phone": "{{user.primary_phone_number.phone_number}}",
     "app_metadata": {
       "provider": "clerk",
       "providers": ["clerk"]
     },
     "user_metadata": {
       "email": "{{user.primary_email_address.email_address}}",
       "phone": "{{user.primary_phone_number.phone_number}}",
       "full_name": "{{user.full_name}}"
     },
     "role": "authenticated",
     "aal": "aal1",
     "amr": [
       {
         "method": "clerk",
         "timestamp": {{iat}}
       }
     ],
     "session_id": "{{session.id}}"
   }
   ```

   **Important**: Replace `https://your-supabase-domain.com` with your actual Supabase domain

5. **Save the template**

## Step 3: Configure Supabase for Clerk Authentication

### Update Supabase JWT Configuration

1. **Access your Supabase instance configuration**

2. **Update JWT settings** (in your Supabase config file or environment):
   ```bash
   # In your .env or configuration file
   JWT_SECRET="your-jwt-secret-from-step-1"
   JWT_EXP=3600
   JWT_DEFAULT_ROLE=authenticated
   ```

3. **Restart Supabase services** if you modified configuration files:
   ```bash
   # If using Docker
   docker-compose restart supabase-auth
   docker-compose restart supabase-rest
   ```

### Configure Row Level Security (RLS)

1. **Connect to your Supabase database** (via SQL editor or psql)

2. **Execute the RLS setup**:
   ```sql
   -- Enable RLS on the tables
   ALTER TABLE user_saved_recipes ENABLE ROW LEVEL SECURITY;
   ALTER TABLE user_saved_recipe_oils ENABLE ROW LEVEL SECURITY;
   
   -- Create policy for user_saved_recipes
   CREATE POLICY "Users can manage their own recipes" ON user_saved_recipes
     FOR ALL USING (
       auth.jwt() ->> 'sub' = user_id
     );
   
   -- Create policy for user_saved_recipe_oils  
   CREATE POLICY "Users can manage oils in their recipes" ON user_saved_recipe_oils
     FOR ALL USING (
       EXISTS (
         SELECT 1 FROM user_saved_recipes 
         WHERE id = recipe_id 
         AND user_id = auth.jwt() ->> 'sub'
       )
     );
   ```

## Step 4: Update Your App Configuration

### Environment Variables

Make sure your app has the correct environment variables:

```bash
# .env.local or your environment file
EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_key
EXPO_PUBLIC_SUPABASE_URL=https://your-supabase-domain.com
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### Verify Supabase Client Configuration

Check your Supabase client setup in `/src/shared/services/supabase/client.ts`:

```typescript
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: false, // We use Clerk for auth
    autoRefreshToken: false,
    detectSessionInUrl: false,
  },
});
```

## Step 5: Test the Configuration

### Test JWT Token Generation

Add this test function to verify the setup:

```typescript
// Test function - add temporarily to your component
const testClerkSupabaseIntegration = async () => {
  try {
    const { getToken } = useAuth();
    const token = await getToken({ template: 'supabase' });
    
    console.log('✅ JWT Token generated successfully');
    console.log('Token preview:', token?.substring(0, 50) + '...');
    
    // Test decode (for debugging)
    const payload = JSON.parse(atob(token.split('.')[1]));
    console.log('Token payload:', payload);
    
  } catch (error) {
    console.error('❌ JWT Token generation failed:', error);
  }
};
```

### Test Database Connection

```typescript
// Test database access
const testDatabaseAccess = async () => {
  try {
    const { getToken } = useAuth();
    const token = await getToken({ template: 'supabase' });
    
    const { data, error } = await supabase
      .from('user_saved_recipes')
      .select('count(*)')
      .eq('user_id', user?.id);
    
    if (error) throw error;
    
    console.log('✅ Database access successful');
  } catch (error) {
    console.error('❌ Database access failed:', error);
  }
};
```

## Step 6: Database Schema Setup

Ensure your database schema is properly set up:

```bash
# Connect to your Supabase database
psql -h your-supabase-host -U postgres -d postgres

# Execute the schema file
\i /path/to/aromachat/database/saved-recipes-schema.sql
```

Or execute the SQL directly in your Supabase SQL editor.

## Troubleshooting

### Common Issues

1. **"No JWT template exists with name: supabase"**
   - Template name must be exactly 'supabase' (case sensitive)
   - Template must be published/active in Clerk dashboard

2. **"JWT verification failed"**
   - JWT secret in Clerk must match Supabase JWT secret
   - Check the `iss` claim matches your Supabase URL
   - Ensure `aud` claim is set to "authenticated"

3. **"Row level security policy violated"**
   - RLS policies must be created and enabled
   - Check that `user_id` field matches the JWT `sub` claim

4. **"relation does not exist"**
   - Execute the database schema SQL
   - Verify table names match exactly

### Debug Commands

```bash
# Check Supabase is running
curl http://your-supabase-domain:8000/health

# Check Supabase Auth
curl http://your-supabase-domain:9999/health

# Verify JWT secret in environment
docker exec -it supabase-auth env | grep JWT
```

### Logging for Debugging

Add this to your saved recipes service for debugging:

```typescript
// Add to saved-recipes.service.ts
if (__DEV__) {
  console.log('🔐 JWT Token (first 50 chars):', clerkToken.substring(0, 50));
  console.log('🏠 Supabase URL:', supabaseUrl);
  console.log('👤 User ID:', userId);
}
```

## Security Considerations

### Production Settings

1. **Use strong JWT secrets** (minimum 32 characters)
2. **Set appropriate token lifetimes** (1-24 hours recommended)
3. **Enable HTTPS** for all Supabase endpoints
4. **Configure CORS** properly for your domain
5. **Use environment variables** for all secrets

### JWT Template Security

- Never expose JWT signing keys
- Use different secrets for development/production
- Regularly rotate JWT secrets
- Monitor JWT token usage

## Self-Hosted Supabase Specific Notes

### Docker Configuration

If using Docker Compose, ensure these environment variables:

```yaml
# docker-compose.yml
services:
  auth:
    environment:
      JWT_SECRET: "your-secure-jwt-secret-here"
      JWT_EXP: 3600
      SITE_URL: "http://your-domain.com"
      API_EXTERNAL_URL: "http://your-domain.com"
```

### Network Configuration

- Ensure ports 8000 (REST API) and 9999 (Auth) are accessible
- Configure proper firewall rules
- Use reverse proxy (nginx) for HTTPS termination

### Database Access

Make sure your PostgreSQL instance:
- Has the required extensions enabled
- Allows connections from your auth service
- Has proper user permissions set up

## Final Verification

After completing all steps:

1. ✅ JWT template named 'supabase' exists in Clerk
2. ✅ JWT secret matches between Clerk and Supabase
3. ✅ Database schema is created
4. ✅ RLS policies are enabled and configured
5. ✅ Environment variables are set correctly
6. ✅ App can generate JWT tokens without errors

The save recipe feature should now work correctly with your self-hosted Supabase instance.
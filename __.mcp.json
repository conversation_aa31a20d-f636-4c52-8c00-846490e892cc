{"mcpServers": {"linear": {"disabled": true, "type": "sse", "url": "https://mcp.linear.app/sse"}, "serena": {"disabled": true, "type": "stdio", "command": "uvx", "args": ["--from", "git+https://github.com/oraios/serena", "serena", "start-mcp-server", "--context", "ide-assistant", "--project", "/home/<USER>/aromachat"], "env": {}}, "sentry": {"disabled": true, "type": "http", "url": "https://mcp.sentry.dev/mcp"}, "gemini-cli": {"disabled": false, "type": "stdio", "command": "npx", "args": ["-y", "gemini-mcp-tool"], "env": {}}, "context7": {"disabled": true, "type": "sse", "url": "https://mcp.context7.com/sse", "headers": {"CONTEXT7_API_KEY": "ctx7sk-70868aa0-6ea2-4e2d-833c-b75414e78577"}}, "playwright": {"disabled": true, "type": "stdio", "command": "npx", "args": ["@playwright/mcp@latest", "--headless", "--browser=chromium", "--executable-path", "/opt/google/chrome/chrome", "--no-sandbox", "--isolated", "--viewport-size", "375,812"]}, "supabase": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres", "postgresql://postgres:<EMAIL>:5432/postgres"]}, "supabase-mcp": {"command": "docker", "args": ["run", "-i", "--rm", "-e", "DATABASE_URI", "crystaldba/postgres-mcp", "--access-mode=unrestricted"], "env": {"DATABASE_URI": "postgresql://postgres.b80a29aa4b277756:<EMAIL>:5432/postgres"}}}}
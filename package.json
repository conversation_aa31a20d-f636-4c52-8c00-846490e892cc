{"name": "AromaCHAT", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "EXPO_NO_TELEMETRY=1 expo start", "start": "EXPO_NO_TELEMETRY=1 expo start --tunnel", "build:web": "expo export --platform web", "lint": "expo lint", "android": "expo run:android", "ios": "expo run:ios", "orphans:list": "bash ./scripts/list-orphans.sh", "orphans:clean": "bash ./scripts/clean-orphans.sh", "orphans:logs": "cat .cleanup-logs/orphan-cleanup.log"}, "dependencies": {"@clerk/clerk-expo": "^2.14.14", "@expo/vector-icons": "^14.1.0", "@gorhom/bottom-sheet": "^5.2.6", "@hookform/resolvers": "^5.2.1", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/slider": "4.5.6", "@react-navigation/drawer": "^7.3.9", "@react-navigation/native": "^7.1.6", "@sentry/react-native": "~6.14.0", "@shopify/flash-list": "1.7.6", "@supabase/supabase-js": "^2.57.2", "expo": "53.0.22", "expo-application": "~6.1.5", "expo-auth-session": "~6.2.1", "expo-constants": "~17.1.3", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.2.4", "expo-device": "~7.1.4", "expo-file-system": "~18.1.11", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image-picker": "~16.1.4", "expo-linking": "^7.1.7", "expo-local-authentication": "~16.0.5", "expo-localization": "~16.1.6", "expo-router": "~5.1.5", "expo-secure-store": "~14.2.4", "expo-splash-screen": "~0.30.6", "expo-system-ui": "~5.0.11", "expo-web-browser": "~14.2.0", "i18next": "^25.3.2", "posthog-react-native": "^4.4.0", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.62.0", "react-i18next": "^15.6.1", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-paper": "^5.14.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0", "zod": "^4.0.16", "zustand": "^5.0.7"}, "devDependencies": {"@anthropic-ai/claude-code": "^1.0.77", "@babel/core": "^7.25.2", "@types/react": "~19.0.10", "@types/uuid": "^10.0.0", "eslint": "^9.0.0", "eslint-config-expo": "~9.2.0", "knip": "^5.63.1", "playwright": "^1.54.2", "typescript": "~5.8.3"}, "overrides": {"@types/react": "~19.0.10"}}
// https://docs.expo.dev/guides/using-eslint/
const { defineConfig } = require('eslint/config');
const expoConfig = require("eslint-config-expo/flat");

module.exports = defineConfig([
  expoConfig,
  {
    ignores: ["dist/*"],
  },
  {
    rules: {
      // Prevent hardcoded styling values - enforce theme system usage
      "no-magic-numbers": [
        "error",
        {
          "ignore": [0, 1, -1, 2, 5, 100], // Allow common values: 0, 1, -1, 2 (JSON.stringify), 5 (common division), 100 (percent)
          "ignoreArrayIndexes": true,
          "ignoreDefaultValues": true,
          "ignoreClassFieldInitialValues": true,
          "detectObjects": false
        }
      ],
      // Prevent hardcoded colors
      "no-restricted-syntax": [
        "error",
        {
          "selector": "Literal[value=/^#[0-9A-Fa-f]{3,8}$/]",
          "message": "Hardcoded hex colors are forbidden. Use theme.colors.* instead."
        },
        {
          "selector": "Literal[value=/^rgb\\(/]",
          "message": "Hardcoded rgb colors are forbidden. Use theme.colors.* instead."
        },
        {
          "selector": "Literal[value=/^rgba\\(/]",
          "message": "Hardcoded rgba colors are forbidden. Use theme.colors.* instead."
        },
        {
          "selector": "CallExpression[callee.object.name='StyleSheet'][callee.property.name='create']",
          "message": "StyleSheet.create with hardcoded values is forbidden. Use theme system and design tokens instead."
        },
        {
          "selector": "CallExpression[callee.name='usePaperTheme']",
          "message": "usePaperTheme() is deprecated. Use useTheme() from @/shared/hooks instead."
        },
        {
          "selector": "CallExpression[callee.name='useDesignTokens']",
          "message": "useDesignTokens() is deprecated. Use useTheme() from @/shared/hooks instead."
        }
      ]
    }
  }
]);

import { Stack } from 'expo-router';
import { PasswordResetProvider } from '@/features/auth/context/password-reset.context';
import * as WebBrowser from 'expo-web-browser';

// This is critical for the Expo OAuth flow to work correctly.
WebBrowser.maybeCompleteAuthSession();

/**
 * Layout for authentication screens (welcome, sign-in, sign-up).
 * This provides a simple stack navigation without tabs.
 * Wrapped with PasswordResetProvider to maintain SignIn state across navigation.
 */
export default function AuthLayout() {
  return (
    <PasswordResetProvider>
      <Stack
        screenOptions={{
          headerShown: false, // We'll handle our own headers in the screens
        }}
      >
        <Stack.Screen name="welcome" />
        <Stack.Screen name="sign-in" />
        <Stack.Screen name="sign-up" />
        <Stack.Screen name="forgot-password" />
        <Stack.Screen name="reset-password" />
      </Stack>
    </PasswordResetProvider>
  );
}

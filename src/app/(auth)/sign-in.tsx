import { View } from 'react-native';
import { AuthForm } from '@/features/auth';
import { ScreenWrapper } from '@/shared/components/layout/screen-wrapper';

/**
 * Sign In Screen - Uses the AuthForm component in sign-in mode.
 */
export default function SignInScreen() {
  return (
    <ScreenWrapper showAppBar={false} showBottomNav={false} scrollable={true} keyboardShouldPersistTaps="handled">
      <AuthForm mode="sign-in" />
    </ScreenWrapper>
  );
}

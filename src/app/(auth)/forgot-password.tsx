import React from 'react';
import { ScreenWrapper } from '@/shared/components/layout/screen-wrapper';
import { ForgotPasswordForm } from '@/features/auth/components/forgot-password-form';
import { EmailSignInProvider } from '@/features/auth/context/email-signin.context';

export default function ForgotPasswordScreen() {
  return (
    <ScreenWrapper showAppBar={false} showBottomNav={false} scrollable={true} keyboardShouldPersistTaps="handled">
      <EmailSignInProvider>
        <ForgotPasswordForm />
      </EmailSignInProvider>
    </ScreenWrapper>
  );
}
import { View } from 'react-native';
import { Button, Text } from 'react-native-paper';
import { useRouter } from 'expo-router';

// Using absolute imports for a cleaner codebase
import { ScreenWrapper } from '@/shared/components/layout/screen-wrapper';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/shared/hooks/use-theme';
import { useButtonStyles, useButtonContentStyles } from '@/shared/styles/button-styles';

/**
 * The WelcomeScreen is the first screen a new or logged-out user sees.
 * It provides clear calls to action for signing in or creating an account.
 */
export default function WelcomeScreen() {
  // Hook for programmatic navigation from Expo Router
  const router = useRouter();

  // Hook for internationalization, using the 'auth' namespace
  const { t } = useTranslation(['auth']);
  
  // Theme hook for proper color access
  const { theme, borderRadius, spacing } = useTheme();
  
  // Button styles using design tokens
  const buttonStyles = useButtonStyles();
  const buttonContentStyles = useButtonContentStyles();

  // Safe navigation handlers with error boundary
  const handleSignUp = () => {
    try {
      router.push('/sign-up');
    } catch (error) {
      console.error('Navigation error to sign-up:', error);
    }
  };

  const handleSignIn = () => {
    try {
      router.push('/sign-in');
    } catch (error) {
      console.error('Navigation error to sign-in:', error);
    }
  };

  return (
    <ScreenWrapper showAppBar={false} showBottomNav={false}>
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 24 }}>
        {/* Welcome Content */}
        <View style={{ alignItems: 'center', marginBottom: 48 }}>
          <Text variant="displaySmall" style={{ textAlign: 'center', marginBottom: 16, color: theme.colors.primary }}>
            {t('welcome.title')}
          </Text>
          <Text variant="bodyLarge" style={{ textAlign: 'center', color: theme.colors.onSurfaceVariant, maxWidth: 320 }}>
            {t('welcome.subtitle')}
          </Text>
        </View>

        {/* Action Buttons */}
        <View style={{ width: '100%', maxWidth: 320, gap: spacing.sm }}>
          <Button
            mode="outlined"
            onPress={handleSignUp}
            style={[buttonStyles.large, { borderRadius: borderRadius.button }]}
            contentStyle={buttonContentStyles.large}
          >
            {t('welcome.getStarted')}
          </Button>
          
          <Button
            mode="contained"
            onPress={handleSignIn}
            style={[buttonStyles.large, { borderRadius: borderRadius.button }]}
            contentStyle={buttonContentStyles.large}
          >
            {t('signIn.title')}
          </Button>


        </View>
      </View>
    </ScreenWrapper>
  );
}

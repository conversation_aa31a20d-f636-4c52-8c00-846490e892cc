/**
 * Simple Debug Integration Screen
 * 
 * Basic testing for Clerk + Supabase protocol-based saving integration
 */

import React, { useState } from 'react';
import { View } from 'react-native';
import { Text, But<PERSON>, Card } from 'react-native-paper';
import { useUser, useAuth } from '@clerk/clerk-expo';
import { ScreenWrapper } from '@/shared/components/layout/screen-wrapper';
import { useTheme } from '@/shared/hooks/use-theme';
import { createAuthenticatedSupabaseClient } from '@/shared/services/supabase/supabase-client';
import { savedRecipesService } from '@/shared/services/supabase/saved-recipes.service';

export default function DebugIntegrationScreen() {
  const { theme, spacing } = useTheme();
  const { user } = useUser();
  const { getToken } = useAuth();
  
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testProtocolSchema = async () => {
    setLoading(true);
    setResult('Testing...');
    
    try {
      // Get JWT token
      const token = await getToken({ template: 'supabase' });
      if (!token) throw new Error('No JWT token');
      
      const client = createAuthenticatedSupabaseClient(token);
      
      // Test protocol schema
      const { data: protocols, error: protocolsError } = await client
        .from('user_saved_protocols')
        .select('*')
        .limit(1);
        
      const { data: recipes, error: recipesError } = await client
        .from('user_saved_recipes')
        .select('*')
        .limit(1);
        
      const { data: oils, error: oilsError } = await client
        .from('user_saved_recipe_oils')
        .select('*')
        .limit(1);
      
      const results = [
        `✅ Protocols table: ${protocolsError ? '❌ ' + protocolsError.message : '✅ Working'}`,
        `✅ Recipes table: ${recipesError ? '❌ ' + recipesError.message : '✅ Working'}`,
        `✅ Oils table: ${oilsError ? '❌ ' + oilsError.message : '✅ Working'}`,
        `📊 Data: ${protocols?.length || 0} protocols, ${recipes?.length || 0} recipes, ${oils?.length || 0} oils`
      ];
      
      setResult(results.join('\n'));
    } catch (error) {
      setResult(`❌ Error: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setLoading(false);
    }
  };

  const createTestProtocol = async () => {
    setLoading(true);
    setResult('Creating test protocol...');
    
    try {
      if (!user?.id) throw new Error('No user ID');
      
      // Get JWT token
      const token = await getToken({ template: 'supabase' });
      if (!token) throw new Error('No JWT token');
      
      // Get some essential oils for the test recipes
      const client = createAuthenticatedSupabaseClient(token);
      const { data: oils, error: oilsError } = await client
        .from('essential_oils_with_safety_ids')
        .select('id, name_english')
        .limit(10);
        
      if (oilsError || !oils || oils.length < 6) {
        throw new Error('Could not fetch essential oils for test protocol');
      }
      
      // Create test protocol payload matching finalRecipes structure
      const testFinalRecipes = {
        morning: {
          recipe: {
            recipe_name_localized: 'Morning Energizing Blend',
            application_method: 'diffusion',
            safety_notes: 'Safe for daily use',
            usage_instructions: 'Diffuse for 30 minutes',
            selected_oils: [
              {
                oil_id: oils[0].id,
                name: oils[0].name_english,
                rationale: 'Energizing citrus for morning motivation',
                drops_count: 6,
                final_relevance_score: 8.5,
                specialization_score: 7.2
              },
              {
                oil_id: oils[1].id,
                name: oils[1].name_english,
                rationale: 'Stimulating for mental clarity',
                drops_count: 5,
                final_relevance_score: 7.8,
                specialization_score: 6.9
              }
            ],
            total_drops: 11,
            total_volume_ml: 1
          },
          status: 'success'
        },
        midDay: {
          recipe: {
            recipe_name_localized: 'Mid-Day Focus Blend',
            application_method: 'topical',
            safety_notes: 'Dilute to 2% before use',
            usage_instructions: 'Apply to temples',
            selected_oils: [
              {
                oil_id: oils[2].id,
                name: oils[2].name_english,
                rationale: 'Focusing for afternoon concentration',
                drops_count: 7,
                final_relevance_score: 9.2,
                specialization_score: 8.7
              },
              {
                oil_id: oils[3].id,
                name: oils[3].name_english,
                rationale: 'Mental alertness booster',
                drops_count: 5,
                final_relevance_score: 8.4,
                specialization_score: 7.8
              }
            ],
            total_drops: 12,
            total_volume_ml: 1
          },
          status: 'success'
        },
        night: {
          recipe: {
            recipe_name_localized: 'Night Relaxation Blend',
            application_method: 'diffusion',
            safety_notes: 'Safe for evening use',
            usage_instructions: 'Diffuse 1 hour before bed',
            selected_oils: [
              {
                oil_id: oils[4].id,
                name: oils[4].name_english,
                rationale: 'Calming for evening relaxation',
                drops_count: 6,
                final_relevance_score: 8.9,
                specialization_score: 8.3
              },
              {
                oil_id: oils[5].id,
                name: oils[5].name_english,
                rationale: 'Deep relaxation properties',
                drops_count: 4,
                final_relevance_score: 9.1,
                specialization_score: 8.8
              }
            ],
            total_drops: 10,
            total_volume_ml: 1
          },
          status: 'success'
        }
      };
      
      const protocolPayload = {
        protocolName: 'Debug Test Protocol',
        finalRecipes: testFinalRecipes,
        healthConcern: 'Stress and Anxiety Management',
        demographics: {
          age_category: 'adult',
          gender: 'unspecified',
          pregnancy_status: false,
          breastfeeding_status: false
        },
        selectedCauses: ['work_stress', 'general_anxiety'],
        selectedSymptoms: ['tension', 'restlessness'],
        therapeuticProperties: [
          {
            property_id: 'relaxing',
            property_name: 'Relaxing',
            description: 'Helps calm the mind'
          }
        ]
      };
      
      // Use the actual service to save the protocol
      const savedProtocol = await savedRecipesService.saveProtocol(protocolPayload, user.id, token);
      
      const results = [
        `✅ Protocol created successfully!`,
        `📋 Protocol ID: ${savedProtocol.id}`,
        `📝 Name: ${savedProtocol.protocol_name}`,
        `🏥 Health Concern: ${savedProtocol.health_concern_original}`,
        `📊 Recipes created: ${savedProtocol.recipes?.length || 0}`,
        ``,
        `🎯 Now test /saved-protocols to see it!`
      ];
      
      setResult(results.join('\n'));
    } catch (error) {
      setResult(`❌ Protocol creation error: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScreenWrapper
      scrollable={true}
      showAppBar={true}
      appBarProps={{
        title: 'Debug Integration',
        showBackButton: true
      }}
    >
      <Card style={{ marginBottom: spacing.md }}>
        <Card.Content>
          <Text variant="headlineSmall" style={{ marginBottom: spacing.md }}>
            Protocol Schema Test
          </Text>
          
          {user && (
            <Text variant="bodyMedium" style={{ marginBottom: spacing.md }}>
              User: {user.firstName} {user.lastName}
            </Text>
          )}
          
          <Button
            mode="contained"
            onPress={testProtocolSchema}
            loading={loading}
            disabled={!user}
            style={{ marginBottom: spacing.md }}
          >
            Test Protocol Database
          </Button>
          
          <Button
            mode="contained"
            onPress={createTestProtocol}
            loading={loading}
            disabled={!user}
            style={{ marginBottom: spacing.md, backgroundColor: theme.colors.secondary }}
          >
            Create Test Protocol
          </Button>
          
          {result && (
            <View style={{
              backgroundColor: theme.colors.surfaceVariant,
              padding: spacing.md,
              borderRadius: spacing.sm,
            }}>
              <Text variant="bodySmall" style={{ fontFamily: 'monospace' }}>
                {result}
              </Text>
            </View>
          )}
        </Card.Content>
      </Card>
    </ScreenWrapper>
  );
}
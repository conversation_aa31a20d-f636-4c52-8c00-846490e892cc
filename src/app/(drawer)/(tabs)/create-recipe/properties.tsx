import React, { useState, useEffect, useMemo, useRef } from 'react';
import { View, StyleSheet } from 'react-native';
import { Appbar, Button, Text, Banner } from 'react-native-paper';
import { ScreenWrapper } from '@/shared/components/layout/screen-wrapper';
import { PropertiesSelection } from '@/features/create-recipe/components/screens';
import { useCombinedRecipeStore } from '@/features/create-recipe/store/combined-store';
import { useTheme } from '@/shared/hooks/use-theme';
import { usePropertiesScreen } from '@/features/create-recipe/hooks';
import { usePropertiesSelection } from '@/features/create-recipe/hooks/use-properties-selection';
import { haptics } from '@/shared/utils/haptics';
import { useTranslation } from 'react-i18next';

/**
 * Properties Selection Step Screen V4 (Consistent Pattern)
 * Fifth step in the recipe creation wizard using same V4 pattern as other screens
 * Uses ScreenWrapper with dynamicBottomSpace for expandable action area
 */
export default function PropertiesScreen() {
  const { theme } = useTheme();
  const { t } = useTranslation('create-recipe');
  const { therapeuticProperties } = useCombinedRecipeStore();
  
  // Get streaming state from properties hook for banner
  const propertiesHook = usePropertiesSelection();
  const { parallelStreamingState = { isStreaming: false } } = propertiesHook || {};
  
  // Banner state management - show once after page load, never reappear after dismissal
  const [bannerDismissed, setBannerDismissed] = useState(false);
  const [showDelayedBanner, setShowDelayedBanner] = useState(false);
  const bannerShownRef = useRef(false);

  const {
    isValid,
    isLoading,
    allPropertiesEnriched,
    hasProperties,
    buttonText,
    buttonIcon,
    title,
    subtitle,
    handleValidationChange,
    handleSubmitReady,
    handleLoadingChange,
    handleAction,
  } = usePropertiesScreen();

  // Simple banner logic: show once after 5s page load, never reappear after dismissal
  useEffect(() => {
    let timeout: NodeJS.Timeout;
    
    // Only show banner once per page load and if not dismissed
    if (parallelStreamingState.isStreaming && !bannerDismissed && !bannerShownRef.current) {
      timeout = setTimeout(() => {
        setShowDelayedBanner(true);
        bannerShownRef.current = true; // Mark as shown, prevents future appearances
      }, 5000);
    }
    
    // Hide banner when streaming stops
    if (!parallelStreamingState.isStreaming) {
      setShowDelayedBanner(false);
    }

    return () => {
      if (timeout) clearTimeout(timeout);
    };
  }, [parallelStreamingState.isStreaming, bannerDismissed]);

  // Banner visibility logic - show after delay and not dismissed
  const shouldShowBanner = useMemo(() => 
    parallelStreamingState.isStreaming && showDelayedBanner && !bannerDismissed,
    [parallelStreamingState.isStreaming, showDelayedBanner, bannerDismissed]
  );

  return (
    <ScreenWrapper
      showAppBar={true}
      appBarProps={{
        title: t('pages.therapeuticProperties'),
        showBackButton: true,
        titleStyle: { color: theme.colors.primary },
        actions: [
          <Appbar.Action key="more" icon="dots-vertical" onPress={() => {}} />
        ]
      }}
      showBottomNav={false}
      scrollable={true}
      dynamicBottomSpace={{
        enabled: true,
        height: 180,
        trigger: {
          scrollThreshold: 0.6,
          hideThreshold: 0.3,
          condition: () => hasProperties,
          enableProgressiveDisclosure: true,
        },
        content: (
          <View style={styles.actionContainer}>
            {/* Title */}
            <Text variant="titleSmall" style={[styles.title, { color: theme.colors.onSurface }]}>
              {title}
            </Text>
            
            {/* Subtitle */}
            <Text variant="bodySmall" style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}>
              {subtitle}
            </Text>
            
            {/* Navigation Button */}
            <Button
              mode="contained"
              onPress={handleAction}
              disabled={!isValid || isLoading}
              loading={isLoading}
              style={[styles.continueButton, { minHeight: 44 }]}
              contentStyle={[styles.buttonContent, { minHeight: 36 }]}
              icon={buttonIcon}
              accessibilityLabel={`${buttonText}. ${therapeuticProperties.length > 0 ? `${therapeuticProperties.length} properties selected` : 'Select properties first'}`}
              accessibilityHint="Double tap to proceed to the next step"
              accessibilityState={{
                disabled: !isValid || isLoading,
                busy: isLoading,
              }}
            >
              {buttonText}
            </Button>
          </View>
        ),
        animation: {
          duration: 400,
          easing: 'spring',
          useNativeDriver: false,
        },
      }}
    >
      {/* Oil Suggestion Banner - appears first after 5s delay during streaming */}
      <Banner
        visible={shouldShowBanner}
        actions={[
          {
            label: t('common:buttons.close'),
            onPress: () => {
              haptics.light();
              setBannerDismissed(true);
            },
            textColor: theme.colors.onPrimary,
          },
        ]}
        icon={{
          source: 'flask-outline',
          color: theme.colors.onPrimary,
        }}
        style={{
          backgroundColor: theme.colors.primary,
          elevation: 0,
        }}
        contentStyle={{
          color: theme.colors.onPrimary,
        }}
        onShowAnimationFinished={() =>
          console.log('Oil suggestion banner show animation completed')
        }
        onHideAnimationFinished={() =>
          console.log('Oil suggestion banner hide animation completed')
        }
      >
        <Text style={{ color: theme.colors.onPrimary }}>
          {t('propertiesSelection.bannerMessage')}
        </Text>
      </Banner>

      <PropertiesSelection 
        onValidationChange={handleValidationChange}
        onSubmitReady={handleSubmitReady}
        onLoadingChange={handleLoadingChange}
      />
    </ScreenWrapper>
  );
}

const styles = StyleSheet.create({
  actionContainer: {
    alignItems: 'center',
    gap: 4,
  },
  title: {
    marginBottom: 4,
  },
  subtitle: {
    marginBottom: 12,
    textAlign: 'center',
  },
  continueButton: {
    borderRadius: 8,
    elevation: 2,
    minWidth: '100%',
  },
  buttonContent: {
    paddingVertical: 8,
  },
});
import { Stack } from 'expo-router';
import { useTheme } from '@/shared/hooks/use-theme';
import { useTranslation } from 'react-i18next';

/**
 * Layout for the Create Recipe wizard navigation
 * Uses Expo Router Stack for step-by-step wizard flow
 * Integrates with React Native Paper Material Design 3 theming
 * Following: https://callstack.github.io/react-native-paper/docs/guides/theming-with-react-navigation/
 */
export default function CreateRecipeLayout() {
  const { t } = useTranslation('create-recipe');
  const { theme } = useTheme();

  return (
    <Stack
        screenOptions={({ route }) => ({
          headerShown: false, // Hide default header to use Paper Appbar
          // Material Design 3 transitions
          animation: 'slide_from_right',
          gestureEnabled: true,
          contentStyle: {
            backgroundColor: theme.colors.background,
          },
        })}
      >
      <Stack.Screen
        name="index"
        options={{
          title: 'Create Recipe',
        }}
      />
      <Stack.Screen
        name="health-concern"
        options={{
          title: t('pages.healthConcern'),
          presentation: 'card',
        }}
      />
      <Stack.Screen
        name="demographics"
        options={{
          title: t('pages.demographics'),
          presentation: 'card',
        }}
      />
      <Stack.Screen
        name="causes"
        options={{
          title: t('pages.potentialCauses'),
          presentation: 'card',
        }}
      />
      <Stack.Screen
        name="symptoms"
        options={{
          title: t('pages.symptoms'),
          presentation: 'card',
        }}
      />
      <Stack.Screen
        name="properties"
        options={{
          title: t('pages.therapeuticProperties'),
          presentation: 'card',
        }}
      />
      <Stack.Screen
        name="final-recipes"
        options={{
          title: t('pages.yourRecipes'),
          presentation: 'card',
        }}
      />

      </Stack>
  );
}
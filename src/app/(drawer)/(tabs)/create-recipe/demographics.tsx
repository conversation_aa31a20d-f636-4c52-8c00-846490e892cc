import React, { useEffect } from 'react';
import { Appbar } from 'react-native-paper';
import { ScreenWrapper } from '@/shared/components/layout/screen-wrapper';
import { DemographicsForm } from '@/features/create-recipe/components/screens/demographics';
import { RecipeActionControls } from '@/features/create-recipe/components/navigation/recipe-action-controls';
import { useCombinedRecipeStore } from '@/features/create-recipe/store/combined-store';
import { useStepScreenCoordination } from '@/features/create-recipe/hooks';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/shared/hooks/use-theme';

/**
 * Demographics Step Screen V4
 * Second step in the recipe creation wizard with discovery-focused UX
 * Uses ScreenWrapper with dynamicBottomSpace for content push behavior
 */
export default function DemographicsScreen() {
  const { t } = useTranslation('create-recipe');
  const theme = useTheme();
  const { demographics } = useCombinedRecipeStore();
  
  // Use step coordination hook for all business logic
  const {
    validationState,
    isLoading,
    isFormValid,
    handleValidationChange,
    handleSubmitReady,
    handleLoadingChange,
    handleContinue,
  } = useStepScreenCoordination();
  
  // Debug: Log demographics changes
  useEffect(() => {
    console.log('Demographics store updated:', demographics);
  }, [demographics]);

  return (
    <ScreenWrapper
      showAppBar={true}
      appBarProps={{
        title: t('pages.demographics'),
        showBackButton: true,
        titleStyle: { color: theme.colors.primary },
        actions: [
          <Appbar.Action key="more" icon="dots-vertical" onPress={() => {}} />
        ]
      }}
      showBottomNav={false}
      scrollable={false}
      dynamicBottomSpace={{
        enabled: true,
        height: 180,
        trigger: {
          scrollThreshold: 0, // Show immediately when valid
          hideThreshold: -1, // Never hide once shown
          condition: () => isFormValid, // Only show when form is valid
          enableProgressiveDisclosure: false, // Show immediately without animation
        },
        content: (
          <RecipeActionControls
            key={`demographics-${validationState.isValid}-${JSON.stringify(validationState.formData)}`}
            step="demographics"
            isValid={isFormValid}
            isLoading={isLoading}
            currentSelections={isFormValid ? 1 : 0}
            recommendedSelections={1}
            onContinue={handleContinue}
          />
        ),
        animation: {
          duration: 200,
          easing: 'ease',
          useNativeDriver: true,
        },
      }}
    >
      <DemographicsForm 
        onValidationChange={handleValidationChange}
        onSubmitReady={handleSubmitReady}
        onLoadingChange={handleLoadingChange}
      />
    </ScreenWrapper>
  );
}
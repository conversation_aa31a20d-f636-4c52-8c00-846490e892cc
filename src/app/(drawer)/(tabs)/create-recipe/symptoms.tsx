import React from 'react';
import { Appbar } from 'react-native-paper';
import { ScreenWrapper } from '@/shared/components/layout/screen-wrapper';
import { SymptomsSelection } from '@/features/create-recipe/components/screens/symptoms-selection';
import { RecipeActionControls } from '@/features/create-recipe/components/navigation/recipe-action-controls';
import { useCombinedRecipeStore } from '@/features/create-recipe/store/combined-store';
import { useStepScreenCoordination } from '@/features/create-recipe/hooks';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/shared/hooks';

/**
 * Symptoms Selection Step Screen V4
 * Fourth step in the recipe creation wizard with discovery-focused UX
 * Uses ScreenWrapper with dynamicBottomSpace for content push behavior
 */
export default function SymptomsScreen() {
  const { t } = useTranslation('create-recipe');
  const theme = useTheme();
  const { selectedSymptoms } = useCombinedRecipeStore();
  
  const {
    isLoading,
    isFormValid,
    handleValidationChange,
    handleSubmitReady,
    handleLoadingChange,
    handleContinue,
  } = useStepScreenCoordination();

  return (
    <ScreenWrapper
      showAppBar={true}
      appBarProps={{
        title: t('symptoms.title'),
        showBackButton: true,
        titleStyle: { color: theme.colors.primary },
        actions: [
          <Appbar.Action key="more" icon="dots-vertical" onPress={() => {}} />
        ]
      }}
      showBottomNav={false}
      scrollable={true}
      dynamicBottomSpace={{
        enabled: true,
        height: 180,
        trigger: {
          scrollThreshold: 0.6,
          hideThreshold: 0.3,
          condition: () => isFormValid,
          enableProgressiveDisclosure: true,
        },
        content: (
          <RecipeActionControls
            step="symptoms"
            isValid={isFormValid}
            isLoading={isLoading}
            currentSelections={selectedSymptoms.length}
            recommendedSelections={1}
            onContinue={handleContinue}
          />
        ),
        animation: {
          duration: 400,
          easing: 'spring',
          useNativeDriver: false,
        },
      }}
    >
      <SymptomsSelection 
        onValidationChange={handleValidationChange}
        onSubmitReady={handleSubmitReady}
        onLoadingChange={handleLoadingChange}
      />
    </ScreenWrapper>
  );
}
import React from 'react';
import { View } from 'react-native';
import {
  Text,
  Button,
  Appbar,
  Chip,
  ProgressBar,
  List,
  Surface,
  TouchableRipple
} from 'react-native-paper';
import { useTheme } from '@/shared/hooks/use-theme';
import { ScreenWrapper } from '@/shared/components/layout/screen-wrapper';
import { useRecipeIndexScreen } from '@/features/create-recipe/hooks';
import { useCombinedRecipeStore } from '@/features/create-recipe/store/combined-store';
import { WIZARD_STEPS } from '@/features/create-recipe/constants/recipe.constants';
import { useTranslation } from 'react-i18next';

/**
 * Create Recipe Index Screen V4
 * Entry point for the recipe creation wizard with V4 expandable actions
 * Uses always-expanded dynamicBottomSpace for wizard navigation
 */
export default function CreateRecipeIndex() {
  const { t } = useTranslation('create-recipe');
  const theme = useTheme();
  
  // Get store data for step display
  const { completedSteps, currentStep } = useCombinedRecipeStore();
  
  // Use feature hook for all business logic
  const {
    progress,
    progressSummary,
    hasProgress,
    handleStartWizard,
    handleContinueWizard,
    handleResetWizard,
  } = useRecipeIndexScreen();

  // If no progress, return null while redirecting (prevents flash)
  if (!hasProgress) {
    return null;
  }

  return (
    <ScreenWrapper
      showAppBar={true}
      appBarProps={{
        title: t('wizard.title'),
        showBackButton: true,
        titleStyle: { color: theme.colors.primary },
        actions: [
          <Appbar.Action key="more" icon="dots-vertical" onPress={() => {}} />
        ]
      }}
      showBottomNav={false}
      scrollable={true}
      dynamicBottomSpace={{
        enabled: true,
        height: 180,
        trigger: {
          scrollThreshold: 0.0, // Always show (0 means no scroll needed)
          hideThreshold: -1, // Never hide (-1 disables hiding)
          condition: () => true, // Always show
          enableProgressiveDisclosure: false, // Always expanded
        },
        content: (
          <View style={{
            alignItems: 'center',
            gap: theme.spacing.xs,
          }}>
            {/* Title */}
            <Text variant="titleSmall" style={{
              color: theme.colors.onSurface,
              marginBottom: theme.spacing.xs,
            }}>
              {hasProgress ? t('wizard.continueYourJourney') : t('wizard.startYourJourney')}
            </Text>
            
            {/* Subtitle */}
            <Text variant="bodySmall" style={{
              color: theme.colors.onSurfaceVariant,
              marginBottom: theme.spacing.md,
              textAlign: 'center',
            }}>
              {hasProgress 
                ? `${Math.round(progress * 100)}% complete - ${t('wizard.continueBuildingRecipe')}`
                : t('wizard.description')
              }
            </Text>
            
            {/* Navigation Buttons */}
            {hasProgress ? (
              <View style={{
                flexDirection: 'row',
                gap: theme.spacing.md,
                width: '100%',
              }}>
                <Button
                  mode="outlined"
                  onPress={handleResetWizard}
                  style={{
                    flex: 1,
                    borderRadius: theme.borderRadius.md,
                  }}
                  contentStyle={{
                    paddingVertical: theme.spacing.sm,
                  }}
                  icon="refresh"
                >
                  {t('wizard.startOver')}
                </Button>
                <Button
                  mode="contained"
                  onPress={handleContinueWizard}
                  style={{
                    flex: 1,
                    borderRadius: theme.borderRadius.md,
                  }}
                  contentStyle={{
                    paddingVertical: theme.spacing.sm,
                  }}
                  icon="play"
                  accessibilityLabel={t('wizard.continueRecipeWizard')}
                  accessibilityHint="Double tap to proceed with recipe creation"
                >
                  {t('wizard.continueWizard')}
                </Button>
              </View>
            ) : (
              <Button
                mode="contained"
                onPress={handleStartWizard}
                style={{
                  borderRadius: theme.borderRadius.md,
                  minHeight: 44,
                  minWidth: '100%',
                }}
                contentStyle={{
                  minHeight: 36,
                  paddingVertical: theme.spacing.sm,
                }}
                icon="magic-staff"
                accessibilityLabel={t('wizard.startRecipeWizard')}
                accessibilityHint="Double tap to proceed with recipe creation"
              >
                {t('wizard.startRecipeWizard')}
              </Button>
            )}
          </View>
        ),
        animation: {
          duration: 400,
          easing: 'spring',
          useNativeDriver: false,
        },
      }}>
      {/* Welcome Section */}
      <Surface 
        mode="flat"
        elevation={1}
        style={{
          backgroundColor: theme.colors.primaryContainer,
          borderRadius: theme.borderRadius.lg,
          marginBottom: theme.spacing.lg,
        }}
      >
        <View style={{ padding: theme.spacing.cardPadding }}>
          <Text 
            variant="headlineMedium" 
            style={{ 
              color: theme.colors.onPrimaryContainer,
              marginBottom: theme.spacing.sm,
            }}
          >
            {t('wizard.wizardTitle')}
          </Text>
          <Text 
            variant="bodyLarge" 
            style={{ 
              color: theme.colors.onPrimaryContainer,
              lineHeight: theme.typography.bodyLarge.lineHeight,
            }}
          >
            {t('wizard.wizardDescription')}
          </Text>
        </View>
      </Surface>

      {/* Progress Section */}
      {hasProgress && (
        <List.Section>
          <List.Subheader style={{ color: theme.colors.onSurface }}>
            {t('wizard.yourProgress')}
          </List.Subheader>
          <Surface
            mode="flat"
            elevation={1}
            style={{
              backgroundColor: theme.colors.surface,
              borderRadius: theme.borderRadius.lg,
              marginBottom: theme.spacing.lg,
            }}
          >
            <View style={{ padding: theme.spacing.cardPadding }}>
              <View style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: theme.spacing.lg,
              }}>
                <Text 
                  variant="titleLarge" 
                  style={{ color: theme.colors.onSurface }}
                >
                  {t('wizard.progressOverview')}
                </Text>
                <Chip 
                  mode="outlined"
                  style={{ backgroundColor: theme.colors.secondaryContainer }}
                  textStyle={{ color: theme.colors.onSecondaryContainer }}
                >
                  {Math.round(progress * 100)}{t('wizard.percentComplete')}
                </Chip>
              </View>
              
              <ProgressBar 
                progress={progress} 
                color={theme.colors.primary}
                style={{
                  height: 8,
                  borderRadius: theme.borderRadius.xs,
                  marginBottom: theme.spacing.lg,
                }}
              />
              
              <View style={{ gap: theme.spacing.sm }}>
                {progressSummary.map((item, index) => (
                  <List.Item
                    key={index}
                    title={item.title}
                    description={item.value}
                    left={props => <List.Icon {...props} icon={item.icon} />}
                    titleStyle={{ color: theme.colors.onSurface }}
                    descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
                  />
                ))}
              </View>
            </View>
          </Surface>
        </List.Section>
      )}

      {/* Steps Overview */}
      <List.Section>
        <List.Subheader style={{ color: theme.colors.onSurface }}>
          {t('wizard.wizardSteps')}
        </List.Subheader>
        <Surface
          mode="flat"
          elevation={1}
          style={{
            backgroundColor: theme.colors.surface,
            borderRadius: theme.borderRadius.lg,
            marginBottom: theme.spacing.lg,
          }}
        >
          {WIZARD_STEPS.map((step, index) => {
            const isCompleted = completedSteps.includes(step.key);
            const isCurrent = currentStep === step.key;
            
            return (
              <TouchableRipple
                key={step.key}
                onPress={() => {}}
                style={{
                  backgroundColor: isCurrent 
                    ? theme.colors.primaryContainer 
                    : 'transparent',
                }}
              >
                <List.Item
                  title={step.title}
                  description={step.description}
                  left={props => (
                    <Surface 
                      style={{
                        width: 32,
                        height: 32,
                        borderRadius: 16,
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginRight: theme.spacing.sm,
                        backgroundColor: isCompleted 
                          ? theme.colors.primary 
                          : isCurrent 
                            ? theme.colors.primaryContainer
                            : theme.colors.surfaceVariant,
                      }}
                      elevation={isCompleted || isCurrent ? 2 : 0}
                    >
                      <Text 
                        variant="labelLarge"
                        style={{
                          color: isCompleted 
                            ? theme.colors.onPrimary
                            : isCurrent
                              ? theme.colors.onPrimaryContainer
                              : theme.colors.onSurfaceVariant,
                        }}
                      >
                        {index + 1}
                      </Text>
                    </Surface>
                  )}
                  right={props => 
                    isCompleted ? (
                      <List.Icon 
                        {...props} 
                        icon="check-circle" 
                        color={theme.colors.primary} 
                      />
                    ) : null
                  }
                  titleStyle={{ 
                    color: isCurrent 
                      ? theme.colors.primary 
                      : theme.colors.onSurface 
                  }}
                  descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
                  style={{
                    paddingVertical: theme.spacing.sm,
                  }}
                />
              </TouchableRipple>
            );
          })}
        </Surface>
      </List.Section>


      {/* Info Section */}
      <List.Section>
        <List.Subheader style={{ color: theme.colors.onSurface }}>
          {t('wizard.howItWorks')}
        </List.Subheader>
        <Surface
          mode="flat"
          elevation={1}
          style={{
            backgroundColor: theme.colors.surface,
            borderRadius: theme.borderRadius.lg,
            marginBottom: theme.spacing.lg,
          }}
        >
          <View style={{ padding: theme.spacing.cardPadding }}>
            <Text 
              variant="bodyMedium" 
              style={{ 
                color: theme.colors.onSurfaceVariant,
                lineHeight: theme.typography.bodyMedium.lineHeight,
              }}
            >
              {t('wizard.step1')}{'\n'}
              {t('wizard.step2')}{'\n'}
              {t('wizard.step3')}{'\n'}
              {t('wizard.step4')}{'\n'}
              {t('wizard.step5')}
            </Text>
          </View>
        </Surface>
      </List.Section>
    </ScreenWrapper>
  );
}
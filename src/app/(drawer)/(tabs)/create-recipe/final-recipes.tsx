import { useState, useRef } from 'react';
import { View } from 'react-native';
import { Text, Button, Appbar } from 'react-native-paper';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { useTheme } from '@/shared/hooks/use-theme';
import { useCombinedRecipeStore } from '@/features/create-recipe/store/combined-store';
import { ScreenWrapper } from '@/shared/components/layout/screen-wrapper';
import ParallelStreamingBottomSheet from '@/shared/components/modals/streaming/parallel-streaming-bottom-sheet';
import { OverviewTab, SafetyTab, useFinalRecipes } from '@/features/create-recipe/components/screens/final-recipes';
import { FinalRecipeDetailsModal } from '@/features/create-recipe/components/modals/final-recipe-details-modal';
import type { RecipeTimeSlot } from '@/features/create-recipe/types';
import { useTranslation } from 'react-i18next';
import { StandardizedSegmentedButtons, SegmentedButtonPresets } from '@/shared/components/ui';

/**
 * Final Recipes Screen - ENHANCED VERSION
 * Sixth and final step in the recipe creation wizard
 * Displays personalized essential oil recipes for morning, mid-day, and night
 * 
 * NEW FEATURES:
 * - Post-enrichment scoring with final_relevance_score calculation
 * - Safety library generation with deduplication
 * - 3 parallel API calls (morning/mid-day/night) for time-specific optimization
 * - Enhanced data quality with scored oils and safety information
 * 
 * Architecture:
 * - Single Responsibility: Screen orchestration only
 * - Open/Closed: Easy to extend with new tabs
 * - Dependency Inversion: Depends on enhanced hook abstractions
 * - Preserves existing enrichment system (runs AFTER isEnriched: true)
 */
export default function FinalRecipesScreen() {
  const { t } = useTranslation('create-recipe');
  const { theme } = useTheme();

  // Get store data for passing to tabs (DRY - single source)
  const { healthConcern, demographics, selectedCauses, selectedSymptoms } = useCombinedRecipeStore();

  // Use custom hook for business logic (SRP - Single Responsibility)
  const {
    finalRecipes,
    generateFinalRecipes,
    showStreamingModal,
    setShowStreamingModal,
    streamingItems,
    streamingCompleted,
    hasAnyRecipe,
    isLoading,
    hasError
  } = useFinalRecipes();

  // Tab navigation state (UI concern only) - Recipes tab removed, modal system instead
  const [activeTab, setActiveTab] = useState<'overview' | 'safety'>('overview');
  
  // Modal state for recipe viewing
  const [modalVisible, setModalVisible] = useState(false);
  const [modalInitialTimeSlot, setModalInitialTimeSlot] = useState<RecipeTimeSlot>('morning');
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);

  // Render loading state
  if (isLoading) {
    return (
      <ScreenWrapper
        showAppBar={true}
        appBarProps={{
          title: t('pages.finalRecipes'),
          showBackButton: true,
          titleStyle: { color: theme.colors.primary },
          actions: [
            <Appbar.Action key="more" icon="dots-vertical" onPress={() => {}} />
          ]
        }}
        showBottomNav={false}
        scrollable={false}
      >
        <ParallelStreamingBottomSheet
          visible={showStreamingModal}
          onDismiss={() => setShowStreamingModal(false)}
          title={t('finalRecipes.generatingTitle')}
          description={t('finalRecipes.generatingSubtitle')}
          items={streamingItems}
          isComplete={streamingCompleted}
        />
      </ScreenWrapper>
    );
  }

  // Render error state
  if (hasError) {
    return (
      <ScreenWrapper
        showAppBar={true}
        appBarProps={{
          title: t('pages.finalRecipes'),
          showBackButton: true,
          titleStyle: { color: theme.colors.primary },
          actions: [
            <Appbar.Action key="more" icon="dots-vertical" onPress={() => {}} />
          ]
        }}
        showBottomNav={false}
        scrollable={false}
      >
        <View style={{ padding: theme.spacing.lg, alignItems: 'center' }}>
          <Text variant="headlineSmall" style={{ color: theme.colors.error, textAlign: 'center', marginBottom: theme.spacing.md }}>
            {t('finalRecipes.generationFailed')}
          </Text>
          <Text variant="bodyMedium" style={{ textAlign: 'center', marginBottom: theme.spacing.lg, color: theme.colors.onSurfaceVariant }}>
            {finalRecipes.globalError}
          </Text>
          <Button
            mode="contained"
            onPress={generateFinalRecipes}
            style={{ marginTop: theme.spacing.md }}
          >
            {t('finalRecipes.tryAgain')}
          </Button>
        </View>
      </ScreenWrapper>
    );
  }

  // Render empty state
  if (!hasAnyRecipe) {
    return (
      <ScreenWrapper
        showAppBar={true}
        appBarProps={{
          title: t('pages.finalRecipes'),
          showBackButton: true,
          titleStyle: { color: theme.colors.primary },
          actions: [
            <Appbar.Action key="more" icon="dots-vertical" onPress={() => {}} />
          ]
        }}
        showBottomNav={false}
        scrollable={false}
      >
        <View style={{ padding: theme.spacing.lg, alignItems: 'center' }}>
          <Text variant="headlineSmall" style={{ textAlign: 'center', marginBottom: theme.spacing.md }}>
            {t('finalRecipes.noRecipesGenerated')}
          </Text>
          <Text variant="bodyMedium" style={{ textAlign: 'center', marginBottom: theme.spacing.lg, color: theme.colors.onSurfaceVariant }}>
            {t('finalRecipes.completePreviousSteps')}
          </Text>
          <Button
            mode="contained"
            onPress={generateFinalRecipes}
            style={{ marginTop: theme.spacing.md }}
          >
            {t('finalRecipes.generateRecipes')}
          </Button>
        </View>
      </ScreenWrapper>
    );
  }

  // Main render with tab navigation
  return (
    <ScreenWrapper
      showAppBar={true}
      appBarProps={{
        title: t('finalRecipes.yourPersonalizedRecipes'),
        showBackButton: true,
        titleStyle: { color: theme.colors.primary },
        actions: [
          <Appbar.Action key="more" icon="dots-vertical" onPress={() => {}} />
        ]
      }}
      showBottomNav={false}
      scrollable={true}
    >
      {/* Tab Navigation (KISS - Simple segmented buttons) */}
      <View style={{ padding: theme.spacing.md }}>
        <StandardizedSegmentedButtons
          value={activeTab}
          onValueChange={(value) => setActiveTab(value as 'overview' | 'safety')}
          buttons={SegmentedButtonPresets.viewToggle((key: string) => t(`finalRecipes.${key}`))}
        />
      </View>

      {/* Tab Content (OCP - Open for extension, closed for modification) */}
      {activeTab === 'overview' && (
        <OverviewTab
          healthConcern={healthConcern}
          demographics={demographics}
          selectedCauses={selectedCauses}
          selectedSymptoms={selectedSymptoms}
          finalRecipes={finalRecipes}
          onSwitchToRecipes={(protocol) => {
            // Debug: Check if recipe data is available before opening modal
            const targetTimeSlot = protocol || 'morning';
            const targetRecipeKey = targetTimeSlot === 'mid-day' ? 'midDay' : targetTimeSlot;
            const targetRecipe = finalRecipes[targetRecipeKey]?.recipe;
            
            if (__DEV__) {
              console.log('🔍 Opening modal for:', targetTimeSlot);
              console.log('🔍 Recipe data available:', {
                hasRecipe: !!targetRecipe,
                hasSelectedOils: !!targetRecipe?.selected_oils,
                selectedOilsCount: targetRecipe?.selected_oils?.length || 0
              });
            }
            
            // Open modal regardless - let modal components handle empty states
            setModalInitialTimeSlot(protocol || 'morning');
            setModalVisible(true);
            bottomSheetModalRef.current?.present();
          }}
        />
      )}

      {activeTab === 'safety' && (
        <SafetyTab finalRecipes={finalRecipes} />
      )}

      {/* Final Recipe Details Modal */}
      <FinalRecipeDetailsModal
        visible={modalVisible}
        onDismiss={() => {
          setModalVisible(false);
          bottomSheetModalRef.current?.dismiss();
        }}
        initialTimeSlot={modalInitialTimeSlot}
        finalRecipes={(() => {
          const transformedRecipes = {
            morning: finalRecipes.morning?.recipe || null,
            'mid-day': finalRecipes.midDay?.recipe || null,
            night: finalRecipes.night?.recipe || null
          };
          
          if (__DEV__) {
            console.log('🔍 Transformed recipes for modal:', {
              morning: {
                hasRecipe: !!transformedRecipes.morning,
                hasSelectedOils: !!transformedRecipes.morning?.selected_oils,
                selectedOilsCount: transformedRecipes.morning?.selected_oils?.length || 0
              },
              'mid-day': {
                hasRecipe: !!transformedRecipes['mid-day'],
                hasSelectedOils: !!transformedRecipes['mid-day']?.selected_oils,
                selectedOilsCount: transformedRecipes['mid-day']?.selected_oils?.length || 0
              },
              night: {
                hasRecipe: !!transformedRecipes.night,
                hasSelectedOils: !!transformedRecipes.night?.selected_oils,
                selectedOilsCount: transformedRecipes.night?.selected_oils?.length || 0
              }
            });
          }
          
          return transformedRecipes;
        })()}
        bottomSheetModalRef={bottomSheetModalRef}
      />
    </ScreenWrapper>
  );
}
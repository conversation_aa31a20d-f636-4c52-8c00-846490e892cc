import React, { useState, useCallback } from 'react';
import { View, StyleSheet, Platform, Dimensions } from 'react-native';
import {
  Appbar,
  Card,
  Text,
  Button,
  List,
  Chip,
  Divider,
  Switch,
  TouchableRipple,
} from 'react-native-paper';
import { Stack, useRouter } from 'expo-router';
import { usePaperTheme, useThemeToggle } from '@/shared/hooks/use-theme';
import { useUser } from '@clerk/clerk-expo';
import { ScreenWrapper } from '@/shared/components/layout/screen-wrapper';
import type { AppBarProps } from '@/shared/components/layout/app-bar';
import type { BottomNavProps } from '@/shared/components/layout/bottom-navigation';
import { useIsFocused } from '@react-navigation/native';
import { useUserPreferences } from '@/shared/contexts/user-preferences-context';

/**
 * Debug Screen with Pull-to-Refresh functionality
 * Tests React Native pull-to-refresh feature with counter reset
 */
export default function DebugScreen() {
  const theme = usePaperTheme();
  const { toggleTheme, isDarkTheme } = useThemeToggle();
  const { user } = useUser();
  const router = useRouter();

  // Debug state
  const [counter, setCounter] = useState(0);
  const [refreshing, setRefreshing] = useState(false);
  const [bottomNavIndex, setBottomNavIndex] = useState(0);
  const [debugInfo, setDebugInfo] = useState({
    platform: Platform.OS,
    version: Platform.Version,
    screenWidth: Dimensions.get('window').width,
    screenHeight: Dimensions.get('window').height,
    timestamp: new Date().toLocaleString(),
  });

  // Pull-to-refresh handler
  const onRefresh = useCallback(() => {
    console.log('🔄 Pull-to-refresh triggered - resetting counter to 0');
    setRefreshing(true);
    
    // Simulate refresh delay
    setTimeout(() => {
      setCounter(0); // Reset counter to 0
      setDebugInfo(prev => ({
        ...prev,
        timestamp: new Date().toLocaleString(),
      }));
      setRefreshing(false);
      console.log('✅ Refresh completed - counter reset');
    }, 1000);
  }, []);

  // Increment counter
  const incrementCounter = () => {
    setCounter(prev => {
      const newValue = prev + 1;
      console.log(`🔢 Counter incremented to: ${newValue}`);
      return newValue;
    });
  };

  // AppBar configuration - enhanced to match homescreen quality
  const appBarProps: AppBarProps = {
    title: "Debug Screen",
    showBackButton: true,
    onBackPress: () => router.back(),
    actions: [
      <Appbar.Action
        key="refresh"
        icon={refreshing ? "loading" : "refresh"}
        onPress={onRefresh}
        disabled={refreshing}
        accessibilityLabel="Refresh debug data"
      />,
      <Appbar.Action
        key="menu"
        icon="dots-vertical"
        onPress={() => {
          console.log('Debug menu pressed');
          // Could show debug options menu
        }}
        accessibilityLabel="Debug menu"
      />
    ],
    elevation: 4,
    backgroundColor: theme.colors.surface,
  };

  // Main debug content - now just the content, no ScrollView wrapper
  const renderDebugContent = () => (
    <View style={{ padding: 16 }}>
      {/* Pull-to-Refresh Test Section */}
      <Card style={styles.debugCard}>
        <Card.Content>
          <Text variant="headlineSmall" style={{ marginBottom: 16 }}>
            🔄 Pull-to-Refresh Test
          </Text>
          
          <Text variant="bodyLarge" style={{ marginBottom: 16, color: theme.colors.onSurfaceVariant }}>
            Pull down to refresh and reset counter to 0
          </Text>

          <View style={styles.counterContainer}>
            <Text variant="displayMedium" style={{ color: theme.colors.primary }}>
              {counter}
            </Text>
            <Text variant="titleMedium" style={{ color: theme.colors.onSurfaceVariant }}>
              Current Count
            </Text>
          </View>

          <View style={styles.buttonRow}>
            <Button 
              mode="contained" 
              onPress={incrementCounter}
              style={{ flex: 1, marginRight: 8 }}
            >
              Increment (+1)
            </Button>
            <Button 
              mode="outlined" 
              onPress={() => setCounter(0)}
              style={{ flex: 1, marginLeft: 8 }}
            >
              Reset to 0
            </Button>
          </View>

          <Text variant="bodySmall" style={{ marginTop: 16, color: theme.colors.onSurfaceVariant }}>
            💡 Tip: Pull down from the top of the screen to trigger refresh
          </Text>
        </Card.Content>
      </Card>

      {/* Device Info */}
      <Card style={styles.debugCard}>
        <Card.Content>
          <Text variant="headlineSmall" style={{ marginBottom: 16 }}>
            📱 Device Information
          </Text>
          
          <List.Item
            title="Platform"
            description={debugInfo.platform}
            left={props => <List.Icon {...props} icon="cellphone" />}
          />
          <List.Item
            title="Version"
            description={String(debugInfo.version)}
            left={props => <List.Icon {...props} icon="information" />}
          />
          <List.Item
            title="Screen Size"
            description={`${debugInfo.screenWidth} x ${debugInfo.screenHeight}`}
            left={props => <List.Icon {...props} icon="monitor" />}
          />
          <List.Item
            title="Last Updated"
            description={debugInfo.timestamp}
            left={props => <List.Icon {...props} icon="clock" />}
          />
        </Card.Content>
      </Card>

      {/* Theme Testing */}
      <Card style={styles.debugCard}>
        <Card.Content>
          <Text variant="headlineSmall" style={{ marginBottom: 16 }}>
            🎨 Theme Testing
          </Text>

          <TouchableRipple onPress={toggleTheme}>
            <View style={styles.preference}>
              <View>
                <Text variant="titleMedium">Dark Theme</Text>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                  Toggle between light and dark themes
                </Text>
              </View>
              <Switch value={isDarkTheme} />
            </View>
          </TouchableRipple>

          <Divider style={{ marginVertical: 16 }} />

          <Text variant="titleMedium" style={{ marginBottom: 8 }}>Theme Colors</Text>
          <View style={styles.colorGrid}>
            <Chip style={{ backgroundColor: theme.colors.primary, margin: 4 }}>Primary</Chip>
            <Chip style={{ backgroundColor: theme.colors.secondary, margin: 4 }}>Secondary</Chip>
            <Chip style={{ backgroundColor: theme.colors.tertiary, margin: 4 }}>Tertiary</Chip>
            <Chip style={{ backgroundColor: theme.colors.surface, margin: 4 }}>Surface</Chip>
          </View>
        </Card.Content>
      </Card>

      {/* User Info (if authenticated) */}
      {user && (
        <Card style={styles.debugCard}>
          <Card.Content>
            <Text variant="headlineSmall" style={{ marginBottom: 16 }}>
              👤 User Information
            </Text>
            
            <List.Item
              title="Name"
              description={`${user.firstName} ${user.lastName}`}
              left={props => <List.Icon {...props} icon="account" />}
            />
            <List.Item
              title="Email"
              description={user.primaryEmailAddress?.emailAddress || 'N/A'}
              left={props => <List.Icon {...props} icon="email" />}
            />
            <List.Item
              title="User ID"
              description={user.id}
              left={props => <List.Icon {...props} icon="key" />}
            />
          </Card.Content>
        </Card>
      )}

      {/* Debug Actions */}
      <Card style={styles.debugCard}>
        <Card.Content>
          <Text variant="headlineSmall" style={{ marginBottom: 16 }}>
            🔧 Debug Actions
          </Text>

          <View style={styles.debugActions}>
            <Button 
              mode="outlined" 
              onPress={() => console.log('🐛 Debug log triggered')}
              style={{ marginBottom: 8 }}
              icon="bug"
            >
              Log Debug Message
            </Button>
            
            <Button 
              mode="outlined" 
              onPress={() => {
                console.log('📊 Current debug state:', { 
                  counter, 
                  refreshing, 
                  isDarkTheme,
                  timestamp: new Date().toISOString()
                });
              }}
              style={{ marginBottom: 8 }}
              icon="file-document"
            >
              Log Current State
            </Button>

          </View>
        </Card.Content>
      </Card>

      {/* Instructions */}
      <Card style={styles.infoCard}>
        <Card.Content>
          <Text variant="titleMedium">How to Test Pull-to-Refresh:</Text>
          <Text variant="bodyMedium" style={{ marginTop: 8, color: theme.colors.onSurfaceVariant }}>
            1. Use the &quot;Increment (+1)&quot; button to increase the counter{'\n'}
            2. Pull down from the top of this screen{'\n'}
            3. Watch the counter reset to 0 when refresh completes{'\n'}
            4. Check the console logs for debug messages{'\n\n'}
            The refresh control uses React Native&apos;s built-in RefreshControl component.
          </Text>
        </Card.Content>
      </Card>
    </View>
  );

  // BottomNav configuration - parent has full control
  const bottomNavProps: BottomNavProps = {
    routes: [
      { key: 'debug', title: 'Debug', focusedIcon: 'bug', unfocusedIcon: 'bug-outline' },
      { key: 'info', title: 'Info', focusedIcon: 'information', unfocusedIcon: 'information-outline' },
      { key: 'settings', title: 'Settings', focusedIcon: 'cog', unfocusedIcon: 'cog-outline' },
    ],
    activeIndex: bottomNavIndex,
    onTabPress: (index, route) => {
      setBottomNavIndex(index);
      console.log(`Debug screen tab pressed: ${route.key}`);
    },
  };

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <ScreenWrapper
        scrollable={true}
        showAppBar={true}
        appBarProps={appBarProps}
        showBottomNav={true}
        bottomNavProps={bottomNavProps}
        enableRefresh={true}
        refreshing={refreshing}
        onRefresh={onRefresh}
      >
        {renderDebugContent()}
      </ScreenWrapper>
    </>
  );
}

const styles = StyleSheet.create({
  debugCard: {
    marginBottom: 16,
    elevation: 2,
  },
  infoCard: {
    marginTop: 8,
    marginBottom: 16,
    elevation: 1,
  },
  counterContainer: {
    alignItems: 'center',
    padding: 24,
    marginVertical: 16,
  },
  buttonRow: {
    flexDirection: 'row',
    marginTop: 16,
  },
  preference: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  debugActions: {
    gap: 8,
  },
});
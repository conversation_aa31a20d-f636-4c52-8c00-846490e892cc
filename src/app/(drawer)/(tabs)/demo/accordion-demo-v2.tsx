/**
 * @fileoverview Accordion Demo Screen V2 - Without Avatars
 * Showcase of varied List.Item layouts without left avatars for more compact design
 * Demonstrates 2-row layouts with dividers and diverse information organization
 */

import React, { useState } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { 
  Text, 
  List,
  Chip,
  Badge,
  Checkbox,
  IconButton,
  Divider,
  Switch,
} from 'react-native-paper';
import { Stack } from 'expo-router';
import { usePaperTheme } from '@/shared/hooks/use-theme';
import { AppBar, BottomNav } from '@/shared/components/layout';
import { haptics } from '@/shared/utils/haptics';

export default function AccordionDemoV2() {
  const theme = usePaperTheme();
  const [bottomNavIndex, setBottomNavIndex] = useState(0);
  
  // State for various interactions
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [toggleStates, setToggleStates] = useState<Set<string>>(new Set());

  const bottomRoutes = [
    { key: 'products', title: 'Products', focusedIcon: 'shopping', unfocusedIcon: 'shopping-outline' },
    { key: 'travel', title: 'Travel', focusedIcon: 'airplane', unfocusedIcon: 'airplane-takeoff' },
    { key: 'finance', title: 'Finance', focusedIcon: 'trending-up', unfocusedIcon: 'chart-line' },
    { key: 'essentials', title: 'Essential Oils', focusedIcon: 'leaf', unfocusedIcon: 'leaf-outline' },
  ];

  const toggleFavorite = async (id: string) => {
    await haptics.light();
    setFavorites(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(id)) {
        newFavorites.delete(id);
      } else {
        newFavorites.add(id);
      }
      return newFavorites;
    });
  };

  const toggleSelection = async (id: string) => {
    await haptics.select();
    setSelectedItems(prev => {
      const newSelected = new Set(prev);
      if (newSelected.has(id)) {
        newSelected.delete(id);
      } else {
        newSelected.add(id);
      }
      return newSelected;
    });
  };

  const toggleState = async (id: string) => {
    await haptics.toggle();
    setToggleStates(prev => {
      const newStates = new Set(prev);
      if (newStates.has(id)) {
        newStates.delete(id);
      } else {
        newStates.add(id);
      }
      return newStates;
    });
  };

  const renderProductsSection = () => (
    <View style={styles.sectionContainer}>
      <List.Section>
        <List.Subheader>E-Commerce - Pattern 1: Price Focus Layout (No Avatars)</List.Subheader>
        
        <List.Accordion
          title="Electronics Store"
          left={props => <List.Icon {...props} icon="shopping" />}
        >
          {/* Pattern 1: Simple price-focused layout without avatar */}
          <List.Item
            right={() => (
              <View style={styles.priceRightContainer}>
                <Text style={[styles.bigPrice, { color: theme.colors.primary }]}>$299</Text>
                <IconButton
                  icon={favorites.has('headphones') ? 'heart' : 'heart-outline'}
                  iconColor={favorites.has('headphones') ? theme.colors.error : theme.colors.onSurfaceVariant}
                  onPress={() => toggleFavorite('headphones')}
                  size={24}
                />
              </View>
            )}
            title={() => (
              <View style={styles.simpleTitle}>
                <Text style={[styles.productName, { color: theme.colors.onSurface }]}>
                  Sony WH-1000XM4 Headphones
                </Text>
                <View style={styles.singleRowMeta}>
                  <Badge style={[styles.discountBadge, { backgroundColor: theme.colors.error }]} size={18}>
                    -20%
                  </Badge>
                  <Text style={[styles.strikePrice, { color: theme.colors.onSurfaceVariant }]}>$379</Text>
                </View>
              </View>
            )}
            description={() => (
              <View style={styles.simpleDescription}>
                <Text style={{ color: theme.colors.onSurfaceVariant, marginBottom: 6 }}>
                  Noise canceling • 30h battery • Premium sound
                </Text>
                <View style={styles.horizontalChips}>
                  <Chip 
                    icon="star" 
                    compact 
                    style={styles.smallChip}
                    textStyle={styles.chipText}
                  >
                    4.8
                  </Chip>
                  <Chip 
                    compact 
                    style={styles.smallChip}
                    textStyle={styles.chipText}
                  >
                    2,847 reviews
                  </Chip>
                  <Chip 
                    icon="truck-delivery" 
                    compact 
                    style={styles.smallChip}
                    textStyle={styles.chipText}
                  >
                    Free Ship
                  </Chip>
                </View>
              </View>
            )}
          />
          <Divider />

          {/* Pattern 2: Two-row with divider layout without avatar */}
          <List.Item
            right={() => (
              <View style={styles.actionRightContainer}>
                <Checkbox 
                  status={selectedItems.has('smartphone') ? 'checked' : 'unchecked'} 
                  onPress={() => toggleSelection('smartphone')}
                />
                <IconButton
                  icon={favorites.has('smartphone') ? 'heart' : 'heart-outline'}
                  iconColor={favorites.has('smartphone') ? theme.colors.error : theme.colors.onSurfaceVariant}
                  onPress={() => toggleFavorite('smartphone')}
                  size={20}
                />
              </View>
            )}
            title={() => (
              <View style={styles.twoRowTitle}>
                <View style={styles.titleRowOne}>
                  <Text style={[styles.productName, { color: theme.colors.onSurface }]}>
                    iPhone 15 Pro Max
                  </Text>
                  <View style={styles.priceGroup}>
                    <Text style={[styles.bigPrice, { color: theme.colors.primary }]}>$999</Text>
                    <Badge style={[styles.newBadge, { backgroundColor: theme.colors.secondary }]} size={16}>NEW</Badge>
                  </View>
                </View>
                <View style={[styles.dividerLine, { backgroundColor: theme.colors.outline }]} />
                <View style={styles.titleRowTwo}>
                  <Text style={[styles.specText, { color: theme.colors.onSurfaceVariant }]}>
                    256GB • Titanium Blue • A17 Pro Chip
                  </Text>
                  <View style={styles.colorDots}>
                    <View style={[styles.colorDot, { backgroundColor: '#1f2937' }]} />
                    <View style={[styles.colorDot, { backgroundColor: '#6366f1' }]} />
                    <View style={[styles.colorDot, { backgroundColor: '#f59e0b' }]} />
                  </View>
                </View>
              </View>
            )}
            description={() => (
              <View style={styles.compactDescription}>
                <View style={styles.horizontalChips}>
                  <Chip 
                    icon="star" 
                    compact 
                    style={styles.ratingChip}
                    textStyle={styles.chipText}
                  >
                    4.6
                  </Chip>
                  <Chip 
                    compact 
                    style={styles.categoryChip}
                    textStyle={styles.chipText}
                  >
                    Mobile
                  </Chip>
                  <Chip 
                    icon="camera" 
                    compact 
                    style={styles.featureChip}
                    textStyle={styles.chipText}
                  >
                    48MP
                  </Chip>
                </View>
              </View>
            )}
          />
          <Divider />

          {/* Pattern 8: Divider layout without avatar */}
          <List.Item
            right={() => null}
            title={() => (
              <View style={styles.watchTitle}>
                <View style={styles.watchTitleRow}>
                  <Text style={[styles.watchName, { color: theme.colors.onSurface }]}>
                    Smart Watch Pro
                  </Text>
                  <Text style={[styles.bigPrice, { color: theme.colors.primary }]}>$449</Text>
                </View>
                <Text style={[styles.watchSpecs, { color: theme.colors.onSurfaceVariant }]}>
                  GPS • Heart Rate • 7-day battery • Waterproof
                </Text>
              </View>
            )}
            description={() => (
              <View style={styles.watchDescription}>
                <View style={[styles.dividerLine, { backgroundColor: theme.colors.outline }]} />
                <View style={styles.watchFeatureRow}>
                  <View style={styles.featureGroup}>
                    <Text style={[styles.featureLabel, { color: theme.colors.onSurfaceVariant }]}>Fitness</Text>
                    <Text style={[styles.featureValue, { color: theme.colors.onSurface }]}>24/7 Tracking</Text>
                  </View>
                  <View style={styles.featureGroup}>
                    <Text style={[styles.featureLabel, { color: theme.colors.onSurfaceVariant }]}>Display</Text>
                    <Text style={[styles.featureValue, { color: theme.colors.onSurface }]}>Always-On OLED</Text>
                  </View>
                  <View style={styles.featureGroup}>
                    <Text style={[styles.featureLabel, { color: theme.colors.onSurfaceVariant }]}>Storage</Text>
                    <Text style={[styles.featureValue, { color: theme.colors.onSurface }]}>32GB Music</Text>
                  </View>
                </View>
                <View style={styles.watchActionRow}>
                  <View style={styles.watchChips}>
                    <Chip 
                      icon="star" 
                      compact 
                      style={styles.ratingChip}
                      textStyle={styles.chipText}
                    >
                      4.9
                    </Chip>
                    <Chip 
                      compact 
                      style={styles.categoryChip}
                      textStyle={styles.chipText}
                    >
                      Wearable
                    </Chip>
                  </View>
                  <View style={styles.watchButtons}>
                    <IconButton
                      icon={favorites.has('watch') ? 'heart' : 'heart-outline'}
                      iconColor={favorites.has('watch') ? theme.colors.error : theme.colors.onSurfaceVariant}
                      onPress={() => toggleFavorite('watch')}
                      size={20}
                    />
                    <Checkbox 
                      status={selectedItems.has('watch') ? 'checked' : 'unchecked'} 
                      onPress={() => toggleSelection('watch')}
                    />
                  </View>
                </View>
              </View>
            )}
          />
        </List.Accordion>
      </List.Section>
    </View>
  );

  const renderTravelSection = () => (
    <View style={styles.sectionContainer}>
      <List.Section>
        <List.Subheader>Travel - Pattern 2: Flight Times Layout (No Avatars)</List.Subheader>
        
        <List.Accordion
          title="Flight Bookings"
          left={props => <List.Icon {...props} icon="airplane" />}
        >
          {/* Pattern 3: Flight times layout without avatar */}
          <List.Item
            right={() => (
              <View style={styles.flightRightContainer}>
                <Text style={[styles.bigPrice, { color: theme.colors.primary }]}>$387</Text>
                <IconButton
                  icon={favorites.has('flight-aa') ? 'heart' : 'heart-outline'}
                  iconColor={favorites.has('flight-aa') ? theme.colors.error : theme.colors.onSurfaceVariant}
                  onPress={() => toggleFavorite('flight-aa')}
                  size={24}
                />
              </View>
            )}
            title={() => (
              <View style={styles.flightTitle}>
                <View style={styles.flightHeaderRow}>
                  <Text style={[styles.routeText, { color: theme.colors.onSurface }]}>NYC → LAX</Text>
                  <Text style={{ color: theme.colors.onSurfaceVariant, fontSize: 12 }}>American Airlines</Text>
                </View>
                <View style={[styles.dividerLine, { backgroundColor: theme.colors.outline }]} />
                <View style={styles.flightTimesRow}>
                  <View style={styles.timeBlock}>
                    <Text style={[styles.timeText, { color: theme.colors.onSurface }]}>8:45a</Text>
                    <Text style={[styles.airportText, { color: theme.colors.onSurfaceVariant }]}>JFK</Text>
                  </View>
                  <View style={styles.flightPath}>
                    <Text style={[styles.durationText, { color: theme.colors.onSurfaceVariant }]}>5h 50m</Text>
                    <View style={[styles.flightLine, { backgroundColor: theme.colors.outline }]} />
                    <Text style={[styles.directText, { color: theme.colors.primary }]}>Direct</Text>
                  </View>
                  <View style={styles.timeBlock}>
                    <Text style={[styles.timeText, { color: theme.colors.onSurface }]}>2:35p</Text>
                    <Text style={[styles.airportText, { color: theme.colors.onSurfaceVariant }]}>LAX</Text>
                  </View>
                </View>
              </View>
            )}
            description={() => (
              <View style={styles.flightDescription}>
                <View style={styles.horizontalChips}>
                  <Chip 
                    icon="seat-recline-normal" 
                    compact 
                    style={styles.seatChip}
                    textStyle={styles.chipText}
                  >
                    Economy
                  </Chip>
                  <Chip 
                    icon="briefcase" 
                    compact 
                    style={styles.baggageChip}
                    textStyle={styles.chipText}
                  >
                    1 Bag
                  </Chip>
                  <Switch
                    value={toggleStates.has('flight-aa')}
                    onValueChange={() => toggleState('flight-aa')}
                    style={styles.inlineSwitch}
                  />
                </View>
              </View>
            )}
          />
          <Divider />

          {/* Nested Accordion: Compare Airlines without avatars */}
          <List.Accordion
            title="Compare Airlines"
            left={props => <List.Icon {...props} icon="compare" />}
            style={{ marginLeft: 20 }}
          >
            <List.Item
              right={() => (
                <Text style={[styles.comparePrice, { color: theme.colors.error }]}>$425</Text>
              )}
              title={() => (
                <View>
                  <Text style={[styles.airlineName, { color: theme.colors.onSurface }]}>Delta Airlines</Text>
                  <Text style={[styles.routeDetails, { color: theme.colors.onSurfaceVariant }]}>
                    JFK → LAX • 6h 15m • 1 stop
                  </Text>
                </View>
              )}
              description={() => (
                <View style={styles.airlineFeatures}>
                  <Chip compact style={styles.categoryChip} textStyle={styles.chipText}>Premium</Chip>
                  <Chip compact style={styles.featureChip} textStyle={styles.chipText}>Meals</Chip>
                  <Switch
                    value={toggleStates.has('delta')}
                    onValueChange={() => toggleState('delta')}
                    style={styles.inlineSwitch}
                  />
                </View>
              )}
            />
            <Divider style={{ marginLeft: 40 }} />

            <List.Item
              right={() => (
                <Text style={[styles.comparePrice, { color: theme.colors.secondary }]}>$395</Text>
              )}
              title={() => (
                <View>
                  <Text style={[styles.airlineName, { color: theme.colors.onSurface }]}>United Airlines</Text>
                  <Text style={[styles.routeDetails, { color: theme.colors.onSurfaceVariant }]}>
                    JFK → LAX • 5h 45m • Direct
                  </Text>
                </View>
              )}
              description={() => (
                <View style={styles.airlineFeatures}>
                  <Chip compact style={styles.categoryChip} textStyle={styles.chipText}>Standard</Chip>
                  <Chip compact style={styles.featureChip} textStyle={styles.chipText}>WiFi</Chip>
                  <Switch
                    value={toggleStates.has('united')}
                    onValueChange={() => toggleState('united')}
                    style={styles.inlineSwitch}
                  />
                </View>
              )}
            />
            <Divider style={{ marginLeft: 40 }} />

            {/* Double-Nested Accordion: Seat Upgrade Options without avatars */}
            <List.Accordion
              title="Seat Upgrade Options"
              left={props => <List.Icon {...props} icon="seat-recline-extra" />}
              style={{ marginLeft: 40 }}
            >
              <List.Item
                right={() => (
                  <Text style={[styles.upgradePrice, { color: theme.colors.primary }]}>+$89</Text>
                )}
                title="Premium Economy"
                description={() => (
                  <View style={styles.seatUpgradeFeatures}>
                    <Text style={[styles.seatDescription, { color: theme.colors.onSurfaceVariant }]}>
                      Extra legroom • Priority boarding • Enhanced meal
                    </Text>
                    <View style={styles.seatSelectionRow}>
                      <Chip compact style={styles.seatChip} textStyle={styles.chipText}>38" pitch</Chip>
                      <Checkbox
                        status={selectedItems.has('premium-economy') ? 'checked' : 'unchecked'}
                        onPress={() => toggleSelection('premium-economy')}
                      />
                    </View>
                  </View>
                )}
              />

              <List.Item
                right={() => (
                  <Text style={[styles.upgradePrice, { color: theme.colors.tertiary }]}>+$249</Text>
                )}
                title="Business Class"
                description={() => (
                  <View style={styles.seatUpgradeFeatures}>
                    <Text style={[styles.seatDescription, { color: theme.colors.onSurfaceVariant }]}>
                      Lie-flat seats • Lounge access • Gourmet dining
                    </Text>
                    <View style={styles.seatSelectionRow}>
                      <Chip compact style={styles.seatChip} textStyle={styles.chipText}>78" pitch</Chip>
                      <Checkbox
                        status={selectedItems.has('business-class') ? 'checked' : 'unchecked'}
                        onPress={() => toggleSelection('business-class')}
                      />
                    </View>
                  </View>
                )}
              />
            </List.Accordion>
          </List.Accordion>
          <Divider />

          {/* Pattern 4: Hotel booking with expanded info without avatar */}
          <List.Item
            right={() => (
              <View style={styles.hotelRightContainer}>
                <View style={styles.pricePerNight}>
                  <Text style={[styles.bigPrice, { color: theme.colors.primary }]}>$245</Text>
                  <Text style={{ fontSize: 12, color: theme.colors.onSurfaceVariant }}>/night</Text>
                </View>
                <IconButton
                  icon={favorites.has('hotel') ? 'heart' : 'heart-outline'}
                  iconColor={favorites.has('hotel') ? theme.colors.error : theme.colors.onSurfaceVariant}
                  onPress={() => toggleFavorite('hotel')}
                  size={20}
                />
              </View>
            )}
            title={() => (
              <View style={styles.hotelTitle}>
                <Text style={[styles.hotelName, { color: theme.colors.onSurface }]}>
                  Grand Plaza Hotel & Spa
                </Text>
                <Text style={[styles.hotelLocation, { color: theme.colors.onSurfaceVariant }]}>
                  Downtown • 0.3 miles from center
                </Text>
              </View>
            )}
            description={() => (
              <View style={styles.hotelDescription}>
                <View style={styles.hotelAmenityRow}>
                  <View style={styles.ratingSection}>
                    <Chip 
                      icon="star" 
                      compact 
                      style={styles.ratingChip}
                      textStyle={styles.chipText}
                    >
                      4.7
                    </Chip>
                    <Text style={[styles.reviewText, { color: theme.colors.onSurfaceVariant }]}>(1,245)</Text>
                  </View>
                  <Badge style={[styles.dealBadge, { backgroundColor: theme.colors.tertiary }]} size={16}>
                    Best Deal
                  </Badge>
                </View>
                <View style={styles.amenityTags}>
                  <Text style={[styles.amenityText, { color: theme.colors.onSurfaceVariant }]}>
                    Pool • Spa • Gym • Free WiFi • Free Breakfast
                  </Text>
                </View>
              </View>
            )}
          />
          <Divider />

          {/* Pattern 7: Two icons layout without avatar */}
          <List.Item
            right={() => null}
            title={() => (
              <View style={styles.carRentalTitle}>
                <View style={styles.carTitleRow}>
                  <Text style={[styles.carRentalName, { color: theme.colors.onSurface }]}>
                    Premium Car Rental
                  </Text>
                  <View style={styles.priceHeartGroup}>
                    <Text style={[styles.bigPrice, { color: theme.colors.primary }]}>$67</Text>
                    <IconButton
                      icon={favorites.has('rental') ? 'heart' : 'heart-outline'}
                      iconColor={favorites.has('rental') ? theme.colors.error : theme.colors.onSurfaceVariant}
                      onPress={() => toggleFavorite('rental')}
                      size={20}
                      style={styles.compactHeart}
                    />
                  </View>
                </View>
                <Text style={[styles.carDetails, { color: theme.colors.onSurfaceVariant }]}>
                  Luxury Sedan • 4 seats • Automatic
                </Text>
              </View>
            )}
            description={() => (
              <View style={styles.carRentalDescription}>
                <View style={[styles.dividerLine, { backgroundColor: theme.colors.outline }]} />
                <View style={styles.bottomIconRow}>
                  <View style={styles.leftIconGroup}>
                    <IconButton
                      icon="gas-station"
                      size={20}
                      iconColor={theme.colors.onSurfaceVariant}
                      style={styles.bottomIcon}
                    />
                    <Text style={[styles.iconLabel, { color: theme.colors.onSurfaceVariant }]}>Full Tank</Text>
                  </View>
                  <View style={styles.centerSpacing} />
                  <View style={styles.rightIconGroup}>
                    <IconButton
                      icon="shield-check"
                      size={20}
                      iconColor={theme.colors.secondary}
                      style={styles.bottomIcon}
                    />
                    <Text style={[styles.iconLabel, { color: theme.colors.onSurfaceVariant }]}>Insured</Text>
                  </View>
                </View>
              </View>
            )}
          />
        </List.Accordion>
      </List.Section>
    </View>
  );

  const renderFinanceSection = () => (
    <View style={styles.sectionContainer}>
      <List.Section>
        <List.Subheader>Finance - Pattern 3: Data-Heavy Layout (No Avatars)</List.Subheader>
        
        <List.Accordion
          title="Stock Portfolio"
          left={props => <List.Icon {...props} icon="trending-up" />}
        >
          {/* Pattern 5: Complex financial data layout without avatar */}
          <List.Item
            right={() => (
              <View style={styles.stockRightContainer}>
                <View style={styles.stockActions}>
                  <View style={styles.stockPriceColumn}>
                    <Text style={[styles.stockPrice, { color: theme.colors.primary }]}>$847.58</Text>
                    <Text style={[styles.priceChange, { color: theme.colors.error }]}>+$23.45 (2.85%)</Text>
                  </View>
                  <IconButton
                    icon={selectedItems.has('nvda') ? 'star' : 'star-outline'}
                    iconColor={selectedItems.has('nvda') ? theme.colors.secondary : theme.colors.onSurfaceVariant}
                    onPress={() => toggleSelection('nvda')}
                    size={20}
                  />
                </View>
              </View>
            )}
            title={() => (
              <View style={styles.stockTitle}>
                <View style={styles.stockHeaderRow}>
                  <Text style={[styles.stockSymbol, { color: theme.colors.onSurface }]}>NVDA</Text>
                  <Text style={[styles.companyName, { color: theme.colors.onSurfaceVariant }]}>NVIDIA Corp</Text>
                </View>
                <View style={[styles.dividerLine, { backgroundColor: theme.colors.outline }]} />
                <View style={styles.stockDataRow}>
                  <View style={styles.dataColumn}>
                    <Text style={[styles.dataLabel, { color: theme.colors.onSurfaceVariant }]}>Market Cap</Text>
                    <Text style={[styles.dataValue, { color: theme.colors.onSurface }]}>$2.1T</Text>
                  </View>
                  <View style={styles.dataColumn}>
                    <Text style={[styles.dataLabel, { color: theme.colors.onSurfaceVariant }]}>P/E Ratio</Text>
                    <Text style={[styles.dataValue, { color: theme.colors.onSurface }]}>65.4</Text>
                  </View>
                  <View style={styles.dataColumn}>
                    <Text style={[styles.dataLabel, { color: theme.colors.onSurfaceVariant }]}>Volume</Text>
                    <Text style={[styles.dataValue, { color: theme.colors.onSurface }]}>45.2M</Text>
                  </View>
                </View>
              </View>
            )}
            description={() => (
              <View style={styles.stockDescription}>
                <View style={styles.performanceRow}>
                  <Chip 
                    icon="trending-up" 
                    compact 
                    style={[styles.trendChip, { backgroundColor: theme.colors.errorContainer }]}
                    textStyle={[styles.chipText, { color: theme.colors.onErrorContainer }]}
                  >
                    Today +2.85%
                  </Chip>
                  <Chip 
                    compact 
                    style={[styles.performanceChip, { backgroundColor: theme.colors.primaryContainer }]}
                    textStyle={[styles.chipText, { color: theme.colors.onPrimaryContainer }]}
                  >
                    YTD +187%
                  </Chip>
                  <Badge style={[styles.volumeBadge, { backgroundColor: theme.colors.secondary }]} size={16}>
                    High Vol
                  </Badge>
                </View>
              </View>
            )}
          />
          <Divider />

          {/* Pattern 6: Minimal financial layout without avatar */}
          <List.Item
            right={() => (
              <View style={styles.minimalRightContainer}>
                <View style={styles.quickStats}>
                  <Text style={[styles.stockPrice, { color: theme.colors.primary }]}>$248.42</Text>
                  <Text style={[styles.changePercent, { color: theme.colors.secondary }]}>-2.05%</Text>
                </View>
                <Checkbox 
                  status={selectedItems.has('tsla') ? 'checked' : 'unchecked'} 
                  onPress={() => toggleSelection('tsla')}
                />
              </View>
            )}
            title={() => (
              <View style={styles.minimalTitle}>
                <View style={styles.stockRow}>
                  <Text style={[styles.stockSymbol, { color: theme.colors.onSurface }]}>TSLA</Text>
                  <Text style={{ color: theme.colors.onSurfaceVariant, marginLeft: 8, fontSize: 12 }}>Tesla Inc</Text>
                </View>
                <Text style={[styles.marketCapText, { color: theme.colors.onSurfaceVariant }]}>
                  Market Cap: $791B • P/E: 85.2
                </Text>
              </View>
            )}
            description={() => (
              <View style={styles.minimalDescription}>
                <View style={styles.trendRow}>
                  <Chip 
                    icon="trending-down" 
                    compact 
                    style={styles.downTrendChip}
                    textStyle={styles.chipText}
                  >
                    -2.05%
                  </Chip>
                  <Chip 
                    compact 
                    style={styles.ytdChip}
                    textStyle={styles.chipText}
                  >
                    YTD +42.1%
                  </Chip>
                  <Text style={[styles.alertText, { color: theme.colors.error }]}>• Price Alert</Text>
                </View>
              </View>
            )}
          />
        </List.Accordion>
      </List.Section>
    </View>
  );

  const renderEssentialOilsSection = () => (
    <View style={styles.sectionContainer}>
      <List.Section>
        <List.Subheader>Essential Oils - Improved Information Display</List.Subheader>
        
        {/* Accordion 1: Therapeutic Properties */}
        <List.Accordion
          title="Therapeutic Properties"
          left={props => <List.Icon {...props} icon="medical-bag" />}
        >
          {/* Pattern: Property with compact relevancy and cause/symptom display */}
          <List.Item
            right={() => (
              <View style={styles.propertyRightContainer}>
                <Text style={[styles.relevancyScore, { color: theme.colors.primary }]}>4.8/5</Text>
                <Checkbox 
                  status={selectedItems.has('relaxing') ? 'checked' : 'unchecked'} 
                  onPress={() => toggleSelection('relaxing')}
                />
              </View>
            )}
            title={() => (
              <View style={styles.propertyTitle}>
                <View style={styles.propertyHeaderRow}>
                  <Text style={[styles.propertyName, { color: theme.colors.onSurface }]}>
                    Relaxing & Calming
                  </Text>
                  <Badge style={[styles.oilCountBadge, { backgroundColor: theme.colors.secondary }]} size={16}>
                    12 oils
                  </Badge>
                </View>
                <Text style={[styles.propertyDescription, { color: theme.colors.onSurfaceVariant }]}>
                  Promotes relaxation, reduces anxiety and stress
                </Text>
              </View>
            )}
            description={() => (
              <View style={styles.propertyChipsContainer}>
                <View style={[styles.dividerLine, { backgroundColor: theme.colors.outline }]} />
                <View style={styles.categoryChipsRow}>
                  <View style={styles.causesGroup}>
                    <Text style={[styles.chipGroupLabel, { color: theme.colors.onSurfaceVariant }]}>Addresses:</Text>
                    <View style={styles.horizontalChips}>
                      <Chip compact style={styles.causeChip} textStyle={styles.chipText}>Anxiety</Chip>
                      <Chip compact style={styles.causeChip} textStyle={styles.chipText}>Stress</Chip>
                      <Chip compact style={styles.symptomChip} textStyle={styles.chipText}>Insomnia</Chip>
                    </View>
                  </View>
                  <IconButton
                    icon={favorites.has('relaxing') ? 'heart' : 'heart-outline'}
                    iconColor={favorites.has('relaxing') ? theme.colors.error : theme.colors.onSurfaceVariant}
                    onPress={() => toggleFavorite('relaxing')}
                    size={20}
                  />
                </View>
              </View>
            )}
          />
          <Divider />

          {/* Pattern: Antimicrobial Property */}
          <List.Item
            right={() => (
              <View style={styles.propertyRightContainer}>
                <Text style={[styles.relevancyScore, { color: theme.colors.primary }]}>4.6/5</Text>
                <Checkbox 
                  status={selectedItems.has('antimicrobial') ? 'checked' : 'unchecked'} 
                  onPress={() => toggleSelection('antimicrobial')}
                />
              </View>
            )}
            title={() => (
              <View style={styles.propertyTitle}>
                <View style={styles.propertyHeaderRow}>
                  <Text style={[styles.propertyName, { color: theme.colors.onSurface }]}>
                    Antimicrobial & Antiseptic
                  </Text>
                  <Badge style={[styles.oilCountBadge, { backgroundColor: theme.colors.secondary }]} size={16}>
                    8 oils
                  </Badge>
                </View>
                <Text style={[styles.propertyDescription, { color: theme.colors.onSurfaceVariant }]}>
                  Fights bacteria, viruses, and fungi
                </Text>
              </View>
            )}
            description={() => (
              <View style={styles.propertyChipsContainer}>
                <View style={[styles.dividerLine, { backgroundColor: theme.colors.outline }]} />
                <View style={styles.categoryChipsRow}>
                  <View style={styles.causesGroup}>
                    <Text style={[styles.chipGroupLabel, { color: theme.colors.onSurfaceVariant }]}>Addresses:</Text>
                    <View style={styles.horizontalChips}>
                      <Chip compact style={styles.causeChip} textStyle={styles.chipText}>Infections</Chip>
                      <Chip compact style={styles.symptomChip} textStyle={styles.chipText}>Acne</Chip>
                      <Chip compact style={styles.symptomChip} textStyle={styles.chipText}>Cuts</Chip>
                    </View>
                  </View>
                  <IconButton
                    icon={favorites.has('antimicrobial') ? 'heart' : 'heart-outline'}
                    iconColor={favorites.has('antimicrobial') ? theme.colors.error : theme.colors.onSurfaceVariant}
                    onPress={() => toggleFavorite('antimicrobial')}
                    size={20}
                  />
                </View>
              </View>
            )}
          />
        </List.Accordion>
        <Divider />

        {/* Accordion 2: Essential Oil Safety */}
        <List.Accordion
          title="Essential Oil Safety"
          left={props => <List.Icon {...props} icon="shield-check" />}
        >
          {/* Pattern: Lavender Oil Safety Info */}
          <List.Item
            right={() => (
              <View style={styles.safetyStatusContainer}>
                <Text style={[styles.safetyStatus, { color: theme.colors.secondary }]}>SAFE</Text>
              </View>
            )}
            title={() => (
              <View style={styles.oilSafetyTitle}>
                <View style={styles.oilNameRow}>
                  <Text style={[styles.oilName, { color: theme.colors.onSurface }]}>Lavender Oil</Text>
                  <Text style={[styles.botanicalName, { color: theme.colors.onSurfaceVariant }]}>Lavandula angustifolia</Text>
                </View>
                <View style={styles.safetyScoresRow}>
                  <Text style={[styles.relevancyText, { color: theme.colors.primary }]}>Relevancy: 4.9/5</Text>
                  <Text style={[styles.similarityText, { color: theme.colors.secondary }]}>Match: 96%</Text>
                </View>
              </View>
            )}
            description={() => (
              <View style={styles.safetyDetailsContainer}>
                <View style={[styles.dividerLine, { backgroundColor: theme.colors.outline }]} />
                <View style={styles.safetyInfoGrid}>
                  <View style={styles.safetyColumn}>
                    <Text style={[styles.safetyLabel, { color: theme.colors.onSurfaceVariant }]}>Pregnancy</Text>
                    <Chip compact style={styles.safetyGreenChip} textStyle={styles.chipText}>Safe</Chip>
                  </View>
                  <View style={styles.safetyColumn}>
                    <Text style={[styles.safetyLabel, { color: theme.colors.onSurfaceVariant }]}>Children</Text>
                    <Chip compact style={styles.safetyGreenChip} textStyle={styles.chipText}>2+ years</Chip>
                  </View>
                  <View style={styles.safetyColumn}>
                    <Text style={[styles.safetyLabel, { color: theme.colors.onSurfaceVariant }]}>Dilution</Text>
                    <Chip compact style={styles.dilutionChip} textStyle={styles.chipText}>1-3%</Chip>
                  </View>
                </View>
                <View style={styles.usageRow}>
                  <Chip compact style={styles.usageChip} textStyle={styles.chipText}>Topical</Chip>
                  <Chip compact style={styles.usageChip} textStyle={styles.chipText}>Aromatherapy</Chip>
                  <Switch
                    value={toggleStates.has('lavender-usage')}
                    onValueChange={() => toggleState('lavender-usage')}
                    style={styles.inlineSwitch}
                  />
                </View>
              </View>
            )}
          />
          <Divider />

          {/* Pattern: Tea Tree Oil Safety Info - More Cautious */}
          <List.Item
            right={() => (
              <View style={styles.safetyStatusContainer}>
                <Text style={[styles.safetyStatus, { color: theme.colors.tertiary }]}>CAUTION</Text>
              </View>
            )}
            title={() => (
              <View style={styles.oilSafetyTitle}>
                <View style={styles.oilNameRow}>
                  <Text style={[styles.oilName, { color: theme.colors.onSurface }]}>Tea Tree Oil</Text>
                  <Text style={[styles.botanicalName, { color: theme.colors.onSurfaceVariant }]}>Melaleuca alternifolia</Text>
                </View>
                <View style={styles.safetyScoresRow}>
                  <Text style={[styles.relevancyText, { color: theme.colors.primary }]}>Relevancy: 4.5/5</Text>
                  <Text style={[styles.similarityText, { color: theme.colors.secondary }]}>Match: 91%</Text>
                </View>
              </View>
            )}
            description={() => (
              <View style={styles.safetyDetailsContainer}>
                <View style={[styles.dividerLine, { backgroundColor: theme.colors.outline }]} />
                <View style={styles.safetyInfoGrid}>
                  <View style={styles.safetyColumn}>
                    <Text style={[styles.safetyLabel, { color: theme.colors.onSurfaceVariant }]}>Pregnancy</Text>
                    <Chip compact style={styles.safetyYellowChip} textStyle={styles.chipText}>Caution</Chip>
                  </View>
                  <View style={styles.safetyColumn}>
                    <Text style={[styles.safetyLabel, { color: theme.colors.onSurfaceVariant }]}>Children</Text>
                    <Chip compact style={styles.safetyYellowChip} textStyle={styles.chipText}>6+ years</Chip>
                  </View>
                  <View style={styles.safetyColumn}>
                    <Text style={[styles.safetyLabel, { color: theme.colors.onSurfaceVariant }]}>Dilution</Text>
                    <Chip compact style={styles.dilutionChip} textStyle={styles.chipText}>0.5-1%</Chip>
                  </View>
                </View>
                <View style={styles.usageRow}>
                  <Chip compact style={styles.usageChip} textStyle={styles.chipText}>Topical Only</Chip>
                  <Text style={[styles.warningText, { color: theme.colors.error }]}>• Never Internal</Text>
                  <Switch
                    value={toggleStates.has('teatree-usage')}
                    onValueChange={() => toggleState('teatree-usage')}
                    style={styles.inlineSwitch}
                  />
                </View>
              </View>
            )}
          />
        </List.Accordion>
        <Divider />

        {/* Accordion 3: Oil Database Matches */}
        <List.Accordion
          title="Oil Database Matches"
          left={props => <List.Icon {...props} icon="database" />}
        >
          {/* Pattern: Multiple Oil Suggestions with Ratings */}
          <List.Item
            right={() => (
              <View style={styles.matchScoreContainer}>
                <Text style={[styles.matchScore, { color: theme.colors.primary }]}>96%</Text>
                <IconButton
                  icon={favorites.has('lavender-match') ? 'star' : 'star-outline'}
                  iconColor={favorites.has('lavender-match') ? theme.colors.secondary : theme.colors.onSurfaceVariant}
                  onPress={() => toggleFavorite('lavender-match')}
                  size={20}
                />
              </View>
            )}
            title={() => (
              <View style={styles.oilMatchTitle}>
                <View style={styles.matchHeaderRow}>
                  <Text style={[styles.oilMatchName, { color: theme.colors.onSurface }]}>Lavender (Bulgarian)</Text>
                  <View style={styles.matchBadges}>
                    <Badge style={[styles.premiumBadge, { backgroundColor: theme.colors.tertiary }]} size={14}>
                      Premium
                    </Badge>
                    <Badge style={[styles.availabilityBadge, { backgroundColor: theme.colors.secondary }]} size={14}>
                      In Stock
                    </Badge>
                  </View>
                </View>
                <Text style={[styles.botanicalMatch, { color: theme.colors.onSurfaceVariant }]}>
                  Lavandula angustifolia • Organic • Steam Distilled
                </Text>
              </View>
            )}
            description={() => (
              <View style={styles.oilMatchDetails}>
                <View style={[styles.dividerLine, { backgroundColor: theme.colors.outline }]} />
                <View style={styles.oilAttributesRow}>
                  <View style={styles.attributeGroup}>
                    <Text style={[styles.attributeLabel, { color: theme.colors.onSurfaceVariant }]}>Price</Text>
                    <Text style={[styles.attributeValue, { color: theme.colors.primary }]}>$24.99</Text>
                  </View>
                  <View style={styles.attributeGroup}>
                    <Text style={[styles.attributeLabel, { color: theme.colors.onSurfaceVariant }]}>Size</Text>
                    <Text style={[styles.attributeValue, { color: theme.colors.onSurface }]}>10ml</Text>
                  </View>
                  <View style={styles.attributeGroup}>
                    <Text style={[styles.attributeLabel, { color: theme.colors.onSurfaceVariant }]}>Origin</Text>
                    <Text style={[styles.attributeValue, { color: theme.colors.onSurface }]}>Bulgaria</Text>
                  </View>
                </View>
                <View style={styles.oilTagsRow}>
                  <Chip compact style={styles.ratingChip} textStyle={styles.chipText}>⭐ 4.9</Chip>
                  <Chip compact style={styles.categoryChip} textStyle={styles.chipText}>Therapeutic</Chip>
                  <Chip compact style={styles.featureChip} textStyle={styles.chipText}>GC/MS Tested</Chip>
                </View>
              </View>
            )}
          />
          <Divider />

          {/* Pattern: Alternative Oil Match */}
          <List.Item
            right={() => (
              <View style={styles.matchScoreContainer}>
                <Text style={[styles.matchScore, { color: theme.colors.secondary }]}>89%</Text>
                <IconButton
                  icon={favorites.has('eucalyptus-match') ? 'star' : 'star-outline'}
                  iconColor={favorites.has('eucalyptus-match') ? theme.colors.secondary : theme.colors.onSurfaceVariant}
                  onPress={() => toggleFavorite('eucalyptus-match')}
                  size={20}
                />
              </View>
            )}
            title={() => (
              <View style={styles.oilMatchTitle}>
                <View style={styles.matchHeaderRow}>
                  <Text style={[styles.oilMatchName, { color: theme.colors.onSurface }]}>Eucalyptus Globulus</Text>
                  <View style={styles.matchBadges}>
                    <Badge style={[styles.standardBadge, { backgroundColor: theme.colors.primaryContainer }]} size={14}>
                      Standard
                    </Badge>
                    <Badge style={[styles.availabilityBadge, { backgroundColor: theme.colors.errorContainer }]} size={14}>
                      Low Stock
                    </Badge>
                  </View>
                </View>
                <Text style={[styles.botanicalMatch, { color: theme.colors.onSurfaceVariant }]}>
                  Eucalyptus globulus • Conventional • Steam Distilled
                </Text>
              </View>
            )}
            description={() => (
              <View style={styles.oilMatchDetails}>
                <View style={[styles.dividerLine, { backgroundColor: theme.colors.outline }]} />
                <View style={styles.oilAttributesRow}>
                  <View style={styles.attributeGroup}>
                    <Text style={[styles.attributeLabel, { color: theme.colors.onSurfaceVariant }]}>Price</Text>
                    <Text style={[styles.attributeValue, { color: theme.colors.primary }]}>$12.99</Text>
                  </View>
                  <View style={styles.attributeGroup}>
                    <Text style={[styles.attributeLabel, { color: theme.colors.onSurfaceVariant }]}>Size</Text>
                    <Text style={[styles.attributeValue, { color: theme.colors.onSurface }]}>15ml</Text>
                  </View>
                  <View style={styles.attributeGroup}>
                    <Text style={[styles.attributeLabel, { color: theme.colors.onSurfaceVariant }]}>Origin</Text>
                    <Text style={[styles.attributeValue, { color: theme.colors.onSurface }]}>Australia</Text>
                  </View>
                </View>
                <View style={styles.oilTagsRow}>
                  <Chip compact style={styles.ratingChip} textStyle={styles.chipText}>⭐ 4.3</Chip>
                  <Chip compact style={styles.categoryChip} textStyle={styles.chipText}>Respiratory</Chip>
                  <Chip compact style={styles.cautionChip} textStyle={styles.chipText}>Strong Scent</Chip>
                </View>
              </View>
            )}
          />
        </List.Accordion>
      </List.Section>
    </View>
  );

  const renderContent = () => {
    switch (bottomNavIndex) {
      case 0: return renderProductsSection();
      case 1: return renderTravelSection();
      case 2: return renderFinanceSection();
      case 3: return renderEssentialOilsSection();
      default: return renderProductsSection();
    }
  };

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <AppBar 
          title="Accordion Showcase V2" 
          showBackButton 
          actions={[
            <IconButton 
              key="info" 
              icon="information" 
              onPress={() => haptics.light()} 
            />
          ]}
        />
        
        <View style={styles.content}>
          <ScrollView 
            style={styles.scrollView}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
          >
            {renderContent()}
          </ScrollView>
        </View>
        
        <BottomNav
          routes={bottomRoutes}
          activeIndex={bottomNavIndex}
          onTabPress={(index) => {
            setBottomNavIndex(index);
            haptics.tab();
          }}
        />
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 16,
  },
  sectionContainer: {
    flex: 1,
  },

  // Divider line for 2-row layouts
  dividerLine: {
    height: 1,
    marginVertical: 6,
  },

  // Pattern 1: Simple price-focused layout
  priceRightContainer: {
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  simpleTitle: {
    flex: 1,
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  singleRowMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  bigPrice: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  strikePrice: {
    textDecorationLine: 'line-through',
    fontSize: 12,
  },
  simpleDescription: {
    marginTop: 4,
  },
  horizontalChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
  },
  smallChip: {
    height: 22,
    paddingHorizontal: 8,
    minHeight: 22,
  },
  
  // Custom chip text style for better visibility
  chipText: {
    fontSize: 10,
    lineHeight: 14,
    textAlign: 'center',
    textAlignVertical: 'center',
  },

  // Pattern 2: Two-row with divider
  actionRightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  twoRowTitle: {
    flex: 1,
  },
  titleRowOne: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  titleRowTwo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  priceGroup: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  specText: {
    fontSize: 12,
    flex: 1,
  },
  colorDots: {
    flexDirection: 'row',
    gap: 4,
  },
  colorDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  compactDescription: {
    marginTop: 6,
  },

  // Pattern 3: Flight layout (Image #2 style)
  flightRightContainer: {
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  flightTitle: {
    flex: 1,
  },
  flightHeaderRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  routeText: {
    fontSize: 16,
    fontWeight: '600',
  },
  flightTimesRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  timeBlock: {
    alignItems: 'center',
  },
  timeText: {
    fontSize: 16,
    fontWeight: '500',
  },
  airportText: {
    fontSize: 11,
  },
  flightPath: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 16,
  },
  durationText: {
    fontSize: 10,
    marginBottom: 2,
  },
  flightLine: {
    height: 1,
    width: '80%',
    marginBottom: 2,
  },
  directText: {
    fontSize: 10,
    fontWeight: '500',
  },
  flightDescription: {
    marginTop: 6,
  },
  inlineSwitch: {
    transform: [{ scale: 0.7 }],
  },

  // Pattern 4: Hotel layout
  hotelRightContainer: {
    alignItems: 'flex-end',
  },
  pricePerNight: {
    alignItems: 'flex-end',
    marginBottom: 4,
  },
  hotelTitle: {
    flex: 1,
  },
  hotelName: {
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 2,
  },
  hotelLocation: {
    fontSize: 12,
  },
  hotelDescription: {
    marginTop: 6,
  },
  hotelAmenityRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  ratingSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  reviewText: {
    fontSize: 12,
  },
  amenityTags: {
    marginTop: 4,
  },
  amenityText: {
    fontSize: 11,
  },

  // Pattern 7: Two icons layout (Image 3.png style)
  carRentalTitle: {
    flex: 1,
  },
  carTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  carRentalName: {
    fontSize: 15,
    fontWeight: '600',
    flex: 1,
  },
  priceHeartGroup: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  compactHeart: {
    margin: 0,
    marginLeft: 4,
  },
  carDetails: {
    fontSize: 12,
  },
  carRentalDescription: {
    marginTop: 8,
  },
  bottomIconRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  leftIconGroup: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  centerSpacing: {
    flex: 1,
  },
  rightIconGroup: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  bottomIcon: {
    margin: 0,
    marginRight: 4,
  },
  iconLabel: {
    fontSize: 11,
    fontWeight: '500',
  },

  // Pattern 8: Divider layout without right avatar
  watchTitle: {
    flex: 1,
  },
  watchTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  watchName: {
    fontSize: 15,
    fontWeight: '600',
    flex: 1,
  },
  watchSpecs: {
    fontSize: 12,
  },
  watchDescription: {
    marginTop: 8,
  },
  watchFeatureRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
    paddingTop: 8,
  },
  featureGroup: {
    flex: 1,
    alignItems: 'center',
  },
  featureLabel: {
    fontSize: 10,
    textAlign: 'center',
  },
  featureValue: {
    fontSize: 11,
    fontWeight: '500',
    textAlign: 'center',
    marginTop: 2,
  },
  watchActionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  watchChips: {
    flexDirection: 'row',
    gap: 6,
  },
  watchButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },

  // Pattern 5: Complex financial data
  stockRightContainer: {
    alignItems: 'flex-end',
  },
  stockActions: {
    alignItems: 'flex-end',
  },
  stockPriceColumn: {
    alignItems: 'flex-end',
    marginBottom: 4,
  },
  stockPrice: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  priceChange: {
    fontSize: 11,
  },
  stockTitle: {
    flex: 1,
  },
  stockHeaderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  stockSymbol: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  companyName: {
    fontSize: 12,
  },
  stockDataRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  dataColumn: {
    alignItems: 'center',
    flex: 1,
  },
  dataLabel: {
    fontSize: 10,
  },
  dataValue: {
    fontSize: 12,
    fontWeight: '500',
  },
  stockDescription: {
    marginTop: 6,
  },
  performanceRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
    alignItems: 'center',
  },

  // Pattern 6: Minimal financial
  minimalRightContainer: {
    alignItems: 'flex-end',
  },
  quickStats: {
    alignItems: 'flex-end',
    marginBottom: 4,
  },
  changePercent: {
    fontSize: 11,
  },
  minimalTitle: {
    flex: 1,
  },
  stockRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  marketCapText: {
    fontSize: 11,
    marginTop: 2,
  },
  minimalDescription: {
    marginTop: 6,
  },
  trendRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  alertText: {
    fontSize: 11,
    fontWeight: '500',
  },

  // Chip styles with better padding for readability
  ratingChip: {
    height: 24,
    paddingHorizontal: 10,
    minHeight: 24,
  },
  categoryChip: {
    height: 24,
    paddingHorizontal: 10,
    minHeight: 24,
  },
  featureChip: {
    height: 24,
    paddingHorizontal: 10,
    minHeight: 24,
  },
  seatChip: {
    height: 24,
    paddingHorizontal: 10,
    minHeight: 24,
  },
  baggageChip: {
    height: 24,
    paddingHorizontal: 10,
    minHeight: 24,
  },
  trendChip: {
    height: 24,
    paddingHorizontal: 10,
    minHeight: 24,
  },
  performanceChip: {
    height: 24,
    paddingHorizontal: 10,
    minHeight: 24,
  },
  downTrendChip: {
    height: 24,
    paddingHorizontal: 10,
    minHeight: 24,
  },
  ytdChip: {
    height: 24,
    paddingHorizontal: 10,
    minHeight: 24,
  },

  // Badge styles
  discountBadge: {
    marginRight: 4,
  },
  newBadge: {
    marginLeft: 4,
  },
  dealBadge: {
    marginLeft: 4,
  },
  volumeBadge: {
    marginLeft: 4,
  },

  // Nested Accordion: Airline comparison styles
  comparePrice: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  airlineName: {
    fontSize: 14,
    fontWeight: '600',
  },
  routeDetails: {
    fontSize: 12,
    marginTop: 2,
  },
  airlineFeatures: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginTop: 4,
  },

  // Seat upgrade styles
  upgradePrice: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  seatUpgradeFeatures: {
    marginTop: 2,
  },
  seatDescription: {
    fontSize: 12,
    marginBottom: 6,
  },
  seatSelectionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  // Essential Oils Section Styles
  
  // Therapeutic Properties styles
  propertyRightContainer: {
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  propertyTitle: {
    flex: 1,
  },
  propertyHeaderRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  propertyName: {
    fontSize: 15,
    fontWeight: '600',
    flex: 1,
  },
  propertyDescription: {
    fontSize: 12,
    lineHeight: 16,
  },
  oilCountBadge: {
    marginLeft: 8,
  },
  relevancyScore: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  propertyChipsContainer: {
    marginTop: 6,
  },
  categoryChipsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 8,
  },
  causesGroup: {
    flex: 1,
  },
  chipGroupLabel: {
    fontSize: 11,
    fontWeight: '500',
    marginBottom: 4,
  },
  causeChip: {
    backgroundColor: '#ffebee',
    height: 22,
    paddingHorizontal: 8,
    minHeight: 22,
  },
  symptomChip: {
    backgroundColor: '#fff3e0',
    height: 22,
    paddingHorizontal: 8,
    minHeight: 22,
  },

  // Essential Oil Safety styles
  safetyStatusContainer: {
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  safetyStatus: {
    fontSize: 12,
    fontWeight: 'bold',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  oilSafetyTitle: {
    flex: 1,
  },
  oilNameRow: {
    marginBottom: 4,
  },
  oilName: {
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 2,
  },
  botanicalName: {
    fontSize: 11,
    fontStyle: 'italic',
  },
  safetyScoresRow: {
    flexDirection: 'row',
    gap: 12,
  },
  relevancyText: {
    fontSize: 11,
    fontWeight: '500',
  },
  similarityText: {
    fontSize: 11,
    fontWeight: '500',
  },
  safetyDetailsContainer: {
    marginTop: 6,
  },
  safetyInfoGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 8,
    paddingBottom: 8,
  },
  safetyColumn: {
    flex: 1,
    alignItems: 'center',
  },
  safetyLabel: {
    fontSize: 10,
    textAlign: 'center',
    marginBottom: 4,
  },
  safetyGreenChip: {
    backgroundColor: '#e8f5e8',
    height: 22,
    paddingHorizontal: 8,
    minHeight: 22,
  },
  safetyYellowChip: {
    backgroundColor: '#fff8e1',
    height: 22,
    paddingHorizontal: 8,
    minHeight: 22,
  },
  dilutionChip: {
    backgroundColor: '#e3f2fd',
    height: 22,
    paddingHorizontal: 8,
    minHeight: 22,
  },
  usageRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginTop: 4,
  },
  usageChip: {
    backgroundColor: '#f3e5f5',
    height: 22,
    paddingHorizontal: 8,
    minHeight: 22,
  },
  warningText: {
    fontSize: 11,
    fontWeight: '500',
  },

  // Oil Database Matches styles
  matchScoreContainer: {
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  matchScore: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  oilMatchTitle: {
    flex: 1,
  },
  matchHeaderRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  oilMatchName: {
    fontSize: 15,
    fontWeight: '600',
    flex: 1,
  },
  matchBadges: {
    flexDirection: 'row',
    gap: 4,
  },
  premiumBadge: {
    marginLeft: 4,
  },
  standardBadge: {
    marginLeft: 4,
  },
  availabilityBadge: {
    marginLeft: 4,
  },
  botanicalMatch: {
    fontSize: 11,
    lineHeight: 14,
  },
  oilMatchDetails: {
    marginTop: 6,
  },
  oilAttributesRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 8,
    paddingBottom: 8,
  },
  attributeGroup: {
    flex: 1,
    alignItems: 'center',
  },
  attributeLabel: {
    fontSize: 10,
    textAlign: 'center',
    marginBottom: 2,
  },
  attributeValue: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
  oilTagsRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
    marginTop: 4,
  },
  cautionChip: {
    backgroundColor: '#fff3e0',
    height: 22,
    paddingHorizontal: 8,
    minHeight: 22,
  },
});
/**
 * @fileoverview Accordion Demo Screen
 * Showcase of varied List.Item layouts - each section uses different visual patterns
 * Demonstrates 2-row layouts with dividers and diverse information organization
 */

import React, { useState } from 'react';
import { View } from 'react-native';
import { 
  Text, 
  List,
  Chip,
  IconButton,
  Divider,
  Badge,
  Banner,
  FAB,
  Snackbar,
  Button,
  Switch,
} from 'react-native-paper';
import { Stack } from 'expo-router';
import { useTheme } from '@/shared/hooks/use-theme';
import { ScreenWrapper } from '@/shared/components/layout';
import { haptics } from '@/shared/utils/haptics';

export default function AccordionDemo() {
  const { theme, toggleTheme, themeMode } = useTheme();
  const styles = getStyles(theme);
  
  // State for accordion expansion
  const [accordionExpanded, setAccordionExpanded] = useState(false);
  const relevancyScore = 4; // Score from 1-5

  // Banner state management
  const [bannerVisible, setBannerVisible] = useState(true);

  // Snackbar state management
  const [snackbarOptions, setSnackbarOptions] = useState({
    showSnackbar: false,
    showAction: true,
    showCloseIcon: false,
    showLongerMessage: false,
    showLongerAction: false,
  });

  const {
    showSnackbar,
    showAction,
    showCloseIcon,
    showLongerMessage,
    showLongerAction,
  } = snackbarOptions;

  const SHORT_MESSAGE = 'Single-line snackbar';
  const LONG_MESSAGE = 'Snackbar with longer message which does not fit in one line';

  const snackbarAction = {
    label: showLongerAction ? 'Toggle Theme' : 'Action',
    onPress: () => {
      haptics.light();
      toggleTheme();
    },
  };


  const renderProductsSection = () => (
    <View style={styles.sectionContainer}>
      <List.Section>
        <List.Subheader>Propriedades terapêuticas</List.Subheader>
        <Divider />
        
        <List.Accordion
          left={() => (
            <View style={{ justifyContent: 'center' }}>
              {/* 
                TODO: When loading suggested oils for this property, replace Badge with loading state:
                
                Option 1 - Selected with loading:
                <IconButton
                  icon=""
                  mode="contained"
                  selected
                  loading
                  size={24}
                  onPress={() => {}}
                />
                
                Option 2 - Disabled with loading:
                <IconButton
                  icon=""
                  mode="contained-tonal"
                  disabled
                  size={24}
                  onPress={() => {}}
                  loading
                />
              */}
              <Badge 
                visible={true}
                style={[
                  styles.leftBadge,
                  { 
                    backgroundColor: accordionExpanded ? theme.colors.onPrimary : theme.colors.primary,
                    color: accordionExpanded ? theme.colors.primary : theme.colors.onPrimary,
                  }
                ]}
              >
                4
              </Badge>
            </View>
          )}
          title="Calmante"
          description={
            <View>
              <Text style={{ 
                color: accordionExpanded ? theme.colors.onPrimary : theme.colors.onSurfaceVariant, 
                marginBottom: 6
              }}>
                Ajuda a reduzir a agitação mentaç e emocional, facilitando o relaxamento antes do sono, especialmente em casos de insônia causada por estresse e ansiedade.
              </Text>
              <View style={[
                styles.dividerLine, 
                { backgroundColor: accordionExpanded ? theme.colors.onPrimary : theme.colors.outline }
              ]} />
              <View style={styles.relevancyRow}>
                <Text style={{
                  color: accordionExpanded ? theme.colors.onPrimary : theme.colors.onSurfaceVariant,
                  fontSize: 12,
                  marginRight: 6
                }}>
                  Relevância:
                </Text>
                <View style={styles.starsContainer}>
                  {Array.from({ length: 5 }, (_, i) => i + 1).map((star) => (
                    <IconButton
                      key={star}
                      icon={star <= relevancyScore ? 'star' : 'star-outline'}
                      iconColor={
                        star <= relevancyScore 
                          ? (accordionExpanded ? theme.colors.onPrimary : theme.colors.tertiary)
                          : (accordionExpanded ? theme.colors.onPrimary : theme.colors.onSurfaceVariant)
                      }
                      size={12}
                      style={styles.starIcon}
                    />
                  ))}
                </View>
              </View>
            </View>
          }

          expanded={accordionExpanded}
          onPress={() => setAccordionExpanded(!accordionExpanded)}
          style={{
            backgroundColor: accordionExpanded ? theme.colors.primary : 'transparent'
          }}
          titleStyle={{
            color: accordionExpanded ? theme.colors.onPrimary : theme.colors.onSurface
          }}
          right={() => (
            <List.Icon 
              icon={accordionExpanded ? 'chevron-up' : 'chevron-down'} 
              color={accordionExpanded ? theme.colors.onPrimary : theme.colors.onSurfaceVariant}
            />
          )}
        >
          <Divider />
          <List.Item
            right={() => null}
            title={() => (
              <View style={styles.watchTitle}>
                <View style={styles.watchTitleRow}>
                  <Text style={[styles.watchName, { color: theme.colors.onSurface }]}>
                    Alecrim (Rosemary)
                  </Text>
                  <Chip 
                    icon="star" 
                    style={styles.safetyChip}
                  >
                    4.9
                  </Chip>
                </View>
                <Text style={[styles.watchSpecs, { color: theme.colors.onSurfaceVariant }]}>
                  Rosimarinus officinalis
                </Text>
                <Text style={[styles.watchSpecs, { color: theme.colors.onSurfaceVariant }]}>
                  Indicado para acalmar principalmente estudantes e pessoas que trabalham com alta demanda mental. Auxilia na concentração, foco e memória, além de aliviar dores de cabeça tensionais.
                </Text>
              </View>
            )}
            description={() => (
              <View style={styles.watchDescription}>
                <View style={styles.watchFeatureRow}>
                  <View style={styles.leftGroup}>
                    <View style={[
                      styles.itemCircle,
                      { backgroundColor: theme.colors.primaryContainer }
                    ]}>
                      <Text style={[
                        styles.itemCircleNumber,
                        { color: theme.colors.onPrimaryContainer }
                      ]}>
                        S
                      </Text>
                    </View>
                    <View>
                      <Text style={[styles.featureLabel, { color: theme.colors.onSurfaceVariant }]}>Dilution Range</Text>
                      <Text style={[styles.featureValue, { color: theme.colors.onSurface }]}>25% - 50%</Text>
                    </View>
                  </View>
                  
                  <View style={styles.rightGroup}>
                    <Text style={[styles.featureLabel, { color: theme.colors.onSurfaceVariant }]}>Ratio</Text>
                    <Text style={[styles.featureValue, { color: theme.colors.onSurface }]}>1:10</Text>
                  </View>
                </View>
                <Text style={[styles.fullWidthDescription, { color: theme.colors.onSurfaceVariant }]}>
                Óleo essencial de alecrim deve ser diluído em óleo vegetal: use 1 gota para cada 5 ml de óleo carreador (≈1%).
                </Text>
                <View style={styles.fullWidthRangeBar}>
                  <View style={[styles.rangeBarContainer, { backgroundColor: theme.colors.outlineVariant }]}>
                    <View 
                      style={[
                        styles.rangeBarFill, 
                        { 
                          backgroundColor: theme.colors.primary,
                          left: '25%',
                          width: '25%'
                        }
                      ]} 
                    />
                  </View>
                </View>
                <View style={styles.safetyContainer}>
                  <View style={styles.safetyColumn}>
                    <Chip 
                      icon="close-circle"
                      style={styles.safetyChip}
                    >
                      Não ingerir
                    </Chip>
                    <Text style={[styles.safetyText, { color: theme.colors.onSurfaceVariant }]}>
                      Nunca consuma este óleo essencial. Apenas uso tópico ou inalação.
                    </Text>
                  </View>
                  <View style={styles.safetyColumn}>
                    <Chip 
                      icon="alert-circle"
                      style={styles.safetyChip}
                    >
                      Fototóxico
                    </Chip>
                    <Text style={[styles.safetyText, { color: theme.colors.onSurfaceVariant }]}>
                      Evite exposição solar por 12-24h após uso na pele.
                    </Text>
                  </View>
                </View>
              </View>
            )}
          />
          <Divider />
          <List.Item
            right={() => null}
            title={() => (
              <View style={styles.watchTitle}>
                <View style={styles.watchTitleRow}>
                  <Text style={[styles.watchName, { color: theme.colors.onSurface }]}>
                    Lavanda (Lavender)
                  </Text>
                  <Chip 
                    icon="star" 
                    style={styles.safetyChip}
                  >
                    4.7
                  </Chip>
                </View>
                <Text style={[styles.watchSpecs, { color: theme.colors.onSurfaceVariant }]}>
                  Lavandula angustifolia
                </Text>
                <Text style={[styles.watchSpecs, { color: theme.colors.onSurfaceVariant }]}>
                  Ideal para relaxamento e sono. Possui propriedades calmantes e anti-inflamatórias, sendo amplamente usado em aromaterapia para reduzir ansiedade e promover bem-estar.
                </Text>
              </View>
            )}
            description={() => (
              <View style={styles.watchDescription}>
                <View style={styles.watchFeatureRow}>
                  <View style={styles.leftGroup}>
                    <View style={[
                      styles.itemCircle,
                      { backgroundColor: theme.colors.primaryContainer }
                    ]}>
                      <Text style={[
                        styles.itemCircleNumber,
                        { color: theme.colors.onPrimaryContainer }
                      ]}>
                        S
                      </Text>
                    </View>
                    <View>
                      <Text style={[styles.featureLabel, { color: theme.colors.onSurfaceVariant }]}>Dilution Range</Text>
                      <Text style={[styles.featureValue, { color: theme.colors.onSurface }]}>1% - 3%</Text>
                    </View>
                  </View>
                  
                  <View style={styles.rightGroup}>
                    <Text style={[styles.featureLabel, { color: theme.colors.onSurfaceVariant }]}>Ratio</Text>
                    <Text style={[styles.featureValue, { color: theme.colors.onSurface }]}>1:30</Text>
                  </View>
                </View>
                <Text style={[styles.fullWidthDescription, { color: theme.colors.onSurfaceVariant }]}>
                Óleo essencial de lavanda é suave e pode ser usado puro em pequenas quantidades: use 1-2 gotas para cada 10ml de óleo carreador.
                </Text>
                <View style={styles.fullWidthRangeBar}>
                  <View style={[styles.rangeBarContainer, { backgroundColor: theme.colors.outlineVariant }]}>
                    <View 
                      style={[
                        styles.rangeBarFill, 
                        { 
                          backgroundColor: theme.colors.primary,
                          left: '1%',
                          width: '2%'
                        }
                      ]} 
                    />
                  </View>
                </View>
                <View style={styles.safetyContainer}>
                  <View style={styles.safetyColumn}>
                    <Chip 
                      icon="check-circle"
                      style={styles.safetyChip}
                    >
                      Pode ingerir
                    </Chip>
                    <Text style={[styles.safetyText, { color: theme.colors.onSurfaceVariant }]}>
                      Seguro para consumo em pequenas quantidades quando diluído adequadamente.
                    </Text>
                  </View>
                  <View style={styles.safetyColumn}>
                    <Chip 
                      icon="weather-sunny"
                      style={styles.safetyChip}
                    >
                      Seguro no sol
                    </Chip>
                    <Text style={[styles.safetyText, { color: theme.colors.onSurfaceVariant }]}>
                      Não causa fotossensibilidade. Seguro para exposição solar normal.
                    </Text>
                  </View>
                </View>
              </View>
            )}
          />
        </List.Accordion>
      </List.Section>
    </View>
  );

  const renderSnackbarSection = () => (
    <View style={styles.sectionContainer}>
      <List.Section>
        <List.Subheader>Snackbar Options</List.Subheader>
        <Divider />
        
        <View style={styles.row}>
          <Text style={{ color: theme.colors.onSurface }}>Action button</Text>
          <Switch
            value={showAction}
            onValueChange={() =>
              setSnackbarOptions({ ...snackbarOptions, showAction: !showAction })
            }
          />
        </View>
        
        <View style={styles.row}>
          <Text style={{ color: theme.colors.onSurface }}>Close icon button</Text>
          <Switch
            value={showCloseIcon}
            onValueChange={() =>
              setSnackbarOptions({ ...snackbarOptions, showCloseIcon: !showCloseIcon })
            }
          />
        </View>
        
        <View style={styles.row}>
          <Text style={{ color: theme.colors.onSurface }}>Longer message</Text>
          <Switch
            value={showLongerMessage}
            onValueChange={() =>
              setSnackbarOptions({
                ...snackbarOptions,
                showLongerMessage: !showLongerMessage,
              })
            }
          />
        </View>
        
        <View style={styles.row}>
          <Text style={{ color: theme.colors.onSurface }}>Longer action</Text>
          <Switch
            value={showLongerAction}
            onValueChange={() =>
              setSnackbarOptions({
                ...snackbarOptions,
                showLongerAction: !showLongerAction,
              })
            }
          />
        </View>

        <View style={styles.buttonWrapper}>
          <Button
            mode="outlined"
            onPress={() => {
              haptics.light();
              setSnackbarOptions({ ...snackbarOptions, showSnackbar: !showSnackbar });
            }}
          >
            {showSnackbar ? 'Hide Snackbar' : 'Show Snackbar'}
          </Button>
        </View>
      </List.Section>
    </View>
  );

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      
      <ScreenWrapper
        scrollable={true}
        showAppBar={true}
        appBarProps={{
          title: "Accordion Showcase",
          showBackButton: true,
          actions: [
            <IconButton 
              key="info" 
              icon="information" 
              onPress={() => haptics.light()} 
            />
          ]
        }}
      >
        {/* Banner Component - positioned inside ScreenWrapper content */}
        <Banner
          actions={[
            {
              label: `Switch to ${themeMode === 'light' ? 'dark' : 'light'} theme`,
              onPress: () => {
                haptics.light();
                toggleTheme();
              },
            },
            {
              label: 'Hide banner',
              onPress: () => {
                haptics.medium();
                setBannerVisible(false);
              },
            },
          ]}
          icon="information"
          visible={bannerVisible}
          onShowAnimationFinished={() =>
            console.log('Banner show animation completed')
          }
          onHideAnimationFinished={() =>
            console.log('Banner hide animation completed')
          }
        >
          React Native Paper Banner demo - showcases content pushing behavior and theming capabilities.
        </Banner>
        
        {/* Content flows naturally below banner */}
        {renderProductsSection()}
        
        {/* Snackbar demo section */}
        {renderSnackbarSection()}
      </ScreenWrapper>
      
      {/* FAB Toggle Control */}
      <FAB
        icon={bannerVisible ? 'eye-off' : 'eye'}
        label={bannerVisible ? 'Hide banner' : 'Show banner'}
        style={styles.fab}
        onPress={() => {
          haptics.light();
          setBannerVisible(!bannerVisible);
        }}
      />
      
      {/* Snackbar Component */}
      <Snackbar
        visible={showSnackbar}
        onDismiss={() => setSnackbarOptions({ ...snackbarOptions, showSnackbar: false })}
        action={showAction ? snackbarAction : undefined}
        onIconPress={
          showCloseIcon
            ? () => setSnackbarOptions({ ...snackbarOptions, showSnackbar: false })
            : undefined
        }
        duration={Snackbar.DURATION_MEDIUM}
        style={showLongerAction ? styles.longerAction : undefined}
      >
        {showLongerMessage ? LONG_MESSAGE : SHORT_MESSAGE}
      </Snackbar>
    </>
  );
}

const getStyles = (_theme: any): any => ({
  sectionContainer: {
    flex: 1,
  },

  // Divider line for 2-row layouts
  dividerLine: {
    height: 1,
    marginVertical: 6,
  },

  // Watch item styles
  watchTitle: {
    flex: 1,
  },
  watchTitleRow: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
    marginBottom: 2,
  },
  watchName: {
    fontSize: 15,
    fontWeight: '600' as const,
    flex: 1,
  },
  watchSpecs: {
    fontSize: 12,
    marginTop: 1,
  },
  watchDescription: {
    marginTop: 8,
  },
  watchFeatureRow: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    marginBottom: 12,
    paddingTop: 8,
  },
  leftGroup: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 8,
  },
  rightGroup: {
    alignItems: 'flex-end',
  },
  featureLabel: {
    fontSize: 10,
    textAlign: 'center' as const,
  },
  featureValue: {
    fontSize: 11,
    fontWeight: '500' as const,
    textAlign: 'center' as const,
    marginTop: 2,
  },
  // Relevancy styles
  relevancyRow: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    marginTop: 4,
  },
  starsContainer: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
  },
  starIcon: {
    margin: 0,
    width: 16,
    height: 16,
  },

  // Left badge styles
  leftBadge: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginLeft: 16,
    marginRight: 8,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    fontSize: 14,
    fontWeight: '600' as const,
    textAlign: 'center' as const,
    textAlignVertical: 'center' as const,
    lineHeight: 32,
  },

  // Item circle styles
  itemCircle: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    marginRight: 8,
  },
  itemCircleNumber: {
    fontSize: 14,
    fontWeight: '700' as const,
    textAlign: 'center' as const,
  },

  // Range bar styles
  fullWidthRangeBar: {
    marginTop: 8,
    marginBottom: 8,
  },
  rangeBarContainer: {
    width: '100%',
    height: 6,
    borderRadius: 3,
    marginBottom: 4,
    position: 'relative' as const,
  },
  rangeBarFill: {
    position: 'absolute' as const,
    height: '100%',
    borderRadius: 3,
  },
  // Full width description
  fullWidthDescription: {
    fontSize: 12,
    lineHeight: 16,
    marginTop: 8,
    marginBottom: 8,
    paddingHorizontal: 0,
    textAlign: 'left' as const,
  },

  // Safety indicators
  safetyContainer: {
    flexDirection: 'row',
    marginVertical: 12,
    gap: 12,
  },
  safetyColumn: {
    flex: 1,
    alignItems: 'stretch',
  },
  safetyChip: {
    marginBottom: 6,
  },
  safetyText: {
    fontSize: 10,
    lineHeight: 14,
    textAlign: 'left',
    paddingHorizontal: 4,
  },

  // Banner demo styles
  fab: {
    alignSelf: 'center',
    position: 'absolute',
    bottom: 0,
    margin: 16,
  },

  // Snackbar demo styles
  row: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'space-between' as const,
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  buttonWrapper: {
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    paddingVertical: 16,
  },
  longerAction: {
    flexDirection: 'column' as const,
  },
});
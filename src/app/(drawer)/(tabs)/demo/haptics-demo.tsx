/**
 * @fileoverview Haptics Demo Screen
 * Demonstrates various haptic feedback patterns used in popular apps
 * Follows app structure with consistent top/bottom navigation
 */

import React, { useState } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { 
  Text, 
  Card, 
  Button,
  Divider,
  List,
  Chip,
  IconButton,
} from 'react-native-paper';
import { Stack } from 'expo-router';
import { usePaperTheme } from '@/shared/hooks/use-theme';
import { AppBar, BottomNav } from '@/shared/components/layout';
import { haptics } from '@/shared/utils/haptics';

/**
 * Haptics Demo Screen with consistent navigation
 */
export default function HapticsDemo() {
  const theme = usePaperTheme();
  const [bottomNavIndex, setBottomNavIndex] = useState(0);
  const [lastTriggered, setLastTriggered] = useState<string>('');

  // Bottom navigation for this demo screen
  const bottomRoutes = [
    { key: 'basic', title: 'Basic', focusedIcon: 'gesture-tap', unfocusedIcon: 'gesture-tap-hold' },
    { key: 'feedback', title: 'Feedback', focusedIcon: 'bell', unfocusedIcon: 'bell-outline' },
    { key: 'interactions', title: 'Actions', focusedIcon: 'gesture-swipe', unfocusedIcon: 'gesture-swipe-horizontal' },
  ];

  const handleHaptic = async (type: string, hapticFn: () => Promise<void>) => {
    await hapticFn();
    setLastTriggered(type);
  };

  const renderBasicHaptics = () => (
    <View>
      <Text variant="titleLarge" style={{ marginBottom: 16, color: theme.colors.onSurface }}>
        Basic Impact Feedback
      </Text>
      <Text variant="bodyMedium" style={{ marginBottom: 16, color: theme.colors.onSurfaceVariant }}>
        Used for button presses and general interactions
      </Text>

      <Card style={styles.demoCard}>
        <Card.Content>
          <Text variant="titleMedium" style={{ marginBottom: 12 }}>Light Impact</Text>
          <Text variant="bodySmall" style={{ marginBottom: 12, color: theme.colors.onSurfaceVariant }}>
            Used in: Instagram likes, Twitter hearts, gentle button presses
          </Text>
          <Button 
            mode="contained" 
            onPress={() => handleHaptic('Light Impact', haptics.light)}
            style={{ marginBottom: 8 }}
          >
            Try Light Haptic
          </Button>
        </Card.Content>
      </Card>

      <Card style={styles.demoCard}>
        <Card.Content>
          <Text variant="titleMedium" style={{ marginBottom: 12 }}>Medium Impact</Text>
          <Text variant="bodySmall" style={{ marginBottom: 12, color: theme.colors.onSurfaceVariant }}>
            Used in: Pull-to-refresh, swipe actions, moderate feedback
          </Text>
          <Button 
            mode="contained" 
            onPress={() => handleHaptic('Medium Impact', haptics.medium)}
            style={{ marginBottom: 8 }}
          >
            Try Medium Haptic
          </Button>
        </Card.Content>
      </Card>

      <Card style={styles.demoCard}>
        <Card.Content>
          <Text variant="titleMedium" style={{ marginBottom: 12 }}>Heavy Impact</Text>
          <Text variant="bodySmall" style={{ marginBottom: 12, color: theme.colors.onSurfaceVariant }}>
            Used in: Long press actions, context menus, strong feedback
          </Text>
          <Button 
            mode="contained" 
            onPress={() => handleHaptic('Heavy Impact', haptics.heavy)}
            style={{ marginBottom: 8 }}
          >
            Try Heavy Haptic
          </Button>
        </Card.Content>
      </Card>
    </View>
  );

  const renderFeedbackHaptics = () => (
    <View>
      <Text variant="titleLarge" style={{ marginBottom: 16, color: theme.colors.onSurface }}>
        Notification Feedback
      </Text>
      <Text variant="bodyMedium" style={{ marginBottom: 16, color: theme.colors.onSurfaceVariant }}>
        Used to communicate success, warnings, and errors
      </Text>

      <Card style={styles.demoCard}>
        <Card.Content>
          <Text variant="titleMedium" style={{ marginBottom: 12 }}>Success</Text>
          <Text variant="bodySmall" style={{ marginBottom: 12, color: theme.colors.onSurfaceVariant }}>
            Used in: Message sent, file uploaded, task completed
          </Text>
          <Button 
            mode="contained" 
            buttonColor={theme.colors.primary}
            onPress={() => handleHaptic('Success', haptics.success)}
            style={{ marginBottom: 8 }}
          >
            Try Success Haptic
          </Button>
        </Card.Content>
      </Card>

      <Card style={styles.demoCard}>
        <Card.Content>
          <Text variant="titleMedium" style={{ marginBottom: 12 }}>Warning</Text>
          <Text variant="bodySmall" style={{ marginBottom: 12, color: theme.colors.onSurfaceVariant }}>
            Used in: Unsaved changes, low battery, caution alerts
          </Text>
          <Button 
            mode="contained" 
            buttonColor={theme.colors.tertiary}
            onPress={() => handleHaptic('Warning', haptics.warning)}
            style={{ marginBottom: 8 }}
          >
            Try Warning Haptic
          </Button>
        </Card.Content>
      </Card>

      <Card style={styles.demoCard}>
        <Card.Content>
          <Text variant="titleMedium" style={{ marginBottom: 12 }}>Error</Text>
          <Text variant="bodySmall" style={{ marginBottom: 12, color: theme.colors.onSurfaceVariant }}>
            Used in: Failed login, network errors, validation failures
          </Text>
          <Button 
            mode="contained" 
            buttonColor={theme.colors.error}
            onPress={() => handleHaptic('Error', haptics.error)}
            style={{ marginBottom: 8 }}
          >
            Try Error Haptic
          </Button>
        </Card.Content>
      </Card>
    </View>
  );

  const renderInteractionHaptics = () => (
    <View>
      <Text variant="titleLarge" style={{ marginBottom: 16, color: theme.colors.onSurface }}>
        Interaction Patterns
      </Text>
      <Text variant="bodyMedium" style={{ marginBottom: 16, color: theme.colors.onSurfaceVariant }}>
        Common interaction patterns from popular apps
      </Text>

      <Card style={styles.demoCard}>
        <Card.Content>
          <Text variant="titleMedium" style={{ marginBottom: 12 }}>Selection</Text>
          <Text variant="bodySmall" style={{ marginBottom: 12, color: theme.colors.onSurfaceVariant }}>
            Used in: iOS picker wheels, Spotify volume, scrolling through options
          </Text>
          <Button 
            mode="outlined" 
            onPress={() => handleHaptic('Selection', haptics.select)}
            style={{ marginBottom: 8 }}
          >
            Try Selection Haptic
          </Button>
        </Card.Content>
      </Card>

      <Card style={styles.demoCard}>
        <Card.Content>
          <Text variant="titleMedium" style={{ marginBottom: 12 }}>Tab Switch</Text>
          <Text variant="bodySmall" style={{ marginBottom: 12, color: theme.colors.onSurfaceVariant }}>
            Used in: Instagram tabs, Spotify navigation, YouTube tabs
          </Text>
          <Button 
            mode="outlined" 
            onPress={() => handleHaptic('Tab Switch', haptics.tab)}
            style={{ marginBottom: 8 }}
          >
            Try Tab Switch Haptic
          </Button>
        </Card.Content>
      </Card>

      <Card style={styles.demoCard}>
        <Card.Content>
          <Text variant="titleMedium" style={{ marginBottom: 12 }}>Toggle Switch</Text>
          <Text variant="bodySmall" style={{ marginBottom: 12, color: theme.colors.onSurfaceVariant }}>
            Used in: Settings toggles, dark mode switch, feature toggles
          </Text>
          <Button 
            mode="outlined" 
            onPress={() => handleHaptic('Toggle', haptics.toggle)}
            style={{ marginBottom: 8 }}
          >
            Try Toggle Haptic
          </Button>
        </Card.Content>
      </Card>

      <Card style={styles.demoCard}>
        <Card.Content>
          <Text variant="titleMedium" style={{ marginBottom: 12 }}>Swipe Action</Text>
          <Text variant="bodySmall" style={{ marginBottom: 12, color: theme.colors.onSurfaceVariant }}>
            Used in: Tinder swipes, iOS mail actions, gesture navigation
          </Text>
          <Button 
            mode="outlined" 
            onPress={() => handleHaptic('Swipe Action', haptics.swipe)}
            style={{ marginBottom: 8 }}
          >
            Try Swipe Haptic
          </Button>
        </Card.Content>
      </Card>

      <Divider style={{ marginVertical: 16 }} />

      <Card style={styles.demoCard}>
        <Card.Content>
          <Text variant="titleMedium" style={{ marginBottom: 12 }}>Special Patterns</Text>
          
          <View style={{ flexDirection: 'row', gap: 8, marginBottom: 12 }}>
            <Button 
              mode="contained-tonal" 
              onPress={() => handleHaptic('Achievement', haptics.achievement)}
              compact
            >
              Achievement
            </Button>
            <Button 
              mode="contained-tonal" 
              buttonColor={theme.colors.errorContainer}
              onPress={() => handleHaptic('Destructive', haptics.destructive)}
              compact
            >
              Destructive
            </Button>
          </View>
          
          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
            Achievement: Level up, streak milestones{'\n'}
            Destructive: Delete actions, logout confirmations
          </Text>
        </Card.Content>
      </Card>
    </View>
  );

  const renderContent = () => {
    switch (bottomNavIndex) {
      case 0: return renderBasicHaptics();
      case 1: return renderFeedbackHaptics();
      case 2: return renderInteractionHaptics();
      default: return renderBasicHaptics();
    }
  };

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        {/* Top App Bar */}
        <AppBar 
          title="Haptics Demo" 
          showBackButton 
          actions={[
            <IconButton 
              key="info" 
              icon="information" 
              onPress={() => handleHaptic('Info Button', haptics.light)} 
            />
          ]}
        />
        
        {/* Content */}
        <View style={styles.content}>
          <ScrollView 
            style={styles.scrollView}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
          >
            {/* Status indicator */}
            {lastTriggered && (
              <Card style={[styles.statusCard, { backgroundColor: theme.colors.primaryContainer }]}>
                <Card.Content>
                  <Text variant="bodyMedium" style={{ color: theme.colors.onPrimaryContainer }}>
                    Last triggered: {lastTriggered}
                  </Text>
                </Card.Content>
              </Card>
            )}

            {renderContent()}

            {/* Info card */}
            <Card style={styles.infoCard}>
              <Card.Content>
                <Text variant="titleMedium" style={{ marginBottom: 8 }}>
                  💡 About Haptic Feedback
                </Text>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                  Haptic feedback enhances user experience by providing tactile responses to interactions. 
                  These patterns are based on usage in popular apps like Instagram, Twitter, Spotify, and iOS system apps.
                  {'\n\n'}
                  Note: Haptic feedback only works on physical devices (iOS/Android), not in simulators or web browsers.
                </Text>
              </Card.Content>
            </Card>
          </ScrollView>
        </View>
        
        {/* Bottom Navigation */}
        <BottomNav
          routes={bottomRoutes}
          activeIndex={bottomNavIndex}
          onTabPress={(index, route) => {
            setBottomNavIndex(index);
            // Haptic feedback for tab switching
            haptics.tab();
          }}
        />
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    gap: 16,
  },
  demoCard: {
    marginBottom: 12,
    elevation: 1,
  },
  statusCard: {
    marginBottom: 16,
    elevation: 2,
  },
  infoCard: {
    marginTop: 16,
    elevation: 1,
  },
});
import { Stack } from 'expo-router';
import { useTranslation } from 'react-i18next';

/**
 * Layout for the main app (protected routes).
 * Now uses a single Stack layout since we handle navigation internally 
 * in the main screen using the navigation-demo structure.
 */
export default function TabsLayout() {
  const { t } = useTranslation('homescreen');

  return (
    <Stack
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          title: t('navigation.home'),
        }}
      />
    </Stack>
  );
}

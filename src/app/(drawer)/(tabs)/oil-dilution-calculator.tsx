import React, { useEffect, useState, useRef, useCallback } from 'react';
import { View } from 'react-native';
import { Text, List, Divider, TouchableRipple, Appbar } from 'react-native-paper';
import { router } from 'expo-router';
import { BottomSheetModal, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import { useTheme } from '@/shared/hooks/use-theme';
import { ScreenWrapper } from '@/shared/components/layout/screen-wrapper';
import { useTranslation } from 'react-i18next';
import { useUnifiedCalculator } from '@/features/oil-calculator/hooks';
import { OilInputRow } from '@/features/oil-calculator/components/inputs/oil-input-row';
import { ResultsDisplay } from '@/features/oil-calculator/components/displays/results-display';
import { BottleControls } from '@/features/oil-calculator/components/inputs/bottle-controls';
import { DropRatioBottomSheetContent } from '@/features/oil-calculator/components/inputs/drop-ratio-menu';

// --- MAIN SCREEN COMPONENT ---
export default function OilDilutionCalculatorScreen() {
    const { theme, spacing } = useTheme();
    const { t } = useTranslation('oil-calculator');
    const {
        bottleSize, targetPercentage, dropsPerMl, mlPerDrop, oils, totalDrops, dilutionPercentage,
        handleBottleSizeChange, handleTargetPercentageChange, handleManualDropChange, handleDropsPerMlChange,
        addOil, removeOil, updateOilName, resetToDefaults, constants
    } = useUnifiedCalculator();

    const { MIN_PERCENTAGE, MAX_PERCENTAGE } = constants;
    const eoVolume = totalDrops * mlPerDrop;
    const carrierVolume = bottleSize - eoVolume;

    // Pull to refresh state
    const [isRefreshing, setIsRefreshing] = useState(false);
    
    // Drop ratio modal ref
    const dropRatioModalRef = useRef<BottomSheetModal>(null);

    // Reset to defaults on initial load
    useEffect(() => {
        resetToDefaults();
    }, [resetToDefaults]);

    const handlePullToRefresh = async () => {
        setIsRefreshing(true);
        // Reset to defaults: 15ml bottle, 10% dilution
        resetToDefaults();
        // Small delay for better UX
        const REFRESH_DELAY = 500;
        await new Promise(resolve => setTimeout(resolve, REFRESH_DELAY));
        setIsRefreshing(false);
    };

    const handleSettingsPress = () => {
        dropRatioModalRef.current?.present();
    };

    const handleDropDialogDismiss = () => {
        dropRatioModalRef.current?.dismiss();
    };

    // Custom backdrop for modal
    const renderBackdrop = useCallback(
        (props: any) => (
            <BottomSheetBackdrop
                {...props}
                disappearsOnIndex={-1}
                appearsOnIndex={0}
                opacity={0.5}
            />
        ),
        []
    );

    const handleDropRatioSave = (newDropsPerMl: number) => {
        handleDropsPerMlChange(newDropsPerMl);
    };

    // AppBar configuration
    const appBarProps = {
        title: t('title'),
        showBackButton: true,
        onBackPress: () => {
            router.back();
        },
        actions: [
            <Appbar.Action key="settings" icon="cog" onPress={handleSettingsPress} />
        ]
    };

    return (
        <ScreenWrapper 
            scrollable
            showAppBar={true}
            appBarProps={appBarProps}
            enableRefresh={true}
            refreshing={isRefreshing}
            onRefresh={handlePullToRefresh}
            dynamicBottomSpace={{
                enabled: true,
                height: 230,  // Adjusted height for bottle controls
                backgroundColor: theme.colors.primary,  // Primary color control center
                content: (
                    <BottleControls 
                        bottleSize={bottleSize}
                        targetPercentage={targetPercentage}
                        minPercentage={MIN_PERCENTAGE}
                        maxPercentage={MAX_PERCENTAGE}
                        onBottleSizeChange={handleBottleSizeChange}
                        onTargetPercentageChange={handleTargetPercentageChange}
                    />
                ),
                trigger: {
                    scrollThreshold: 0.0,  // Always visible (0.0 = auto-trigger)
                    hideThreshold: -1,     // Never hide (-1 = never hide)
                },
                animation: {
                    duration: 300,
                    easing: 'ease-in-out',
                }
            }}
        >
            {/* Results section back at the top */}
            <View style={{ paddingHorizontal: spacing.screenPadding, paddingTop: spacing.lg}}>
                <ResultsDisplay 
                    dilutionPercentage={dilutionPercentage}
                    totalDrops={totalDrops}
                    eoVolume={eoVolume}
                    carrierVolume={carrierVolume}
                    oils={oils}
                    bottleSize={bottleSize}
                    mlPerDrop={mlPerDrop}
                />
            </View>

            {/* List section without padding - edge-to-edge */}
            <List.Section>
                <List.Subheader style={{ paddingHorizontal: spacing.screenPadding }}>
                    <Text variant="titleMedium">{t('oilBlend')}</Text>
                </List.Subheader>
                
                {oils.map((oil, index) => (
                    <React.Fragment key={oil.id}>
                        <OilInputRow 
                            oil={oil} 
                            onDropsChange={handleManualDropChange} 
                            onNameChange={updateOilName}
                            onRemove={removeOil}
                            canRemove={oils.length > 1}
                            totalDrops={totalDrops}
                            bottleSize={bottleSize}
                            dropsPerMl={dropsPerMl}
                        />
                        {index < oils.length - 1 && <Divider />}
                    </React.Fragment>
                ))}
                
                {/* Dashed outline "Add Oil" button */}
                <View style={{ paddingHorizontal: spacing.screenPadding, paddingTop: spacing.md }}>
                    <TouchableRipple 
                        onPress={addOil}
                        style={{
                            borderWidth: 1,
                            borderColor: theme.colors.outline,
                            borderStyle: 'dashed',
                            borderRadius: spacing.sm,
                            paddingVertical: spacing.lg,
                            paddingHorizontal: spacing.md,
                            backgroundColor: 'transparent',
                            alignItems: 'center',
                            justifyContent: 'center'
                        }}
                        borderless={true}
                        rippleColor={theme.colors.primary + '20'}
                    >
                        <View style={{ flexDirection: 'row', alignItems: 'center', gap: spacing.xs }}>
                            <Text 
                                variant="bodyLarge" 
                                style={{ 
                                    color: theme.colors.onSurfaceVariant,
                                    fontWeight: '500'
                                }}
                            >
                                + {t('addOil')}
                            </Text>
                        </View>
                    </TouchableRipple>
                </View>
            </List.Section>

            {/* Drop Ratio Settings Bottom Sheet */}
            <BottomSheetModal
                ref={dropRatioModalRef}
                index={0}
                snapPoints={['60%']}
                enableDynamicSizing={false}
                backdropComponent={renderBackdrop}
                backgroundStyle={getModalStyles(theme).modalBackground}
                handleIndicatorStyle={getModalStyles(theme).handleIndicator}
            >
                <DropRatioBottomSheetContent
                    onDismiss={handleDropDialogDismiss}
                    initialDropsPerMl={dropsPerMl}
                    onSave={handleDropRatioSave}
                />
            </BottomSheetModal>
        </ScreenWrapper>
    );
}

// Modal styling to match profile modal pattern
const getModalStyles = (theme: any) => ({
    modalBackground: {
        backgroundColor: theme.colors.surface,
    },
    handleIndicator: {
        backgroundColor: theme.colors.onSurfaceVariant,
    },
});
import React, { useState, useRef, useCallback, useEffect } from 'react';
import { View, StyleSheet, Alert, Platform } from 'react-native';
import {
  Portal,
  Text,
  TouchableRipple,
  Avatar,
  Divider,
  List,
  Dialog,
  Button,
  Surface,
} from 'react-native-paper';
import { useAuth, useUser } from '@clerk/clerk-expo';
import { useRouter } from 'expo-router';
import { DrawerActions } from '@react-navigation/native';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '@/shared/hooks/use-theme';
import { useHomeScreenRefresh } from '@/shared/hooks';
import { useRealtime } from '@/shared/providers/realtime-provider';
import { getTimeBasedGreeting } from '@/shared/utils/date-utils';
import { getLastRefreshTimestamp } from '@/shared/utils/refresh-utils';
import { haptics } from '@/shared/utils/haptics';
// Updated to use @gorhom/bottom-sheet modal system
// Using global BottomSheetModalProvider from _layout.tsx
import { BottomSheetModal, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import { ProfileModalContent } from '@/features/auth/components';
import { ScreenWrapper } from '@/shared/components/layout';
import { useTranslation } from 'react-i18next';
// Clean implementation without heavy modern components

// Types for navigation state
interface NavigationState {
  bottomNavIndex: number;
}

/**
 * Main App Home Content - Uses the new modal system
 */
function HomeScreenContent() {
  const {
    theme,
    toggleTheme,
    isDarkTheme,
    spacing,
    fontWeight,
    borderRadius,
  } = useTheme();
  const styles = getStyles(theme);
  const profileModalRef = useRef<BottomSheetModal>(null);
  const { user } = useUser();
  const { signOut } = useAuth();
  const router = useRouter();
  const navigation = useNavigation();

  const { t } = useTranslation(['common', 'homescreen']);
  
  // Delay presence hook to avoid telemetry collision with login flow
  const [shouldStartPresence, setShouldStartPresence] = useState(false);
  
  // Delay presence hook until login telemetry settles
  useEffect(() => {
    if (user?.id && !shouldStartPresence) {
      const timer = setTimeout(() => {
        setShouldStartPresence(true);
      }, 1500); // 1.5 second delay after user is available
      
      return () => clearTimeout(timer);
    }
  }, [user?.id, shouldStartPresence]);
  
  // Simple connection - duplicate prevention handled by RealtimeProvider
  const { onlineCount, isLoading, error, retry } = useRealtime();

  // Apply the delay logic to the display, not the hook call
  const displayCount = shouldStartPresence ? onlineCount : 0;
  const displayLoading = shouldStartPresence ? isLoading : true;
  
  const [state, setState] = useState<NavigationState>({
    bottomNavIndex: 0,
  });

  // Profile modal function using @gorhom/bottom-sheet
  const showProfileModal = () => {
    haptics.light(); // Light haptic for avatar tap (Instagram pattern)
    profileModalRef.current?.present();
  };

  const hideProfileModal = () => {
    profileModalRef.current?.dismiss();
  };

  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
      />
    ),
    []
  );
  const [showSignOutDialog, setShowSignOutDialog] = useState(false);
  const [greeting, setGreeting] = useState(getTimeBasedGreeting(t));
  const [lastRefreshTime, setLastRefreshTime] = useState<Date | null>(null);



  // Sign out functionality - following backup file pattern
  const performSignOut = async () => {
    try {
      console.log('Starting sign out process...');
      await signOut();
      console.log('Sign out completed');
      setShowSignOutDialog(false);
      router.replace('/welcome');
    } catch (error) {
      console.error('Sign out error:', error);
      setShowSignOutDialog(false);

      if (Platform.OS === 'web') {
        Alert.alert(t('homescreen:error.title'), t('homescreen:error.signOutFailed'));
      } else {
        Alert.alert(
          t('homescreen:error.title'),
          t('homescreen:error.signOutFailed'),
          [{ text: t('homescreen:alerts.ok') }]
        );
      }
    }
  };

  const handleSignOut = async () => {
    if (Platform.OS === 'web') {
      // Use Dialog for web platform
      setShowSignOutDialog(true);
    } else {
      // Use Alert for native platforms
      Alert.alert(
        t('homescreen:signOut.title'),
        t('homescreen:signOut.message'),
        [
          { text: t('homescreen:signOut.cancel'), style: 'cancel' },
          { text: t('homescreen:signOut.confirm'), style: 'destructive', onPress: performSignOut },
        ]
      );
    }
  };

  // Computed AppBar props for ScreenWrapper
  const getAppBarProps = () => {
    return {
      title: t('homescreen:appTitle'),
      showMenuButton: true,
      showBackButton: false,
      onMenuPress: () => {
        haptics.light();
        navigation.dispatch(DrawerActions.openDrawer());
      },
      actions: [
        user?.imageUrl ? (
          <Avatar.Image
            key="avatar"
            size={44}
            source={{ uri: user.imageUrl }}
            style={{
              marginRight: spacing.xs,
            }}
            onTouchEnd={showProfileModal}
          />
        ) : (
          <Avatar.Icon
            key="avatar"
            size={44}
            icon="account"
            style={{
              backgroundColor: theme.colors.primary,
              marginRight: spacing.xs,
            }}
            onTouchEnd={showProfileModal}
          />
        )
      ],
    };
  };

  // Pull-to-refresh functionality
  const { refreshControlProps } = useHomeScreenRefresh({
    updateGreeting: () => {
      console.log('🕐 Updating greeting based on current time');
      setGreeting(getTimeBasedGreeting(t));
    },
    updateUserData: async () => {
      console.log('👤 Refreshing user data from Clerk');
      // User data automatically updates from Clerk hook
      // This could include additional user data fetching if needed
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API call
    },
    updateTimestamp: () => {
      console.log('⏰ Updating last refresh timestamp');
      setLastRefreshTime(new Date());
    },
  });

  // Clean Home Screen - Modern minimalist approach
  const renderMainScreen = () => (
    <>
      {/* Clean Greeting Section - Typography Only */}
      <View style={{ 
        marginBottom: spacing.xl,
        paddingHorizontal: spacing.screenPadding,
        paddingTop: spacing.lg, // Add top padding for app bar clearance
      }}>
        <Text variant="headlineMedium" style={{ 
          marginBottom: spacing.microSpacing, 
          color: theme.colors.onBackground,
          fontWeight: fontWeight.bold,
        }}>
          {greeting}
        </Text>
        <Text variant="titleLarge" style={{ 
          color: theme.colors.onSurfaceVariant,
          fontWeight: fontWeight.medium,
        }}>
          {user?.firstName || t('homescreen:chef')}!
        </Text>
      </View>

      {/* Hero Feature - Create Recipe */}
      <Surface
        mode="flat"
        elevation={1}
        style={{
          borderRadius: borderRadius.lg,
          marginBottom: spacing.xl,
          marginHorizontal: spacing.screenPadding,
          overflow: 'hidden',
        }}
      >
        <TouchableRipple
          onPress={() => {
            haptics.light();
            router.push('/create-recipe/');
          }}
          style={{
            padding: spacing.heroCardPadding,
            minHeight: 120,
            backgroundColor: theme.colors.primaryContainer,
          }}
        >
          <View>
            <View style={{ marginBottom: spacing.md }}>
              <Avatar.Icon
                size={56}
                icon="chef-hat"
                style={{
                  backgroundColor: theme.colors.primary,
                  marginBottom: spacing.sm,
                }}
              />
            </View>
            
            <Text
              variant="headlineSmall"
              style={{
                color: theme.colors.onPrimaryContainer,
                fontWeight: fontWeight.bold,
                marginBottom: spacing.microSpacing,
              }}
            >
              {t('recipe.createRecipe')}
            </Text>
            
            <Text
              variant="bodyLarge"
              style={{
                color: theme.colors.onPrimaryContainer,
                opacity: 0.8,
              }}
            >
              {t('recipe.getAipoweredEssentialOilR')}
            </Text>
          </View>
        </TouchableRipple>
      </Surface>

      {/* Quick Actions Section Header */}
      <View style={{ paddingHorizontal: spacing.screenPadding }}>
        <Divider />
      </View>
      <List.Section>
        <List.Item
          title={t('homescreen:quickActions')}
          description={t('homescreen:mostUsedFeatures')}
        />
      </List.Section>

      {/* Quick Actions - Two Actions in Row */}
      <View style={{
        paddingHorizontal: spacing.screenPadding,
        marginBottom: spacing.sectionSpacing,
        flexDirection: 'row',
        gap: spacing.md,
      }}>
        {/* Oil Calculator */}
        <View style={{ flex: 1 }}>
          <Surface
            mode="flat"
            elevation={0}
            style={{
              borderRadius: borderRadius.card,
              overflow: 'hidden',
              backgroundColor: theme.colors.surface,
            }}
          >
            <TouchableRipple
              onPress={() => {
                haptics.light();
                router.push('/oil-dilution-calculator');
              }}
              style={{
                padding: spacing.cardPadding,
                minHeight: 140,
                justifyContent: 'space-between',
              }}
            >
              <View>
                <Avatar.Icon
                  size={44}
                  icon="beaker"
                  style={{
                    backgroundColor: theme.colors.tertiaryContainer,
                    marginBottom: spacing.sm,
                  }}
                />
                
                <Text
                  variant="titleMedium"
                  style={{
                    color: theme.colors.onSurface,
                    fontWeight: fontWeight.semiBold,
                    marginBottom: spacing.microSpacing,
                  }}
                >
                  {t('recipe.oilDilutionCalculator')}
                </Text>
                
                <Text
                  variant="bodySmall"
                  style={{
                    color: theme.colors.onSurfaceVariant,
                    lineHeight: 18,
                  }}
                  numberOfLines={3}
                >
                  {t('recipe.calculateEssentialOilDilu')}
                </Text>
              </View>
            </TouchableRipple>
          </Surface>
        </View>

        {/* Realtime Chat */}
        <View style={{ flex: 1 }}>
          <Surface
            mode="flat"
            elevation={0}
            style={{
              borderRadius: borderRadius.card,
              overflow: 'hidden',
              backgroundColor: theme.colors.surface,
            }}
          >
            <TouchableRipple
              onPress={() => {
                haptics.light();
                router.push('/realtime-chat');
              }}
              style={{
                padding: spacing.cardPadding,
                minHeight: 140,
                justifyContent: 'space-between',
              }}
            >
              <View>
                <Avatar.Icon
                  size={44}
                  icon="chat"
                  style={{
                    backgroundColor: theme.colors.secondaryContainer,
                    marginBottom: spacing.sm,
                  }}
                />
                
                <Text
                  variant="titleMedium"
                  style={{
                    color: theme.colors.onSurface,
                    fontWeight: fontWeight.semiBold,
                    marginBottom: spacing.microSpacing,
                  }}
                >
                  Chat em Tempo Real
                </Text>
                
                <Text
                  variant="bodySmall"
                  style={{
                    color: theme.colors.onSurfaceVariant,
                    lineHeight: 18,
                  }}
                  numberOfLines={3}
                >
                  Converse com outros usuários em tempo real
                </Text>
              </View>
            </TouchableRipple>
          </Surface>
        </View>
      </View>

      {/* Community Section */}
      <View style={{ paddingHorizontal: spacing.screenPadding }}>
        <Divider />
      </View>
      <List.Section>
        <List.Item
          title="Comunidade"
          description="Conecte-se com outros usuários"
        />
      </List.Section>

      <View style={{
        paddingHorizontal: spacing.screenPadding,
        marginBottom: spacing.lg,
      }}>
        <Text variant="bodyLarge" style={{ 
          color: theme.colors.onSurfaceVariant,
          textAlign: 'center',
          fontStyle: 'italic'
        }}>
          {displayLoading 
            ? '⏳ Carregando...'
            : `🟢 ${displayCount} usuários online`
          }
        </Text>
      </View>
    </>
  );




  // Computed BottomNav props for ScreenWrapper
  const getBottomNavProps = () => {
    // Home screen bottom navigation - 4 tabs including live chat
    const routes = [
      // NOTE: Home and Live Chat tabs are intentionally hardcoded - do not translate
      { key: 'home', title: 'Home', focusedIcon: 'home', unfocusedIcon: 'home-outline' },
      { key: 'recipes', title: t('homescreen:recipe'), focusedIcon: 'chef-hat', unfocusedIcon: 'chef-hat' },
      { key: 'chat', title: 'Live Chat', focusedIcon: 'chat', unfocusedIcon: 'chat-outline' },
      { key: 'profile', title: t('homescreen:navigation.profile'), focusedIcon: 'account', unfocusedIcon: 'account-outline' },
    ];

    return {
      routes,
      activeIndex: state.bottomNavIndex,
      onTabPress: (index: number, route: any) => {
        setState(prev => ({ ...prev, bottomNavIndex: index }));
        // Handle navigation based on selected tab
        switch (route.key) {
          case 'home':
            // Already on main screen
            break;
          case 'recipes':
            router.push('/create-recipe/');
            break;
          case 'chat':
            router.push('/realtime-chat');
            break;
          case 'profile':
            showProfileModal();
            break;
        }
      },
    };
  };


  const bottomNavProps = getBottomNavProps();

  return (
    <>
      <ScreenWrapper
        // AppBar Integration
        showAppBar={true}
        appBarProps={getAppBarProps()}
        
        // BottomNav Integration  
        showBottomNav={!!bottomNavProps}
        bottomNavProps={bottomNavProps || undefined}
        
        // Content Management
        scrollable={true}
        enableRefresh={true}
        refreshing={refreshControlProps.refreshing}
        onRefresh={refreshControlProps.onRefresh}
        
        // Note: Modal integration handled by global BottomSheetModalProvider in _layout.tsx
        
        // Theme Integration
        customBackgroundColor={theme.colors.background}
      >
        {renderMainScreen()}
      </ScreenWrapper>

      {/* Profile Modal using @gorhom/bottom-sheet */}
      <BottomSheetModal
        ref={profileModalRef}
        index={0}
        snapPoints={['90%']}
        enablePanDownToClose={true}
        enableDynamicSizing={false}
        backdropComponent={renderBackdrop}
        backgroundStyle={styles.modalBackground}
        handleIndicatorStyle={styles.handleIndicator}
      >
        <ProfileModalContent onDismiss={hideProfileModal} />
      </BottomSheetModal>

      {/* Sign Out Dialog for Web Platform */}
      <Portal>
        <Dialog visible={showSignOutDialog} onDismiss={() => setShowSignOutDialog(false)}>
          <Dialog.Title>{t('homescreen:signOut.title')}</Dialog.Title>
          <Dialog.Content>
            <Text>{t('homescreen:signOut.message')}</Text>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setShowSignOutDialog(false)}>{t('homescreen:signOut.cancel')}</Button>
            <Button onPress={performSignOut}>{t('homescreen:signOut.confirm')}</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </>
  );
}

/**
 * Main Home Screen with @gorhom/bottom-sheet modal provider
 * Uses global BottomSheetModalProvider from _layout.tsx
 */
function HomeScreen() {
  return <HomeScreenContent />;
}

export default HomeScreen;

const getStyles = (theme: any) => StyleSheet.create({
  modalBackground: {
    backgroundColor: theme.colors.surface,
  },
  handleIndicator: {
    backgroundColor: theme.colors.onSurfaceVariant,
    width: 32,
    height: 4,
  },
});
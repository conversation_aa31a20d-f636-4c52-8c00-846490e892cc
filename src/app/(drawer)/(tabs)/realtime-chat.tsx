/**
 * Realtime Chat Screen - Final KISS & SLC Implementation
 *
 * This screen orchestrates the chat experience, focusing on performance,
 * data management, and a clean user interface.
 *
 * - UPDATE: Now allows scrolling while the keyboard is open by using the
 * `keyboardShouldPersistTaps` prop, a more refined and standard UX pattern.
 *
 * - UX (Lovable): Implements message grouping for a clean, scannable UI.
 * - UX (Complete): Includes an empty state and handles optimistic UI on send.
 * - ARCH (Simple): Uses declarative props for keyboard handling and data
 * processing with useMemo for high performance and maintainability.
 */
import React, { useState, useMemo, useCallback } from 'react';
import {
  View,
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
} from 'react-native';
import { FlashList } from '@shopify/flash-list';
import { Text, IconButton, Badge } from 'react-native-paper';
import { useUser } from '@clerk/clerk-expo';
import { ScreenWrapper } from '@/shared/components/layout/screen-wrapper';
import { ChatInputBar } from '@/shared/components/layout/chat-input-bar';
import {
  ChatMessageComponent,
  ChatMessage,
  FriendsStatusBar,
} from '@/features/realtime-chat';
import { useRealtime } from '@/shared/providers/realtime-provider';
import { AvatarWithBadge } from '@/shared/components/ui';
import { useTheme } from '@/shared/hooks/use-theme';
import { useTranslation } from 'react-i18next';

// This is the data structure after we process it for the UI
interface ProcessedMessage extends ChatMessage {
  isFirstInGroup: boolean;
  isLastInGroup: boolean;
}

function RealtimeChatScreen() {
  const { user } = useUser();
  const { theme } = useTheme();
  const { t } = useTranslation();
  const [inputText, setInputText] = useState('');

  // Debug log user properties when component mounts or user changes
  React.useEffect(() => {
    if (__DEV__ && user) {
      console.log('🎭 [RealtimeChatScreen] User Debug Info:');
      console.log('  - User ID:', user.id);
      console.log('  - Full Name:', user.fullName);
      console.log('  - First Name:', user.firstName);
      console.log('  - Last Name:', user.lastName);
      console.log('  - Username:', user.username);
      console.log('  - Primary Email:', user.primaryEmailAddress?.emailAddress);
      console.log('  - Image URL:', user.imageUrl);
      console.log('  - Has Image:', user.hasImage);
      console.log('  - Profile Image URL:', user.imageUrl); // profileImageUrl is same as imageUrl
      console.log('  - All available user props:', Object.keys(user));
      console.log('  - Full user object:', user);
    }
  }, [user]);

  const { 
    messages, 
    sendMessage, 
    isConnected, 
    isReconnecting,
    connectionQuality,
    onlineUsers, 
    onlineCount, 
    error 
  } = useRealtime();

  // Filter out current user from online users for display  
  const otherUsers = useMemo(() => 
    onlineUsers.filter(onlineUser => onlineUser.id !== user?.id),
    [onlineUsers, user?.id]
  );
  
  // Only show friends bar when there are other users online
  const shouldShowFriendsBar = otherUsers.length > 0;
  
  // AromaFriendsStatusBar visibility toggle (user controlled)
  const [showFriendsBar, setShowFriendsBar] = useState(true);

  // PERF: Memoize processed messages to add grouping info.
  const processedMessages = useMemo((): ProcessedMessage[] => {
    return messages.map((msg, index) => {
      const prevMessageInTime = messages[index + 1];
      const nextMessageInTime = messages[index - 1];

      return {
        ...msg,
        isFirstInGroup: !prevMessageInTime || prevMessageInTime.user_id !== msg.user_id,
        isLastInGroup: !nextMessageInTime || nextMessageInTime.user_id !== msg.user_id,
      };
    });
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputText.trim() || !isConnected) return;
    try {
      await sendMessage(inputText);
      setInputText('');
    } catch (e) {
      console.error('Failed to send message:', e);
    }
  };

  // PERF: Memoize renderItem to prevent re-creation on each render
  const renderMessage = useCallback(
    ({ item }: { item: ProcessedMessage }) => (
      <ChatMessageComponent
        message={item}
        currentUserId={user?.id}
        isFirstInGroup={item.isFirstInGroup}
        isLastInGroup={item.isLastInGroup}
      />
    ),
    [user?.id],
  );

  // UX: Memoize the empty component to prevent re-creation
  const ListEmptyComponent = useMemo(
    () => (
      <View style={styles.emptyContainer}>
        <Text
          variant="bodyMedium"
          style={{ color: theme.colors.onSurfaceVariant, textAlign: 'center' }}
        >
          {isConnected ? 'No messages yet. Say hello! 👋' : 'Connecting...'}
        </Text>
      </View>
    ),
    [isConnected, theme.colors.onSurfaceVariant],
  );

  return (
    <ScreenWrapper
      showAppBar
      appBarProps={{ 
        title: 'Chat',
        subtitle: isReconnecting ? 'Reconnecting...' : 
                 connectionQuality === 'poor' ? 'Poor connection' :
                 connectionQuality === 'disconnected' ? 'Disconnected' :
                 isConnected ? 'Connected' : 'Connecting...',
        showBackButton: true,
        actions: [
          // Friends icon with online user count badge - toggles AromaFriendsStatusBar
          <View key="friends-icon" style={{ position: 'relative' }}>
            <IconButton
              icon="account-group"
              size={32}
              onPress={() => setShowFriendsBar(prev => !prev)}
              style={{ margin: 0 }}
            />
            <Badge
              visible={true}
              size={18}
              style={{
                position: 'absolute',
                top: 2,
                right: 2,
                backgroundColor: connectionQuality === 'good' ? theme.colors.primary :
                                connectionQuality === 'poor' ? theme.colors.secondary :
                                theme.colors.error,
              }}
            >
              {onlineCount}
            </Badge>
          </View>
        ]
      }}
      scrollable={false}
      style={{ backgroundColor: theme.colors.surface }}
    >
      <KeyboardAvoidingView
        style={styles.flexOne}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
      >
        {/* Aroma Friends Status Bar - Toggle controlled by friends icon */}
        {showFriendsBar && (
          <FriendsStatusBar 
            onlineUsers={otherUsers.length > 0 ? otherUsers : [{
              id: user?.id || 'current-user',
              name: user?.fullName || user?.primaryEmailAddress?.emailAddress || 'You',
              avatar: user?.imageUrl,
              online_at: new Date().toISOString()
            }]}
            totalCount={onlineCount}
            maxVisibleAvatars={4}
          />
        )}
        
        <View style={styles.flexOne}>
          <FlashList
            data={processedMessages}
            renderItem={renderMessage}
            keyExtractor={(item) => item.id}
            estimatedItemSize={80}
            inverted
            // This is the key change for the new behavior
            keyboardShouldPersistTaps="handled"
            maintainVisibleContentPosition={{ minIndexForVisible: 0 }}
            contentContainerStyle={styles.listContentContainer}
            ListEmptyComponent={ListEmptyComponent}
          />
        </View>

        <ChatInputBar
          placeholder={t('realtime.chat.placeholder')}
          value={inputText}
          onChangeText={setInputText}
          onSendMessage={handleSendMessage}
          disabled={!isConnected}
          isSendEnabled={isConnected && inputText.trim().length > 0}
        />
      </KeyboardAvoidingView>
    </ScreenWrapper>
  );
}

const styles = StyleSheet.create({
  flexOne: {
    flex: 1,
  },
  listContentContainer: {
    paddingVertical: 10,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
    // This is crucial for inverted lists to display the component correctly
    transform: [{ scaleY: -1 }],
  },
});

export default React.memo(RealtimeChatScreen);
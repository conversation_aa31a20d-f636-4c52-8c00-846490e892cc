import React from 'react';
import { View } from 'react-native';
import { Button, Text } from 'react-native-paper';
import { useRouter } from 'expo-router';
import { ScreenWrapper } from '@/shared/components/layout/screen-wrapper';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/shared/hooks/use-theme';
import { useButtonStyles, useButtonContentStyles } from '@/shared/styles/button-styles';

/**
 * Demo Center Screen - Hub for all demo and test screens
 * Only accessible in development mode via drawer navigation
 */
export default function DemoCenterScreen() {
  const router = useRouter();
  const { t } = useTranslation(['homescreen', 'general']);
  const { theme } = useTheme();
  const buttonStyles = useButtonStyles();
  const buttonContentStyles = useButtonContentStyles();

  // Demo links data matching the ones from drawer and welcome screen
  const demoLinks = [
    {
      label: t('homescreen:navigation.accordionDemo'),
      route: '/demo/accordion-demo',
      icon: '📋'
    },
    {
      label: t('homescreen:navigation.accordionDemoV2'),
      route: '/demo/accordion-demo-v2', 
      icon: '📝'
    },
    {
      label: t('homescreen:hapticsDemo'),
      route: '/demo/haptics-demo',
      icon: '📳'
    },
    {
      label: t('general:debugIntegration'),
      route: '/debug-integration',
      icon: '🐛'
    },
  ];

  const handleNavigation = (route: string) => {
    try {
      router.push(route as any);
    } catch (error) {
      console.error('Navigation error to:', route, error);
    }
  };

  return (
    <ScreenWrapper
      showAppBar={true}
      appBarProps={{
        title: 'Demo Center',
        showBackButton: true
      }}
      scrollable={true}
    >
      <View style={{ flex: 1, padding: 24 }}>
        {/* Header */}
        <View style={{ alignItems: 'center', marginBottom: 32 }}>
          <Text variant="displaySmall" style={{ 
            textAlign: 'center', 
            marginBottom: 16, 
            color: theme.colors.primary 
          }}>
            🛠️ Demo Center
          </Text>
          <Text variant="bodyLarge" style={{ 
            textAlign: 'center', 
            color: theme.colors.onSurfaceVariant, 
            maxWidth: 320 
          }}>
            Development and testing screens hub
          </Text>
        </View>

        {/* Demo Links */}
        <View style={{ width: '100%', gap: 12 }}>
          {demoLinks.map((link, index) => (
            <Button
              key={index}
              mode="outlined"
              onPress={() => handleNavigation(link.route)}
              style={buttonStyles.large}
              contentStyle={buttonContentStyles.large}
            >
              {link.icon} {link.label}
            </Button>
          ))}
        </View>
      </View>
    </ScreenWrapper>
  );
}
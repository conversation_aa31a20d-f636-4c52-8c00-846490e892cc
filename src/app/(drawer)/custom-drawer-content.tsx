import React, { useState } from 'react';
import { View, StyleSheet, Alert, Platform } from 'react-native';
import { DrawerContentScrollView } from '@react-navigation/drawer';
import {
  Portal,
  Text,
  Switch,
  TouchableRipple,
  Drawer,
  Dialog,
  Button,
  List,
} from 'react-native-paper';
import { useAuth } from '@clerk/clerk-expo';
import { useRouter } from 'expo-router';
import { useTheme } from '@/shared/hooks/use-theme';
import { haptics } from '@/shared/utils/haptics';
import { useTranslation } from 'react-i18next';


// Drawer items data - using i18n keys from old implementation
const DrawerItemsData = [
  {
    labelKey: 'recipe.createRecipe',
    icon: 'chef-hat',
    route: '/create-recipe/',
    key: 0,
  },
  {
    labelKey: 'recipe.oilDilutionCalculator',
    icon: 'beaker',
    route: '/oil-dilution-calculator',
    key: 2,
  },
  {
    label: 'Chat em Tempo Real',
    icon: 'chat',
    route: '/realtime-chat',
    key: 3,
  },
];

const DevOnlyDrawerItemsData = [
  {
    labelKey: 'recipe.savedProtocols',
    icon: 'bookmark',
    route: '/saved-protocols',
    key: 1,
  },
];


export function CustomDrawerContent(props: any) {
  const { theme, spacing, toggleTheme, isDarkTheme } = useTheme();
  const { signOut } = useAuth();
  const router = useRouter();
  const { t } = useTranslation(['common', 'homescreen', 'general', 'recipe', 'modal']);
  const [activeDrawerItem, setActiveDrawerItem] = useState<number>(0);
  const [showSignOutDialog, setShowSignOutDialog] = useState(false);

  const _setActiveDrawerItem = (key: number) => setActiveDrawerItem(key);

  // Sign out functionality
  const performSignOut = async () => {
    try {
      await signOut();
      setShowSignOutDialog(false);
      router.replace('/welcome');
    } catch (error) {
      console.error('Sign out error:', error);
      setShowSignOutDialog(false);
      Alert.alert(
        t('homescreen:error.title'),
        t('homescreen:error.signOutFailed'),
        [{ text: t('homescreen:alerts.ok') }]
      );
    }
  };

  const handleSignOut = async () => {
    if (Platform.OS === 'web') {
      setShowSignOutDialog(true);
    } else {
      Alert.alert(
        t('homescreen:signOut.title'),
        t('homescreen:signOut.message'),
        [
          { text: t('homescreen:signOut.cancel'), style: 'cancel' },
          { text: t('homescreen:signOut.confirm'), style: 'destructive', onPress: performSignOut },
        ]
      );
    }
  };

  const styles = getStyles(theme, spacing);


  return (
    <DrawerContentScrollView
      alwaysBounceVertical={false}
      style={[
        styles.drawerContent,
        {
          backgroundColor: theme.colors.surface,
        },
      ]}
    >
      {/* Main Navigation Items */}
      <Drawer.Section title={t('homescreen:navigation.main')}>
        {DrawerItemsData.map((item) => (
          <Drawer.Item
            key={item.key}
            label={'labelKey' in item ? t(item.labelKey) : item.label}
            icon={item.icon}
            active={activeDrawerItem === item.key}
            onPress={() => {
              haptics.light();
              _setActiveDrawerItem(item.key);
              router.push(item.route as any);
              props.navigation.closeDrawer();
            }}
          />
        ))}
        {/* Dev-only main navigation items */}
        {__DEV__ && DevOnlyDrawerItemsData.map((item) => (
          <Drawer.Item
            key={item.key}
            label={t(item.labelKey)}
            icon={item.icon}
            active={activeDrawerItem === item.key}
            onPress={() => {
              haptics.light();
              _setActiveDrawerItem(item.key);
              router.push(item.route as any);
              props.navigation.closeDrawer();
            }}
          />
        ))}
      </Drawer.Section>

      {/* Developer Items - Only in DEV */}
      {__DEV__ && (
        <Drawer.Section title={t('homescreen:devTools')}>
          <Drawer.Item
            key={99}
            label={t('common:general.demoCenter')}
            icon="bug-check"
            active={activeDrawerItem === 99}
            onPress={() => {
              haptics.light();
              _setActiveDrawerItem(99);
              router.push('/demo-center' as any);
              props.navigation.closeDrawer();
            }}
          />
        </Drawer.Section>
      )}

      {/* Preferences */}
      <Drawer.Section title={t('homescreen:preferences')}>
        <TouchableRipple onPress={() => {
          haptics.toggle();
          toggleTheme();
        }}>
          <View style={[styles.preference, styles.v3Preference]}>
            <Text variant="labelLarge">{t('common:general.darkTheme')}</Text>
            <View pointerEvents="none">
              <Switch value={isDarkTheme} />
            </View>
          </View>
        </TouchableRipple>
      </Drawer.Section>

        <TouchableRipple onPress={() => {
          haptics.destructive();
          handleSignOut();
        }}>
          <View style={[styles.preference, styles.v3Preference]}>
            <Text variant="labelLarge" style={{ color: theme.colors.error }}>{t('common:modal.signOut')}</Text>
            <List.Icon icon="logout" color={theme.colors.error} />
          </View>
        </TouchableRipple>

      {/* Sign Out Confirmation Dialog */}
      <Portal>
        <Dialog visible={showSignOutDialog} onDismiss={() => setShowSignOutDialog(false)}>
          <Dialog.Title>{t('homescreen:signOut.title')}</Dialog.Title>
          <Dialog.Content>
            <Text>{t('homescreen:signOut.message')}</Text>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setShowSignOutDialog(false)}>
              {t('homescreen:signOut.cancel')}
            </Button>
            <Button onPress={performSignOut}>
              {t('homescreen:signOut.confirm')}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </DrawerContentScrollView>
  );
}

const getStyles = (_theme: any, spacing: any) => StyleSheet.create({
  drawerContent: {
    flex: 1,
  },
  preference: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  v3Preference: {
    height: 56,
    paddingHorizontal: 28,
  },
  annotation: {
    marginHorizontal: spacing.xLarge,
    marginVertical: spacing.none,
  },
});

export default CustomDrawerContent;


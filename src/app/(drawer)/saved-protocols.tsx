import React, { useState, useCallback } from 'react';
import { View, FlatList } from 'react-native';
import { Text, Button, Card, IconButton, Chip, Divider, TouchableRipple } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { useFocusEffect } from '@react-navigation/native';
import { ScreenWrapper } from '@/shared/components/layout/screen-wrapper';
import { useHomeScreenRefresh } from '@/shared/hooks';
import { getLastRefreshTimestamp } from '@/shared/utils/refresh-utils';
import { useTheme } from '@/shared/hooks/use-theme';
// import { useModalManager } from '@/shared/hooks/use-modal-manager'; // TODO: Re-enable after modal refactoring
import { useUser, useAuth } from '@clerk/clerk-expo';
import { savedRecipesService } from '@/shared/services/supabase/saved-recipes.service';
import { haptics } from '@/shared/utils/haptics';
// import { ProtocolDetailModal } from '@/features/create-recipe/components/modals/protocol-detail-modal'; // TODO: Re-enable after refactoring
import type { SavedProtocol } from '@/shared/services/supabase/types/saved-recipe.types';

export default function SavedProtocolsScreen() {
  const { t } = useTranslation(['homescreen']);
  const { theme, spacing, borderRadius } = useTheme();
  const [lastRefreshTime, setLastRefreshTime] = useState<Date | null>(null);
  const { user } = useUser();
  const { getToken } = useAuth();
  
  // Saved protocols state
  const [savedProtocols, setSavedProtocols] = useState<SavedProtocol[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Protocol detail modal state - TODO: Re-enable after modal refactoring
  // const { modalRef: protocolModalRef, presentModal: presentProtocolModal, dismissModal: dismissProtocolModal } = useModalManager();
  // const [selectedProtocol, setSelectedProtocol] = useState<SavedProtocol | null>(null);

  // Load saved protocols
  const loadSavedProtocols = useCallback(async () => {
    if (!user?.id) {
      setError('Please log in to view your saved protocols');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const clerkToken = await getToken({ template: 'supabase' });
      if (!clerkToken) {
        throw new Error('Authentication token not available');
      }
      
      const protocols = await savedRecipesService.getUserProtocols(user.id, clerkToken);
      setSavedProtocols(protocols);
      
      if (__DEV__) {
        console.log('📋 Loaded saved protocols:', {
          count: protocols.length,
          userId: user.id
        });
      }
    } catch (err) {
      console.error('Failed to load saved protocols:', err);
      setError(err instanceof Error ? err.message : 'Failed to load saved protocols');
      haptics.error();
    } finally {
      setLoading(false);
    }
  }, [user?.id]); // Remove getToken from dependencies to prevent infinite loop

  // Load protocols on screen focus
  useFocusEffect(
    useCallback(() => {
      loadSavedProtocols();
    }, [loadSavedProtocols])
  );

  // Delete protocol handler
  const handleDeleteProtocol = useCallback(async (protocolId: string) => {
    if (!user?.id) return;
    
    try {
      const clerkToken = await getToken({ template: 'supabase' });
      if (!clerkToken) throw new Error('Authentication token not available');
      
      await savedRecipesService.deleteProtocol(protocolId, user.id, clerkToken);
      setSavedProtocols(prev => prev.filter(protocol => protocol.id !== protocolId));
      haptics.success();
      
      if (__DEV__) {
        console.log('🗑️ Deleted saved protocol:', { protocolId, userId: user.id });
      }
    } catch (err) {
      console.error('Failed to delete protocol:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete protocol');
      haptics.error();
    }
  }, [user?.id, getToken]); // Keep getToken here as this is only called on user action

  // Protocol detail modal handler - TODO: Re-enable after modal refactoring
  const handleViewProtocol = useCallback((protocol: SavedProtocol) => {
    // Placeholder: Show coming soon feedback
    haptics.light();
    console.log('Protocol detail view coming soon after refactoring');
  }, []);

  // Pull-to-refresh functionality
  const { refreshControlProps } = useHomeScreenRefresh({
    updateGreeting: () => {
      console.log('📋 Saved Protocols: Updating greeting');
    },
    updateTimestamp: () => {
      console.log('📋 Saved Protocols: Updating timestamp');
      setLastRefreshTime(new Date());
    },
    additionalRefreshLogic: async () => {
      console.log('📋 Saved Protocols: Refreshing saved protocols');
      await loadSavedProtocols();
    },
  });

  // Calculate protocol stats
  const getProtocolStats = (protocol: SavedProtocol) => {
    const totalRecipes = protocol.recipes.length;
    const totalDrops = protocol.recipes.reduce((sum, recipe) => sum + recipe.total_drops, 0);
    const totalVolume = protocol.recipes.reduce((sum, recipe) => sum + recipe.total_volume_ml, 0);
    const totalOils = protocol.recipes.reduce((sum, recipe) => sum + recipe.oil_count, 0);
    
    return { totalRecipes, totalDrops, totalVolume, totalOils };
  };

  // Get time slot chips for a protocol
  const getTimeSlotChips = (protocol: SavedProtocol) => {
    return protocol.recipes.map(recipe => {
      const timeSlot = recipe.time_slot;
      return {
        label: timeSlot === 'mid-day' ? 'Mid-Day' : timeSlot.charAt(0).toUpperCase() + timeSlot.slice(1),
        color: timeSlot === 'morning' ? theme.colors.primary : 
               timeSlot === 'mid-day' ? theme.colors.secondary : 
               theme.colors.tertiary
      };
    });
  };

  return (
    <ScreenWrapper
      scrollable={true}
      showAppBar={true}
      appBarProps={{ 
        title: 'Saved Protocols',
        showBackButton: true
      }}
      enableRefresh={true}
      refreshing={refreshControlProps.refreshing}
      onRefresh={refreshControlProps.onRefresh}
      customBackgroundColor={theme.colors.background}
    >
      {/* Header Section */}
      <View style={{
        marginBottom: spacing.md,
        backgroundColor: theme.colors.surface,
        borderRadius: borderRadius.card,
        padding: spacing.cardPadding,
      }}>
        <Text variant="headlineSmall">Saved Protocols</Text>
        <Text variant="bodyMedium" style={{ marginTop: spacing.sm, color: theme.colors.onSurfaceVariant }}>
          Your complete daily aromatherapy protocols. Each protocol contains multiple recipes for different times of day.
        </Text>
        {lastRefreshTime && (
          <Text variant="bodySmall" style={{ marginTop: spacing.large, color: theme.colors.onSurfaceVariant, opacity: 0.7 }}>
            {getLastRefreshTimestamp(lastRefreshTime)}
          </Text>
        )}
      </View>

      {/* Error State */}
      {error && (
        <View style={{
          backgroundColor: theme.colors.errorContainer,
          padding: spacing.md,
          borderRadius: borderRadius.card,
          marginBottom: spacing.md,
        }}>
          <Text style={{ color: theme.colors.onErrorContainer, textAlign: 'center' }}>
            {error}
          </Text>
        </View>
      )}

      {/* Loading State */}
      {loading && (
        <View style={{
          backgroundColor: theme.colors.surface,
          borderRadius: borderRadius.card,
          padding: spacing.lg,
          alignItems: 'center',
          marginBottom: spacing.md,
        }}>
          <Text variant="bodyLarge">Loading your saved protocols...</Text>
        </View>
      )}

      {/* Empty State */}
      {!loading && !error && savedProtocols.length === 0 && (
        <View style={{
          backgroundColor: theme.colors.surface,
          borderRadius: borderRadius.card,
          padding: spacing.lg,
          alignItems: 'center',
          marginBottom: spacing.md,
        }}>
          <Text variant="headlineSmall" style={{ marginBottom: spacing.md, color: theme.colors.onSurfaceVariant }}>
            No saved protocols yet
          </Text>
          <Text variant="bodyMedium" style={{ textAlign: 'center', marginBottom: spacing.lg, color: theme.colors.onSurfaceVariant }}>
            Complete the recipe wizard to create your first personalized aromatherapy protocol with morning, mid-day, and night recipes.
          </Text>
          <Button 
            mode="contained" 
            icon="plus" 
            onPress={() => {
              // Navigate to create recipe - would need router here
              haptics.light();
            }}
          >
            Create Protocol
          </Button>
        </View>
      )}

      {/* Protocols List */}
      {!loading && !error && savedProtocols.length > 0 && (
        <FlatList
          data={savedProtocols}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
          scrollEnabled={false}
          renderItem={({ item: protocol }) => {
            const stats = getProtocolStats(protocol);
            const timeSlotChips = getTimeSlotChips(protocol);
            
            return (
              <TouchableRipple
                onPress={() => handleViewProtocol(protocol)}
                borderless
                style={{
                  borderRadius: borderRadius.card,
                }}
              >
                <Card style={{
                  marginBottom: spacing.md,
                  backgroundColor: theme.colors.surface,
                }}>
                  <Card.Content>
                  {/* Protocol Header */}
                  <View style={{ 
                    flexDirection: 'row', 
                    justifyContent: 'space-between', 
                    alignItems: 'flex-start', 
                    marginBottom: spacing.sm 
                  }}>
                    <View style={{ flex: 1 }}>
                      <Text variant="titleMedium" style={{ fontWeight: 'bold', marginBottom: spacing.xs }}>
                        {protocol.protocol_name}
                      </Text>
                      <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                        Created {new Date(protocol.created_at).toLocaleDateString()}
                      </Text>
                    </View>
                    <IconButton
                      icon="delete"
                      size={20}
                      onPress={() => handleDeleteProtocol(protocol.id)}
                      style={{ margin: 0 }}
                    />
                  </View>

                  {/* Health Concern */}
                  <Text variant="bodyMedium" style={{ 
                    marginBottom: spacing.sm, 
                    color: theme.colors.onSurface,
                    fontStyle: 'italic'
                  }}>
                    For: {protocol.health_concern_original}
                  </Text>

                  {/* Time Slots */}
                  <View style={{ 
                    flexDirection: 'row', 
                    flexWrap: 'wrap', 
                    gap: spacing.xs, 
                    marginBottom: spacing.sm 
                  }}>
                    {timeSlotChips.map((chip, index) => (
                      <Chip
                        key={index}
                        compact
                        style={{
                          backgroundColor: chip.color
                        }}
                        textStyle={{ 
                          color: theme.colors.onPrimary,
                          fontSize: 12
                        }}
                      >
                        {chip.label}
                      </Chip>
                    ))}
                  </View>

                  <Divider style={{ marginVertical: spacing.sm }} />

                  {/* Protocol Stats */}
                  <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                    <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                      {stats.totalRecipes} recipes
                    </Text>
                    <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                      {stats.totalDrops} drops total
                    </Text>
                    <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                      {stats.totalOils} oils
                    </Text>
                  </View>

                  {/* Individual Recipe Summary */}
                  {protocol.recipes.length > 0 && (
                    <>
                      <Divider style={{ marginVertical: spacing.sm }} />
                      <Text variant="bodySmall" style={{ 
                        color: theme.colors.onSurfaceVariant, 
                        fontWeight: 'bold',
                        marginBottom: spacing.xs 
                      }}>
                        Recipe Details:
                      </Text>
                      {protocol.recipes.map((recipe, index) => (
                        <Text key={recipe.id} variant="bodySmall" style={{ 
                          color: theme.colors.onSurfaceVariant,
                          marginLeft: spacing.sm 
                        }}>
                          • {recipe.time_slot === 'mid-day' ? 'Mid-Day' : 
                             recipe.time_slot.charAt(0).toUpperCase() + recipe.time_slot.slice(1)}: {recipe.total_drops} drops, {recipe.oil_count} oils
                        </Text>
                      ))}
                    </>
                  )}
                </Card.Content>
                </Card>
              </TouchableRipple>
            );
          }}
        />
      )}

      {/* Protocol Detail Modal - TODO: Re-enable after modal refactoring */}
      {/*
      <ProtocolDetailModal
        modalRef={protocolModalRef}
        visible={false}
        onDismiss={dismissProtocolModal}
        protocol={selectedProtocol}
      />
      */}
    </ScreenWrapper>
  );
}
import { <PERSON><PERSON><PERSON><PERSON>, useAuth } from '@clerk/clerk-expo';
import { Paper<PERSON>rovider, Portal } from 'react-native-paper';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { Drawer } from 'expo-router/drawer';
import { useRouter, useSegments } from 'expo-router';
import { useEffect } from 'react';
import * as SplashScreen from 'expo-splash-screen';
import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';
import { PostHogProvider } from 'posthog-react-native';
import * as Sentry from '@sentry/react-native';
import { initializeSentry } from '@/shared/utils/sentry-config';

// Import unified preferences system
import { UserPreferencesProvider, useUserPreferences } from '@/shared/contexts/user-preferences-context';
import { HapticsProvider } from '@/shared/providers/haptics-provider';
import { RealtimeProvider } from '@/shared/providers/realtime-provider';
import { PostHogClerkIntegration } from '@/shared/services/integrations/posthog-clerk-integration';
import { CustomDrawerContent } from './(drawer)/custom-drawer-content';
import { useTranslation } from 'react-i18next';

// Prevent the splash screen from auto-hiding before we can manage it.
SplashScreen.preventAutoHideAsync();

// Initialize Sentry
initializeSentry();

// Configure token cache for Clerk
const tokenCache = {
  async getToken(key: string) {
    try {
      return await SecureStore.getItemAsync(key);
    } catch {
      return null;
    }
  },
  async saveToken(key: string, value: string) {
    try {
      return await SecureStore.setItemAsync(key, value);
    } catch {
      return;
    }
  },
};

// PostHog configuration - only for native platforms
const posthogOptions = {
  apiKey: process.env.EXPO_PUBLIC_POSTHOG_API_KEY!,
  host: process.env.EXPO_PUBLIC_POSTHOG_HOST || 'https://us.i.posthog.com',
  autocapture: {
    captureTouches: true, // Track touch events
    captureScreens: true, // Track screen navigation
    ignoreLabels: ['sensitive-data', 'private'], // Ignore specific labels
    customLabelProp: 'ph-label', // Custom label prop
    maxElementsCaptured: 20, // Limit elements captured per event
    noCaptureProp: 'ph-no-capture', // Prop to disable capture on specific elements
    propsToCapture: ['testID', 'accessibilityLabel'], // Specific props to capture
  },
  captureConsoleErrors: true,
  enableSessionRecording: true,
  debug: false, // Disable verbose debug logs
};

// Create a conditional PostHog wrapper component
const ConditionalPostHogProvider = ({ children }: { children: React.ReactNode }) => {
  // Only use PostHog on native platforms, not web during SSR
  if (Platform.OS === 'web' && typeof window === 'undefined') {
    // Server-side rendering - skip PostHog
    return <>{children}</>;
  }
  
  return <PostHogProvider {...posthogOptions}>{children}</PostHogProvider>;
};

/**
 * AppProviders component ensures correct provider nesting order.
 * PaperProvider MUST wrap BottomSheetModalProvider so that modals inherit custom theme.
 */
const AppProviders = ({ children }: { children: React.ReactNode }) => {
  const { theme } = useUserPreferences();

  return (
    <PaperProvider theme={theme}>
      <BottomSheetModalProvider>
        <SafeAreaProvider>
          <ConditionalPostHogProvider>
            <RealtimeProvider>
              <HapticsProvider>
                <Portal.Host>
                  {children}
                </Portal.Host>
              </HapticsProvider>
            </RealtimeProvider>
          </ConditionalPostHogProvider>
        </SafeAreaProvider>
      </BottomSheetModalProvider>
    </PaperProvider>
  );
};

/**
 * Root Drawer Navigation component that replaces the nested drawer structure
 * Following Expo Router docs pattern to eliminate extra spacing
 */
const RootDrawerNavigation = () => {
  const { theme } = useUserPreferences();
  const { t } = useTranslation(['homescreen', 'common']);

  return (
    <Drawer
      drawerContent={(props) => <CustomDrawerContent {...props} />}
      screenOptions={{
        headerShown: false, // We use our custom AppBar in ScreenWrapper
        drawerActiveTintColor: theme.colors.primary,
        drawerInactiveTintColor: theme.colors.onSurfaceVariant,
      }}
    >
      <Drawer.Screen
        name="(drawer)/(tabs)"
        options={{
          title: t('homescreen:navigation.home'),
          drawerItemStyle: { display: 'none' }, // Hide from default drawer since we use custom content
        }}
      />
      <Drawer.Screen
        name="(drawer)/saved-protocols"
        options={{
          title: t('homescreen:savedRecipes'),
          drawerItemStyle: { display: 'none' }, // Hide from default drawer since we use custom content
        }}
      />
    </Drawer>
  );
};

/**
 * This component handles the core logic for authentication, theme switching,
 * and splash screen management. It ensures that hooks are called unconditionally.
 */
const InitialLayout = () => {
  // --- 1. Call all hooks at the top level ---
  const { isLoaded, isSignedIn } = useAuth();
  const segments = useSegments();
  const router = useRouter();

  // --- 2. Manage side effects with useEffect ---

  // Language initialization is now handled by LanguageInitializationProvider
  // at a higher level in the component hierarchy to prevent hook violations

  // Effect for global error handling to log errors to terminal
  useEffect(() => {
    console.log('🚀 Setting up global error handlers...');
    
    // Check if ErrorUtils is available (not available in all environments)
    if (typeof ErrorUtils !== 'undefined' && ErrorUtils.getGlobalHandler) {
      const originalErrorHandler = ErrorUtils.getGlobalHandler();
      
      const customErrorHandler = (error: any, isFatal?: boolean) => {
        // Log to terminal with clear markers
        console.error('🚨 DEVICE ERROR CAPTURED:', {
          message: error.message || 'Unknown error',
          stack: error.stack || 'No stack trace',
          isFatal: isFatal || false,
          timestamp: new Date().toISOString(),
          errorName: error.name || 'Unknown'
        });
        
        // Check for accessibility errors specifically
        const errorString = String(error.message || error);
        if (errorString.includes('accessibility') || errorString.includes('slider') || errorString.includes('Invalid accessibility role')) {
          console.error('🎯 ACCESSIBILITY ERROR DETECTED:', errorString);
          console.error('📍 This error is likely from a component with incorrect accessibilityRole property');
        }
        
        // Check for navigation errors
        if (errorString.includes('navigation') || errorString.includes('route') || errorString.includes('UIFrameGuarded')) {
          console.error('🧭 NAVIGATION ERROR DETECTED:', errorString);
        }
        
        // Call original handler if it exists
        if (originalErrorHandler) {
          originalErrorHandler(error, isFatal);
        }
      };
      
      ErrorUtils.setGlobalHandler(customErrorHandler);
      
      // Cleanup function to restore original handler
      return () => {
        if (originalErrorHandler) {
          ErrorUtils.setGlobalHandler(originalErrorHandler);
        }
      };
    } else {
      // Fallback: Override console.error to catch errors
      console.log('⚠️ ErrorUtils not available, using console.error override');
      
      const originalConsoleError = console.error;
      
      console.error = (...args) => {
        // Log with clear markers
        originalConsoleError('🚨 CONSOLE ERROR CAPTURED:', ...args);
        
        // Check for accessibility errors specifically
        const errorString = args.join(' ');
        if (errorString.includes('accessibility') || errorString.includes('slider') || errorString.includes('Invalid accessibility role')) {
          originalConsoleError('🎯 ACCESSIBILITY ERROR DETECTED:', errorString);
          originalConsoleError('📍 This error is likely from a component with incorrect accessibilityRole property');
        }
        
        // Check for navigation errors
        if (errorString.includes('navigation') || errorString.includes('route') || errorString.includes('UIFrameGuarded')) {
          originalConsoleError('🧭 NAVIGATION ERROR DETECTED:', errorString);
        }
      };
      
      // Cleanup function to restore original console.error
      return () => {
        console.error = originalConsoleError;
      };
    }
  }, []);

  // NativeWind automatically follows system preference with darkMode: 'media'
  // No manual synchronization needed

  // Effect for handling authentication-based routing
  useEffect(() => {
    // Wait until Clerk has loaded its authentication state
    if (!isLoaded) {
      return;
    }

    const inAuthGroup = segments[0] === '(auth)';
    const isNavigationDemo = segments[0] === 'navigation-demo';

    // Allow navigation-demo for both authenticated and unauthenticated users
    if (isNavigationDemo) {
      return; // Don't redirect, allow access to navigation demo
    }

    // If the user is signed in but is currently in the auth flow (e.g., they pressed back),
    // redirect them to the main app.
    if (isSignedIn && inAuthGroup) {
      router.replace('/(drawer)/(tabs)');
    }
    // If the user is not signed in and is not in the auth flow,
    // redirect them to the welcome screen.
    else if (!isSignedIn && !inAuthGroup) {
      router.replace('/welcome');
    }
  }, [isLoaded, isSignedIn, segments, router]);

  // Effect for hiding the splash screen once everything is ready
  useEffect(() => {
    if (isLoaded) {
      SplashScreen.hideAsync();
    }
  }, [isLoaded]);

  // --- 3. Conditional return after all hooks are called ---

  // If Clerk is not loaded yet, return null. The splash screen will remain visible.
  if (!isLoaded) {
    return null;
  }

  // --- 4. Render the main content ---
  // No PaperProvider here anymore - it's handled in AppProviders
  return (
    <>
      <PostHogClerkIntegration />
      <RootDrawerNavigation />
    </>
  );
};

/**
 * This is the root layout for the entire application.
 * It sets up the top-level providers, like Clerk for authentication.
 */
function RootLayout() {
  // You can load fonts or other global assets here if needed
  // const [fontsLoaded] = useFonts({...});

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <ClerkProvider 
        publishableKey={process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY!}
        tokenCache={tokenCache}
      >
        <UserPreferencesProvider>
          {/* AppProviders ensures PaperProvider wraps BottomSheetModalProvider */}
          <AppProviders>
            <InitialLayout />
          </AppProviders>
        </UserPreferencesProvider>
      </ClerkProvider>
    </GestureHandlerRootView>
  );
}

// Export the RootLayout wrapped with Sentry for error tracking and performance monitoring
export default Sentry.wrap(RootLayout);

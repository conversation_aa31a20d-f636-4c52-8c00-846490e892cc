# Assets Directory

This directory contains static assets for the AromaCHAT Go application.

## Structure

- `images/` - Application images and graphics
- `icons/` - Icon files and illustrations  
- `fonts/` - Custom font files

## Usage

Import assets using Expo's asset system:

```tsx
import { Image } from 'expo-image';

// For images
<Image source={require('@/assets/images/logo.png')} />

// For icons in components
import { MaterialIcons } from '@expo/vector-icons';
<MaterialIcons name="home" size={24} />
```

## Note

Default Expo assets (icon.png, splash.png, etc.) should be placed in the root `assets/` directory as configured in `app.json`.

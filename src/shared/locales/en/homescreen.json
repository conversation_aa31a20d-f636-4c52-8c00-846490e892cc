{"appTitle": "AromaChat", "greeting": "Welcome, {{name}}! 👨‍🍳", "chef": "Chef", "quickActions": "Quick Actions", "mostUsedFeatures": "Your most-used features", "instagramModalDebug": "Instagram Modal Debug", "instagramModalDebugDescription": "Test Instagram-style modal with swipe gestures", "savedRecipes": "Saved <PERSON><PERSON><PERSON>", "savedRecipesDescription": "Your favorite recipes will appear here.", "pullToRefresh": "Pull down to refresh content 🔄", "comingSoon": "Coming Soon", "recipeCollections": "Recipe Collections", "cookingHistory": "Cooking History", "mealPlanning": "Meal Planning", "all": "All", "categories": "Categories", "recent": "Recent", "cookingAssistant": "Cooking Assistant", "welcomeDrawer": "Welcome, {{name}}! 👨‍🍳", "drawerFooter": "Theme switching and settings available.\nYour culinary journey starts here!", "version": "AromaChat v1.0.0", "recipe": "Recipe", "haptics": "Haptics", "signOut": {"title": "Sign Out", "message": "Are you sure you want to sign out?", "confirm": "Sign Out", "cancel": "Cancel"}, "error": {"title": "Error", "signOutFailed": "Failed to sign out. Please try again."}, "title": "AromaChat", "comingSoonMessage": "{{feature}} will be available soon!", "greetings": {"lateNight": "Good Night", "morning": "Good Morning", "afternoon": "Good Afternoon", "evening": "Good Evening", "night": "Good Night"}, "navigation": {"home": "Home", "main": "Main", "profile": "Profile", "accordionDemo": "Accordion <PERSON>", "accordionDemoV2": "Accordion Demo V2", "withoutAvatars": "Without avatars"}, "preferences": "Preferences", "alerts": {"ok": "OK", "mealPlanningComingSoon": "Meal planning will be available soon!", "shoppingListComingSoon": "Shopping list will be available soon!"}}
{"healthConcern": {"title": "What's your health concern?", "subtitle": "Describe your main health concern or symptoms", "placeholder": "How can I help?", "helperText": "Tips for better results", "greeting": "Hello", "defaultName": "Chef", "progressTitle": "📝 Your Health Concern", "validationMessages": {"submitting": "🔄 Submitting your health concern...", "ready": "✅ Ready to continue", "needMore": "💡 Need {count} more characters", "provideMore": "💡 Provide more details for better recommendations"}, "tipsSection": {"title": "💡 How to describe your health concern", "description": "The more specific you are, the better our AI can analyze potential causes and recommend appropriate essential oil protocols.\n\nTap any suggestion below to get started, or describe your concern in your own words."}, "suggestions": [{"text": "Di<PERSON><PERSON><PERSON>y falling asleep at night", "expanded": "Difficulty falling asleep at night, mind racing with thoughts"}, {"text": "Frequent headaches during stress", "expanded": "Frequent headaches during stressful work periods"}, {"text": "Digestive discomfort after meals", "expanded": "Digestive discomfort after meals, especially fatty foods"}, {"text": "Muscle tension and stiffness", "expanded": "Muscle tension and stiffness, especially in neck and shoulders"}], "errors": {"refreshFailed": "Failed to refresh. Please try again."}, "tips": ["Be specific about symptoms and duration", "Mention any triggers you've noticed", "Include how it affects your daily life", "Note any treatments you've tried"], "validation": {"required": "Health concern is required", "minLength": "Please provide more details (at least 10 characters)", "maxLength": "Please keep your description under 500 characters"}, "button": "Continue to Demographics"}, "demographics": {"title": "Tell us about yourself", "subtitle": "Your demographics help us provide more personalized essential oil recommendations based on age and gender-specific health patterns", "category": "Age Category", "gender": {"label": "Gender", "male": "Male", "female": "Female"}, "ageCategory": {"label": "Age Category", "placeholder": "Select age category"}, "specificAge": {"label": "Specific Age", "placeholder": "Your age", "suffix": "years", "helperText": "Age helps us recommend appropriate essential oil concentrations and safety guidelines"}, "privacy": {"title": "🔒 Privacy & Safety", "points": ["Your demographic information is used only for personalized recommendations", "Age-appropriate safety guidelines are applied to all suggestions", "Gender-specific health patterns help improve accuracy", "All data is processed securely and never shared"]}, "validation": {"genderRequired": "Please select your gender", "ageCategoryRequired": "Please select your age category", "ageRequired": "Please enter your age", "ageInvalid": "Please enter a valid age between 1 and 120"}, "button": "Analyze Potential Causes"}, "ageCategoryOptions": {"child": "Child", "teen": "Teen", "adult": "Adult", "senior": "Senior", "elderly": "Elderly", "unknown": "Unknown"}, "causes": {"title": "Potential Causes", "subtitle": "Choose {min}-{max} factors that might be contributing to your health concerns", "pleaseSelect": "Please select potential causes", "subtitlePrefix": "Choose 1-", "subtitleSuffix": " factors that might be contributing to your health concerns", "counter": "Selected: {count}/{max}", "counterPrefix": "Selected: ", "validation": {"minSelection": "Please select at least {min} cause{plural}", "maxSelection": "Please remove {excess} selection{plural}"}, "button": "Analyze Symptoms", "feedback": {"needMore": "Select at least {needed} more cause{plural}", "tooMany": "Please remove {excess} selection{plural}", "selected": "{count} cause{plural} selected"}}, "symptoms": {"title": "Your Symptoms", "subtitle": "Choose {min}-{max} symptoms you are currently experiencing", "pleaseSelect": "Please select your symptoms", "subtitlePrefix": "Choose 1-", "subtitleSuffix": " symptoms you are currently experiencing", "counter": "Selected: {count}/{max}", "counterPrefix": "Selected: ", "validation": {"minSelection": "Please select at least {min} symptom{plural}", "maxSelection": "Please remove {excess} selection{plural}"}, "button": "Complete Selection", "feedback": {"needMore": "Select at least {needed} more symptom{plural}", "tooMany": "Please remove {excess} selection{plural}", "selected": "{count} symptom{plural} selected"}}, "streaming": {"found": "found", "states": {"loading": "loading", "streaming": "streaming", "completed": "completed", "error": "error"}, "terminal": {"potentialCausesAnalysis": "Potential Causes Analysis", "causesSubtitle": "Understanding factors that may contribute to your symptoms.", "potentialSymptomsAnalysis": "Potential Symptoms Analysis", "symptomsSubtitle": "Identifying symptoms that may manifest based on your selected causes.", "therapeuticPropertiesAnalysis": "Therapeutic Properties Analysis", "propertiesSubtitle": "Finding therapeutic properties to address your symptoms."}, "loading": {"analyzingDemographics": "analyzing demographics...", "analyzingCauses": "analyzing selected causes...", "analyzingSymptoms": "analyzing symptoms..."}, "progress": {"analyzingMoreCauses": "Analyzing more potential causes...", "analyzingMoreSymptoms": "Analyzing more potential symptoms...", "analyzingMoreProperties": "Analyzing more therapeutic properties..."}, "status": {"complete": "Analysis complete. Found {count} {type}.", "initializing": "Initializing analysis engine...", "liveAnalysis": "Live analysis in progress", "autoCloseMessage": "This window will close automatically in {seconds} seconds", "analysisCompleteMessage": "Analysis complete. You may now close this window.", "aiProcessing": "AI is processing your information to find additional insights"}, "error": "Analysis failed. Please try again.", "showingLatest": "Showing latest {maxVisible} of {total}"}, "navigation": {"back": "Back", "next": "Next", "continue": "Continue", "submit": "Submit", "retry": "Retry", "close": "Close"}, "common": {"loading": "Loading...", "processing": "Processing...", "error": "An error occurred", "tryAgain": "Try again", "required": "Required", "optional": "Optional", "noCausesAvailable": "No Causes Available", "completeDemographics": "Please go back and complete the demographics step to generate potential causes.", "noSymptomsAvailable": "No Symptoms Available", "completeCauses": "Please go back and select causes to generate potential symptoms."}, "forms": {"tellUsAboutYourself": "Tell us about yourself", "yourDemographicsHelpUsPro": "Your demographics help us provide more personalized essential oil recommendations based on age and gender-specific health patterns.", "demographicsInformation": "Demographics Information", "gender": "Gender", "yourAge": "Your Age", "privacySafety": "🔒 Privacy & Safety", "privacyBulletPoints": "• Your information is encrypted and secure\n• Data is used only for personalized recommendations\n• We never share your personal details\n• You can delete your data anytime", "ageHelperText": "Used to provide age-appropriate oil recommendations"}, "propertiesSelection": {"title": "Therapeutic Properties", "subtitle": "{count} therapeutic properties found for your selection", "subtitlePrefix": "", "subtitleSuffix": " therapeutic properties found for your selection", "enrichedStatus": "Enriched: {enriched}/{total}", "relevance": "Relevance:", "noOilsMessage": "No oils available", "noOilsDescription": "Oil suggestions will appear here once processed", "loading": {"title": "Personalizing Your Oils", "subtitle": "Finding safe, effective oil suggestions tailored to your specific needs.", "button": "Searching Suggestions..."}, "buttons": {"getOilSuggestions": "Get Oil Suggestions", "generateFinalRecipes": "Generate Final Recipes", "selectProperties": "Select Properties"}, "messages": {"propertiesSelectedGetSuggestions": "{count} properties selected - get essential oil suggestions", "selectPropertiesToContinue": "Select therapeutic properties to continue", "propertiesEnrichedCreateRecipes": "Properties enriched - create your personalized recipes"}, "chips": {"property": "Property", "holistic": "Holistic"}, "safety": {"dilutionRange": "Dilution Range", "ratio": "<PERSON><PERSON>", "safeToIngest": "Safe to consume", "notSafeToIngest": "Do not ingest", "phototoxic": "Phototoxic", "sunSafe": "Sun safe", "consultSpecialist": "Consult specialist", "safeToIngestDesc": "Safe for consumption when properly diluted.", "notSafeToIngestDesc": "For topical use or inhalation only.", "phototoxicDesc": "Avoid sun exposure for 12-24h after skin use.", "sunSafeDesc": "No photosensitivity. Safe for normal sun exposure.", "consultSpecialistDesc": "Consult an aromatherapist for specific guidance.", "dilutionNotAvailable": "Dilution information not available", "followStandardGuidelines": "Follow standard dilution guidelines", "pregnancyWarning": "Not recommended during pregnancy", "childrenWarning": "Not safe for children", "pregnancyWarningDesc": "Avoid use during pregnancy and breastfeeding.", "childrenWarningDesc": "Not suitable for children under {age} years."}, "bannerMessage": "We're analyzing your selected properties to find the perfect essential oils for your needs. This may take a moment..."}, "wizard": {"title": "Create Recipe", "continueYourJourney": "Continue Your Journey", "startYourJourney": "Start Your Journey", "continueBuildingRecipe": "continue building your recipe", "description": "Create personalized essential oil recipes with AI guidance", "startOver": "Start Over", "continueWizard": "Continue Wizard", "continueRecipeWizard": "Continue Recipe Wizard", "startRecipeWizard": "Start Recipe Wizard", "wizardTitle": "Essential Oil Recipe Wizard 🧙‍♂️", "wizardDescription": "Create personalized essential oil recipes based on your health concerns. Our AI will guide you through each step to find the perfect therapeutic blend.", "yourProgress": "Your Progress", "progressOverview": "Progress Overview", "percentComplete": "% Complete", "wizardSteps": "Wizard Steps", "howItWorks": "💡 How it works", "step1": "• Describe your health concern", "step2": "• Share basic demographics", "step3": "• Select potential causes and symptoms", "step4": "• Get AI-generated therapeutic properties", "step5": "• Receive personalized essential oil recipes"}, "pages": {"demographics": "Demographics", "healthConcern": "Health Concern", "potentialCauses": "Potential Causes", "symptoms": "Symptoms", "therapeuticProperties": "Therapeutic Properties", "yourRecipes": "Your Recipes", "finalRecipes": "Final Recipes"}, "finalRecipes": {"generatingTitle": "Generating Your Personalized Recipes", "generatingSubtitle": "Creating morning, mid-day, and night protocols...", "generationFailed": "Generation Failed", "tryAgain": "Try Again", "noRecipesGenerated": "No Recipes Generated", "completePreviousSteps": "Please complete all previous steps to generate your personalized recipes.", "generateRecipes": "Generate Recipes", "yourPersonalizedRecipes": "Your Personalized Recipes", "overview": "Overview", "recipes": "Recipes", "safety": "Safety", "protocols": {"morning": "Morning Protocol", "midDay": "Mid-Day Protocol", "night": "Night Protocol"}, "sections": {"essentialOils": "Essential Oils", "carrierOil": "Carrier Oil", "recommended": "Recommended", "alternative": "Alternative", "rationales": "Why These Oils", "application": "How to Use", "preparation": "Preparation Steps", "userProfile": "User Profile", "protocolSummary": "Protocol Summary"}, "actions": {"viewRecipe": "View Recipe", "selectProtocol": "Select Protocol"}, "stats": {"totalDrops": "Total Drops", "volume": "Volume", "method": "Method", "oils": "oils", "drops": "drops"}, "labels": {"condition": "Condition", "age": "Age", "gender": "Gender", "years": "years", "notProvided": "Not provided", "identifiedCauses": "Identified Causes", "identifiedSymptoms": "Identified Symptoms", "topical": "Topical"}, "states": {"generating": "Generating {{protocol}}...", "noRecipeAvailable": "No recipe available for this time slot", "noRecipeData": "No recipe data available", "incompleteRecipeData": "Recipe data is incomplete or invalid", "safetyProfile": "Safety Profile", "safetyInformation": "Safety Information", "generalSafetyGuidelines": "General Safety Guidelines", "safetyBasedOnProfile": "Based on your age and profile, the recommended recipes are safe when used as instructed.", "patchTestAdvice": "Always perform a patch test before first use", "avoidEyesAdvice": "Avoid contact with eyes and mucous membranes", "keepAwayFromChildrenAdvice": "Keep out of reach of children and pets", "storageAdvice": "Store in a cool, dry place away from direct sunlight"}}, "genderOptions": {"male": "Male", "female": "Female"}, "ageUnit": "years", "errors": {"submitFailed": "Failed to submit. Please try again.", "analyzingCauses": "Analyzing Potential Causes", "processingDemographics": "Processing your demographics to identify potential health factors"}, "actionControls": {"healthConcern": {"title": "Ready to Explore?", "subtitleValid": "Ready to explore demographics", "subtitleInvalid": "Please enter your health concern", "buttonText": "Explore Demographics", "processing": "Processing..."}, "demographics": {"titleReady": "Discover Causes", "titleContinue": "Continue Exploring", "subtitleSingle": "selection made - discover what it reveals", "subtitleMultiple": "selections made - discover what they reveal", "buttonText": "Discover Potential Causes"}, "causes": {"titleReady": "Find Symptoms", "titleMore": "Select More Causes", "subtitleSingle": "cause selected - find related symptoms", "subtitleMultiple": "causes selected - find related symptoms", "buttonText": "Find Your Symptoms"}, "symptoms": {"titleReady": "Explore Properties", "titleMore": "Select Symptoms", "subtitleSingle": "symptom selected - explore therapeutic properties", "subtitleMultiple": "symptoms selected - explore therapeutic properties", "buttonText": "Explore Properties"}, "properties": {"title": "See Your Recipes", "subtitle": "Properties explored - see your personalized recipes", "buttonText": "See Your Recipes"}, "recipes": {"title": "Recipes Ready", "subtitle": "Your personalized recipes are ready to explore", "buttonText": "View Full Recipes"}, "default": {"title": "Continue", "subtitle": "Proceed to the next step", "buttonText": "Continue", "progressText": "In progress"}, "secondaryActions": {"back": "Back", "skip": "<PERSON><PERSON>"}, "accessibility": {"continueHint": "Double tap to proceed to the next step"}}, "modals": {"analyzingSymptoms": {"title": "Analyzing Your Symptoms", "description": "Based on your selected causes, we're finding related symptoms you might be experiencing."}, "analyzingProperties": {"title": "Finding Therapeutic Properties", "description": "Analyzing your symptoms to identify the most effective therapeutic properties for your essential oil blend."}, "generatingRecipes": {"title": "Generating Final Recipes", "description": "Creating personalized recipes for morning, mid-day, and night...", "progress": "Progress", "complete": "complete", "completed": "completed", "analyzing": "analyzing", "failed": "failed", "therapeuticProperties": "Therapeutic Properties", "autoClosing": "Continuing automatically...", "statusMessages": {"ready": "Ready to analyze therapeutic properties...", "completeAnalysis": "Analysis complete! Found essential oils for properties.", "analyzingProperties": "Analyzing properties to find the best essential oils for your needs...", "processing": "Processing results..."}, "propertyStatus": {"analyzing": "Analyzing...", "completedOils": "completed", "failed": "Analysis failed", "waiting": "Waiting..."}}}, "oilSubstitution": {"title": "Substitute {{oilName}}", "subtitle": "Choose a therapeutically equivalent alternative", "bestMatch": "Best Match", "safeInternal": "Safe Internal", "nonPhototoxic": "Non-phototoxic", "dilution": "Max {{max}}%", "pregnancyInfo": "Pregnancy Safe", "childSafety": "Child Safe", "noAlternatives": "No suitable alternatives found for this oil"}, "recipeDetails": {"title": "🧪 Recipe Details", "noData": "No recipe data available", "oilsSelected": "oils selected", "carrierOilsSubtitle": "Recommended and alternative options", "preparationSubtitle": "Step-by-step preparation guide", "step": "Step ", "applicationSubtitle": "Usage instructions and ritual", "frequency": "Frequency: ", "ritualSuggestion": "Ritual Suggestion", "rationalesSubtitle": "Scientific rationale and properties", "overallSynergy": "Overall Synergy", "saveProtocol": "Save Protocol", "protocolSaved": "Protocol \"{{protocolName}}\" saved successfully!"}}
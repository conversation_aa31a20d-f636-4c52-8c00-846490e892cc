{"buttons": {"save": "Save", "cancel": "Cancel", "continue": "Continue", "back": "Back", "next": "Next", "finish": "Finish", "submit": "Submit", "confirm": "Confirm", "close": "Close", "dynamicHapticFeedback12Pa": "• Dynamic haptic feedback (12 patterns from popular apps)", "backdropBlurEffectsWithTh": "• Backdrop blur effects with theme awareness", "tryTheDifferentModalTypes": "Try the different modal types and notice the dynamic haptic feedback! The profile and sheet modals use blur effects, while alerts use traditional backdrop. Swipe down to dismiss and feel the stack card animation.", "basicImpactFeedback": "Basic Impact Feedback", "usedForButtonPressesAndGe": "Used for button presses and general interactions", "usedInInstagramLikesTwitt": "Used in: Instagram likes, Twitter hearts, gentle button presses", "usedInPulltorefreshSwipeA": "Used in: Pull-to-refresh, swipe actions, moderate feedback", "usedInLongPressActionsCon": "Used in: Long press actions, context menus, strong feedback", "notificationFeedback": "Notification Feedback", "interactionPatterns": "Interaction Patterns", "commonInteractionPatterns": "Common interaction patterns from popular apps", "swipeAction": "Swipe Action", "usedInTinderSwipesIosMail": "Used in: Tinder swipes, iOS mail actions, gesture navigation", "aboutHapticFeedback": "💡 About Haptic Feedback", "debugActions": "🔧 Debug Actions", "hapticFeedback": "Haptic <PERSON>", "testHapticFeedbackPattern": "Test haptic feedback patterns from popular apps"}, "labels": {"email": "Email", "password": "Password", "name": "Name", "firstName": "First Name", "lastName": "Last Name", "loading": "Loading...", "error": "Error", "success": "Success", "info": "Info", "failedToSignOut": "Failed to sign out. Please try again.", "ok": "OK", "signOut": "Sign Out", "areYouSureYouWantToSignOut": "Are you sure you want to sign out?", "cancel": "Cancel", "profile": "Profile", "ratio": "<PERSON><PERSON>", "essentialOilVolume": "Essential Oil Volume", "carrierOilVolume": "Carrier Oil Volume", "totalVolume": "Total Volume", "verify": "Verify Code", "resendCode": "Resend code", "resendIn": "Resend in", "seconds": "seconds", "digit": "Digit", "of": "of", "attemptsRemaining": "Attempts remaining", "tooManyAttempts": "Too many attempts", "sendingNewCode": "Sending a new verification code..."}, "units": {"ml": "ml", "drops": "drops", "percent": "%"}, "messages": {"somethingWentWrong": "Something went wrong. Please try again.", "checkInternetConnection": "Please check your internet connection.", "fieldRequired": "This field is required", "invalidEmail": "Please enter a valid email address"}, "recipe": {"multipleOilsCalculator": "Multiple Oils Calculator", "comingSoon": "Coming Soon! 🔬", "customizeYourRecipePrefer": "Customize your recipe preferences below:", "essentialOilStrength": "Essential Oil Strength", "adjustTheConcentrationOfE": "Adjust the concentration of essential oils in your recipes", "recipeDuration": "Recipe Du<PERSON>", "setHowLongEachRecipeShoul": "Set how long each recipe should be used", "recipeSettings": "<PERSON><PERSON><PERSON>s", "whatCanIHelpYouWith": "What can I help you with?", "tryAskingAbout": "Try asking about:", "proTipBeSpecificAboutIngr": "💡 Pro tip: Be specific about ingredients you have, dietary restrictions, or cooking time!", "describeWhatYou": "Describe what you", "typeSomething": "Type something", "createRecipe": "Create Recipe", "getAipoweredEssentialOilR": "Get AI-powered essential oil recipes", "savedRecipes": "Saved <PERSON><PERSON><PERSON>", "savedProtocols": "Saved Protocols", "yourFavoriteRecipeCollect": "Your favorite recipe collection", "oilDilutionCalculator": "Oil Dilution Calculator", "calculateEssentialOilDilu": "Calculate essential oil dilutions for aromatherapy", "recipeCollections": "Recipe Collections", "essentialOil": "Essential Oil", "carrierOil": "Carrier Oil"}, "general": {"mobileStreamingDebug": "Mobile Streaming Debug", "debugIntegration": "Debug Integration", "demoCenter": "Demo Center", "comprehensiveDiagnosticTo": "Comprehensive diagnostic tool for mobile streaming issues", "readablestream": "ReadableStream:", "eventsource": "EventSource:", "apiKey": "API Key:", "authenticationIssueDetect": "🚨 Authentication Issue Detected", "diagnosticTests": "Diagnostic Tests", "testApiConnectivity": "Test API Connectivity", "testReadablestream": "Test ReadableStream", "testCurrentSseReader": "Test Current SSE Reader", "compareWebVsMobile": "Compare Web vs Mobile", "testEventsourceAlternativ": "Test EventSource Alternative", "testWorkingApiApproach": "Test Working API Approach", "diagnosticResults": "Diagnostic Results", "debugInstructions": "Debug Instructions", "step1": "Step 1:", "step2": "Step 2:", "step3": "Step 3:", "whatToLookFor": "What to Look For:", "expectedBehavior": "Expected Behavior:", "success": "Success", "lightImpact": "Light Impact", "mediumImpact": "Medium Impact", "heavyImpact": "Heavy Impact", "usedInUnsavedChangesLowBa": "Used in: Unsaved changes, low battery, caution alerts", "selection": "Selection", "usedInIosPickerWheelsSpot": "Used in: iOS picker wheels, Spotify volume, scrolling through options", "toggleSwitch": "Toggle Switch", "usedInSettingsTogglesDark": "Used in: Settings toggles, dark mode switch, feature toggles", "specialPatterns": "Special Patterns", "hapticsDemo": "Haptics Demo", "pulltorefreshTest": "🔄 Pull-to-Refresh Test", "pullDownToRefreshAndReset": "Pull down to refresh and reset counter to 0", "currentCount": "Current Count", "increment1": "Increment (+1)", "tipPullDownFromTheTopOfTh": "💡 Tip: Pull down from the top of the screen to trigger refresh", "themeTesting": "🎨 Theme Testing", "darkTheme": "Dark Theme", "toggleBetweenLightAndDark": "Toggle between light and dark themes", "themeColors": "Theme Colors", "primary": "Primary", "secondary": "Secondary", "tertiary": "Tertiary", "surface": "Surface", "howToTestPulltorefresh": "How to Test Pull-to-Refresh:", "refreshDebugData": "Refresh debug data", "version": "Version", "screenSize": "Screen Size", "lastUpdated": "Last Updated", "name": "Name", "email": "Email", "userId": "User ID", "settings": "Settings", "home": "Home", "debugScreen": "Debug Screen", "pulltorefreshTestingDebug": "Pull-to-refresh testing & debug utilities", "instagramModalDebug": "Instagram Modal Debug", "testInstagramstyleModalWi": "Test Instagram-style modal with swipe gestures", "comingSoon": "Coming Soon", "aromachat": "AromaChat", "cookingAssistant": "Cooking Assistant", "signOut": "Sign Out", "aromachatV100": "AromaChat v1.0.0", "areYouSureYouWantToSignOu": "Are you sure you want to sign out?", "profile": "Profile", "cookingHistory": "Cooking History", "mealPlanning": "Meal Planning", "shoppingList": "Shopping List", "standardDilutionCalculato": "Standard Dilution Calculator", "ratio": "<PERSON><PERSON>", "dropsNeeded": "drops needed", "aromachatCalculatorUsesAS": "AromaCHAT calculator uses a standard measurement where 20 drops are equal to approximately 1 ml (0.05 ml per drop).", "bottleSize": "Bottle Size", "reverseDilutionCalculator": "Reverse Dilution Calculator", "setBottleSizeAndNumberOfD": "Set bottle size and number of drops used", "dilutionStrength": "dilution strength", "highConcentration": "⚠️ High concentration", "numberOfDrops": "Number of Drops"}, "forms": {"platformInformation": "Platform Information", "platform": "Platform:", "thisExplainsWhyStreamingF": "This explains why streaming fails on all platforms!", "mobileoptimized60fpsPerfo": "• Mobile-optimized 60fps performance", "deviceInformation": "📱 Device Information", "userInformation": "👤 User Information", "ageTextInput": "Age text input"}, "validation": {"error": "Error", "warning": "Warning", "usedToCommunicateSuccessW": "Used to communicate success, warnings, and errors", "invalidCalculationParamet": "Invalid calculation parameters"}, "modal": {"confirm": "Confirm", "cancel": "Cancel", "close": "Close", "search": "Search", "notifications": "Notifications", "getRemindersForYourAromat": "Get reminders for your aromatherapy sessions", "productionreadyModalSyste": "Production-Ready Modal System", "interactiveExamplesOfTheN": "Interactive examples of the new modal system with stack card animations, gesture handling, and Material Design 3 theming.", "imperativeModalsRecommend": "Imperative Modals (Recommended)", "useTheUsemodalHookToShowM": "Use the useModal hook to show modals from anywhere in your app without managing state.", "confirmationDialog": "Confirmation Dialog", "bottomSheetOptions": "Bottom Sheet Options", "customContentModal": "Custom Content Modal", "profileModalTapAvatar": "Profile <PERSON> (Tap Avatar)", "enhancedFeatures": "Enhanced Features", "instagramstyleStackCardAn": "• Instagram-style stack card animations", "swipetodismissGestureSupp": "• Swipe-to-dismiss gesture support", "materialDesign3ThemingInt": "• Material Design 3 theming integration", "completeProfileModalWithS": "• Complete profile modal with settings", "technicalImplementation": "Technical Implementation", "basemodal": "BaseModal:", "modalprovider": "ModalProvider:", "usemodal": "useModal:", "theming": "Theming:", "haptics": "Haptics:", "blur": "Blur:", "profile": "Profile", "deleteAccount": "Delete Account?", "chooseAnOption": "Choose an Option", "modalSystemDemo": "Modal System Demo", "settings": "Settings", "theme": "Theme", "areYouSureYouWantToSignOu": "Are you sure you want to sign out?", "accountSettings": "Account <PERSON><PERSON>", "privacySecurity": "Privacy & Security", "helpSupport": "Help & Support", "signOut": "Sign Out", "comingSoon": "Coming Soon", "selectLanguage": "Select Language", "hapticsEnabled": "On", "hapticsDisabled": "Off", "searchPlaceholder": "Search..."}, "profile": {"demoFirstName": "Demo", "demoLastName": "User", "demoEmail": "<EMAIL>", "language": "Language", "hapticFeedback": "Haptic <PERSON>", "signOutConfirmation": "Are you sure you want to sign out?", "accountSettingsMessage": "Account settings will be available soon!", "privacyMessage": "Privacy settings will be available soon!", "helpMessage": "Help section will be available soon!", "updateAvatar": "Update Profile Picture", "removeAvatar": "Remove Profile Picture", "remove": "Remove", "removeAvatarConfirmation": "Are you sure you want to remove your profile picture?", "avatarUpdateFailed": "Failed to update profile picture. Please try again.", "avatarRemovalFailed": "Failed to remove profile picture. Please try again.", "nameRequired": "Both first name and last name are required.", "nameUpdateFailed": "Failed to update name. Please try again.", "verified": "Verified", "verifyNow": "Verify Now", "setAsPrimary": "Set as Primary", "verifyEmail": "<PERSON><PERSON><PERSON>", "removeEmail": "Re<PERSON><PERSON>", "addEmail": "Add Email Address", "primary": "Primary", "emailPrimaryUpdateFailed": "Failed to set email as primary. Please try again.", "emailRemovalFailed": "Failed to remove email. Please try again.", "cannotRemoveLastEmail": "Cannot remove your only email address.", "cannotRemovePrimaryEmail": "Cannot remove your primary email address.", "removeEmailConfirmation": "Are you sure you want to remove {{email}}?", "emailMustBeVerifiedFirst": "Email must be verified before it can be set as primary.", "emailAddress": "Email Address", "verifyEmailInstructions": "Enter the 6-digit code sent to {{email}}", "invalidEmailFormat": "Please enter a valid email address.", "emailAlreadyExists": "This email address is already added to your account.", "emailLimitReached": "You can only have up to 2 email addresses.", "emailCreationFailed": "Failed to add email address. Please try again.", "emailRateLimited": "Too many requests. Please wait before trying again.", "verificationFailed": "Email verification failed. Please try again.", "invalidVerificationCode": "Invalid verification code. Please check and try again.", "verificationCodeExpired": "Verification code has expired. Please request a new one.", "resendFailed": "Failed to resend verification code. Please try again.", "resendRateLimited": "Too many resend attempts. Please wait before trying again."}, "languages": {"english": "English", "spanish": "Español", "portuguese": "Português"}, "theme": {"light": "Light", "dark": "Dark", "system": "System"}, "navigation": {"fullNavigationWithAppbarI": "• Full navigation with AppBar integration", "tabSwitch": "<PERSON><PERSON> Switch", "usedInInstagramTabsSpotif": "Used in: Instagram tabs, Spotify navigation, YouTube tabs", "debugMenu": "Debug menu"}, "demographics": {"usedInMessageSentFileUplo": "Used in: Message sent, file uploaded, task completed", "language": "Language", "selectLanguage": "Select Language", "enterYourAge": "Enter your age", "setBottleSizeAndDesiredPe": "Set bottle size and desired percentage", "dilutionPercentage": "Dilution Percentage"}, "signIn": {"usedInFailedLoginNetworkE": "Used in: Failed login, network errors, validation failures"}, "realtime": {"status": {"label": "Status", "connected": "Connected", "connecting": "Connecting", "disconnected": "Disconnected", "error": "Connection Error"}, "buttons": {"connect": "Connect", "connecting": "Connecting...", "retry": "Retry"}, "presence": {"online": "Online", "user": "user", "users": "users", "andMore": "and {{count}} more..."}, "friends": {"makeAromaFriends": "Make Aroma Friends", "usersOnlinePrefix": "", "usersOnlineSuffix": " online", "userSingular": "user", "userPlural": "users"}, "chat": {"placeholder": "Type a message...", "liveChat": "Live Chat"}, "users": {"anonymous": "Anonymous", "unknown": "Unknown"}}, "permissions": {"photoLibrary": "Photo Library Access Required", "photoLibraryMessage": "Please allow access to your photo library to update your profile picture.", "camera": "Camera Access Required", "cameraMessage": "Please allow access to your camera to take a photo."}, "errors": {"generic": "Error", "validation": "Validation Error"}}
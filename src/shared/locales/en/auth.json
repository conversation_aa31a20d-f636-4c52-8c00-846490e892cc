{"welcome": {"title": "Welcome to AromaCHAT!", "subtitle": "Create personalized essential oil recipes with artificial intelligence", "getStarted": "Sign Up", "authMockupTest": "<PERSON><PERSON>"}, "signIn": {"title": "Sign In", "subtitle": "Sign in to continue your aromatherapy journey", "submit": "Sign In", "button": "Sign In", "forgotPassword": "Forgot Password?", "noAccount": "Don't have an account?", "signUpLink": "Sign Up", "link": "Sign In", "switchPrompt": "Don't have an account?", "emailPlaceholder": "Enter your email", "passwordPlaceholder": "Enter your password", "error": {"title": "Sign In Error", "generic": "Failed to sign in. Please check your credentials and try again."}, "googleSignIn": "Sign in with Google", "microsoftSignIn": "Sign in with Microsoft"}, "signUp": {"title": "Join AromaCHAT", "subtitle": "Start your personalized aromatherapy experience", "submit": "Sign Up", "button": "Sign Up", "hasAccount": "Already have an account?", "signInLink": "Sign In", "link": "Sign Up", "switchPrompt": "Already have an account?", "firstNamePlaceholder": "First name", "lastNamePlaceholder": "Last name", "emailPlaceholder": "Enter your email", "passwordPlaceholder": "Create a password", "error": {"title": "Sign Up Error", "generic": "Failed to create account. Please try again."}, "googleSignUp": "Sign up with Google", "microsoftSignUp": "Sign up with Microsoft"}, "profile": {"title": "Profile", "signOut": "Sign Out", "editProfile": "Edit Profile", "accountSettings": "Account <PERSON><PERSON>", "preferences": "Preferences", "about": "About", "version": "Version", "takePhoto": "Take Photo", "chooseFromLibrary": "Choose from Library", "selectImageSource": "Select Image Source", "selectImageSourceMessage": "Choose how you'd like to update your profile picture", "cameraFailed": "Failed to take photo. Please try again."}, "form": {"firstName": {"label": "First Name", "accessibilityLabel": "First Name Input"}, "lastName": {"label": "Last Name", "accessibilityLabel": "Last Name Input"}, "email": {"label": "Email", "formatError": "Please enter a valid email address."}, "password": {"label": "Password"}, "completeFields": "Fill all fields", "completeFieldsAccessibility": "Form is incomplete. Fill out all fields to continue."}, "forms": {"comingSoon": "Coming Soon"}, "validation": {"emailVerificationRequired": "Please check your email for verification instructions.", "additionalAuthRequired": "Additional authentication required. Please contact support."}, "forgotPassword": {"link": "Forgot Password?", "title": "Forgot Password?", "emailInstructions": "Enter your email address and we'll send you a verification code to reset your password.", "sendCode": "Send reset code", "backToSignIn": "Back to Sign In", "rememberPassword": "Remember your password?", "resetTitle": "Reset Password", "verifyCode": "Verify Code", "instructions": "Check your email for a verification code sent to {{email}}, then enter it below.", "createNewPassword": "Create New Password", "passwordInstructions": "Enter your new password. Make sure it's strong and secure.", "newPassword": "New Password", "confirmPassword": "Confirm Password", "resetPassword": "Reset Password", "resendCode": "Resend Code", "success": {"title": "Password Reset Successful", "message": "Your password has been reset successfully. You are now signed in."}, "resend": {"title": "Code Sent", "message": "A new verification code has been sent to your email."}, "error": {"invalidEmail": "Please enter a valid email address", "sendFailed": "Failed to send verification code. Please try again.", "generic": "Something went wrong. Please try again.", "resendFailed": "Failed to resend code. Please try again.", "requires2FA": "Two-factor authentication required. Please complete 2FA to continue.", "passwordMismatch": "Passwords do not match", "invalidCode": "Invalid verification code. Please try again."}}, "oauth": {"google": {"error": {"title": "Google Sign In Error", "cancelled": "Google sign in was cancelled", "failed": "Failed to sign in with Google. Please try again.", "networkError": "Network error occurred. Please check your connection and try again."}}, "microsoft": {"error": {"title": "Microsoft Sign In Error", "cancelled": "Microsoft sign in was cancelled", "failed": "Failed to sign in with Microsoft. Please try again.", "networkError": "Network error occurred. Please check your connection and try again."}}}, "verification": {"title": "Verify Your Email", "subtitle": "Check your email for a verification code sent to {{email}}, then enter it below."}, "emailSignIn": {"button": "Send login code", "title": "Sign in with Email Code", "subtitle": "We'll send a code to {{email}} to sign you in", "error": {"sendFailed": "Failed to send verification code. Please try again.", "verificationFailed": "Failed to verify code. Please try again.", "resendFailed": "Failed to resend code. Please try again."}}}
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import { getLocales } from 'expo-localization';

// Import all translations
import { common as commonEn, auth as authEn, onboarding as onboardingEn, homescreen as homescreenEn } from './en';
import { common as commonPt, auth as authPt, onboarding as onboardingPt, homescreen as homescreenPt } from './pt';

// Import createRecipe from the correct files
import createRecipeEn from './en/create-recipe.json';
import createRecipePt from './pt/create-recipe.json';
import oilCalculatorEn from './en/oil-calculator.json';
import oilCalculatorPt from './pt/oil-calculator.json';

// Supported languages
type Language = 'en' | 'pt';
const SUPPORTED_LANGUAGES: Language[] = ['en', 'pt'];

/**
 * Device Language Detection with Fallback Strategy
 * Priority sequence:
 * 1. Device language detection via expo-localization
 * 2. Check if detected language is supported
 * 3. Fall back to English for unsupported languages
 * 4. Fall back to English if detection fails
 */
const getInitialLanguage = (): Language => {
  try {
    // 1. Try device detection using expo-localization
    const deviceLocales = getLocales();
    const deviceLanguage = deviceLocales[0]?.languageCode;
    
    if (__DEV__) {
      console.log('🌐 Device language detected:', deviceLanguage);
      console.log('📱 Full device locales:', deviceLocales);
    }
    
    // 2. Check if device language is supported
    if (deviceLanguage && SUPPORTED_LANGUAGES.includes(deviceLanguage as Language)) {
      if (__DEV__) {
        console.log('✅ Using supported device language:', deviceLanguage);
      }
      return deviceLanguage as Language;
    }
    
    // 3. Fall back to English for unsupported languages
    if (__DEV__) {
      console.log('⚠️ Device language not supported, falling back to English. Detected:', deviceLanguage);
    }
    return 'en';
    
  } catch (error) {
    // 4. Fall back to English if detection fails
    if (__DEV__) {
      console.warn('❌ Device language detection failed, falling back to English:', error);
    }
    return 'en';
  }
};

const resources = {
  en: {
    common: commonEn,
    auth: authEn,
    onboarding: onboardingEn,
    'create-recipe': createRecipeEn,
    homescreen: homescreenEn,
    'oil-calculator': oilCalculatorEn,
  },
  pt: {
    common: commonPt,
    auth: authPt,
    onboarding: onboardingPt,
    'create-recipe': createRecipePt,
    homescreen: homescreenPt,
    'oil-calculator': oilCalculatorPt,
  },
};

// Initialize i18n with device language detection
const initialLanguage = getInitialLanguage();

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: initialLanguage, // Set initial language from device detection
    fallbackLng: 'en',
    defaultNS: 'common',
    ns: ['common', 'auth', 'onboarding', 'create-recipe', 'homescreen', 'oil-calculator'],
    interpolation: {
      escapeValue: false, // React already escapes values
    },
    compatibilityJSON: 'v4', // For React Native compatibility
  });

export default i18n;


// Export types and utilities for external use
export type { Language };
export { SUPPORTED_LANGUAGES, getInitialLanguage };

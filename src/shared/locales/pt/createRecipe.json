{"validation": {"healthConcernIsRequired": "[PT] Health concern is required", "causeNameIsRequired": "[PT] Cause name is required", "causeSuggestionIsRequired": "[PT] Cause suggestion is required", "causeExplanationIsRequire": "[PT] Cause explanation is required", "symptomNameIsRequired": "[PT] Symptom name is required", "symptomSuggestionIsRequir": "[PT] Symptom suggestion is required", "symptomExplanationIsRequi": "[PT] Symptom explanation is required", "invalidPropertyIdFormat": "[PT] Invalid property ID format", "localizedPropertyNameIsRe": "[PT] Localized property name is required", "englishPropertyNameIsRequ": "[PT] English property name is required", "contextualDescriptionIsRe": "[PT] Contextual description is required", "englishOilNameIsRequired": "[PT] English oil name is required", "localOilNameIsRequired": "[PT] Local oil name is required", "oilDescriptionIsRequired": "[PT] Oil description is required", "validGenderIsRequired": "[PT] Valid gender is required", "ageCategoryIsRequired": "[PT] Age category is required", "specificAgeIsRequired": "[PT] Specific age is required", "userLanguageIsRequired": "[PT] User language is required", "invalidLanguageCode": "[PT] Invalid language code", "atLeastOneCauseIsRequired": "[PT] At least one cause is required", "atLeastOneSymptomIsRequir": "[PT] At least one symptom is required", "atLeastOnePropertyIsRequi": "[PT] At least one property is required", "somethingWentWrong": "[PT] 🚨 Something went wrong", "theRecipeWizardEncountere": "[PT] The recipe wizard encountered an unexpected error. \n            Don't worry - your progress should be saved.", "errorDetailsDevelopmentOn": "[PT] <PERSON><PERSON><PERSON> (Development Only):", "tryAgain": "[PT] Try Again", "errorReturnHaserrorTrueEr": "[PT] , error);\n    return { hasError: true, error };\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    // Log error details for debugging\n    console.error(", "errorIfErrorerrorsConsole": "[PT] , error);\n      if (error.errors) {\n        console.error("}, "demographics": {"pleaseSelectAValidGender": "[PT] Please select a valid gender", "ageMustBeAWholeNumber": "[PT] Age must be a whole number"}, "recipe": {"relevancyScoreMustBeBetwe": "[PT] Relevancy score must be between 1 and 5", "relevancyMustBeBetween1An": "[PT] Relevancy must be between 1 and 5", "startRecipeWizard": "[PT] Start Recipe Wizard", "essentialOilRecipeWizard": "[PT] Essential Oil Recipe Wizard 🧙‍♂️", "generationFailed": "[PT] Generation Failed", "tryAgain": "[PT] Try Again", "noRecipesGenerated": "[PT] No Recipes Generated", "pleaseCompleteAllPrevious": "[PT] Please complete all previous steps to generate your personalized recipes.", "generateRecipes": "[PT] Generate Recipes", "generatingYourPersonalize": "[PT] Generating Your Personalized Recipes", "findingOilSuggestions": "[PT] Finding Oil Suggestions", "generatingFinalRecipes": "[PT] Generating Final Recipes", "viewRecipe": "[PT] View Recipe", "noRecipeAvailableForThisT": "[PT] No recipe available for this time slot", "totalDrops": "[PT] Total Drops", "volume": "[PT] Volume", "method": "[PT] Method", "topical": "[PT] Topical", "recommended": "[PT] Recommended", "alternative": "[PT] Alternative", "essentialOils": "[PT] Essential Oils", "carrierOil": "[PT] Carrier Oil", "whyTheseOils": "[PT] Why These Oils", "howToUse": "[PT] How to Use", "preparationSteps": "[PT] Preparation Steps"}, "buttons": {"errormessageCansubmitHeal": "[PT] , errorMessage);\n    }\n  }, [canSubmit, healthConcern, demographics, selectedCauses, selectedSymptoms, selectedPropertyIds, therapeuticProperties, startOilSuggestionStreaming]);\n\n  // Handle final recipe generation (following exact same pattern as handleSubmit)\n  const handleGenerateFinalRecipes = useCallback(async () => {\n    console.log(", "continueWizard": "[PT] Continue Wizard", "continueRecipeWizard": "[PT] Continue Recipe Wizard", "submittingYourHealthConce": "[PT] 🔄 Submitting your health concern...", "readyToContinue": "[PT] ✅ Ready to continue", "generationFailed": "[PT] Generation Failed", "tryAgain": "[PT] Try Again", "noRecipesGenerated": "[PT] No Recipes Generated", "pleaseCompleteAllPrevious": "[PT] Please complete all previous steps to generate your personalized recipes.", "generateRecipes": "[PT] Generate Recipes", "userProfile": "[PT] User Profile", "condition": "[PT] Condition:", "age": "[PT] Age:", "gender": "[PT] Gender:", "identifiedCauses": "[PT] Identified Causes", "identifiedSymptoms": "[PT] Identified Symptoms", "protocolSummary": "[PT] Protocol Summary", "selectProtocol": "[PT] Select Protocol", "viewRecipe": "[PT] View Recipe", "noRecipeAvailableForThisT": "[PT] No recipe available for this time slot", "totalDrops": "[PT] Total Drops", "volume": "[PT] Volume", "method": "[PT] Method", "topical": "[PT] Topical", "safetyProfile": "[PT] Safety Profile", "basedOnYourAgeAndProfileT": "[PT] Based on your age and profile, the recommended recipes are safe when used as instructed.", "safetyInformation": "[PT] Safety Information", "generalSafetyGuidelines": "[PT] General Safety Guidelines", "alwaysPerformAPatchTestBe": "[PT] Always perform a patch test before first use", "avoidContactWithEyesAndMu": "[PT] Avoid contact with eyes and mucous membranes", "keepOutOfReachOfChildrenA": "[PT] Keep out of reach of children and pets", "storeInACoolDryPlaceAwayF": "[PT] Store in a cool, dry place away from direct sunlight", "finalRecipes": "[PT] Final Recipes", "generatingYourPersonalize": "[PT] Generating Your Personalized Recipes", "yourPersonalizedRecipes": "[PT] Your Personalized Recipes", "essentialOils": "[PT] Essential Oils", "howToUse": "[PT] How to Use", "preparationSteps": "[PT] Preparation Steps", "pleaseGoBackAndSelectCaus": "[PT] Please go back and select causes to generate potential symptoms.", "pleaseGoBackAndCompleteTh": "[PT] Please go back and complete the demographics step to generate potential causes.", "startOver": "[PT] Start Over?", "back": "[PT] Back", "skip": "[PT] Skip", "whatHappensNext": "[PT] 🧠 What happens next?"}, "general": {"startOver": "[PT] Start Over", "yourProgress": "[PT] Your Progress", "wizardSteps": "[PT] Wizard Steps", "howItWorks": "[PT] 💡 How it works", "debugPanel": "[PT] 🐛 DEBUG PANEL", "propertiesStatus": "[PT] Properties Status:", "therapeuticProperties": "[PT] Therapeutic Properties", "enriched": "[PT] ✅ ENRICHED", "safetyAnalysis": "[PT] 🔬 Safety Analysis", "pregnancyNursingSafety": "[PT] 🤱 Pregnancy & Nursing Safety:", "childSafety": "[PT] 👶 Child Safety:"}, "health": {"createPersonalizedEssenti": "[PT] Create personalized essential oil recipes based on your health concerns. \n            Our AI will guide you through each step to find the perfect therapeutic blend.", "yourHealthConcern": "[PT] 📝 Your Health Concern", "howToDescribeYourHealthCo": "[PT] 💡 How to describe your health concern", "selectYourSymptoms": "[PT] Select Your Symptoms 🩺", "noSymptomsAvailable": "[PT] No Symptoms Available", "therapeuticPropertiesAnal": "[PT] Therapeutic Properties Analysis", "thesePropertiesAddressYou": "[PT] These properties address your selected causes and symptoms. Choose which ones to include in your recipe.", "selectPotentialCauses": "[PT] Select Potential Causes 🎯", "noCausesAvailable": "[PT] No Causes Available", "analyzingPotentialSymptom": "[PT] Analyzing Potential Symptoms"}, "modal": {"therapeuticProperties": "[PT] Therapeutic Properties", "continuingAutomatically": "[PT] Continuing automatically...", "cancel": "[PT] Cancel", "analysisFailed": "[PT] Analysis Failed", "retry": "[PT] Retry"}, "forms": {"additionalInformation": "📝 Informações Adicionais", "tellUsAboutYourself": "Conte-nos sobre você 👤", "yourDemographicsHelpUsPro": "Suas informações demográficas nos ajudam a fornecer recomendações de óleos essenciais mais personalizadas\nbaseadas em padrões de saúde específicos por idade e gênero.", "demographicsInformation": "Informações Demográficas", "gender": "<PERSON><PERSON><PERSON><PERSON>", "privacySafety": "🔒 Privacidade e Segurança", "yourAge": "<PERSON><PERSON>", "ageHelperText": "A idade nos ajuda a recomendar concentrações apropriadas de óleos essenciais e diretrizes de segurança", "privacyBulletPoints": "• Suas informações demográficas são usadas apenas para recomendações personalizadas\n• Diretrizes de segurança apropriadas para a idade são aplicadas a todas as sugestões\n• Padrões de saúde específicos por gênero ajudam a melhorar a precisão\n• Todos os dados são processados com segurança e nunca compartilhados", "analyzingPotentialCauses": "<PERSON><PERSON><PERSON><PERSON>", "safetyInformation": "Informações de Segurança", "alwaysPerformAPatchTestBe": "Sempre faça um teste de alergia antes do primeiro uso"}, "genderOptions": {"male": "<PERSON><PERSON><PERSON><PERSON>", "female": "Feminino"}, "ageCategories": {"child": "Criança (0-12 anos)", "teen": "Adolescente (13-17 anos)", "adult": "<PERSON><PERSON> (18-64 anos)", "senior": "Idoso (65+ anos)", "elderly": "<PERSON><PERSON><PERSON> (80+ anos)"}, "navigation": {"userProfile": "[PT] User Profile", "condition": "[PT] Condition:", "age": "[PT] Age:", "gender": "[PT] Gender:", "identifiedCauses": "[PT] Identified Causes", "identifiedSymptoms": "[PT] Identified Symptoms", "protocolSummary": "[PT] Protocol Summary", "selectProtocol": "[PT] Select Protocol", "safetyProfile": "[PT] Safety Profile", "basedOnYourAgeAndProfileT": "[PT] Based on your age and profile, the recommended recipes are safe when used as instructed.", "generalSafetyGuidelines": "[PT] General Safety Guidelines", "avoidContactWithEyesAndMu": "[PT] Avoid contact with eyes and mucous membranes", "keepOutOfReachOfChildrenA": "[PT] Keep out of reach of children and pets", "storeInACoolDryPlaceAwayF": "[PT] Store in a cool, dry place away from direct sunlight"}}
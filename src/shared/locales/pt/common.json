{"buttons": {"save": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "continue": "<PERSON><PERSON><PERSON><PERSON>", "back": "Voltar", "next": "Próximo", "finish": "Finalizar", "submit": "Enviar", "confirm": "Confirmar", "close": "<PERSON><PERSON><PERSON>", "dynamicHapticFeedback12Pa": "• Feedback há<PERSON>o dinâmico (12 padrões de aplicativos populares)", "backdropBlurEffectsWithTh": "• Efeitos de desfoque de fundo com reconhecimento de tema", "tryTheDifferentModalTypes": "Experimente os diferentes tipos de modal e observe o feedback háptico dinâmico! Os modais de perfil e planilha usam efeitos de desfoque, enquanto os alertas usam fundo tradicional. Deslize para baixo para dispensar e sinta a animação do cartão empilhado.", "basicImpactFeedback": "Feed<PERSON> de Impacto Básico", "usedForButtonPressesAndGe": "Usado para pressionar botões e interações gerais", "usedInInstagramLikesTwitt": "Usado em: Curtidas do Instagram, corações do Twitter, pressões suaves de botão", "usedInPulltorefreshSwipeA": "Usado em: <PERSON><PERSON><PERSON> para atual<PERSON>, <PERSON><PERSON><PERSON><PERSON>, feedback moderado", "usedInLongPressActionsCon": "Usado em: Ações de pressão longa, menus de contexto, feedback forte", "notificationFeedback": "Feedback de Notificação", "interactionPatterns": "[PT] Interaction Patterns", "commonInteractionPatterns": "[PT] Common interaction patterns from popular apps", "swipeAction": "[PT] Swipe Action", "usedInTinderSwipesIosMail": "[PT] Used in: Tinder swipes, iOS mail actions, gesture navigation", "aboutHapticFeedback": "[PT] 💡 About Haptic Feedback", "debugActions": "[PT] 🔧 Debug Actions", "hapticFeedback": "[PT] Ha<PERSON>", "testHapticFeedbackPattern": "[PT] Test haptic feedback patterns from popular apps"}, "labels": {"email": "Email", "password": "<PERSON><PERSON>", "name": "Nome", "firstName": "Nome", "lastName": "Sobrenome", "loading": "Carregando...", "error": "Erro", "success": "Sucesso", "info": "Informação", "failedToSignOut": "Falha ao sair. Por favor, tente novamente.", "ok": "OK", "signOut": "<PERSON><PERSON>", "areYouSureYouWantToSignOut": "Você tem certeza que quer sair?", "cancel": "<PERSON><PERSON><PERSON>", "profile": "Perfil", "ratio": "Proporção", "essentialOilVolume": "Volume de Óleo Essencial", "carrierOilVolume": "Volume de <PERSON><PERSON>", "totalVolume": "Volume Total"}, "units": {"ml": "ml", "drops": "gotas", "percent": "%"}, "messages": {"somethingWentWrong": "Algo deu errado. Tente novamente.", "checkInternetConnection": "Verifique sua conexão com a internet.", "fieldRequired": "Este campo é obrigatório", "invalidEmail": "Digite um email válido"}, "recipe": {"multipleOilsCalculator": "[PT] Multiple Oils Calculator", "comingSoon": "Em Breve! 🔬", "customizeYourRecipePrefer": "[PT] Customize your recipe preferences below:", "essentialOilStrength": "[PT] Essential Oil Strength", "adjustTheConcentrationOfE": "[PT] Adjust the concentration of essential oils in your recipes", "recipeDuration": "[PT] Recipe Du<PERSON>", "setHowLongEachRecipeShoul": "[PT] Set how long each recipe should be used", "recipeSettings": "[PT] Recipe <PERSON>s", "whatCanIHelpYouWith": "[PT] What can I help you with?", "tryAskingAbout": "[PT] Try asking about:", "proTipBeSpecificAboutIngr": "[PT] 💡 Pro tip: Be specific about ingredients you have, dietary restrictions, or cooking time!", "describeWhatYou": "[PT] Describe what you", "typeSomething": "[PT] Type something", "createRecipe": "<PERSON><PERSON><PERSON>", "getAipoweredEssentialOilR": "Obtenha receitas de óleos essenciais com IA", "savedRecipes": "Receitas Salvas", "savedProtocols": "Protocolos Salvos", "yourFavoriteRecipeCollect": "Sua coleção de receitas favoritas", "oilDilutionCalculator": "Calculadora de Diluição de Óleos", "calculateEssentialOilDilu": "Calcule diluições de óleos essenciais para aromaterapia", "recipeCollections": "[PT] Recipe Collections", "essentialOil": "[PT] Essential Oil", "carrierOil": "[PT] Carrier Oil"}, "general": {"mobileStreamingDebug": "[PT] Mobile Streaming Debug", "debugIntegration": "Integração de Debug", "demoCenter": "Centro de Demonstração", "comprehensiveDiagnosticTo": "[PT] Comprehensive diagnostic tool for mobile streaming issues", "readablestream": "[PT] ReadableStream:", "eventsource": "[PT] EventSource:", "apiKey": "[PT] API Key:", "authenticationIssueDetect": "[PT] 🚨 Authentication Issue Detected", "diagnosticTests": "[PT] Diagnostic Tests", "testApiConnectivity": "[PT] Test API Connectivity", "testReadablestream": "[PT] Test ReadableStream", "testCurrentSseReader": "[PT] Test Current SSE Reader", "compareWebVsMobile": "[PT] Compare Web vs Mobile", "testEventsourceAlternativ": "[PT] Test EventSource Alternative", "testWorkingApiApproach": "[PT] Test Working API Approach", "diagnosticResults": "[PT] Diagnostic Results", "debugInstructions": "[PT] Debug Instructions", "step1": "[PT] Step 1:", "step2": "[PT] Step 2:", "step3": "[PT] Step 3:", "whatToLookFor": "[PT] What to Look For:", "expectedBehavior": "[PT] Expected Behavior:", "success": "[PT] Success", "lightImpact": "[PT] Light Impact", "mediumImpact": "[PT] Medium Impact", "heavyImpact": "[PT] Heavy Impact", "usedInUnsavedChangesLowBa": "[PT] Used in: Unsaved changes, low battery, caution alerts", "selection": "[PT] Selection", "usedInIosPickerWheelsSpot": "[PT] Used in: iOS picker wheels, Spotify volume, scrolling through options", "toggleSwitch": "[PT] Toggle Switch", "usedInSettingsTogglesDark": "[PT] Used in: Settings toggles, dark mode switch, feature toggles", "specialPatterns": "[PT] Special Patterns", "hapticsDemo": "[PT] Haptics Demo", "pulltorefreshTest": "[PT] 🔄 Pull-to-Refresh Test", "pullDownToRefreshAndReset": "[PT] Pull down to refresh and reset counter to 0", "currentCount": "[PT] Current Count", "increment1": "[PT] Increment (+1)", "tipPullDownFromTheTopOfTh": "[PT] 💡 Tip: Pull down from the top of the screen to trigger refresh", "themeTesting": "[PT] 🎨 Theme Testing", "darkTheme": "<PERSON><PERSON>", "toggleBetweenLightAndDark": "[PT] Toggle between light and dark themes", "themeColors": "[PT] Theme Colors", "primary": "[PT] Primary", "secondary": "[PT] Secondary", "tertiary": "[PT] Tertiary", "surface": "[PT] Surface", "howToTestPulltorefresh": "[PT] How to Test Pull-to-Refresh:", "refreshDebugData": "[PT] Refresh debug data", "version": "[PT] Version", "screenSize": "[PT] Screen Size", "lastUpdated": "[PT] Last Updated", "name": "[PT] Name", "email": "[PT] Email", "userId": "[PT] User ID", "settings": "[PT] Settings", "home": "Início", "debugScreen": "[PT] Debug Screen", "pulltorefreshTestingDebug": "[PT] Pull-to-refresh testing & debug utilities", "instagramModalDebug": "[PT] Instagram Modal Debug", "testInstagramstyleModalWi": "[PT] Test Instagram-style modal with swipe gestures", "comingSoon": "Em Breve", "aromachat": "AromaChat", "cookingAssistant": "AromaCHAT", "signOut": "<PERSON><PERSON>", "aromachatV100": "AromaChat v1.0.0", "areYouSureYouWantToSignOu": "Tem certeza de que deseja sair?", "profile": "Perfil", "cookingHistory": "[PT] Cooking History", "mealPlanning": "Planejamento de Refeições", "shoppingList": "Lista de Compras", "standardDilutionCalculato": "[PT] Standard Dilution Calculator", "ratio": "[PT] Ratio", "dropsNeeded": "[PT] drops needed", "aromachatCalculatorUsesAS": "[PT] AromaCHAT calculator uses a standard measurement where 20 drops are equal to approximately 1 ml (0.05 ml per drop).", "bottleSize": "[PT] Bottle Size", "reverseDilutionCalculator": "[PT] Reverse Dilution Calculator", "setBottleSizeAndNumberOfD": "[PT] Set bottle size and number of drops used", "dilutionStrength": "[PT] dilution strength", "highConcentration": "[PT] ⚠️ High concentration", "numberOfDrops": "[PT] Number of Drops"}, "forms": {"platformInformation": "[PT] Platform Information", "platform": "[PT] Platform:", "thisExplainsWhyStreamingF": "[PT] This explains why streaming fails on all platforms!", "mobileoptimized60fpsPerfo": "[PT] • Mobile-optimized 60fps performance", "deviceInformation": "[PT] 📱 Device Information", "userInformation": "[PT] 👤 User Information", "ageTextInput": "[PT] Age text input"}, "validation": {"error": "[PT] Error", "warning": "[PT] Warning", "usedToCommunicateSuccessW": "[PT] Used to communicate success, warnings, and errors", "invalidCalculationParamet": "[PT] Invalid calculation parameters"}, "modal": {"confirm": "Confirmar", "cancel": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "search": "Buscar", "notifications": "[PT] Notifications", "getRemindersForYourAromat": "[PT] Get reminders for your aromatherapy sessions", "productionreadyModalSyste": "[PT] Production-Ready Modal System", "interactiveExamplesOfTheN": "[PT] Interactive examples of the new modal system with stack card animations, gesture handling, and Material Design 3 theming.", "imperativeModalsRecommend": "[PT] Imperative Modals (Recommended)", "useTheUsemodalHookToShowM": "[PT] Use the useModal hook to show modals from anywhere in your app without managing state.", "confirmationDialog": "[PT] Confirmation Dialog", "bottomSheetOptions": "[PT] Bottom Sheet Options", "customContentModal": "[PT] Custom Content Modal", "profileModalTapAvatar": "[PT] Profile <PERSON> (Tap Avatar)", "enhancedFeatures": "[PT] Enhanced Features", "instagramstyleStackCardAn": "[PT] • Instagram-style stack card animations", "swipetodismissGestureSupp": "[PT] • Swipe-to-dismiss gesture support", "materialDesign3ThemingInt": "[PT] • Material Design 3 theming integration", "completeProfileModalWithS": "[PT] • Complete profile modal with settings", "technicalImplementation": "[PT] Technical Implementation", "basemodal": "[PT] BaseModal:", "modalprovider": "[PT] Modal<PERSON>rovider:", "usemodal": "[PT] useModal:", "theming": "[PT] Theming:", "haptics": "[PT] Haptics:", "blur": "[PT] Blur:", "profile": "Perfil", "deleteAccount": "Excluir Conta?", "chooseAnOption": "Escolha uma Opção", "modalSystemDemo": "[PT] Modal System Demo", "settings": "Configurações", "theme": "<PERSON><PERSON>", "areYouSureYouWantToSignOu": "Tem certeza de que deseja sair?", "accountSettings": "Configurações da Conta", "privacySecurity": "Privacidade e Segurança", "helpSupport": "Ajuda e Suporte", "signOut": "<PERSON><PERSON>", "comingSoon": "Em Breve", "selectLanguage": "Selecionar Idioma", "hapticsEnabled": "Ligado", "hapticsDisabled": "Des<PERSON><PERSON>", "searchPlaceholder": "Buscar..."}, "profile": {"demoFirstName": "Demo", "demoLastName": "<PERSON><PERSON><PERSON><PERSON>", "demoEmail": "<EMAIL>", "language": "Idioma", "hapticFeedback": "Feed<PERSON>", "signOutConfirmation": "Tem certeza de que deseja sair?", "accountSettingsMessage": "Configurações da conta estarão disponíveis em breve!", "privacyMessage": "Configurações de privacidade estarão disponíveis em breve!", "helpMessage": "Seção de ajuda estará disponível em breve!", "updateAvatar": "Atualizar Foto do Perfil", "removeAvatar": "Remover Foto do Perfil", "remove": "Remover", "removeAvatarConfirmation": "Tem certeza de que deseja remover sua foto do perfil?", "avatarUpdateFailed": "Falha ao atualizar a foto do perfil. Tente novamente.", "avatarRemovalFailed": "Falha ao remover a foto do perfil. Tente novamente.", "nameRequired": "Nome e sobrenome são obrigatórios.", "nameUpdateFailed": "Falha ao atualizar o nome. Tente novamente.", "verified": "Verificado", "verifyNow": "Verificar Agora", "setAsPrimary": "Definir como Principal", "verifyEmail": "Verificar Email", "removeEmail": "Remover Email", "addEmail": "<PERSON><PERSON><PERSON><PERSON>", "primary": "Principal", "emailPrimaryUpdateFailed": "Falha ao definir email como principal. Tente novamente.", "emailRemovalFailed": "Falha ao remover email. Tente novamente.", "cannotRemoveLastEmail": "Não é possível remover seu único endereço de email.", "cannotRemovePrimaryEmail": "Não é possível remover seu email principal.", "removeEmailConfirmation": "Tem certeza de que deseja remover {{email}}?", "emailMustBeVerifiedFirst": "O email deve ser verificado antes de ser definido como principal.", "emailAddress": "Endereço de Email", "verifyEmailInstructions": "Digite o código de 6 dígitos enviado para {{email}}", "invalidEmailFormat": "Por favor, digite um endereço de email válido.", "emailAlreadyExists": "Este endereço de email já foi adicionado à sua conta.", "emailLimitReached": "Você pode ter apenas até 2 endereços de email.", "emailCreationFailed": "Falha ao adicionar endereço de email. Tente novamente.", "emailRateLimited": "Muitas solicitações. Aguarde antes de tentar novamente.", "verificationFailed": "Verificação de email falhou. Tente novamente.", "invalidVerificationCode": "Código de verificação inválido. Verifique e tente novamente.", "verificationCodeExpired": "Código de verificação expirou. Solicite um novo.", "resendFailed": "Falha ao reenviar código de verificação. Tente novamente.", "resendRateLimited": "Muitas tentativas de reenvio. Aguarde antes de tentar novamente."}, "languages": {"english": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spanish": "Espanhol", "portuguese": "Português"}, "theme": {"light": "<PERSON><PERSON><PERSON>", "dark": "Escuro", "system": "Sistema"}, "navigation": {"fullNavigationWithAppbarI": "[PT] • Full navigation with AppBar integration", "tabSwitch": "[PT] <PERSON>b Switch", "usedInInstagramTabsSpotif": "[PT] Used in: Instagram tabs, Spotify navigation, YouTube tabs", "debugMenu": "[PT] Debug menu"}, "demographics": {"usedInMessageSentFileUplo": "[PT] Used in: Message sent, file uploaded, task completed", "language": "[PT] Language", "selectLanguage": "[PT] Select Language", "enterYourAge": "[PT] Enter your age", "setBottleSizeAndDesiredPe": "[PT] Set bottle size and desired percentage", "dilutionPercentage": "[PT] Dilution Percentage"}, "signIn": {"usedInFailedLoginNetworkE": "[PT] Used in: Failed login, network errors, validation failures"}, "realtime": {"status": {"label": "Status", "connected": "Conectado", "connecting": "<PERSON><PERSON><PERSON><PERSON>", "disconnected": "Desconectado", "error": "Erro de Conexão"}, "buttons": {"connect": "Conectar", "connecting": "Conectando...", "retry": "Tentar Novamente"}, "presence": {"online": "Online", "user": "<PERSON><PERSON><PERSON><PERSON>", "users": "usuários", "andMore": "e mais {{count}}..."}, "friends": {"makeAromaFriends": "Fazer Amigos Arom<PERSON>", "usersOnlinePrefix": "", "usersOnlineSuffix": " online", "userSingular": "<PERSON><PERSON><PERSON><PERSON>", "userPlural": "usuários"}, "chat": {"placeholder": "Digite uma mensagem...", "liveChat": "Chat ao Vivo"}, "users": {"anonymous": "<PERSON><PERSON><PERSON><PERSON>", "unknown": "Desconhecido"}}, "permissions": {"photoLibrary": "Acesso à Biblioteca de Fotos Necessário", "photoLibraryMessage": "Por favor, permita acesso à sua biblioteca de fotos para atualizar sua foto do perfil.", "camera": "Acesso à Câmera Necessário", "cameraMessage": "Por favor, permita acesso à sua câmera para tirar uma foto."}, "errors": {"generic": "Erro", "validation": "Erro de Validação"}}
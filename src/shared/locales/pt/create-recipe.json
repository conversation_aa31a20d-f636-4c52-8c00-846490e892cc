{"healthConcern": {"title": "Qual é sua preocupação de saúde?", "subtitle": "Descreva sua principal preocupação de saúde ou sintomas", "placeholder": "Como posso ajudar?", "helperText": "Dicas para melhores resultados", "greeting": "O<PERSON><PERSON>", "defaultName": "Chef", "progressTitle": "📝 Sua Preocupação de Saúde", "validationMessages": {"submitting": "🔄 Enviando sua preocupação de saúde...", "ready": "✅ Pronto para continuar", "needMore": "💡 Precisa de mais {count} caracteres", "provideMore": "💡 Forneça mais detalhes para melhores recomendações"}, "tipsSection": {"title": "💡 Como descrever sua preocupação de saúde", "description": "Quanto mais específico você for, melhor nossa IA poderá analisar possíveis causas e recomendar protocolos apropriados de óleos essenciais.\n\nToque em qualquer sugestão abaixo para começar, ou descreva sua preocupação com suas próprias palavras."}, "suggestions": [{"text": "Dificuldade para adormecer à noite", "expanded": "Dificuldade para adormecer à noite, mente acelerada com pensamentos"}, {"text": "Dores de cabeça frequentes durante estresse", "expanded": "Dores de cabeça frequentes durante períodos estressantes de trabalho"}, {"text": "Desconforto digestivo após refeições", "expanded": "Desconforto digestivo após refeições, especialmente comidas gordurosas"}, {"text": "Tensão muscular e rigidez", "expanded": "Tensão muscular e rigidez, especialmente no pescoço e ombros"}], "errors": {"refreshFailed": "Falha ao atualizar. Tente novamente."}, "tips": ["Seja específico sobre sintomas e duração", "Mencione quaisquer gatilhos que você notou", "Inclua como isso afeta sua vida diária", "Note quaisquer tratamentos que você tentou"], "validation": {"required": "Preocupação de saúde é obrigatória", "minLength": "Por favor, forne<PERSON> mais de<PERSON> (pelo menos 10 caracteres)", "maxLength": "Por favor, mantenha sua descrição abaixo de 500 caracteres"}, "button": "Continuar para Demografia"}, "demographics": {"title": "Conte-nos sobre você", "subtitle": "Suas informações demográficas nos ajudam a fornecer recomendações de óleos essenciais mais personalizadas baseadas em padrões de saúde específicos por idade e gênero", "category": "Categoria de Idade", "gender": {"label": "<PERSON><PERSON><PERSON><PERSON>", "male": "<PERSON><PERSON><PERSON><PERSON>", "female": "Feminino"}, "ageCategory": {"label": "Categoria de Idade", "placeholder": "Selecione a categoria de idade"}, "specificAge": {"label": "Idade Específica", "placeholder": "<PERSON><PERSON> idade", "suffix": "anos", "helperText": "A idade nos ajuda a recomendar concentrações apropriadas de óleos essenciais e diretrizes de segurança"}, "privacy": {"title": "🔒 Privacidade e Segurança", "points": ["Suas informações demográficas são usadas apenas para recomendações personalizadas", "Diretrizes de segurança apropriadas para a idade são aplicadas a todas as sugestões", "Padrões de saúde específicos por gênero ajudam a melhorar a precisão", "Todos os dados são processados com segurança e nunca compartilhados"]}, "validation": {"genderRequired": "Por favor, selecione seu gênero", "ageCategoryRequired": "Por favor, selecione sua categoria de idade", "ageRequired": "Por favor, insira sua idade", "ageInvalid": "Por favor, insira uma idade válida entre 1 e 120"}, "button": "<PERSON><PERSON><PERSON>"}, "ageCategoryOptions": {"child": "Criança", "teen": "Adolescente", "adult": "Adulto", "senior": "Idoso", "elderly": "<PERSON><PERSON><PERSON>", "unknown": "Desconhecido"}, "causes": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Escolha {min}-{max} fatores que podem estar contribuindo para suas preocupações de saúde", "pleaseSelect": "Por favor, selecione possíveis causas", "subtitlePrefix": "Escolha 1-", "subtitleSuffix": " fatores que podem estar contribuindo para suas preocupações de saúde", "counter": "Selecionados: {count}/{max}", "counterPrefix": "Selecionados: ", "validation": {"minSelection": "Por favor, selecione pelo menos {min} causa{plural}", "maxSelection": "Por favor, remova {excess} seleção{plural}"}, "button": "<PERSON><PERSON><PERSON>", "feedback": {"needMore": "Selecione pelo menos {needed} causa{plural} a mais", "tooMany": "Por favor, remova {excess} seleção{plural}", "selected": "{count} causa{plural} selecionada{plural}"}}, "symptoms": {"title": "<PERSON><PERSON>", "subtitle": "Escolha {min}-{max} sintomas que você está experimentando atualmente", "pleaseSelect": "Por favor, selecione seus sintomas", "subtitlePrefix": "Escolha 1-", "subtitleSuffix": " sintomas que você está experimentando atualmente", "counter": "Selecionados: {count}/{max}", "counterPrefix": "Selecionados: ", "validation": {"minSelection": "Por favor, selecione pelo menos {min} sintoma{plural}", "maxSelection": "Por favor, remova {excess} seleção{plural}"}, "button": "Completar Seleção", "feedback": {"needMore": "Selecione pelo menos {needed} sintoma{plural} a mais", "tooMany": "Por favor, remova {excess} seleção{plural}", "selected": "{count} sintoma{plural} selecionado{plural}"}}, "streaming": {"found": "encontrado", "states": {"loading": "carregando", "streaming": "transmitindo", "completed": "conc<PERSON><PERSON><PERSON>", "error": "erro"}, "terminal": {"potentialCausesAnalysis": "Análise de Causas Potenciais", "causesSubtitle": "Compreendendo fatores que podem contribuir para seus sintomas.", "potentialSymptomsAnalysis": "Análise de Sintomas Potenciais", "symptomsSubtitle": "Identificando sintomas que podem se manifestar baseados nas suas causas selecionadas.", "therapeuticPropertiesAnalysis": "Análise de Propriedades Terapêuticas", "propertiesSubtitle": "Encontrando propriedades terapêuticas para abordar seus sintomas."}, "loading": {"analyzingDemographics": "analisando demografia...", "analyzingCauses": "analisando causas selecionadas...", "analyzingSymptoms": "analisando sintomas..."}, "progress": {"analyzingMoreCauses": "<PERSON><PERSON><PERSON><PERSON> mais causas potenciais...", "analyzingMoreSymptoms": "<PERSON><PERSON><PERSON><PERSON> mais sintomas potenciais...", "analyzingMoreProperties": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON> propriedades terapêuticas..."}, "status": {"complete": "An<PERSON>lise completa. Encontrado {count} {type}.", "initializing": "Inicializando motor de análise...", "liveAnalysis": "Análise ao vivo em progresso", "autoCloseMessage": "Esta janela fechará automaticamente em {seconds} segundos", "analysisCompleteMessage": "<PERSON><PERSON><PERSON><PERSON> completa. Você pode fechar esta janela agora.", "aiProcessing": "IA está processando suas informações para encontrar insights adicionais"}, "error": "<PERSON><PERSON><PERSON><PERSON> falhou. Por favor, tente novamente.", "showingLatest": "Mostrando os últimos {maxVisible} de {total}"}, "navigation": {"back": "Voltar", "next": "Próximo", "continue": "<PERSON><PERSON><PERSON><PERSON>", "submit": "Enviar", "retry": "Tentar novamente", "close": "<PERSON><PERSON><PERSON>"}, "common": {"loading": "Carregando...", "processing": "Processando...", "error": "Ocorreu um erro", "tryAgain": "Tente novamente", "required": "Obrigatório", "optional": "Opcional", "noCausesAvailable": "<PERSON><PERSON><PERSON><PERSON>oní<PERSON>", "completeDemographics": "Por favor, volte e complete a etapa de demografia para gerar causas potenciais.", "noSymptomsAvailable": "<PERSON><PERSON><PERSON>", "completeCauses": "Por favor, volte e selecione causas para gerar sintomas potenciais."}, "forms": {"tellUsAboutYourself": "Conte-nos sobre você", "yourDemographicsHelpUsPro": "Suas informações demográficas nos ajudam a fornecer recomendações de óleos essenciais mais personalizadas baseadas em padrões de saúde específicos por idade e gênero.", "demographicsInformation": "Informações Demográficas", "gender": "<PERSON><PERSON><PERSON><PERSON>", "yourAge": "<PERSON><PERSON>", "privacySafety": "🔒 Privacidade e Segurança", "privacyBulletPoints": "• Suas informações são criptografadas e seguras\n• Os dados são usados apenas para recomendações personalizadas\n• Nunca compartilhamos seus detalhes pessoais\n• Você pode excluir seus dados a qualquer momento", "ageHelperText": "Usado para fornecer recomendações de óleos apropriadas para a idade"}, "propertiesSelection": {"title": "Pro<PERSON><PERSON>ades Terapêuticas", "subtitle": "{count} propriedades terapêuticas encontradas para sua seleção", "subtitlePrefix": "", "subtitleSuffix": " propriedades terapêuticas encontradas para sua seleção", "enrichedStatus": "Enriquecido: {enriched}/{total}", "relevance": "Relevância:", "noOilsMessage": "<PERSON><PERSON><PERSON> di<PERSON>oní<PERSON>", "noOilsDescription": "Sugestões de óleos aparecerão aqui quando processadas", "loading": {"title": "Personalizando <PERSON>", "subtitle": "Encontrando sugestões de óleos seguros e eficazes, personalizados para suas necessidades específicas.", "button": "Buscando <PERSON>..."}, "buttons": {"getOilSuggestions": "Obter Sugestões de Óleos", "generateFinalRecipes": "<PERSON><PERSON>r <PERSON>", "selectProperties": "Selecionar Propriedades"}, "messages": {"propertiesSelectedGetSuggestions": "{count} propriedades selecionadas - obter sugestões de óleos essenciais", "selectPropertiesToContinue": "Selecione propriedades terapêuticas para continuar", "propertiesEnrichedCreateRecipes": "Propriedades enriquecidas - crie suas receitas personalizadas"}, "chips": {"property": "<PERSON><PERSON><PERSON><PERSON>", "holistic": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "safety": {"dilutionRange": "Faixa de Diluição", "ratio": "Proporção", "safeToIngest": "<PERSON><PERSON> ingerir", "notSafeToIngest": "<PERSON><PERSON> ing<PERSON>", "phototoxic": "Fototóxico", "sunSafe": "Se<PERSON>ro no sol", "consultSpecialist": "Consulte especialista", "safeToIngestDesc": "Seguro para consumo quando diluído adequadamente.", "notSafeToIngestDesc": "Apenas uso tópico ou inalação.", "phototoxicDesc": "Evite exposição solar por 12-24h após uso na pele.", "sunSafeDesc": "Não causa fotossensibilidade. Seguro para exposição solar normal.", "consultSpecialistDesc": "Consulte um aromaterapeuta para orientações específicas.", "dilutionNotAvailable": "Informações de diluição não disponíveis", "followStandardGuidelines": "Siga as diretrizes de diluição padrão", "pregnancyWarning": "Não recomendado durante gravidez", "childrenWarning": "Não seguro para crianças", "pregnancyWarningDesc": "Evite o uso durante gravidez e amamentação.", "childrenWarningDesc": "<PERSON>ão adequado para crianças menores de {age} anos."}, "bannerMessage": "Estamos analisando suas propriedades selecionadas para encontrar os óleos essenciais perfeitos para suas necessidades. <PERSON><PERSON> pode levar um momento..."}, "wizard": {"title": "<PERSON><PERSON><PERSON>", "continueYourJourney": "Continue Sua Jornada", "startYourJourney": "Inicie Sua Jornada", "continueBuildingRecipe": "continue construindo sua receita", "description": "Crie receitas personalizadas de óleos essenciais com orientação de IA", "startOver": "Começar <PERSON>", "continueWizard": "<PERSON><PERSON><PERSON><PERSON>", "continueRecipeWizard": "<PERSON><PERSON><PERSON><PERSON> de Receita", "startRecipeWizard": "Iniciar <PERSON> Receita", "wizardTitle": "Assistente de Receita de Óleo Essencial 🧙‍♂️", "wizardDescription": "Crie receitas personalizadas de óleos essenciais baseadas em suas preocupações de saúde. Nossa IA irá orientá-lo através de cada passo para encontrar a mistura terapêutica perfeita.", "yourProgress": "<PERSON><PERSON>", "progressOverview": "Visão Geral do Progresso", "percentComplete": "% Completo", "wizardSteps": "Passos do Assistente", "howItWorks": "💡 Como funciona", "step1": "• Descreva sua preocupação de saúde", "step2": "• Compartilhe dados demográficos básicos", "step3": "• Selecione causas potenciais e sintomas", "step4": "• Obtenha propriedades terapêuticas geradas por IA", "step5": "• Receba receitas personalizadas de óleos essenciais"}, "pages": {"demographics": "Demografia", "healthConcern": "Preocupação de Saúde", "potentialCauses": "<PERSON><PERSON><PERSON>", "symptoms": "<PERSON><PERSON><PERSON>", "therapeuticProperties": "Pro<PERSON><PERSON>ades Terapêuticas", "yourRecipes": "Suas Receitas", "finalRecipes": "Receitas Finais"}, "finalRecipes": {"generatingTitle": "Gerando Suas Receitas Personalizadas", "generatingSubtitle": "<PERSON><PERSON><PERSON> matina<PERSON>, meio-dia e noturnos...", "generationFailed": "Geração Falhada", "tryAgain": "Tente Novamente", "noRecipesGenerated": "<PERSON><PERSON><PERSON><PERSON>ce<PERSON>", "completePreviousSteps": "Por favor, complete todos os passos anteriores para gerar suas receitas personalizadas.", "generateRecipes": "<PERSON><PERSON><PERSON>", "yourPersonalizedRecipes": "Suas Receitas Personalizadas", "overview": "Visão Geral", "recipes": "Receitas", "safety": "Segurança", "protocols": {"morning": "<PERSON><PERSON>", "midDay": "Protocolo Meio-Dia", "night": "Protocolo Noturno"}, "sections": {"essentialOils": "<PERSON><PERSON><PERSON>", "carrierOil": "<PERSON><PERSON>", "recommended": "Recomendado", "alternative": "Alternativo", "rationales": "<PERSON><PERSON> <PERSON>", "application": "Como Usar", "preparation": "Passos de Preparação", "userProfile": "Perfil do Usuário", "protocolSummary": "Resumo do Protocolo"}, "actions": {"viewRecipe": "<PERSON>er Receita", "selectProtocol": "Selecionar Protocolo"}, "stats": {"totalDrops": "Total de Gotas", "volume": "Volume", "method": "<PERSON><PERSON><PERSON><PERSON>", "oils": "<PERSON><PERSON><PERSON>", "drops": "gotas"}, "labels": {"condition": "Condição", "age": "<PERSON><PERSON>", "gender": "<PERSON><PERSON><PERSON><PERSON>", "years": "anos", "notProvided": "Não informado", "identifiedCauses": "Causas Identificadas", "identifiedSymptoms": "Sintomas Identificados", "topical": "Tópico"}, "states": {"generating": "Gerando {{protocol}}...", "noRecipeAvailable": "Nenhuma receita disponível para este período", "noRecipeData": "Nenhum dado de receita disponível", "incompleteRecipeData": "Dados da receita estão incompletos ou inválidos", "safetyProfile": "Perfil de Segurança", "safetyInformation": "Informações de Segurança", "generalSafetyGuidelines": "Diretrizes Gerais de Segurança", "safetyBasedOnProfile": "Com base na sua idade e perfil, as receitas recomendadas são seguras quando usadas conforme instruído.", "patchTestAdvice": "Sempre faça um teste de alergia antes do primeiro uso", "avoidEyesAdvice": "Evite contato com olhos e mucosas", "keepAwayFromChildrenAdvice": "Mantenha fora do alcance de crianças e animais", "storageAdvice": "Armazene em local fresco e seco, longe da luz solar direta"}}, "genderOptions": {"male": "<PERSON><PERSON><PERSON><PERSON>", "female": "Feminino"}, "ageUnit": "anos", "errors": {"submitFailed": "Falhou ao enviar. Tente novamente.", "analyzingCauses": "<PERSON><PERSON><PERSON><PERSON>", "processingDemographics": "Processando seus dados demográficos para identificar fatores de saúde potenciais"}, "actionControls": {"healthConcern": {"title": "Pronto para Explorar?", "subtitleValid": "Pronto para explorar demografia", "subtitleInvalid": "Por favor, insira sua preocupação de saúde", "buttonText": "Explorar Demografia", "processing": "Processando..."}, "demographics": {"titleReady": "<PERSON><PERSON><PERSON><PERSON>", "titleContinue": "Continuar Explorando", "subtitleSingle": "seleção feita - descubra o que revela", "subtitleMultiple": "seleções feitas - descubra o que revelam", "buttonText": "<PERSON><PERSON><PERSON><PERSON>"}, "causes": {"titleReady": "Encontrar <PERSON>", "titleMore": "Selecionar Mais <PERSON>", "subtitleSingle": "causa selecionada - encontrar sintomas relacionados", "subtitleMultiple": "causas selecionadas - encontrar sintomas relacionados", "buttonText": "Encontrar <PERSON>"}, "symptoms": {"titleReady": "Explora<PERSON>", "titleMore": "Selecionar Sintomas", "subtitleSingle": "sintoma selecionado - explorar propriedades terapêuticas", "subtitleMultiple": "sintomas selecionados - explorar propriedades terapêuticas", "buttonText": "Explora<PERSON>"}, "properties": {"title": "Ver Suas <PERSON>", "subtitle": "Propriedades exploradas - veja suas receitas personalizadas", "buttonText": "Ver Suas <PERSON>"}, "recipes": {"title": "Receitas Prontas", "subtitle": "Suas receitas personalizadas estão prontas para explorar", "buttonText": "Ver Receitas Completas"}, "default": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Prosseguir para o próximo passo", "buttonText": "<PERSON><PERSON><PERSON><PERSON>", "progressText": "Em progresso"}, "secondaryActions": {"back": "Voltar", "skip": "<PERSON><PERSON>"}, "accessibility": {"continueHint": "Toque duplo para prosseguir para o próximo passo"}}, "modals": {"analyzingSymptoms": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Com base nas causas selecionadas, estamos encontrando sintomas relacionados que você pode estar experimentando."}, "analyzingProperties": {"title": "Encontrando Propriedades Terapêuticas", "description": "<PERSON>lis<PERSON><PERSON> seus sintomas para identificar as propriedades terapêuticas mais eficazes para sua mistura de óleos essenciais."}, "generatingRecipes": {"title": "Gerando Receitas Finais", "description": "Criando receitas personalizadas para manhã, meio-dia e noite...", "progress": "Progresso", "complete": "completo", "completed": "conc<PERSON><PERSON><PERSON>", "analyzing": "analisando", "failed": "falharam", "therapeuticProperties": "Pro<PERSON><PERSON>ades Terapêuticas", "autoClosing": "Continuando automaticamente...", "statusMessages": {"ready": "Pronto para analisar propriedades terapêuticas...", "completeAnalysis": "Análise concluída! Encontrados óleos essenciais para propriedades.", "analyzingProperties": "<PERSON><PERSON><PERSON><PERSON> propriedades para encontrar os melhores óleos essenciais para suas necessidades...", "processing": "Processando resultados..."}, "propertyStatus": {"analyzing": "<PERSON><PERSON><PERSON><PERSON>...", "completedOils": "finalizado", "failed": "<PERSON><PERSON><PERSON><PERSON> falhou", "waiting": "Aguardando..."}}}, "oilSubstitution": {"title": "Substituir {{oilName}}", "subtitle": "Escolha uma alternativa terapeuticamente equivalente", "bestMatch": "<PERSON><PERSON>", "safeInternal": "<PERSON><PERSON><PERSON>", "nonPhototoxic": "Não-fototóxico", "dilution": "Máx {{max}}%", "pregnancyInfo": "Seguro na Gravidez", "childSafety": "Seguro para Crianças", "noAlternatives": "Nenhuma alternativa adequada encontrada para este óleo"}, "recipeDetails": {"title": "🧪 <PERSON><PERSON><PERSON> da Receita", "noData": "Nenhum dado de receita disponível", "oilsSelected": "ó<PERSON>s selecionados", "carrierOilsSubtitle": "Opções recomendadas e alternativas", "preparationSubtitle": "Guia de preparação passo a passo", "step": "Passo ", "applicationSubtitle": "Instruções de uso e ritual", "frequency": "Frequência: ", "ritualSuggestion": "Sugestão de Ritual", "rationalesSubtitle": "Fundamento científico e propriedades", "overallSynergy": "Sinergia Geral", "saveProtocol": "<PERSON><PERSON>", "protocolSaved": "Protocolo \"{{protocolName}}\" salvo com sucesso!"}}
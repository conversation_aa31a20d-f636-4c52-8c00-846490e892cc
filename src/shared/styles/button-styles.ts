import { useTheme } from '@/shared/hooks/use-theme';

/**
 * Standardized button styles following React Native Paper's official pattern.
 * Based on template-projects/react-native-paper-demo/src/Examples/ButtonExample.tsx
 * 
 * Key principles from the official example:
 * - `style` prop: Controls button container (margin, border radius, flex properties)
 * - `contentStyle` prop: Controls internal content layout (padding, flex direction)
 * - `labelStyle` prop: Controls text styling
 * - Minimal custom styling to preserve Paper's built-in ripple effects
 * 
 * Usage:
 * const buttonStyles = useButtonStyles();
 * const contentStyles = useButtonContentStyles();
 * <Button mode="contained" style={buttonStyles.large} contentStyle={contentStyles.large}>Submit</Button>
 * <Button mode="outlined" style={buttonStyles.default}>Cancel</Button>
 * <Button mode="text" compact style={buttonStyles.compact}>Learn More</Button>
 */
export const useButtonStyles = () => {
  const { spacing, components } = useTheme();
  
  return {
    // Standard button style - minimal styling to preserve ripple effects
    default: {
      marginVertical: spacing.xs,
    },
    
    // Large button style - use for primary CTAs, welcome screens
    large: {
      marginVertical: spacing.xs,
      minHeight: components.button.height.large,
    },
    
    // Compact button style - used with compact prop for small buttons
    compact: {
      marginVertical: spacing.xs / 2,
    },
    
    // Full width button - spans entire container width
    fullWidth: {
      marginVertical: spacing.xs,
      width: '100%',
    },
  };
};

/**
 * Content styles for button internals - use with contentStyle prop
 * These styles are applied to the button's content container, not the button itself
 * Updated to match AuthForm superior pattern with explicit height consistency
 */
export const useButtonContentStyles = () => {
  const { spacing } = useTheme();

  return {
    // Standard content - 40px height for medium buttons
    default: {
      height: 40,
      paddingHorizontal: spacing.md,
    },

    // Large content - 48px height matching AuthForm pattern
    large: {
      height: 48,
      paddingHorizontal: spacing.lg,
    },

    // Compact content - 32px height for small buttons
    compact: {
      height: 32,
      paddingHorizontal: spacing.sm,
    },
  };
};

/**
 * Static button styles for cases where hooks can't be used
 * Import design tokens directly for static usage
 */
export const createButtonStyles = ({ spacing, components }: ReturnType<typeof useTheme>) => ({
  default: {
    marginVertical: spacing.xs,
  },
  large: {
    marginVertical: spacing.xs,
    minHeight: components.button.height.large,
  },
  compact: {
    marginVertical: spacing.xs / 2,
  },
  fullWidth: {
    marginVertical: spacing.xs,
    width: '100%',
  },
});

/**
 * Static content styles for button internals - matching AuthForm superior pattern
 */
export const createButtonContentStyles = ({ spacing }: ReturnType<typeof useTheme>) => ({
  default: {
    height: 40,
    paddingHorizontal: spacing.md,
  },
  large: {
    height: 48,
    paddingHorizontal: spacing.lg,
  },
  compact: {
    height: 32,
    paddingHorizontal: spacing.sm,
  },
});
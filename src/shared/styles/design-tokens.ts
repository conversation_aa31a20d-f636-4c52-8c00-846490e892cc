/**
 * Design Tokens - Single Source of Truth for Design System
 * 
 * Following Material Design 3 guidelines and mobile-first principles
 * All values are optimized for React Native and mobile devices
 */

// Typography Scale (Material Design 3)
export const typography = {
  // Display styles
  displayLarge: {
    fontSize: 57,
    lineHeight: 64,
    fontWeight: '400' as const,
    letterSpacing: -0.25,
  },
  displayMedium: {
    fontSize: 45,
    lineHeight: 52,
    fontWeight: '400' as const,
    letterSpacing: 0,
  },
  displaySmall: {
    fontSize: 36,
    lineHeight: 44,
    fontWeight: '400' as const,
    letterSpacing: 0,
  },

  // Headline styles
  headlineLarge: {
    fontSize: 32,
    lineHeight: 40,
    fontWeight: '400' as const,
    letterSpacing: 0,
  },
  headlineMedium: {
    fontSize: 28,
    lineHeight: 36,
    fontWeight: '400' as const,
    letterSpacing: 0,
  },
  headlineSmall: {
    fontSize: 24,
    lineHeight: 32,
    fontWeight: '400' as const,
    letterSpacing: 0,
  },

  // Title styles
  titleLarge: {
    fontSize: 22,
    lineHeight: 28,
    fontWeight: '400' as const,
    letterSpacing: 0,
  },
  titleMedium: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '500' as const,
    letterSpacing: 0.15,
  },
  titleSmall: {
    fontSize: 14,
    lineHeight: 20,
    fontWeight: '500' as const,
    letterSpacing: 0.1,
  },

  // Label styles
  labelLarge: {
    fontSize: 14,
    lineHeight: 20,
    fontWeight: '500' as const,
    letterSpacing: 0.1,
  },
  labelMedium: {
    fontSize: 12,
    lineHeight: 16,
    fontWeight: '500' as const,
    letterSpacing: 0.5,
  },
  labelSmall: {
    fontSize: 11,
    lineHeight: 16,
    fontWeight: '500' as const,
    letterSpacing: 0.5,
  },

  // Body styles
  bodyLarge: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '400' as const,
    letterSpacing: 0.5,
  },
  bodyMedium: {
    fontSize: 14,
    lineHeight: 20,
    fontWeight: '400' as const,
    letterSpacing: 0.25,
  },
  bodySmall: {
    fontSize: 12,
    lineHeight: 16,
    fontWeight: '400' as const,
    letterSpacing: 0.4,
  },
} as const;

// Spacing Scale (8pt grid system)
export const spacing = {
  // Base units
  xs: 4,    // 4px
  sm: 8,    // 8px
  md: 16,   // 16px
  lg: 24,   // 24px
  xl: 32,   // 32px
  xxl: 48,  // 48px
  xxxl: 64, // 64px

  // Semantic spacing
  tiny: 2,       // 2px
  small: 4,      // 4px
  medium: 8,     // 8px
  large: 12,     // 12px
  xLarge: 16,    // 16px
  xxLarge: 20,   // 20px
  xxxLarge: 24,  // 24px
  huge: 32,      // 32px
  xHuge: 40,     // 40px
  xxHuge: 48,    // 48px

  // Common patterns
  screenPadding: 16,    // Standard screen padding
  cardPadding: 16,      // Card content padding
  listItemPadding: 12,  // List item vertical padding
  buttonPadding: 12,    // Button vertical padding
  inputPadding: 16,     // Input field padding
  sectionSpacing: 24,   // Between major sections
  elementSpacing: 8,    // Between related elements
  
  // Modern grid system
  gridGap: 12,          // Modern card spacing
  heroCardPadding: 24,  // Premium padding for hero elements
  asymmetricMargin: 20, // Breaking uniformity
  microSpacing: 6,      // Fine-tuned micro layouts
} as const;

// Border Radius (Material Design 3)
export const borderRadius = {
  none: 0,
  xs: 4,    // Small components
  sm: 8,    // Cards, buttons
  md: 12,   // Medium components
  lg: 16,   // Large components
  xl: 20,   // Extra large components
  xxl: 28,  // Maximum radius
  full: 9999, // Fully rounded (pills, avatars)

  // Semantic values
  button: 8,
  card: 12,
  modal: 16,
  input: 8,
  avatar: 9999,
} as const;

// Elevation/Shadow (Material Design 3)
export const elevation = {
  none: {
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  level1: {
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.15,
    shadowRadius: 2,
    elevation: 1,
  },
  level2: {
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 2,
  },
  level3: {
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 3,
  },
  level4: {
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 4,
  },
  level5: {
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 5,
  },
  
  // Modern elevation system for premium feel
  hero: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.12,
    shadowRadius: 24,
    elevation: 8,
  },
  modernCard: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 2,
  },
} as const;

// Font Weights
export const fontWeight = {
  thin: '100' as const,
  extraLight: '200' as const,
  light: '300' as const,
  regular: '400' as const,
  medium: '500' as const,
  semiBold: '600' as const,
  bold: '700' as const,
  extraBold: '800' as const,
  black: '900' as const,
} as const;

// Animation Durations (Material Design Motion)
export const duration = {
  fast: 150,     // Quick transitions
  normal: 250,   // Standard transitions
  slow: 350,     // Complex transitions
  slower: 500,   // Major layout changes
} as const;

// Animation Easing
export const easing = {
  linear: 'linear',
  easeIn: 'ease-in',
  easeOut: 'ease-out',
  easeInOut: 'ease-in-out',

  // Material Design curves
  standard: 'cubic-bezier(0.4, 0.0, 0.2, 1)',
  decelerate: 'cubic-bezier(0.0, 0.0, 0.2, 1)',
  accelerate: 'cubic-bezier(0.4, 0.0, 1, 1)',
} as const;

// Icon Sizes
export const iconSize = {
  xs: 12,
  sm: 16,
  md: 20,
  lg: 24,
  xl: 32,
  xxl: 48,

  // Semantic sizes
  button: 20,
  tab: 24,
  appBar: 24,
  avatar: 32,
  hero: 48,
} as const;

// Z-Index Scale
export const zIndex = {
  background: -1,
  default: 0,
  content: 1,
  header: 10,
  overlay: 100,
  modal: 1000,
  toast: 2000,
  tooltip: 3000,
} as const;

// Breakpoints (for responsive design if needed)
export const breakpoints = {
  mobile: 0,
  tablet: 768,
  desktop: 1024,
  wide: 1200,
} as const;

// Component-specific tokens
export const components = {
  button: {
    height: {
      small: 32,
      medium: 40,
      large: 48,
    },
    padding: {
      horizontal: spacing.md,
      vertical: spacing.sm,
    },
    borderRadius: borderRadius.button,
  },

  input: {
    height: 56, // Material Design 3 standard
    padding: spacing.inputPadding,
    borderRadius: borderRadius.input,
  },

  card: {
    padding: spacing.cardPadding,
    borderRadius: borderRadius.card,
    elevation: elevation.level1,
  },

  appBar: {
    height: 56,
    elevation: elevation.level2,
  },

  bottomNavigation: {
    height: 80,
    elevation: elevation.level3,
  },
  
  // Modern hero card with premium styling
  heroCard: {
    padding: spacing.heroCardPadding,
    borderRadius: borderRadius.lg,
    elevation: elevation.hero,
    minHeight: 120,
  },
  
  // Modern grid card styling
  gridCard: {
    padding: spacing.cardPadding,
    borderRadius: borderRadius.card,
    elevation: elevation.modernCard,
    minHeight: 100,
  },
} as const;

// Modern gradient system (colors will be resolved via theme)
export const gradients = {
  hero: {
    // Will be resolved to actual colors in theme context
    primary: ['primary', 'primaryContainer'],
    secondary: ['secondary', 'secondaryContainer'],
    tertiary: ['tertiary', 'tertiaryContainer'],
  },
  subtle: {
    surface: ['surface', 'surfaceVariant'],
    background: ['background', 'surfaceVariant'],
  },
} as const;

// Export all tokens as a single object for convenience
export const designTokens = {
  typography,
  spacing,
  borderRadius,
  elevation,
  fontWeight,
  duration,
  easing,
  iconSize,
  zIndex,
  breakpoints,
  components,
  gradients,
} as const;

// Type definitions
export type Typography = typeof typography;
export type Spacing = typeof spacing;
export type BorderRadius = typeof borderRadius;
export type Elevation = typeof elevation;
export type FontWeight = typeof fontWeight;
export type Duration = typeof duration;
export type Easing = typeof easing;
export type IconSize = typeof iconSize;
export type ZIndex = typeof zIndex;
export type Breakpoints = typeof breakpoints;
export type Components = typeof components;
export type Gradients = typeof gradients;
export type DesignTokens = typeof designTokens;
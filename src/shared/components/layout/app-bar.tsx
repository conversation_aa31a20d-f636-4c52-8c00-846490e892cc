/**
 * @fileoverview Reusable AppBar component following DRY, KISS, YAGNI principles
 * Provides consistent top navigation across the app with Material Design 3 theming
 */

import React from 'react';
import { Appbar } from 'react-native-paper';
import { useRouter } from 'expo-router';
import { usePaperTheme } from '@/shared/hooks/use-theme';
import { haptics } from '@/shared/utils/haptics';
import { useTranslation } from 'react-i18next';

export interface AppBarAction {
  key: string;
  icon: string;
  onPress: () => void;
}

export interface AppBarProps {
  /**
   * Title displayed in the center of the app bar
   */
  title: string;
  
  /**
   * Whether to show the back button (left side)
   * @default false
   */
  showBackButton?: boolean;
  
  /**
   * Whether to show the menu/hamburger button (left side)
   * @default false
   */
  showMenuButton?: boolean;
  
  /**
   * Custom onPress handler for the menu button
   */
  onMenuPress?: () => void;
  
  /**
   * Custom onPress handler for the back button
   * If not provided, uses router.back()
   */
  onBackPress?: () => void;
  
  /**
   * Array of action buttons to display on the right side
   * @default []
   */
  actions?: React.ReactNode[];
  
  /**
   * Custom background color for the app bar
   * If not provided, uses theme.colors.surface
   */
  backgroundColor?: string;
  
  /**
   * Elevation level for the app bar shadow
   * @default 4
   */
  elevation?: number;
  
  /**
   * Custom style for the title text
   */
  titleStyle?: object;
}

/**
 * Reusable AppBar component that follows our design system
 * 
 * @example
 * // Basic usage with back button
 * <AppBar title="Settings" showBackButton />
 * 
 * @example
 * // With menu button and actions
 * <AppBar 
 *   title="Home" 
 *   showMenuButton 
 *   onMenuPress={() => toggleDrawer()}
 *   actions={[
 *     <Appbar.Action key="search" icon="magnify" onPress={() => {}} />,
 *     <Avatar.Icon key="avatar" size={40} icon="account" />
 *   ]}
 * />
 */
export const AppBar: React.FC<AppBarProps> = ({
  title,
  showBackButton = false,
  showMenuButton = false,
  onMenuPress,
  onBackPress,
  actions = [],
  backgroundColor,
  elevation = 4,
  titleStyle,
}) => {
  const { t } = useTranslation('common');
  const theme = usePaperTheme();
  const router = useRouter();

  // Default back handler with haptic feedback
  const handleBackPress = () => {
    // Haptic feedback for back navigation (standard iOS/Android pattern)
    haptics.light();
    
    if (onBackPress) {
      onBackPress();
    } else {
      router.back();
    }
  };

  // Enhanced menu handler with haptic feedback
  const handleMenuPress = () => {
    // Haptic feedback for menu/drawer toggle (Instagram-style navigation)
    haptics.light();
    
    if (onMenuPress) {
      onMenuPress();
    }
  };

  // Render left action (back or menu button)
  const renderLeftAction = () => {
    if (showBackButton) {
      return <Appbar.BackAction onPress={handleBackPress} />;
    }
    if (showMenuButton && onMenuPress) {
      return <Appbar.Action icon="menu" onPress={handleMenuPress} />;
    }
    return null;
  };

  return (
    <Appbar.Header 
      style={{ 
        elevation,
        backgroundColor: backgroundColor || theme.colors.surface 
      }}
    >
      {renderLeftAction()}
      <Appbar.Content title={title} titleStyle={titleStyle} />
      {actions.map((action, index) => (
        <React.Fragment key={index}>
          {action}
        </React.Fragment>
      ))}
    </Appbar.Header>
  );
};

export default AppBar;
import React, { useCallback, useRef, useState } from 'react';
import { View, RefreshControl, StyleSheet, Animated, Easing } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '@/shared/hooks/use-theme';
import { AppBar, AppBarProps } from '@/shared/components/layout/app-bar';
import { BottomNav, BottomNavProps } from '@/shared/components/layout/bottom-navigation';
import { haptics } from '@/shared/utils/haptics';
import type { AppTheme } from '@/shared/utils/theme-utils';

interface ScrollTrigger {
  scrollThreshold?: number;
  hideThreshold?: number;
  condition?: () => boolean;
  enableProgressiveDisclosure?: boolean;
}

interface AnimationConfig {
  duration?: number;
  easing?: 'ease-in-out' | 'spring';
  useNativeDriver?: boolean;
}

interface DynamicBottomSpace {
  enabled: boolean;
  height?: number;
  trigger?: ScrollTrigger;
  content?: React.ReactNode;
  animation?: AnimationConfig;
  backgroundColor?: string;
}



interface ScreenWrapperProps {
  children: React.ReactNode;
  
  // Layout Options
  scrollable?: boolean;
  disableScreenPadding?: boolean; // NEW: Disable horizontal screen padding (for drawer content)
  keyboardShouldPersistTaps?: 'always' | 'never' | 'handled'; // For form interactions
  
  // AppBar Integration - Parent has full control
  showAppBar?: boolean;
  appBarProps?: AppBarProps;
  
  // BottomNav Integration - Accept routes as props
  showBottomNav?: boolean;
  bottomNavProps?: BottomNavProps;
  
  // Note: Modal integration now handled via global BottomSheetModalProvider
  
  // Refresh Control Integration
  enableRefresh?: boolean;
  refreshing?: boolean;
  onRefresh?: () => void;
  
  // Theme Integration
  customBackgroundColor?: string;
  
  // Dynamic bottom space enhancement
  dynamicBottomSpace?: DynamicBottomSpace;
  
}

/**
 * Enhanced ScreenWrapper - A simple, "dumb" layout component
 * 
 * Philosophy: Handles consistent styling and layout structure while giving
 * parent components full control over behavior and state.
 * 
 * What it does:
 * ✅ Safe area handling
 * ✅ Conditional AppBar rendering (parent controls props)
 * ✅ Conditional BottomNav rendering (parent controls props)
 * ✅ Layout utilities (scrollable, refresh control)
 * ✅ Optional modal provider integration
 * ✅ Theme integration
 * 
 * What it does NOT do:
 * ❌ State management (navigation indices, drawer state)
 * ❌ Business logic (data fetching, user interactions)
 * ❌ Navigation decisions (routing, tab switching logic)
 * 
 * @example
 * // Basic usage
 * <ScreenWrapper>
 *   <Content />
 * </ScreenWrapper>
 * 
 * @example
 * // With AppBar and BottomNav
 * <ScreenWrapper
 *   showAppBar={true}
 *   appBarProps={{ title: "Settings", showBackButton: true }}
 *   showBottomNav={true}
 *   bottomNavProps={{ routes, activeIndex: 0, onTabPress: handleTab }}
 * >
 *   <Content />
 * </ScreenWrapper>
 * 
 * @example
 * // Scrollable with refresh
 * <ScreenWrapper
 *   scrollable={true}
 *   enableRefresh={true}
 *   refreshing={isRefreshing}
 *   onRefresh={handleRefresh}
 * >
 *   <Content />
 * </ScreenWrapper>
 */
export const ScreenWrapper: React.FC<ScreenWrapperProps> = ({
  children,
  scrollable = false,
  disableScreenPadding = false, // NEW: Default to false (normal padding behavior)
  keyboardShouldPersistTaps = 'never',
  
  // AppBar props - parent has full control
  showAppBar = false,
  appBarProps,
  
  // BottomNav props - accept routes as props
  showBottomNav = false,
  bottomNavProps,
  
  // Note: Modal integration now handled via global BottomSheetModalProvider
  
  // Refresh props
  enableRefresh = false,
  refreshing = false,
  onRefresh,
  
  // Theme props
  customBackgroundColor,
  
  // Dynamic bottom space props
  dynamicBottomSpace,
  
}) => {
  const { theme, spacing, borderRadius, elevation } = useTheme();
  const insets = useSafeAreaInsets();
  const styles = getStyles(theme, insets, spacing, borderRadius, elevation, disableScreenPadding);
  
  // Haptic feedback wrapper for refresh control
  const handleRefreshWithHaptics = useCallback(async () => {
    if (!onRefresh) return;
    
    try {
      // Call the original refresh function
      await onRefresh();
      
      // Add success haptic feedback when refresh completes
      haptics.success();
    } catch (error) {
      // Add error haptic feedback if refresh fails
      haptics.error();
      console.error('Refresh failed with error:', error);
    }
  }, [onRefresh]);
  
  
  
  // Dynamic bottom space state management
  const autoTrigger = dynamicBottomSpace?.enabled && dynamicBottomSpace?.trigger?.scrollThreshold === 0.0;
  const [bottomSpaceTriggered, setBottomSpaceTriggered] = useState(autoTrigger);
  const contentPaddingAnimation = useRef(new Animated.Value(autoTrigger ? 0 : 0)).current;
  
  // Scroll trigger handler
  const handleScrollTrigger = useCallback((triggered: boolean) => {
    if (dynamicBottomSpace?.enabled) {
      setBottomSpaceTriggered(triggered);
      
      // Animate content padding to accommodate enhanced bottom nav
      const targetPadding = triggered ? 0 : 0; // 120px extra space + 16px base padding
      
      Animated.timing(contentPaddingAnimation, {
        toValue: targetPadding,
        duration: dynamicBottomSpace.animation?.duration || 400,
        easing: dynamicBottomSpace.animation?.easing === 'spring' 
          ? Easing.elastic(1.2) 
          : Easing.bezier(0.4, 0.0, 0.2, 1),
        useNativeDriver: dynamicBottomSpace.animation?.useNativeDriver ?? false,
      }).start();
    }
  }, [dynamicBottomSpace, contentPaddingAnimation]);

  
  // Content renderer with dynamic padding support
  const renderContent = () => {
    if (scrollable) {
      return (
        <Animated.ScrollView
          style={styles.screenContainer}
          contentContainerStyle={{
            paddingBottom: contentPaddingAnimation
          }}
          keyboardShouldPersistTaps={keyboardShouldPersistTaps}
          onScroll={({ nativeEvent }) => {
            // Scroll trigger logic
            if (dynamicBottomSpace?.trigger) {
              const threshold = dynamicBottomSpace.trigger.scrollThreshold || 0.6;
              const hideThreshold = dynamicBottomSpace.trigger.hideThreshold || 0.3;
              
              // Skip scroll-based logic if threshold is 0 (always show) or hideThreshold is -1 (never hide)
              if (threshold === 0.0 && hideThreshold === -1) {
                return; // Always show, no scroll-based changes needed
              }
              
              const { contentOffset, contentSize, layoutMeasurement } = nativeEvent;
              const scrollPercent = contentOffset.y / (contentSize.height - layoutMeasurement.height);
              
              const conditionMet = dynamicBottomSpace.trigger.condition?.() ?? true;
              const shouldTrigger = scrollPercent >= threshold && conditionMet;
              const shouldHide = hideThreshold !== -1 && scrollPercent <= hideThreshold;
             
              if (shouldTrigger && !bottomSpaceTriggered) {
                console.log('🚀 Triggering bottom space!');
                handleScrollTrigger(true);
              } else if (shouldHide && bottomSpaceTriggered) {
                console.log('🔽 Hiding bottom space!');
                handleScrollTrigger(false);
              }
            }
          }}
          scrollEventThrottle={16}
          refreshControl={
            enableRefresh && onRefresh ? (
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefreshWithHaptics}
                tintColor={theme.colors.primary}
                colors={[theme.colors.primary]}
              />
            ) : undefined
          }
        >
          {children}
        </Animated.ScrollView>
      );
    }
    
    return (
      <View style={styles.screenContainer}>
        {children}
      </View>
    );
  };

  
  // Enhanced bottom nav props with dynamic space integration
  const enhancedBottomNavProps = {
    ...bottomNavProps,
    enhancedMode: dynamicBottomSpace?.enabled ? {
      enabled: bottomSpaceTriggered,
      expandedHeight: dynamicBottomSpace.height || 180,
      extraContent: dynamicBottomSpace.content,
      animationConfig: dynamicBottomSpace.animation,
    } : undefined,
  };


  // Main layout structure with enhanced navigation support
  const renderLayout = () => (
    <View style={[styles.container, { backgroundColor: customBackgroundColor || theme.colors.background }]}>
      {/* Fixed App Bar */}
      {showAppBar && appBarProps && (
        <AppBar {...appBarProps} />
      )}
      
      {/* Scrollable Content */}
      <View style={styles.contentContainer}>
        {renderContent()}
      </View>
      
      {/* Bottom Navigation */}
      {showBottomNav && bottomNavProps && (
        <View style={styles.bottomNavContainer}>
          <BottomNav {...enhancedBottomNavProps} />
        </View>
      )}
      
      {/* Dynamic Bottom Space (Discovery Mode) */}
      {!showBottomNav && dynamicBottomSpace?.enabled && bottomSpaceTriggered && (
        <Animated.View 
          style={[
            styles.dynamicBottomSpace,
            {
              height: dynamicBottomSpace.height || 180,
              backgroundColor: dynamicBottomSpace.backgroundColor || theme.colors.surface,
              // Ensure shadow is visible on colored backgrounds
              shadowColor: '#000',
              shadowOffset: { width: 0, height: -2 },
              shadowOpacity: 0.15,
              shadowRadius: 8,
              elevation: 8,
            }
          ]}
        >
          {dynamicBottomSpace.content}
        </Animated.View>
      )}
    </View>
  );
  
  // Note: Modal providers are now handled via global BottomSheetModalProvider
  // instead of being conditionally wrapped here
  return renderLayout();
};

// Styles with chat input integration
const getStyles = (theme: AppTheme, insets: any, spacing: any, borderRadius: any, elevation: any, disableScreenPadding: boolean = false) => StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    flex: 1,
  },
  bottomNavContainer: {
    // Bottom navigation takes its natural height
  },
  screenContainer: {
    flex: 1,
  },
  dynamicBottomSpace: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
    elevation: 8,
    boxShadow: '0px -2px 4px rgba(0, 0, 0, 0.1)',
  },

});

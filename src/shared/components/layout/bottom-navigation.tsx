/**
 * @fileoverview Reusable BottomNavigation component following DRY, KISS, YAGNI principles
 * Provides consistent bottom navigation across the app with Material Design 3 theming
 */

import React, { useState, useRef, useEffect } from 'react';
import { BottomNavigation, Icon } from 'react-native-paper';
import { View, Animated, Easing } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { usePaperTheme } from '@/shared/hooks/use-theme';
import { haptics } from '@/shared/utils/haptics';

export interface EnhancedMode {
  enabled: boolean;
  expandedHeight?: number;
  extraContent?: React.ReactNode;
  animationConfig?: {
    duration?: number;
    easing?: 'ease-in-out' | 'spring';
  };
}

export interface BottomNavRoute {
    key: string;
    title: string;
    focusedIcon: string;
    unfocusedIcon?: string;
    badge?: boolean | number | string;
}

export interface BottomNavProps {
    /**
     * Array of navigation routes
     */
    routes: BottomNavRoute[];

    /**
     * Currently active route index
     */
    activeIndex: number;

    /**
     * Callback when a tab is pressed
     * @param index - Index of the pressed tab
     * @param route - Route object of the pressed tab
     */
    onTabPress: (index: number, route: BottomNavRoute) => void;

    /**
     * Navigation mode:
     * - 'bar': Standalone navigation bar (BottomNavigation.Bar) - for use with external routing
     * - 'full': Full-page navigation with scenes (BottomNavigation) - handles its own content rendering
     * @default 'bar'
     */
    mode?: 'bar' | 'full';

    /**
     * Scene renderer for 'full' mode only
     * Required when mode='full', ignored when mode='bar'
     */
    renderScene?: (props: { route: BottomNavRoute; jumpTo?: (key: string) => void }) => React.ReactNode;

    /**
     * Custom background color for the bottom navigation
     * If not provided, uses theme.colors.surface
     */
    backgroundColor?: string;

    /**
     * Elevation level for the bottom navigation shadow
     * @default 4 (matches AppBar)
     */
    elevation?: number;

    /**
     * Whether to show labels on the tabs
     * @default true
     */
    showLabels?: boolean;

    /**
     * Whether to use compact mode (smaller tabs)
     * @default false
     */
    compact?: boolean;

    /**
     * Scene animation type for 'full' mode only
     * @default undefined (no animation)
     */
    sceneAnimationType?: 'shifting' | 'opacity';

    /**
     * Whether to enable scene animation for 'full' mode
     * @default false
     */
    sceneAnimationEnabled?: boolean;

    /**
     * Enhanced mode for expandable navigation (V4 feature)
     * Enables dynamic height expansion with extra content
     */
    enhancedMode?: EnhancedMode;
}

/**
 * Reusable BottomNavigation component that follows our design system
 * Supports both standalone navigation bar and full-page navigation with scenes
 * 
 * @example
 * // Bar mode (default) - for use with external routing (Expo Router, React Navigation)
 * <BottomNav
 *   mode="bar"
 *   routes={[
 *     { key: 'home', title: 'Home', focusedIcon: 'home', unfocusedIcon: 'home-outline' },
 *     { key: 'search', title: 'Search', focusedIcon: 'magnify' },
 *   ]}
 *   activeIndex={0}
 *   onTabPress={(index, route) => router.push(`/${route.key}`)}
 * />
 * 
 * @example
 * // Full mode - handles its own content rendering (like React Native Paper examples)
 * <BottomNav
 *   mode="full"
 *   routes={routes}
 *   activeIndex={index}
 *   onTabPress={(index) => setIndex(index)}
 *   renderScene={({ route }) => <SomeScreen route={route} />}
 *   sceneAnimationType="opacity"
 *   sceneAnimationEnabled={true}
 * />
 * 
 * @example
 * // With badges and custom styling
 * <BottomNav
 *   routes={[
 *     { key: 'home', title: 'Home', focusedIcon: 'home', unfocusedIcon: 'home-outline' },
 *     { key: 'messages', title: 'Messages', focusedIcon: 'message', badge: 3 },
 *   ]}
 *   activeIndex={0}
 *   onTabPress={handleTabPress}
 *   compact={true}
 * />
 */
export const BottomNav: React.FC<BottomNavProps> = ({
    routes,
    activeIndex,
    onTabPress,
    mode = 'bar',
    renderScene,
    backgroundColor,
    elevation = 4,
    showLabels = true,
    compact = false,
    sceneAnimationType,
    sceneAnimationEnabled = false,
    enhancedMode,
}) => {
    const theme = usePaperTheme();
    const insets = useSafeAreaInsets();
    
    // Enhanced mode animation state
    const [currentHeight, setCurrentHeight] = useState(60);
    const heightAnimation = useRef(new Animated.Value(60)).current;

    // Handle enhanced mode transitions
    useEffect(() => {
        if (enhancedMode?.enabled) {
            const targetHeight = enhancedMode.expandedHeight || 180;
            setCurrentHeight(targetHeight);
            
            Animated.timing(heightAnimation, {
                toValue: targetHeight,
                duration: enhancedMode.animationConfig?.duration || 400,
                easing: enhancedMode.animationConfig?.easing === 'spring' 
                    ? Easing.elastic(1.2) 
                    : Easing.bezier(0.4, 0.0, 0.2, 1),
                useNativeDriver: false,
            }).start();
        } else {
            setCurrentHeight(60);
            Animated.timing(heightAnimation, {
                toValue: 60,
                duration: enhancedMode?.animationConfig?.duration || 400,
                useNativeDriver: false,
            }).start();
        }
    }, [enhancedMode?.enabled, enhancedMode?.expandedHeight, heightAnimation, enhancedMode?.animationConfig]);

    // Convert our routes to React Native Paper format
    const paperRoutes = routes.map(route => ({
        key: route.key,
        title: showLabels ? route.title : '',
        focusedIcon: route.focusedIcon,
        unfocusedIcon: route.unfocusedIcon,
        badge: route.badge,
    }));

    // Common icon renderer for both modes
    const renderIcon = ({ route, focused, color }: { route: any; focused: boolean; color: string }) => (
        <Icon
            source={focused ? route.focusedIcon : (route.unfocusedIcon || route.focusedIcon)}
            size={24}
            color={color}
        />
    );

    // Common style configuration
    const commonStyle = {
        backgroundColor: backgroundColor || theme.colors.surface,
        elevation, // Configurable elevation (default 4 to match AppBar)
    };

    if (mode === 'full') {
        // Full mode: BottomNavigation with scenes (like React Native Paper examples)
        if (!renderScene) {
            console.warn('BottomNav: renderScene is required when mode="full"');
            return null;
        }

        return (
            <BottomNavigation
                navigationState={{
                    index: activeIndex,
                    routes: paperRoutes
                }}
                onIndexChange={(index) => {
                    // Haptic feedback for tab switching (Instagram/Spotify style)
                    haptics.tab();

                    const routeObj = routes[index];
                    onTabPress(index, routeObj);
                }}
                renderScene={({ route }) => {
                    // Find the original route object
                    const originalRoute = routes.find(r => r.key === route.key);
                    return originalRoute ? renderScene({ route: originalRoute }) : null;
                }}
                renderIcon={renderIcon}
                getLabelText={({ route }) => showLabels ? route.title : ''}
                style={commonStyle}
                compact={compact}
                safeAreaInsets={{ bottom: insets.bottom }}
                sceneAnimationType={sceneAnimationType}
                sceneAnimationEnabled={sceneAnimationEnabled}
            />
        );
    }

    // Bar mode (default): BottomNavigation.Bar for standalone navigation
    // Enhanced mode: Wrap in animated container with extra content
    if (enhancedMode) {
        return (
            <Animated.View style={[
                {
                    height: heightAnimation,
                    backgroundColor: backgroundColor || theme.colors.surface,
                    elevation: elevation,
                    paddingBottom: insets.bottom,
                    shadowColor: '#000',
                    shadowOffset: { width: 0, height: -2 },
                    shadowOpacity: 0.1,
                    shadowRadius: 4,
                },
            ]}>
                {/* Enhanced Content Area */}
                {enhancedMode.enabled && enhancedMode.extraContent && (
                    <View style={{
                        flex: 1,
                        paddingHorizontal: 20,
                        paddingVertical: 12,
                        justifyContent: 'center',
                    }}>
                        {enhancedMode.extraContent}
                    </View>
                )}

                {/* Standard Navigation */}
                <View style={{
                    height: 60,
                    opacity: enhancedMode.enabled ? 0.6 : 1.0
                }}>
                    <BottomNavigation.Bar
                        navigationState={{
                            index: activeIndex,
                            routes: paperRoutes
                        }}
                        safeAreaInsets={{ bottom: 0 }} // Already handled by parent
                        onTabPress={({ route: paperRoute }) => {
                            // Find the index of the pressed route
                            const index = paperRoutes.findIndex(r => r.key === paperRoute.key);
                            if (index !== -1) {
                                // Haptic feedback for tab switching (Instagram/Spotify style)
                                haptics.tab();

                                const routeObj = routes[index];
                                onTabPress(index, routeObj);
                            }
                        }}
                        renderIcon={renderIcon}
                        getLabelText={({ route }) => showLabels ? route.title : ''}
                        style={{ backgroundColor: 'transparent' }}
                        compact={compact}
                    />
                </View>
            </Animated.View>
        );
    }

    // Standard bar mode without enhancement
    return (
        <BottomNavigation.Bar
            navigationState={{
                index: activeIndex,
                routes: paperRoutes
            }}
            safeAreaInsets={{ bottom: insets.bottom }}
            onTabPress={({ route: paperRoute }) => {
                // Find the index of the pressed route
                const index = paperRoutes.findIndex(r => r.key === paperRoute.key);
                if (index !== -1) {
                    // Haptic feedback for tab switching (Instagram/Spotify style)
                    haptics.tab();

                    const routeObj = routes[index];
                    onTabPress(index, routeObj);
                }
            }}
            renderIcon={renderIcon}
            getLabelText={({ route }) => showLabels ? route.title : ''}
            style={commonStyle}
            compact={compact}
        />
    );
};

export default BottomNav;
/**
 * @fileoverview Refactored ChatInputBar component - V2.2 Direct Input (Logic Preserved)
 * * This version restores the character count display to maintain full functional parity
 * with the original modal implementation, as per EARS REQ-1.2.
 * The KeyboardAvoidingView has also been made more robust.
 */

import React, { useCallback } from 'react';
import { View, TextInput, KeyboardAvoidingView, Platform, StyleSheet } from 'react-native';
import { IconButton, Text } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '@/shared/hooks/use-theme';
import { haptics } from '@/shared/utils/haptics';
import type { AppTheme } from '@/shared/utils/theme-utils';

interface ChatInputBarProps {
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;
  onSendMessage: () => void;
  disabled?: boolean;
  isSendEnabled?: boolean;
  maxLength?: number;
  characterCount?: number; // **RESTORED: Prop to receive character count**
}

export const ChatInputBar: React.FC<ChatInputBarProps> = ({
  placeholder,
  value,
  onChangeText,
  onSendMessage,
  disabled = false,
  isSendEnabled = false,
  maxLength,
  characterCount,
}) => {
  const { theme, spacing } = useTheme();
  const insets = useSafeAreaInsets();
  const styles = getStyles(theme, spacing);

  const canSend = isSendEnabled && !disabled;

  const handleFocus = useCallback(() => {
    haptics.light();
  }, []);
  
  const handleSendMessageWithHaptics = useCallback(() => {
    if (canSend) {
        haptics.medium(); // Per REQ-2.1
        onSendMessage();
    }
  }, [canSend, onSendMessage]);
  
  // Determine character count color
  const isNearLimit = maxLength && characterCount && characterCount > maxLength * 0.9;
  const isOverLimit = maxLength && characterCount && characterCount > maxLength;
  const characterCountColor = isOverLimit ? theme.colors.error : isNearLimit ? theme.colors.tertiary : theme.colors.onSurfaceVariant;

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 20 : 0} // Standard offset
    >
      <View
        style={[
          styles.container,
          { paddingBottom: insets.bottom > 0 ? insets.bottom : spacing.sm },
        ]}
      >
        {/* Single bordered container with both rows inside */}
        <View style={styles.inputContainer}>
          {/* Input Row */}
          <TextInput
            placeholder={placeholder}
            value={value}
            onChangeText={onChangeText}
            multiline
            numberOfLines={2}
            maxLength={maxLength}
            onFocus={handleFocus}
            editable={!disabled}
            style={styles.textInput}
            placeholderTextColor={theme.colors.onSurfaceVariant}
            accessibilityLabel="Message input"
          />

          {/* Actions Row */}
          <View style={styles.actionsRow}>
            <View style={styles.leftActions}>
              <IconButton
                icon="plus-circle-outline"
                iconColor={theme.colors.onSurfaceVariant}
                size={20}
                style={styles.iconButton}
              />
            </View>

            <View style={styles.rightActions}>
              <IconButton
                icon="send-circle"
                mode="contained"
                size={24}
                disabled={!canSend}
                onPress={handleSendMessageWithHaptics}
                iconColor={canSend ? theme.colors.primary : theme.colors.onSurfaceDisabled}
                style={styles.iconButton}
                accessibilityLabel="Send message"
              />
            </View>
          </View>
        </View>
      </View>
    </KeyboardAvoidingView>
  );
};

const getStyles = (theme: AppTheme, spacing: any) => StyleSheet.create({
  container: {
    backgroundColor: 'transparent', // Match main content background
    paddingHorizontal: spacing.md,
    paddingTop: spacing.sm,
  },
  inputContainer: {
    borderWidth: 1,
    borderColor: theme.colors.outlineVariant,
    borderRadius: 24,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: 'transparent', // Transparent like Gemini
  },
  textInput: {
    backgroundColor: 'transparent',
    fontSize: theme.typography.bodyLarge.fontSize,
    color: theme.colors.onSurface,
    maxHeight: 150,
    minHeight: 30,
    marginBottom: spacing.xs,
    textAlignVertical: 'top',
  },
  actionsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  leftActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rightActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    margin: 0,
    marginHorizontal: -spacing.xs, // Reduce horizontal spacing
  },
});
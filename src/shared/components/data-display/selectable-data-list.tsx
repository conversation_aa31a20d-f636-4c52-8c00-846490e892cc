import React, { useEffect, useRef } from 'react';
import { View, ScrollView } from 'react-native';
import {
  Text,
  Surface,
  HelperText,
  List,
  Checkbox,
  Icon,
  Chip,
} from 'react-native-paper';
import { useTheme } from '@/shared/hooks';
import { haptics } from '@/shared/utils/haptics';
import { UniversalList } from './universal-list';

// Constants for text scaling
const BADGE_TEXT_SCALE = 0.8;
const SUBTITLE_TEXT_SCALE = 0.9;

/**
 * Highly reusable SelectableDataList Component
 * 
 * Supports both selectable and non-selectable modes
 * Fully theme-compliant and accessible
 * Generic data structure with configurable field accessors
 */

interface SelectableDataListProps<T> {
  // Data
  items: T[];
  selectedItems?: T[];
  onItemToggle?: (item: T, isSelected: boolean) => void;
  
  // Core Field Accessors (flexible for any data structure)
  getItemId: (item: T) => string;
  getItemTitle: (item: T) => string;
  getItemDescription: (item: T) => string;
  
  // Optional Field Accessors (NEW - Enhanced flexibility)
  getItemSubtitle?: (item: T) => string; // For explanation/secondary text
  getTitleBadges?: (item: T) => {
    text: string;
    color?: string;
    icon?: string;
  }[]; // For chips/badges in title
  
  // Display Control Props (NEW - Progressive enhancement)
  showSubtitle?: boolean; // Control subtitle display
  showTitleBadges?: boolean; // Control title badges display
  showLeftComponent?: boolean; // Control left component display
  
  // Selection Configuration
  maxSelection?: number;
  minSelection?: number; // For validation support
  selectable?: boolean;
  
  // Selection Validation (NEW - Enhanced validation support)
  onSelectionValidChange?: (isValid: boolean) => void; // Callback when selection validity changes
  
  // UI Configuration
  translationKeys: {
    subtitlePrefix: string;
    subtitleSuffix: string;
    counterPrefix: string;
    emptyStateTitle: string;
    emptyStateMessage: string;
  };
  
  // Advanced Customization (NEW - Enhanced component support)
  customLeftComponent?: (item: T, isSelected: boolean, isDisabled: boolean) => React.ReactNode;
  customRightComponent?: (item: T, isSelected: boolean, isDisabled: boolean) => React.ReactNode;
  errors?: Record<string, string>;
  errorKey?: string;
}

export function SelectableDataList<T>({
  items,
  selectedItems = [],
  onItemToggle,
  getItemId,
  getItemTitle,
  getItemDescription,
  // Optional Field Accessors (NEW)
  getItemSubtitle,
  getTitleBadges,
  // Display Control Props (NEW)
  showSubtitle = false,
  showTitleBadges = false,
  showLeftComponent = false,
  maxSelection = Infinity,
  minSelection = 0,
  selectable = true,
  // Selection Validation (NEW)
  onSelectionValidChange,
  translationKeys,
  // Advanced Customization (NEW)
  customLeftComponent,
  customRightComponent,
  errors = {},
  errorKey,
}: SelectableDataListProps<T>) {
  const theme = useTheme();

  // Selection logic
  const selectedItemIds = selectedItems.map(item => getItemId(item));
  const selectionCount = selectedItems.length;

  // Selection validation
  const isSelectionValid = selectionCount >= minSelection && selectionCount <= maxSelection;
  const prevIsValid = useRef<boolean>(isSelectionValid);

  // Notify parent when selection validity changes
  useEffect(() => {
    if (onSelectionValidChange && isSelectionValid !== prevIsValid.current) {
      prevIsValid.current = isSelectionValid;
      onSelectionValidChange(isSelectionValid);
    }
  }, [isSelectionValid, onSelectionValidChange]);

  // Handle item selection
  const handleItemToggle = (item: T) => {
    if (!selectable || !onItemToggle) return;

    const isSelected = selectedItemIds.includes(getItemId(item));
    const isAtMaxSelection = !isSelected && selectionCount >= maxSelection;
    
    // Enhanced contextual haptic feedback
    if (isAtMaxSelection) {
      haptics.warning(); // User trying to select beyond limit
      return; // Don't allow selection
    } else if (isSelected) {
      haptics.toggle(); // Deselecting item
    } else {
      haptics.select(); // Selecting item
    }
    
    onItemToggle(item, !isSelected);
  };

  // Custom title rendering with badges support
  const renderTitle = (item: T) => {
    if (!showTitleBadges || !getTitleBadges) {
      return getItemTitle(item); // Simple string for backward compatibility
    }
    
    const badges = getTitleBadges(item);
    const TitleWithBadges = ({ ellipsizeMode, color, fontSize }: { ellipsizeMode: any; color: string; fontSize: number }) => (
      <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
        <Text 
          style={{ color, fontSize, flex: 1, fontWeight: 'bold' }}
          ellipsizeMode={ellipsizeMode}
          numberOfLines={2}
        >
          {getItemTitle(item)}
        </Text>
        {badges.map((badge, index) => (
          <Chip 
            key={index}
            icon={badge.icon}
            compact
            textStyle={{ fontSize: fontSize * BADGE_TEXT_SCALE }}
            style={{ 
              marginLeft: theme.spacing.xs,
              backgroundColor: badge.color || theme.colors.primaryContainer 
            }}
          >
            {badge.text}
          </Chip>
        ))}
      </View>
    );
    TitleWithBadges.displayName = 'TitleWithBadges';
    return TitleWithBadges;
  };

  // Custom description rendering with subtitle support
  const renderDescription = (item: T) => {
    const hasSubtitle = showSubtitle && getItemSubtitle;
    
    if (!hasSubtitle) {
      return getItemDescription(item); // Simple string for backward compatibility
    }
    
    const DescriptionWithSubtitle = ({ ellipsizeMode, color, fontSize }: { ellipsizeMode: any; color: string; fontSize: number }) => (
      <View>
        {/* Main description */}
        <Text 
          style={{ color, fontSize }}
          ellipsizeMode={ellipsizeMode}
          numberOfLines={3}
        >
          {getItemDescription(item)}
        </Text>
        {/* Subtitle in italic */}
        <Text 
          style={{ 
            color: theme.colors.onSurfaceVariant, 
            fontSize: fontSize * SUBTITLE_TEXT_SCALE,
            fontStyle: 'italic',
            marginTop: theme.spacing.xs 
          }}
          ellipsizeMode={ellipsizeMode}
          numberOfLines={2}
        >
          {getItemSubtitle!(item)}
        </Text>
      </View>
    );
    DescriptionWithSubtitle.displayName = 'DescriptionWithSubtitle';
    return DescriptionWithSubtitle;
  };

  // Left component rendering
  const renderLeftComponent = (item: T, isSelected: boolean, isDisabled: boolean) => {
    if (!showLeftComponent || !customLeftComponent) {
      return undefined; // No left component
    }
    const LeftComponent = () => customLeftComponent(item, isSelected, isDisabled);
    LeftComponent.displayName = 'LeftComponent';
    return LeftComponent;
  };

  // Smart right component rendering (respects selection mode)
  const renderRightComponent = (item: T) => {
    const isSelected = selectedItemIds.includes(getItemId(item));
    const isDisabled = !isSelected && selectionCount >= maxSelection;

    // When selectable, only show checkbox
    if (selectable) {
      const CheckboxComponent = () => (
        <Checkbox.Android
          status={isSelected ? 'checked' : 'unchecked'}
          disabled={isDisabled}
          onPress={() => handleItemToggle(item)}
          accessibilityLabel={isSelected ? `Uncheck ${getItemTitle(item)}` : `Check ${getItemTitle(item)}`}
        />
      );
      CheckboxComponent.displayName = 'CheckboxComponent';
      return CheckboxComponent;
    }

    // When not selectable, allow custom right component
    if (customRightComponent) {
      const CustomRightComponent = () => customRightComponent(item, isSelected, isDisabled);
      CustomRightComponent.displayName = 'CustomRightComponent';
      return CustomRightComponent;
    }

    return undefined; // No right component
  };

  // This function encapsulates the rendering of ONE selectable item.
  const renderSelectableItem = (item: T, index: number) => {
    const itemId = getItemId(item);
    const isSelected = selectedItemIds.includes(itemId);
    const isDisabled = selectable && !isSelected && selectionCount >= maxSelection;

    // Return the exact List.Item JSX from the original file
    return (
      <List.Item
        title={renderTitle(item)}
        description={renderDescription(item)}
        titleStyle={{ fontWeight: 'bold' }}
        titleNumberOfLines={2}
        descriptionNumberOfLines={3}
        onPress={() => !isDisabled && handleItemToggle(item)}
        disabled={isDisabled}
        style={{
          paddingVertical: theme.spacing.lg,
          paddingHorizontal: theme.spacing.screenPadding, 
          backgroundColor: isSelected ? theme.colors.surfaceVariant : 'transparent',
        }}
        accessibilityLabel={`${getItemTitle(item)}. ${getItemDescription(item)}. ${isSelected ? 'Selected' : 'Not selected'}`}
        accessibilityRole={selectable ? 'checkbox' : undefined}
        accessibilityState={selectable ? { checked: isSelected, disabled: isDisabled } : undefined}
        left={renderLeftComponent(item, isSelected, isDisabled)}
        right={renderRightComponent(item)}
      />
    );
  };

  return (
    <View style={{ flex: 1 }}>
      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{}}
        showsVerticalScrollIndicator={false}
      >
        {items.length > 0 ? (
          <>
            {/* Selection Counter (only when selectable) */}
            {selectable && (
              <View style={{
                paddingHorizontal: theme.spacing.screenPadding,
                paddingBottom: theme.spacing.md, // Add padding to create space
                flexDirection: 'row',
                alignItems: 'center'
              }}>
                <Icon source="check-circle" size={18} color={theme.colors.primary} />
                <Text 
                  variant="titleMedium"
                  style={{ 
                    color: theme.colors.onSurface,
                    marginLeft: theme.spacing.sm
                  }}
                >
                  {translationKeys.counterPrefix}{selectionCount}/{items.length}
                </Text>
              </View>
            )}

            {/* Main List Section */}
            <View
              style={{
                backgroundColor: theme.colors.surface,
                borderTopWidth: theme.spacing.xs / 2, // Using theme spacing for border width
                borderTopColor: theme.colors.primary,
              }}
            >
              <UniversalList<T>
                items={items}
                getItemId={getItemId}
                renderItem={renderSelectableItem}
                useInsetDividers={false} // Use inset dividers as per original design
              />
            </View>
          </>
        ) : (
          /* Empty State */
          <Surface mode="flat" style={{ 
            backgroundColor: theme.colors.errorContainer, 
            borderRadius: theme.spacing.lg, 
            marginHorizontal: theme.spacing.screenPadding 
          }}>
            <View style={{ padding: theme.spacing.xl }}>
              <Text variant="titleMedium" style={{ 
                color: theme.colors.onErrorContainer, 
                textAlign: 'center', 
                marginBottom: theme.spacing.sm 
              }}>
                {translationKeys.emptyStateTitle}
              </Text>
              <Text variant="bodyMedium" style={{ 
                color: theme.colors.onErrorContainer, 
                textAlign: 'center' 
              }}>
                {translationKeys.emptyStateMessage}
              </Text>
            </View>
          </Surface>
        )}
      </ScrollView>

      {/* Error Display (optional) */}
      {errorKey && (
        <HelperText 
          type="error" 
          visible={!!errors[errorKey]} 
          style={{ 
            paddingHorizontal: theme.spacing.screenPadding, 
            color: theme.colors.error 
          }}
        >
          {errors[errorKey]}
        </HelperText>
      )}
    </View>
  );
}
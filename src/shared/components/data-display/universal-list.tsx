/**
 * @fileoverview UniversalList - Generic List Component
 * Unified component for both selectable lists and accordion lists
 * Eliminates code duplication between SelectableDataList and PropertiesListUI
 */

import React from 'react';
import { List, Divider } from 'react-native-paper';

interface UniversalListProps<T> {
  items: T[];
  getItemId: (item: T) => string;
  
  // The CORE of this refactor. This function receives data and returns a JSX element.
  renderItem: (item: T, index: number) => React.ReactNode;

  // Optional shared features
  listHeaderComponent?: React.ReactNode;
  useInsetDividers?: boolean; // Controls divider style for all items
}

export function UniversalList<T>({
  items,
  getItemId,
  renderItem,
  listHeaderComponent,
  useInsetDividers = false,
}: UniversalListProps<T>) {
  if (items.length === 0) {
    // Handle empty state here, maybe with another prop
    return null; 
  }

  return (
    <List.Section>
      {listHeaderComponent}
      <Divider /> 
      {items.map((item, index) => {
        const key = getItemId(item);
        return (
          <React.Fragment key={key}>
            {renderItem(item, index)}
            {index < items.length - 1 && (
              useInsetDividers ? <Divider leftInset /> : <Divider />
            )}
          </React.Fragment>
        );
      })}
    </List.Section>
  );
}
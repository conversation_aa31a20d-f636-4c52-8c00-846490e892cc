import React, { useRef, useEffect, useCallback, useMemo } from 'react';
import { View, Platform, Animated as RNAnimated } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { Text, List, Divider } from 'react-native-paper';
import { 
  BottomSheetModal, 
  BottomSheetScrollView, 
  BottomSheetBackdrop,
  BottomSheetHandle
} from '@gorhom/bottom-sheet';
import { useTheme } from '@/shared/hooks/use-theme';
import { useTranslation } from 'react-i18next';
import { haptics } from '@/shared/utils/haptics';

// Import types from shared location following DRY principle
import type { 
  EnhancedAIStreamingModalProps,
  StreamingItem,
  ModalState
} from './types';

// Constants for shimmer animation
const SHIMMER_INITIAL_OPACITY = 0.5;
const SHIMMER_ANIMATION_DURATION = 600;
const SKELETON_ICON_SIZE_MULTIPLIER = 1.5;
const SKELETON_ICON_MARGIN_MULTIPLIER = 2;
const SKELETON_LINE_HEIGHT_OFFSET = 2;

/**
 * Get analysis type specific content (icons and text)
 * Uses proper i18n translations - icons only, text comes from props
 */
const getAnalysisContent = (type: string) => {
  switch (type) {
    case 'symptoms':
      return { icon: 'medical-bag' };
    case 'properties':
      return { icon: 'leaf' };
    case 'causes':
      return { icon: 'format-list-bulleted' };
    default:
      return { icon: 'magnify' };
  }
};

/**
 * Enhanced Skeleton loader component with dynamic shimmer effect
 * Shows loading placeholders that get progressively replaced by real content
 * Exact implementation from EnhancedAIStreamingModal
 */
const SkeletonLoader = ({ theme }: { theme: any }) => {
  const opacity = useRef(new RNAnimated.Value(SHIMMER_INITIAL_OPACITY)).current;

  useEffect(() => {
    RNAnimated.loop(
      RNAnimated.sequence([
        RNAnimated.timing(opacity, { 
          toValue: 1, 
          duration: SHIMMER_ANIMATION_DURATION, 
          useNativeDriver: Platform.OS !== 'web' 
        }),
        RNAnimated.timing(opacity, { 
          toValue: SHIMMER_INITIAL_OPACITY, 
          duration: SHIMMER_ANIMATION_DURATION, 
          useNativeDriver: Platform.OS !== 'web' 
        }),
      ])
    ).start();
  }, [opacity]);

  const skeletonStyles = createSkeletonStyles(theme);
  
  return (
    <View style={skeletonStyles.container}>
      <View style={[skeletonStyles.icon, { backgroundColor: theme.colors.surfaceVariant }]} />
      <View style={{ flex: 1 }}>
        <RNAnimated.View 
          style={[
            skeletonStyles.line, 
            { width: '70%', backgroundColor: theme.colors.surfaceVariant, opacity }
          ]} 
        />
        <RNAnimated.View 
          style={[
            skeletonStyles.line, 
            { width: '90%', backgroundColor: theme.colors.surfaceVariant, opacity }
          ]} 
        />
        <RNAnimated.View 
          style={[
            skeletonStyles.line, 
            { width: '50%', backgroundColor: theme.colors.surfaceVariant, opacity }
          ]} 
        />
      </View>
    </View>
  );
};

/**
 * StreamingBottomSheet Component
 * 
 * Replaces EnhancedAIStreamingModal with @gorhom/bottom-sheet implementation
 * while maintaining exact prop interface compatibility and auto-behavior.
 * 
 * CRITICAL IMPLEMENTATION REQUIREMENTS:
 * - Maintains identical prop interface to EnhancedAIStreamingModal
 * - Auto-open/close behavior via visible prop changes  
 * - Gesture control: stops at 90% height, no dragging beyond
 * - Performance: 60fps animations, proper memory management
 */
interface StreamingBottomSheetProps extends EnhancedAIStreamingModalProps {
  // All props inherited from EnhancedAIStreamingModalProps
  // Additional optional props for bottom sheet customization
  snapPoints?: string[];
  enableOverDrag?: boolean;
  enableDynamicSizing?: boolean;
}

const StreamingBottomSheet: React.FC<StreamingBottomSheetProps> = ({
  visible,
  onDismiss,
  title,
  description,
  items,
  analysisType,
  modalState = 'loading',
  isComplete = false,
  error = null,
  autoCloseDelay,
  onRetry,
  hapticType = 'none',
  enableBlur = true,
  enableStackAnimation = true,
  enableSwipeToDismiss = false,
  // Bottom sheet specific props with safe defaults  
  snapPoints = ['90%'],
  enableOverDrag = false,
  enableDynamicSizing = false,
}) => {
  const theme = useTheme();
  const { t } = useTranslation('create-recipe');
  const bottomSheetRef = useRef<BottomSheetModal>(null);

  // ✨ DYNAMIC SKELETON COUNT LOGIC (EXACT FROM ORIGINAL)
  // Modal state computation (matching original logic)
  const currentState: ModalState = useMemo(() => {
    if (modalState) return modalState;
    if (error) return 'error';
    if (isComplete) return 'completed';
    return items.length > 0 ? 'streaming' : 'loading';
  }, [modalState, error, isComplete, items.length]);

  // Starts with 5 skeletons, reduces as items arrive, always keeps 1 skeleton minimum
  const dynamicSkeletonCount = useMemo(() => {
    const currentItemCount = items.length;
    const initialSkeletonCount = 5;
    const minimumSkeletonCount = 1;
    
    if (currentState === 'loading') {
      return initialSkeletonCount; // Show 5 skeletons when no items yet
    }
    
    if (currentState === 'streaming' || currentState === 'completed') {
      // Calculate remaining skeletons: 5 - items.length, but never less than 1
      const remainingSkeletons = Math.max(initialSkeletonCount - currentItemCount, minimumSkeletonCount);
      return remainingSkeletons;
    }
    
    return 0; // No skeletons for error state
  }, [currentState, items.length]);

  // CRITICAL: Auto-open/close behavior preservation
  // This useEffect pattern MUST respond immediately to visible prop changes
  useEffect(() => {
    if (visible) {
      bottomSheetRef.current?.present();
    } else {
      bottomSheetRef.current?.dismiss();
    }
  }, [visible]);

  // Handle dismissal events and trigger parent callback
  const handleDismiss = useCallback(() => {
    if (onDismiss) {
      onDismiss();
    }
  }, [onDismiss]);

  // Haptic feedback when new streaming items arrive
  const prevItemCount = useRef(items.length);
  useEffect(() => {
    const currentItemCount = items.length;
    
    // Only trigger haptic if:
    // 1. We have more items than before (new content arrived)
    // 2. We're in streaming state (not initial load or completed)
    // 3. Previous count was > 0 (don't trigger on first item to avoid double haptic with modal open)
    if (
      currentItemCount > prevItemCount.current && 
      currentState === 'streaming' &&
      prevItemCount.current > 0
    ) {
      // Light haptic for each new streaming item - subtle but noticeable
      haptics.light();
    }
    
    prevItemCount.current = currentItemCount;
  }, [items.length, currentState]);

  // Constants for backdrop opacity
  const BACKDROP_OPACITY_WITH_BLUR = 0.5;
  const BACKDROP_OPACITY_WITHOUT_BLUR = 0.3;

  // Custom backdrop with controlled press behavior
  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={enableBlur ? BACKDROP_OPACITY_WITH_BLUR : BACKDROP_OPACITY_WITHOUT_BLUR}
        // Prevent manual closing when clicking outside - streaming content should not be accidentally dismissed
        pressBehavior="none"
      />
    ),
    [enableBlur]
  );

  // Custom handle with title

  // Get analysis content for icons and text
  const analysisContent = getAnalysisContent(analysisType || 'default');

  // Render individual streaming items using List.Item (matches original design)
  const renderItem = useCallback(({ item, index }: { item: StreamingItem; index: number }) => (
    <View key={item.id || index}>
      <List.Item
        title={item.title}
        description={item.subtitle || item.description}
        left={props => <List.Icon {...props} icon={analysisContent.icon} />}
        titleStyle={{ color: theme.colors.onSurface }}
        descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
      />
      {index < items.length - 1 && <Divider />}
    </View>
  ), [theme, analysisContent.icon, items.length]);

  // Loading state content with skeleton loaders (exact 5+1 logic)
  const renderLoadingContent = () => (
    <View style={{ paddingHorizontal: theme.spacing.screenPadding }}>
      {/* Show initial 5 skeletons during loading */}
      {Array.from({ length: dynamicSkeletonCount }).map((_, index) => (
        <SkeletonLoader key={`skeleton-${index}`} theme={theme} />
      ))}
    </View>
  );

  // Error state content with proper i18n
  const renderErrorContent = () => (
    <View style={{
      padding: theme.spacing.screenPadding,
      alignItems: 'center',
    }}>
      <Text 
        variant="titleMedium" 
        style={{ 
          color: theme.colors.error,
          textAlign: 'center',
          marginBottom: theme.spacing.md 
        }}
      >
        {t('errors.submitFailed')}
      </Text>
      {error && (
        <Text 
          variant="bodyMedium" 
          style={{ 
            color: theme.colors.onSurfaceVariant,
            textAlign: 'center',
            marginBottom: theme.spacing.lg 
          }}
        >
          {error}
        </Text>
      )}
      {onRetry && (
        <Text 
          variant="labelLarge"
          style={{
            color: theme.colors.primary,
            textAlign: 'center',
          }}
          onPress={onRetry}
        >
          {t('buttons.retry')}
        </Text>
      )}
    </View>
  );

  return (
    <BottomSheetModal
      ref={bottomSheetRef}
      snapPoints={snapPoints}
      onDismiss={handleDismiss}
      backdropComponent={renderBackdrop}
      // CRITICAL: Gesture control props from EARS requirements
      enableOverDrag={enableOverDrag}           // REQUIRED: Stops drag at 90%
      enableDynamicSizing={enableDynamicSizing}  // REQUIRED: Prevents content-based sizing  
      enableContentPanningGesture={true}        // REQUIRED: Enables content dragging
      enablePanDownToClose={enableSwipeToDismiss}
      // Background styling
      backgroundStyle={{
        backgroundColor: theme.colors.surface,
      }}
      // Handle indicator styling
      handleIndicatorStyle={{
        backgroundColor: theme.colors.onSurfaceVariant,
      }}
    >
      {error ? (
        renderErrorContent()
      ) : currentState === 'loading' ? (
        renderLoadingContent()
      ) : (
        <BottomSheetScrollView
          contentContainerStyle={{
            paddingBottom: theme.spacing.lg,
          }}
          showsVerticalScrollIndicator={false}
          // CRITICAL: Focus hook for proper scroll/pan gesture coordination
          focusHook={useFocusEffect}
        >
          {/* Modal Header */}
          <View style={{
            paddingHorizontal: theme.spacing.screenPadding,
            paddingTop: theme.spacing.md,
            paddingBottom: theme.spacing.lg,
            alignItems: 'center'
          }}>
            <Text 
              variant="headlineSmall" 
              style={{ 
                color: theme.colors.onSurface,
                textAlign: 'center',
                fontWeight: 'bold',
                marginBottom: description ? theme.spacing.xs : 0
              }}
            >
              {title}
            </Text>
            {description && (
              <Text 
                variant="bodyMedium" 
                style={{ 
                  color: theme.colors.onSurfaceVariant,
                  textAlign: 'center',
                  marginTop: theme.spacing.xs 
                }}
              >
                {description}
              </Text>
            )}
          </View>

          {/* Real items first */}
          {items.map((item, index) => renderItem({ item, index }))}
          
          {/* Then dynamic skeletons (reducing count as items arrive, always keeping 1) */}
          {dynamicSkeletonCount > 0 && (
            <>
              {items.length > 0 && <Divider />}
              {Array.from({ length: dynamicSkeletonCount }).map((_, index) => (
                <SkeletonLoader key={`streaming-skeleton-${index}`} theme={theme} />
              ))}
            </>
          )}
        </BottomSheetScrollView>
      )}
    </BottomSheetModal>
  );
};

// Skeleton Loader Styles - Theme-based version of original styles
const createSkeletonStyles = (theme: any) => ({
  container: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    paddingVertical: theme.spacing.sm,
    marginHorizontal: theme.spacing.screenPadding,
    marginBottom: theme.spacing.xs,
  },
  icon: {
    width: theme.spacing.lg * SKELETON_ICON_SIZE_MULTIPLIER, // 24px equivalent
    height: theme.spacing.lg * SKELETON_ICON_SIZE_MULTIPLIER, // 24px equivalent
    borderRadius: theme.borderRadius.lg,
    marginRight: theme.spacing.xl * SKELETON_ICON_MARGIN_MULTIPLIER, // 32px equivalent
  },
  line: {
    height: theme.spacing.xs + SKELETON_LINE_HEIGHT_OFFSET, // 10px equivalent
    borderRadius: theme.borderRadius.xs,
    marginBottom: theme.spacing.xs,
  },
});

export default StreamingBottomSheet;
export type { StreamingBottomSheetProps };
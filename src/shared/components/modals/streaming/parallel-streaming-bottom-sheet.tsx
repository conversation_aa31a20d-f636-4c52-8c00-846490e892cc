import React, { useRef, useEffect, useCallback, useMemo, useState } from 'react';
import { View } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { 
  Text, 
  ActivityIndicator, 
  ProgressBar, 
  Surface,
  List,
  Divider
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { 
  BottomSheetModal, 
  BottomSheetScrollView, 
  BottomSheetBackdrop,
  BottomSheetHandle
} from '@gorhom/bottom-sheet';
import { useTheme } from '@/shared/hooks/use-theme';
import { useTranslation } from 'react-i18next';
import type { TherapeuticProperty } from '@/features/create-recipe/types';

// Import exact interface from original component for compatibility
export interface ParallelStreamingBottomSheetProps {
  visible: boolean;
  onDismiss?: () => void;
  title: string;
  description: string;
  selectedProperties: TherapeuticProperty[];
  parallelStreamingState: {
    isStreaming: boolean;
    results: Map<string, any>;
    errors: Map<string, string>;
    completedCount?: number;
  };
  onComplete?: () => void;
  autoCloseDelay?: number;
  hapticType?: 'light' | 'medium' | 'heavy' | 'success' | 'error' | 'warning' | 'select' | 'toggle' | 'destructive' | 'none';
  enableBlur?: boolean;
  enableStackAnimation?: boolean;
  enableSwipeToDismiss?: boolean;
}

interface PropertyProgressItemProps {
  property: TherapeuticProperty;
  status: 'pending' | 'loading' | 'completed' | 'error';
  oilsFound?: number;
  theme: any;
}

/**
 * Individual Property Progress Item Component
 * Shows the status of each property during parallel streaming
 */
const PropertyProgressItem: React.FC<PropertyProgressItemProps> = ({ 
  property, 
  status, 
  oilsFound,
  theme 
}) => {
  const { t } = useTranslation('create-recipe');
  
  const getStatusIcon = () => {
    switch (status) {
      case 'loading':
        return <ActivityIndicator size="small" color={theme.colors.primary} />;
      case 'completed':
        return <MaterialCommunityIcons name="check-circle" size={20} color={theme.colors.secondary} />;
      case 'error':
        return <MaterialCommunityIcons name="alert-circle" size={20} color={theme.colors.error} />;
      case 'pending':
        return <MaterialCommunityIcons name="clock-outline" size={20} color={theme.colors.onSurfaceVariant} />;
      default:
        return null;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'loading':
        return t('modals.generatingRecipes.propertyStatus.analyzing');
      case 'completed':
        return t('modals.generatingRecipes.propertyStatus.completedOils');
      case 'error':
        return t('modals.generatingRecipes.propertyStatus.failed');
      case 'pending':
        return t('modals.generatingRecipes.propertyStatus.waiting');
      default:
        return '';
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'loading':
        return theme.colors.primary;
      case 'completed':
        return theme.colors.secondary;
      case 'error':
        return theme.colors.error;
      case 'pending':
        return theme.colors.onSurfaceVariant;
      default:
        return theme.colors.onSurface;
    }
  };

  return (
    <View>
      <List.Item
        title={property.property_name_localized}
        description={getStatusText()}
        left={() => (
          <View style={{ 
            marginRight: theme.spacing.sm, 
            justifyContent: 'center',
            alignItems: 'center',
            width: 40 
          }}>
            {getStatusIcon()}
          </View>
        )}
        titleStyle={{ 
          color: status === 'completed' ? theme.colors.onPrimaryContainer : theme.colors.onSurface 
        }}
        descriptionStyle={{ 
          color: getStatusColor() 
        }}
        style={{
          backgroundColor: status === 'completed' 
            ? theme.colors.primaryContainer 
            : 'transparent',
          paddingVertical: theme.spacing.sm,
        }}
      />
    </View>
  );
};

/**
 * ParallelStreamingBottomSheet Component
 * 
 * Modern @gorhom/bottom-sheet implementation for parallel streaming operations.
 * Provides improved gesture control and consistent Instagram-style modal experience.
 */
const ParallelStreamingBottomSheet: React.FC<ParallelStreamingBottomSheetProps> = ({
  visible,
  onDismiss,
  title,
  description,
  selectedProperties,
  parallelStreamingState,
  onComplete,
  autoCloseDelay,
  hapticType = 'medium',
  enableBlur = true,
  enableStackAnimation = true,
  enableSwipeToDismiss = false,
}) => {
  const theme = useTheme();
  const { t } = useTranslation('create-recipe');
  const bottomSheetRef = useRef<BottomSheetModal>(null);

  // Auto-close timer state
  const [isAutoClosing, setIsAutoClosing] = useState(false);
  const autoCloseTimerRef = useRef<NodeJS.Timeout>();

  // CRITICAL: Auto-open/close behavior preservation
  useEffect(() => {
    if (visible) {
      bottomSheetRef.current?.present();
    } else {
      bottomSheetRef.current?.dismiss();
    }
  }, [visible]);

  // Handle dismissal events
  const handleDismiss = useCallback(() => {
    if (onDismiss) {
      onDismiss();
    }
  }, [onDismiss]);

  // Calculate progress statistics - exact logic from original
  const progressStats = useMemo(() => {
    const total = selectedProperties.length;
    const completed = parallelStreamingState.results.size;
    const failed = parallelStreamingState.errors.size;
    const inProgress = parallelStreamingState.isStreaming ? total - completed - failed : 0;
    const progress = total > 0 ? (completed + failed) / total : 0;
    
    return {
      total,
      completed,
      failed, 
      inProgress,
      progress,
      isAllComplete: !parallelStreamingState.isStreaming && (completed + failed) === total
    };
  }, [
    selectedProperties.length,
    parallelStreamingState.results.size,
    parallelStreamingState.errors.size,
    parallelStreamingState.isStreaming
  ]);

  // Generate property status for each property - exact logic from original
  const propertyStatuses = useMemo(() => {
    return selectedProperties.map(property => {
      const propertyId = property.property_id;
      const hasResult = parallelStreamingState.results.has(propertyId);
      const hasError = parallelStreamingState.errors.has(propertyId);
      
      let status: 'pending' | 'loading' | 'completed' | 'error';
      let oilsFound: number | undefined;

      if (hasError) {
        status = 'error';
      } else if (hasResult) {
        status = 'completed';
        const result = parallelStreamingState.results.get(propertyId);
        oilsFound = result?.suggested_oils?.length || 0;
      } else if (parallelStreamingState.isStreaming) {
        status = 'loading';
      } else {
        status = 'pending';
      }

      return {
        property,
        status,
        oilsFound
      };
    });
  }, [
    selectedProperties,
    parallelStreamingState.results,
    parallelStreamingState.errors,
    parallelStreamingState.isStreaming
  ]);

  // Generate live status message with simple i18n - exact logic from original
  const liveStatusMessage = useMemo(() => {
    if (!parallelStreamingState.isStreaming) {
      if (progressStats.completed > 0) {
        return t('modals.generatingRecipes.statusMessages.completeAnalysis');
      }
      return t('modals.generatingRecipes.statusMessages.ready');
    }

    if (progressStats.inProgress > 0) {
      return t('modals.generatingRecipes.statusMessages.analyzingProperties');
    }

    return t('modals.generatingRecipes.statusMessages.processing');
  }, [
    parallelStreamingState.isStreaming,
    progressStats.completed,
    progressStats.inProgress,
    t
  ]);

  // Auto-close logic - exact from original
  useEffect(() => {
    if (progressStats.isAllComplete && autoCloseDelay && !isAutoClosing) {
      setIsAutoClosing(true);
      autoCloseTimerRef.current = setTimeout(() => {
        if (onDismiss) onDismiss();
        if (onComplete) onComplete();
      }, autoCloseDelay);
    }
    return () => {
      if (autoCloseTimerRef.current) clearTimeout(autoCloseTimerRef.current);
    };
  }, [progressStats.isAllComplete, autoCloseDelay, isAutoClosing, onDismiss, onComplete]);

  // Backdrop configuration with controlled press behavior
  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
        pressBehavior="none" // Prevent accidental dismissal during streaming
      />
    ),
    []
  );

  // Custom handle with title and description

  return (
    <BottomSheetModal
      ref={bottomSheetRef}
      snapPoints={['90%']}
      onDismiss={handleDismiss}
      backdropComponent={renderBackdrop}
      // CRITICAL: Gesture control props
      enableOverDrag={false}                    // Stops drag at 90%
      enableDynamicSizing={false}               // Prevents content-based sizing  
      enableContentPanningGesture={true}        // Enables content dragging
      enablePanDownToClose={enableSwipeToDismiss}
      // Background styling
      backgroundStyle={{
        backgroundColor: theme.colors.surface,
      }}
      handleIndicatorStyle={{
        backgroundColor: theme.colors.onSurfaceVariant,
      }}
    >
      <BottomSheetScrollView
        contentContainerStyle={{
          paddingBottom: theme.spacing.xl,
        }}
        showsVerticalScrollIndicator={false}
        focusHook={useFocusEffect}
      >
        {/* Modal Header */}
        <View style={{
          paddingHorizontal: theme.spacing.screenPadding,
          paddingTop: theme.spacing.md,
          paddingBottom: theme.spacing.lg,
          alignItems: 'center'
        }}>
          <Text 
            variant="headlineSmall" 
            style={{ 
              color: theme.colors.onSurface,
              textAlign: 'center',
              fontWeight: 'bold',
              marginBottom: description ? theme.spacing.xs : 0
            }}
          >
            {title}
          </Text>
          {description && (
            <Text 
              variant="bodyMedium" 
              style={{ 
                color: theme.colors.onSurfaceVariant,
                textAlign: 'center',
                marginTop: theme.spacing.xs 
              }}
            >
              {description}
            </Text>
          )}
        </View>

        {/* Progress Section */}
        <View style={{
          paddingHorizontal: theme.spacing.screenPadding,
          paddingBottom: theme.spacing.lg,
        }}>
          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: theme.spacing.md,
          }}>
            <Text variant="titleMedium" style={{ color: theme.colors.onSurface }}>
              {t('modals.generatingRecipes.progress')} ({progressStats.completed + progressStats.failed}/{progressStats.total})
            </Text>
            <Text 
              variant="bodySmall" 
              style={{ color: theme.colors.onSurfaceVariant }}
            >
              {Math.round(progressStats.progress * 100)}% {t('modals.generatingRecipes.complete')}
            </Text>
          </View>
          
          <ProgressBar
            progress={progressStats.progress}
            color={theme.colors.primary}
            style={{
              height: 8,
              borderRadius: theme.borderRadius.sm,
              backgroundColor: theme.colors.surfaceVariant,
              marginBottom: theme.spacing.md,
            }}
          />

          {/* Stats Row */}
          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-around',
            flexWrap: 'wrap',
          }}>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              gap: theme.spacing.xs,
            }}>
              <MaterialCommunityIcons 
                name="check-circle" 
                size={16} 
                color={theme.colors.secondary} 
              />
              <Text variant="bodySmall" style={{ color: theme.colors.secondary }}>
                {progressStats.completed} {t('modals.generatingRecipes.completed')}
              </Text>
            </View>
            
            {progressStats.inProgress > 0 && (
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                gap: theme.spacing.xs,
              }}>
                <ActivityIndicator size={16} color={theme.colors.primary} />
                <Text variant="bodySmall" style={{ color: theme.colors.primary }}>
                  {progressStats.inProgress} {t('modals.generatingRecipes.analyzing')}
                </Text>
              </View>
            )}

            {progressStats.failed > 0 && (
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                gap: theme.spacing.xs,
              }}>
                <MaterialCommunityIcons 
                  name="alert-circle" 
                  size={16} 
                  color={theme.colors.error} 
                />
                <Text variant="bodySmall" style={{ color: theme.colors.error }}>
                  {progressStats.failed} {t('modals.generatingRecipes.failed')}
                </Text>
              </View>
            )}
          </View>
        </View>

        {/* Properties List */}
        <View style={{
          paddingHorizontal: theme.spacing.screenPadding,
        }}>
          <Text 
            variant="titleSmall" 
            style={{
              color: theme.colors.onSurface,
              marginBottom: theme.spacing.md,
            }}
          >
            {t('modals.generatingRecipes.therapeuticProperties')}
          </Text>
          
          {propertyStatuses.map(({ property, status, oilsFound }, index) => (
            <View key={property.property_id}>
              <PropertyProgressItem
                property={property}
                status={status}
                oilsFound={oilsFound}
                theme={theme}
              />
              {index < propertyStatuses.length - 1 && <Divider />}
            </View>
          ))}
        </View>

        {/* Auto-close indicator */}
        {isAutoClosing && (
          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingVertical: theme.spacing.md,
            paddingHorizontal: theme.spacing.screenPadding,
            borderTopWidth: 1,
            borderTopColor: theme.colors.outline,
            marginTop: theme.spacing.lg,
          }}>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              {t('modals.generatingRecipes.autoClosing')}
            </Text>
          </View>
        )}

        {/* Live Status Footer */}
        <Surface 
          style={{
            backgroundColor: theme.colors.secondaryContainer,
            marginTop: theme.spacing.lg,
            paddingVertical: theme.spacing.md,
            paddingHorizontal: theme.spacing.screenPadding,
          }}
          elevation={0}
        >
          <Text 
            variant="bodySmall" 
            style={{
              color: theme.colors.onSecondaryContainer,
              textAlign: 'center',
              fontStyle: 'italic',
            }}
          >
            {liveStatusMessage}
          </Text>
        </Surface>
      </BottomSheetScrollView>
    </BottomSheetModal>
  );
};

export default ParallelStreamingBottomSheet;
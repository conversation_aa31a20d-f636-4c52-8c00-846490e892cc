/**
 * Hook for getting analysis type specific content (icons and text)
 * 
 * Maps analysis types to their corresponding Material Community icons.
 * Text content comes from i18n props, this only handles icon mapping.
 * 
 * @param analysisType - Type of analysis being performed
 * @returns Object containing icon name for the analysis type
 */
export const useAnalysisContent = (analysisType?: string) => {
  switch (analysisType) {
    case 'symptoms':
      return { icon: 'medical-bag' };
    case 'properties':
      return { icon: 'leaf' };
    case 'causes':
      return { icon: 'format-list-bulleted' };
    default:
      return { icon: 'magnify' };
  }
};
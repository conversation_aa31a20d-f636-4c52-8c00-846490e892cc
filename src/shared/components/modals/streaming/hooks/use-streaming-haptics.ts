import { useRef, useEffect } from 'react';
import { haptics } from '@/shared/utils/haptics';
import type { ModalState } from '../types';

/**
 * Hook for managing haptic feedback during streaming operations
 * 
 * Provides tactile confirmation when new streaming content arrives.
 * Only triggers during active streaming state to avoid double haptics.
 * 
 * @param items - Array of streaming items
 * @param currentState - Current modal state (loading, streaming, completed, error)
 */
export const useStreamingHaptics = (items: any[], currentState: ModalState) => {
  const prevItemCount = useRef(items.length);
  
  useEffect(() => {
    const currentItemCount = items.length;
    
    // Only trigger haptic if:
    // 1. We have more items than before (new content arrived)
    // 2. We're in streaming state (not initial load or completed)
    // 3. Previous count was > 0 (don't trigger on first item to avoid double haptic with modal open)
    if (
      currentItemCount > prevItemCount.current && 
      currentState === 'streaming' &&
      prevItemCount.current > 0
    ) {
      // Light haptic for each new streaming item - subtle but noticeable
      haptics.light();
    }
    
    prevItemCount.current = currentItemCount;
  }, [items.length, currentState]);
};
import React, { useRef, useEffect } from 'react';
import { View, Platform, Animated as RNAnimated } from 'react-native';

// Constants for shimmer animation - EXACT same values as original
const SHIMMER_INITIAL_OPACITY = 0.5;
const SHIMMER_ANIMATION_DURATION = 600;
const SKELETON_ICON_SIZE_MULTIPLIER = 1.5;
const SKELETON_ICON_MARGIN_MULTIPLIER = 2;
const SKELETON_LINE_HEIGHT_OFFSET = 2;

interface SkeletonLoaderProps {
  theme: any;
}

/**
 * Enhanced Skeleton loader component with dynamic shimmer effect
 * Shows loading placeholders that get progressively replaced by real content
 * EXACT implementation from original EnhancedAIStreamingModal
 */
const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({ theme }) => {
  const opacity = useRef(new RNAnimated.Value(SHIMMER_INITIAL_OPACITY)).current;

  useEffect(() => {
    RNAnimated.loop(
      RNAnimated.sequence([
        RNAnimated.timing(opacity, { 
          toValue: 1, 
          duration: SHIMMER_ANIMATION_DURATION, 
          useNativeDriver: Platform.OS !== 'web' 
        }),
        RNAnimated.timing(opacity, { 
          toValue: SHIMMER_INITIAL_OPACITY, 
          duration: SHIMMER_ANIMATION_DURATION, 
          useNativeDriver: Platform.OS !== 'web' 
        }),
      ])
    ).start();
  }, [opacity]);

  const skeletonStyles = createSkeletonStyles(theme);
  
  return (
    <View style={skeletonStyles.container}>
      <View style={[skeletonStyles.icon, { backgroundColor: theme.colors.surfaceVariant }]} />
      <View style={{ flex: 1 }}>
        <RNAnimated.View 
          style={[
            skeletonStyles.line, 
            { width: '70%', backgroundColor: theme.colors.surfaceVariant, opacity }
          ]} 
        />
        <RNAnimated.View 
          style={[
            skeletonStyles.line, 
            { width: '90%', backgroundColor: theme.colors.surfaceVariant, opacity }
          ]} 
        />
        <RNAnimated.View 
          style={[
            skeletonStyles.line, 
            { width: '50%', backgroundColor: theme.colors.surfaceVariant, opacity }
          ]} 
        />
      </View>
    </View>
  );
};

// Skeleton Loader Styles - EXACT same theme-based version as original
const createSkeletonStyles = (theme: any) => ({
  container: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    paddingVertical: theme.spacing.sm,
    marginHorizontal: theme.spacing.screenPadding,
    marginBottom: theme.spacing.xs,
  },
  icon: {
    width: theme.spacing.lg * SKELETON_ICON_SIZE_MULTIPLIER, // 24px equivalent
    height: theme.spacing.lg * SKELETON_ICON_SIZE_MULTIPLIER, // 24px equivalent
    borderRadius: theme.borderRadius.lg,
    marginRight: theme.spacing.xl * SKELETON_ICON_MARGIN_MULTIPLIER, // 32px equivalent
  },
  line: {
    height: theme.spacing.xs + SKELETON_LINE_HEIGHT_OFFSET, // 10px equivalent
    borderRadius: theme.borderRadius.xs,
    marginBottom: theme.spacing.xs,
  },
});

interface SkeletonProviderProps {
  count: number;
  theme: any;
  keyPrefix?: string;
}

/**
 * Provider component that renders multiple skeleton loaders
 * 
 * @param count - Number of skeleton loaders to render
 * @param theme - Theme object for styling
 * @param keyPrefix - Optional prefix for React keys (default: 'skeleton')
 */
export const SkeletonProvider: React.FC<SkeletonProviderProps> = ({ 
  count, 
  theme, 
  keyPrefix = 'skeleton' 
}) => {
  return (
    <>
      {Array.from({ length: count }).map((_, index) => (
        <SkeletonLoader key={`${keyPrefix}-${index}`} theme={theme} />
      ))}
    </>
  );
};
/**
 * Shared Types for Streaming Modal System
 * 
 * Centralized type definitions following DRY principle.
 * All streaming modal components should import from here.
 */

import type { HapticType } from '@/shared/providers/haptics-provider';

/**
 * Represents a single item in the streaming response
 */
export interface StreamingItem {
  id?: string;
  title: string;
  subtitle?: string;
  description?: string;
  timestamp?: Date;
}

/**
 * Modal state for streaming operations
 */
export type ModalState = 'loading' | 'streaming' | 'completed' | 'error';

/**
 * Base props for streaming modal components
 * Following Interface Segregation Principle (ISP)
 */
export interface EnhancedAIStreamingModalProps {
  visible: boolean;
  onDismiss?: () => void;
  title: string;
  description: string;
  items: StreamingItem[];
  analysisType?: string;
  modalState?: ModalState;
  isComplete?: boolean;
  error?: string | null;
  autoCloseDelay?: number;
  onRetry?: () => void;
  // Instagram-style enhancements
  hapticType?: HapticType;
  enableBlur?: boolean;
  enableStackAnimation?: boolean;
  // Swipe-to-dismiss control
  enableSwipeToDismiss?: boolean;
}
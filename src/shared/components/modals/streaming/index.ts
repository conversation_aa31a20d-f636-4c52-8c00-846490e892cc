/**
 * Streaming Modal System - Clean Public API
 * 
 * Refactored streaming modal components following SOLID/DRY principles.
 * Maintains exact same functionality and visual appearance as original implementation.
 * 
 * Usage:
 * import { StreamingBottomSheet, StreamingItem } from '@/shared/components/modals/streaming';
 */

// Main component export
export { default as StreamingBottomSheet } from './streaming-bottom-sheet';

// Type exports - centralized following DRY principle
export type { 
  StreamingItem, 
  ModalState, 
  EnhancedAIStreamingModalProps 
} from './types';

// Extended component prop types
export type { StreamingBottomSheetProps } from './streaming-bottom-sheet';

// Component exports for advanced usage
export { StreamingContentRenderer } from './streaming-content-renderer';
export { SkeletonProvider } from './skeleton-provider';

// Hook exports for composition
export { useStreamingHaptics } from './hooks/use-streaming-haptics';
export { useAnalysisContent } from './hooks/use-analysis-content';
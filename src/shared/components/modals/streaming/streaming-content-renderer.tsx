import React, { useCallback, useMemo } from 'react';
import { View } from 'react-native';
import { Text, List, Divider } from 'react-native-paper';
import { useFocusEffect } from '@react-navigation/native';
import { BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { useTranslation } from 'react-i18next';

// Import types from shared location following DRY principle
import type { 
  StreamingItem,
  ModalState
} from './types';

// Import our extracted components
import { SkeletonProvider } from './skeleton-provider';
import { useAnalysisContent } from './hooks/use-analysis-content';

interface StreamingContentRendererProps {
  items: StreamingItem[];
  currentState: ModalState;
  analysisType?: string;
  error?: string | null;
  onRetry?: () => void;
  theme: any;
}

/**
 * StreamingContentRenderer Component
 * 
 * Handles ONLY content rendering based on streaming state.
 * Maintains EXACT same rendering behavior as original implementation.
 */
export const StreamingContentRenderer: React.FC<StreamingContentRendererProps> = ({
  items,
  currentState,
  analysisType,
  error,
  onRetry,
  theme
}) => {
  const { t } = useTranslation('create-recipe');
  const analysisContent = useAnalysisContent(analysisType);

  // ✨ DYNAMIC SKELETON COUNT LOGIC (EXACT FROM ORIGINAL)
  // Starts with 5 skeletons, reduces as items arrive, always keeps 1 skeleton minimum
  const dynamicSkeletonCount = useMemo(() => {
    const currentItemCount = items.length;
    const initialSkeletonCount = 5;
    const minimumSkeletonCount = 1;
    
    if (currentState === 'loading') {
      return initialSkeletonCount; // Show 5 skeletons when no items yet
    }
    
    if (currentState === 'streaming' || currentState === 'completed') {
      // Calculate remaining skeletons: 5 - items.length, but never less than 1
      const remainingSkeletons = Math.max(initialSkeletonCount - currentItemCount, minimumSkeletonCount);
      return remainingSkeletons;
    }
    
    return 0; // No skeletons for error state
  }, [currentState, items.length]);

  // Render individual streaming items using List.Item (EXACT same as original)
  const renderItem = useCallback(({ item, index }: { item: StreamingItem; index: number }) => (
    <View key={item.id || index}>
      <List.Item
        title={item.title}
        description={item.subtitle || item.description}
        left={props => <List.Icon {...props} icon={analysisContent.icon} />}
        titleStyle={{ color: theme.colors.onSurface }}
        descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
      />
      {index < items.length - 1 && <Divider />}
    </View>
  ), [theme, analysisContent.icon, items.length]);

  // Loading state content with skeleton loaders (EXACT 5+1 logic)
  const renderLoadingContent = () => (
    <View style={{ paddingHorizontal: theme.spacing.screenPadding }}>
      <SkeletonProvider count={dynamicSkeletonCount} theme={theme} />
    </View>
  );

  // Error state content with proper i18n (EXACT same as original)
  const renderErrorContent = () => (
    <View style={{
      padding: theme.spacing.screenPadding,
      alignItems: 'center',
    }}>
      <Text 
        variant="titleMedium" 
        style={{ 
          color: theme.colors.error,
          textAlign: 'center',
          marginBottom: theme.spacing.md 
        }}
      >
        {t('errors.submitFailed')}
      </Text>
      {error && (
        <Text 
          variant="bodyMedium" 
          style={{ 
            color: theme.colors.onSurfaceVariant,
            textAlign: 'center',
            marginBottom: theme.spacing.lg 
          }}
        >
          {error}
        </Text>
      )}
      {onRetry && (
        <Text 
          variant="labelLarge"
          style={{
            color: theme.colors.primary,
            textAlign: 'center',
          }}
          onPress={onRetry}
        >
          {t('buttons.retry')}
        </Text>
      )}
    </View>
  );

  // EXACT same conditional rendering logic as original
  if (error) {
    return renderErrorContent();
  }

  if (currentState === 'loading') {
    return renderLoadingContent();
  }

  // Streaming/completed content (EXACT same structure as original)
  return (
    <BottomSheetScrollView
      contentContainerStyle={{
        paddingBottom: theme.spacing.lg,
      }}
      showsVerticalScrollIndicator={false}
      // CRITICAL: Focus hook for proper scroll/pan gesture coordination
      focusHook={useFocusEffect}
    >
      {/* Real items first */}
      {items.map((item, index) => renderItem({ item, index }))}
      
      {/* Then dynamic skeletons (reducing count as items arrive, always keeping 1) */}
      {dynamicSkeletonCount > 0 && (
        <>
          {items.length > 0 && <Divider />}
          <SkeletonProvider 
            count={dynamicSkeletonCount} 
            theme={theme} 
            keyPrefix="streaming-skeleton" 
          />
        </>
      )}
    </BottomSheetScrollView>
  );
};
import React from 'react';
import { View } from 'react-native';
import { Text, Button } from 'react-native-paper';
import { useTranslation } from '@/shared/locales/i18n';
import { useTheme } from '@/shared/hooks/use-theme';
import { useButtonStyles, useButtonContentStyles } from '@/shared/styles/button-styles';
import type { AppTheme } from '@/shared/utils';

// --- Confirmation Alert Modal ---
interface ConfirmationModalProps {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void;
  onCancel: () => void; // This will also be used to dismiss the modal
}

export const ConfirmationModalContent: React.FC<ConfirmationModalProps> = (props) => {
  const { title, message, onConfirm, onCancel, confirmText, cancelText } = props;
  const { t } = useTranslation('common');
  const { theme } = useTheme();
  const buttonStyles = useButtonStyles();
  const buttonContentStyles = useButtonContentStyles();
  const styles = getSpecificStyles(theme);

  return (
    <View style={styles.alertContainer}>
      <Text style={styles.alertTitle}>{title}</Text>
      <Text style={styles.alertMessage}>{message}</Text>
      <View style={styles.buttonRow}>
        <Button mode="text" onPress={onCancel} style={buttonStyles.default} contentStyle={buttonContentStyles.default}>
          {cancelText || t('modal.cancel')}
        </Button>
        <Button mode="contained" onPress={onConfirm} style={buttonStyles.default} contentStyle={buttonContentStyles.default}>
          {confirmText || t('modal.confirm')}
        </Button>
      </View>
    </View>
  );
};

// --- Bottom Sheet Content ---
// Updated for @gorhom/bottom-sheet compatibility
interface BottomSheetProps {
  title?: string;
  children: React.ReactNode;
  noPadding?: boolean; // New prop to control padding
  scrollY?: any; // Pass through scrollY for Instagram-style scrolling
  showHandle?: boolean; // Control whether to show internal handle (usually false for @gorhom/bottom-sheet)
}

export const BottomSheetContent: React.FC<BottomSheetProps> = ({ 
  title, 
  children, 
  noPadding = false, 
  scrollY,
  showHandle = false // Default to false for @gorhom/bottom-sheet compatibility
}) => {
  const { theme } = useTheme();
  const styles = getSpecificStyles(theme);

  return (
    <View style={[styles.sheetContainer, noPadding && styles.sheetContainerNoPadding]}>
      {/* Only show handle if explicitly requested (for legacy compatibility) */}
      {showHandle && <View style={styles.handle} />}
      {title && (
        <Text style={[styles.sheetTitle, noPadding && styles.sheetTitleWithPadding]}>
          {title}
        </Text>
      )}
      {/* Pass scrollY prop to children if they accept it */}
      {React.isValidElement(children) 
        ? React.cloneElement(children, { scrollY })
        : children
      }
    </View>
  );
};


// --- Theme-Aware Styles for the Content ---
const getSpecificStyles = (theme: AppTheme) => {
  return {
    alertContainer: {
      paddingHorizontal: theme.spacing?.xl,
      paddingTop: theme.spacing?.xl,
      paddingBottom: theme.spacing?.lg,
      alignItems: 'flex-start' as const,
      gap: theme.spacing?.lg,
    },
    alertTitle: {
      ...theme.fonts.headlineSmall,
      color: theme.colors.onSurface,
    },
    alertMessage: {
      ...theme.fonts.bodyMedium,
      color: theme.colors.onSurfaceVariant,
    },
    buttonRow: {
      flexDirection: 'row' as const,
      justifyContent: 'flex-end' as const,
      width: '100%' as const,
      marginTop: theme.spacing?.sm,
      gap: theme.spacing?.sm,
    },
    sheetContainer: {
      paddingHorizontal: theme.spacing?.lg,
      paddingBottom: theme.spacing?.xxl,
      gap: theme.spacing?.lg,
      minHeight: '100%' as const,
    },
    sheetContainerNoPadding: {
      paddingHorizontal: 0,
      gap: 0,
      minHeight: '100%' as const,
    },
    sheetTitle: {
      ...theme.fonts.titleLarge,
      color: theme.colors.onSurface,
      textAlign: 'center',
    },
    sheetTitleWithPadding: {
      paddingHorizontal: theme.spacing?.lg,
      marginBottom: theme.spacing?.lg,
    },
    handle: {
      width: 48,
      height: 4,
      borderRadius: 2,
      backgroundColor: theme.colors.onSurfaceVariant,
      opacity: 0.4,
      alignSelf: 'center' as const,
      marginTop: theme.spacing?.md,
      marginBottom: theme.spacing?.sm,
    },
  };
};
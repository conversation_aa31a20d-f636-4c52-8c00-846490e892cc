import React, { useState, useEffect, useCallback, useRef } from 'react';
import { View, KeyboardAvoidingView, Platform, TextInput, Alert } from 'react-native';
import {
  Text,
  Button,
  HelperText,
} from 'react-native-paper';
import {
  BottomSheetModal,
  BottomSheetBackdrop,
  BottomSheetView,
} from '@gorhom/bottom-sheet';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/shared/hooks/use-theme';
import { useButtonStyles, useButtonContentStyles } from '@/shared/styles/button-styles';
import { haptics } from '@/shared/utils/haptics';

interface GenericOTPModalProps {
  // Modal state
  isVisible: boolean;
  onClose: () => void;
  
  // Content configuration
  title: string;
  subtitle: string;
  
  // Verification logic (passed from parent)
  onVerify: (code: string) => Promise<void> | void;
  onResend: () => Promise<void>;
  
  // State management
  isLoading: boolean;
  error?: string;
  
  // Optional customization
  codeLength?: number; // default 6
  resendTimeoutSeconds?: number; // default 60
  maxAttempts?: number; // default 3
}

export const GenericOtpModal: React.FC<GenericOTPModalProps> = ({
  isVisible,
  onClose,
  title,
  subtitle,
  onVerify,
  onResend,
  isLoading,
  error,
  codeLength = 6,
  resendTimeoutSeconds = 60,
  maxAttempts = 3,
}) => {
  const { t } = useTranslation('common');
  const { theme, spacing, borderRadius } = useTheme();
  const buttonStyles = useButtonStyles();
  const buttonContentStyles = useButtonContentStyles();

  const modalRef = useRef<BottomSheetModal>(null);
  const inputRefs = useRef<(TextInput | null)[]>([]);
  const [otpCode, setOtpCode] = useState<string[]>(Array(codeLength).fill(''));
  const [focusedIndex, setFocusedIndex] = useState<number | null>(null);
  const [countdown, setCountdown] = useState(resendTimeoutSeconds);
  const [attempts, setAttempts] = useState(0);
  const hasSubmitted = useRef(false);

  const isOtpComplete = otpCode.every(digit => digit);
  const isResendDisabled = countdown > 0;

  // Timer effect
  useEffect(() => {
    let timer: NodeJS.Timeout;
    const TIMER_INTERVAL = 1000; // 1 second
    if (isVisible && countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), TIMER_INTERVAL);
    }
    return () => clearTimeout(timer);
  }, [isVisible, countdown]);

  // Modal presentation and auto-focus effect
  useEffect(() => {
    if (isVisible) {
      modalRef.current?.present();
      setCountdown(resendTimeoutSeconds);
      setAttempts(0); // Reset attempts when modal opens
      hasSubmitted.current = false;
      const FOCUS_DELAY = 300; // ms - delay to allow modal to fully present
      setTimeout(() => {
        inputRefs.current[0]?.focus();
      }, FOCUS_DELAY);
    } else {
      modalRef.current?.dismiss();
    }
  }, [isVisible, resendTimeoutSeconds]);

  // Enhanced verification with retry limit and auto-rescue
  const handleVerification = useCallback(async (code: string) => {
    if (hasSubmitted.current || !isOtpComplete) return;
    hasSubmitted.current = true;
    
    haptics.light();
    
    try {
      await onVerify(code);
      // Success: reset attempts
      setAttempts(0);
      hasSubmitted.current = false;
    } catch (err: any) {
      hasSubmitted.current = false;
      const newAttempts = attempts + 1;
      setAttempts(newAttempts);
      
      if (newAttempts >= maxAttempts) {
        // Auto-rescue: force resend and reset
        haptics.error();
        Alert.alert(
          t('labels.tooManyAttempts') || 'Too many attempts', 
          t('labels.sendingNewCode') || 'Sending a new verification code...'
        );
        try {
          await onResend();
          setAttempts(0);
          setOtpCode(Array(codeLength).fill(''));
          setCountdown(resendTimeoutSeconds);
          const REFOCUS_DELAY = 100; // ms - brief delay to allow UI update
          setTimeout(() => inputRefs.current[0]?.focus(), REFOCUS_DELAY);
        } catch {
          // If resend fails, let parent handle the error
          throw err;
        }
      } else {
        // Let parent handle specific error display
        haptics.error();
        throw err;
      }
    }
  }, [onVerify, isOtpComplete, attempts, maxAttempts, onResend, codeLength, resendTimeoutSeconds, t]);

  const handleOtpChange = useCallback((text: string, index: number) => {
    const newOtpCode = [...otpCode];
    newOtpCode[index] = text;
    setOtpCode(newOtpCode);
    
    if (text && index < codeLength - 1) {
      inputRefs.current[index + 1]?.focus();
    }
  }, [otpCode, codeLength]);

  const handleKeyPress = useCallback((key: string, index: number) => {
    if (key === 'Backspace' && !otpCode[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  }, [otpCode]);
  
  const handleResend = useCallback(async () => {
    haptics.light();
    try {
      await onResend();
      setOtpCode(Array(codeLength).fill(''));
      setCountdown(resendTimeoutSeconds);
      setAttempts(0); // Reset attempts on manual resend
      hasSubmitted.current = false;
      const REFOCUS_DELAY = 100; // ms - brief delay to allow UI update
      setTimeout(() => inputRefs.current[0]?.focus(), REFOCUS_DELAY);
    } catch (err) {
      haptics.error();
      // Let parent handle resend errors
      throw err;
    }
  }, [onResend, codeLength, resendTimeoutSeconds]);

  const renderBackdrop = useCallback((props: any) => (
    <BottomSheetBackdrop 
      {...props} 
      disappearsOnIndex={-1} 
      appearsOnIndex={0} 
      opacity={0.5}
      pressBehavior="none"
    />
  ), []);

  return (
    <BottomSheetModal
      ref={modalRef}
      snapPoints={['90%']}
      onDismiss={onClose}
      backdropComponent={renderBackdrop}
      handleIndicatorStyle={{ backgroundColor: theme.colors.onSurfaceVariant }}
      backgroundStyle={{ backgroundColor: theme.colors.surface }}
      enablePanDownToClose={false}
      enableDynamicSizing={false}
    >
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'} 
        style={{ flex: 1 }}
      >
        <BottomSheetView style={{ flex: 1, padding: spacing.lg, justifyContent: 'space-between' }}>
          <View>
            <Text 
              variant="headlineSmall" 
              style={{ 
                textAlign: 'center', 
                marginBottom: spacing.lg,
                color: theme.colors.onSurface,
                fontWeight: 'bold'
              }}
              accessibilityRole="header"
            >
              {title}
            </Text>
            
            <Text 
              variant="bodyLarge" 
              style={{ 
                textAlign: 'center', 
                marginBottom: spacing.lg, 
                color: theme.colors.onSurfaceVariant, 
                lineHeight: 22 
              }}
            >
              {subtitle}
            </Text>

            <View style={getOtpInputStyles(theme).otpContainer}>
              {otpCode.map((digit, index) => (
                <TextInput
                  key={index}
                  ref={(ref) => (inputRefs.current[index] = ref)}
                  style={[
                    getOtpInputStyles(theme).otpInput,
                    {
                      borderColor: error
                        ? theme.colors.error
                        : focusedIndex === index
                        ? theme.colors.primary
                        : theme.colors.outline,
                      backgroundColor: theme.colors.surface,
                      color: theme.colors.onSurface,
                    },
                  ]}
                  value={digit}
                  onChangeText={(text) => handleOtpChange(text.slice(-1), index)}
                  onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
                  onFocus={() => setFocusedIndex(index)}
                  onBlur={() => setFocusedIndex(null)}
                  keyboardType="number-pad"
                  maxLength={1}
                  editable={!isLoading}
                  selectTextOnFocus
                  textContentType="oneTimeCode"
                  accessibilityLabel={`${t('labels.digit') || 'Digit'} ${index + 1} ${t('labels.of') || 'of'} ${codeLength}`}
                  onSubmitEditing={index === codeLength - 1 ? () => handleVerification(otpCode.join('')) : undefined}
                />
              ))}
            </View>

            <HelperText 
              type="error" 
              visible={!!error} 
              style={{ textAlign: 'center' }}
              accessibilityLiveRegion="polite"
            >
              {error}
            </HelperText>

            {/* Show attempt counter if > 0 attempts */}
            {attempts > 0 && attempts < maxAttempts && (
              <HelperText 
                type="info" 
                visible={true} 
                style={{ textAlign: 'center' }}
              >
                {t('labels.attemptsRemaining') || 'Attempts remaining'}: {maxAttempts - attempts}
              </HelperText>
            )}
          </View>

          <View style={{ marginTop: spacing.lg, gap: spacing.sm }}>
            <Button
              mode="contained"
              onPress={() => handleVerification(otpCode.join(''))}
              loading={isLoading}
              disabled={!isOtpComplete || isLoading}
              style={[buttonStyles.large, { borderRadius: borderRadius.button }]}
              contentStyle={buttonContentStyles.large}
              accessibilityLabel={`${t('labels.verify') || 'Verify'} ${title.toLowerCase()}`}
            >
              {t('labels.verify') || 'Verify Code'}
            </Button>

            <Button
              mode="text"
              onPress={handleResend}
              disabled={isResendDisabled || isLoading}
              accessibilityLabel={isResendDisabled ? `${t('labels.resendIn') || 'Resend in'} ${countdown} ${t('labels.seconds') || 'seconds'}` : t('labels.resendCode') || 'Resend code'}
            >
              {isResendDisabled 
                ? `${t('labels.resendCode') || 'Resend code'} in ${countdown}s` 
                : t('labels.resendCode') || 'Resend code'
              }
            </Button>
          </View>
        </BottomSheetView>
      </KeyboardAvoidingView>
    </BottomSheetModal>
  );
};

const getOtpInputStyles = (theme: any) => ({
  otpContainer: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between',
    marginBottom: theme.spacing.md,
  },
  otpInput: {
    width: 45,
    height: 55,
    borderWidth: 2,
    borderRadius: 8,
    textAlign: 'center' as const,
    fontSize: 20,
    fontWeight: '600' as const,
  },
});
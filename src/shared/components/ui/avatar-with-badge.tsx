import React from 'react';
import { View, ViewStyle, ImageSourcePropType } from 'react-native';
import { Avatar, Badge, TouchableRipple } from 'react-native-paper';
import { useTheme } from '@/shared/hooks/use-theme';

// Constants to avoid magic numbers
const SMALL_AVATAR_THRESHOLD = 32;
const SMALL_BADGE_SIZE = 8;
const DEFAULT_BADGE_SIZE = 10;
const BADGE_POSITION_OFFSET = 2;

/**
 * Avatar with Badge Component
 * 
 * A reusable avatar component that consolidates all avatar patterns in AromaCHAT.
 * Supports both icon and image variants with optional badge for status indication.
 * Parent component has full control over badge presence and status.
 * 
 * Features:
 * - Supports both Avatar.Icon and Avatar.Image variants
 * - Optional badge with online/offline status controlled by parent
 * - Consistent theming using design tokens
 * - Proper touch interactions with haptic feedback
 * - Follows DRY and KISS principles
 * 
 * @example
 * // Avatar with icon and online badge
 * <AvatarWithBadge
 *   variant="icon"
 *   icon="account"
 *   badge={{ visible: true, status: 'online' }}
 *   onPress={showProfile}
 * />
 * 
 * @example
 * // Avatar with image, no badge
 * <AvatarWithBadge
 *   variant="image"
 *   source={{ uri: user.imageUrl }}
 * />
 * 
 * @example
 * // Avatar with offline status
 * <AvatarWithBadge
 *   variant="icon"
 *   icon="account"
 *   badge={{ visible: true, status: 'offline' }}
 *   size={32}
 * />
 */

export interface AvatarWithBadgeProps {
  /** Avatar variant type - determines if it renders as icon or image */
  variant: 'icon' | 'image';
  
  /** Icon name for variant='icon' */
  icon?: string;
  
  /** Image source for variant='image' */
  source?: ImageSourcePropType;
  
  /** Avatar size */
  size?: number;
  
  /** Avatar style overrides */
  style?: ViewStyle;
  
  /** Badge configuration - parent controls presence and status */
  badge?: {
    /** Whether badge is visible */
    visible: boolean;
    /** Badge status affects color */
    status: 'online' | 'offline';
    /** Badge size */
    size?: number;
  };
  
  /** Touch handler */
  onPress?: () => void;
  
  /** Test ID for testing */
  testID?: string;
}

/**
 * AvatarWithBadge - Consolidates avatar patterns with optional badge
 * Follows existing avatar usage patterns from chat messages, home screen, and profile modal
 */
export const AvatarWithBadge: React.FC<AvatarWithBadgeProps> = ({
  variant,
  icon,
  source,
  size = 44,
  style,
  badge,
  onPress,
  testID,
}) => {
  const { theme } = useTheme();

  // Badge styling based on status
  const getBadgeColor = () => {
    if (!badge?.visible) return 'transparent';
    return badge.status === 'online' ? theme.colors.primary : theme.colors.error;
  };

  const badgeSize = badge?.size || (size <= SMALL_AVATAR_THRESHOLD ? SMALL_BADGE_SIZE : DEFAULT_BADGE_SIZE);

  // Render the appropriate avatar variant
  const renderAvatar = () => {
    if (variant === 'image') {
      if (!source) {
        // Fallback to icon if no source provided for image variant
        return (
          <Avatar.Icon
            size={size}
            icon="account"
            style={[{ backgroundColor: theme.colors.primary }, style]}
          />
        );
      }
      return <Avatar.Image size={size} source={source} style={style} />;
    }

    // Icon variant
    return (
      <Avatar.Icon
        size={size}
        icon={icon || 'account'}
        style={[{ backgroundColor: theme.colors.primary }, style]}
      />
    );
  };

  // Container for avatar and badge positioning
  const avatarContainer = (
    <View style={{ position: 'relative' }} testID={testID}>
      {renderAvatar()}
      
      {/* Badge - positioned absolutely on top-right */}
      {badge?.visible && (
        <Badge
          visible={badge.visible}
          size={badgeSize}
          style={{
            position: 'absolute',
            bottom: BADGE_POSITION_OFFSET,
            left: BADGE_POSITION_OFFSET,
            backgroundColor: getBadgeColor(),
          }}
        />
      )}
    </View>
  );

  // If onPress is provided, wrap in TouchableRipple for consistent touch feedback
  if (onPress) {
    return (
      <TouchableRipple
        onPress={onPress}
        borderless
        rippleColor={theme.colors.onSurface}
        style={{
          borderRadius: size / 2,
          overflow: 'hidden',
        }}
      >
        {avatarContainer}
      </TouchableRipple>
    );
  }

  return avatarContainer;
};

/**
 * Preset configurations for common avatar with badge usage patterns
 */
export const AvatarWithBadgePresets = {
  /** Standard user profile avatar with online status */
  userProfileOnline: {
    variant: 'icon' as const,
    icon: 'account',
    size: 44,
    badge: { visible: true, status: 'online' as const },
  },
  
  /** User profile avatar with offline status */
  userProfileOffline: {
    variant: 'icon' as const,
    icon: 'account', 
    size: 44,
    badge: { visible: true, status: 'offline' as const },
  },
  
  /** Chat message avatar (image, no badge) */
  chatMessage: {
    variant: 'image' as const,
    size: 32,
  },
  
  /** Small avatar for lists */
  listItem: {
    variant: 'icon' as const,
    icon: 'account',
    size: 24,
  },
} as const;
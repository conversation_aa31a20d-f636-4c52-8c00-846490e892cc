import React, { useState, forwardRef } from 'react';
import { View } from 'react-native';
import { TextInput } from 'react-native-paper';
import type { TextInputProps } from 'react-native-paper';

// By extending TextInputProps, the new component can accept all props of the original TextInput.
type PasswordInputProps = TextInputProps;

const PasswordInput = forwardRef<any, PasswordInputProps>((props, ref) => {
  const [isVisible, setIsVisible] = useState(false);

  return (
    <View style={{ position: 'relative' }}>
      <TextInput
        {...props} // Pass all original props to the TextInput.
        ref={ref} // Forward the ref to the TextInput component
        secureTextEntry={!isVisible} // Control visibility based on the isVisible state.
        right={
          // Do not override the right prop if it's already provided, ensuring backward compatibility.
          props.right || (
            <TextInput.Icon
              icon={isVisible ? 'eye-off' : 'eye'}
              onPress={() => setIsVisible(prev => !prev)}
              disabled={props.disabled}
              focusable={false}
              tabIndex={-1}
            />
          )
        }
      />
    </View>
  );
});

export default PasswordInput;

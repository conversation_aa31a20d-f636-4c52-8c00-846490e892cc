import React from 'react';
import { ViewStyle } from 'react-native';
import { SegmentedButtons, SegmentedButtonsProps } from 'react-native-paper';
import { useTheme } from '@/shared/hooks/use-theme';

/**
 * Standardized SegmentedButtons Component
 * 
 * A reusable wrapper around React Native Paper's SegmentedButtons that ensures
 * consistent behavior and theming across the entire AromaCHAT app.
 * 
 * Features:
 * - Automatic showSelectedCheck configuration
 * - Consistent flex and spacing
 * - Support for all React Native Paper SegmentedButtons props
 * - Handles icons, labels, multiselect, density, and disabled states
 * 
 * @example
 * // Basic usage with labels and icons
 * <StandardizedSegmentedButtons
 *   value={selectedValue}
 *   onValueChange={setSelectedValue}
 *   buttons={[
 *     { value: 'option1', label: 'Option 1', icon: 'icon-name' },
 *     { value: 'option2', label: 'Option 2', icon: 'icon-name' }
 *   ]}
 * />
 * 
 * @example
 * // Only icons
 * <StandardizedSegmentedButtons
 *   value={selectedValue}
 *   onValueChange={setSelectedValue}
 *   buttons={[
 *     { value: 'walk', icon: 'walk' },
 *     { value: 'car', icon: 'car' }
 *   ]}
 *   showSelectedCheck={false} // Disable checkmarks for icon-only
 * />
 * 
 * @example
 * // Multiselect
 * <StandardizedSegmentedButtons
 *   multiSelect
 *   value={selectedValues}
 *   onValueChange={setSelectedValues}
 *   buttons={options}
 * />
 */

interface ButtonConfig<T> {
  /** Button value */
  value: T;
  /** Button label text (optional) */
  label?: string;
  /** Button icon (optional) */
  icon?: string;
  /** Whether this specific button is disabled (optional) */
  disabled?: boolean;
  /** Custom style for individual button (optional) */
  style?: ViewStyle;
}

export interface StandardizedSegmentedButtonsProps<T = string> extends Omit<SegmentedButtonsProps<T>, 'buttons' | 'style'> {
  /** Array of button configurations */
  buttons: ButtonConfig<T>[];
  
  /** Whether to show checkmarks on selected buttons (default: true) */
  showSelectedCheck?: boolean;
  
  /** Additional container style (optional) */
  style?: ViewStyle;
  
  /** Whether buttons should have equal width (default: true) */
  equalWidth?: boolean;
  
  /** Custom padding horizontal (uses theme.spacing.sm by default) */
  paddingHorizontal?: number;
}

export const StandardizedSegmentedButtons = <T extends string | number = string>({
  buttons,
  showSelectedCheck = true,
  style,
  equalWidth = true,
  paddingHorizontal,
  ...props
}: StandardizedSegmentedButtonsProps<T>) => {
  const { spacing } = useTheme();
  
  // Process buttons to ensure consistent configuration
  const processedButtons = buttons.map(button => ({
    ...button,
    showSelectedCheck: showSelectedCheck,
    style: {
      ...(equalWidth && { flex: 1 }),
      ...button.style, // Allow individual button style overrides
    },
  }));
  
  // Default container styles
  const containerStyles: ViewStyle = {
    justifyContent: 'center',
    paddingHorizontal: paddingHorizontal ?? spacing.sm,
    ...style, // Allow style overrides
  };
  
  return (
    <SegmentedButtons
      {...props}
      buttons={processedButtons}
      style={containerStyles}
    />
  );
};

/**
 * Preset configurations for common use cases
 */
export const SegmentedButtonPresets = {
  /** Gender selection preset */
  gender: (t: (key: string) => string) => [
    { 
      value: 'female' as const, 
      icon: 'human-female',
      label: t('genderOptions.female')
    },
    { 
      value: 'male' as const, 
      icon: 'human-male',
      label: t('genderOptions.male')
    }
  ],
  
  /** Time slots preset */
  timeSlots: () => [
    { value: 'morning' as const, label: 'Morning', icon: 'weather-sunny' },
    { value: 'mid-day' as const, label: 'Midday', icon: 'weather-partly-cloudy' },
    { value: 'night' as const, label: 'Night', icon: 'weather-night' }
  ],
  
  /** View toggle preset */
  viewToggle: (t: (key: string) => string) => [
    { value: 'overview' as const, label: t('overview'), icon: 'information-outline' },
    { value: 'safety' as const, label: t('safety'), icon: 'shield-check-outline' }
  ],
  
  /** Transport modes preset (for demos) */
  transport: () => [
    { value: 'walk' as const, label: 'Walking', icon: 'walk' },
    { value: 'train' as const, label: 'Transit', icon: 'train' },
    { value: 'drive' as const, label: 'Driving', icon: 'car' }
  ]
};

export default StandardizedSegmentedButtons;
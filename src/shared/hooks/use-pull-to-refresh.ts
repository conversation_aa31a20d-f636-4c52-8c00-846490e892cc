import { useState, useCallback } from 'react';
import { usePaperTheme } from './use-theme';

/**
 * Configuration options for the pull-to-refresh hook
 */
export interface PullToRefreshConfig {
  /** 
   * Function to execute when refresh is triggered 
   * Can be async and should handle its own errors
   */
  onRefresh: () => void | Promise<void>;
  /** 
   * Optional delay in milliseconds before completing refresh
   * Useful for better UX when refresh is too fast
   * @default 0
   */
  minRefreshTime?: number;
  /** 
   * Enable debug console logging 
   * @default true
   */
  enableLogging?: boolean;
  /** 
   * Custom log prefix for easier debugging 
   * @default '🔄 Pull-to-Refresh'
   */
  logPrefix?: string;
}

/**
 * Return type for the pull-to-refresh hook
 */
export interface PullToRefreshResult {
  /** Whether refresh is currently in progress */
  refreshing: boolean;
  /** Handler function to trigger refresh manually */
  handleRefresh: () => Promise<void>;
  /** RefreshControl props ready to spread into ScrollView */
  refreshControlProps: {
    refreshing: boolean;
    onRefresh: () => void;
    tintColor: string;
    colors: string[];
    title?: string;
    titleColor?: string;
  };
}

/**
 * Reusable Pull-to-Refresh Hook
 * 
 * Provides a DRY solution for implementing pull-to-refresh functionality
 * across the entire app with consistent theming and behavior.
 * 
 * @example
 * ```typescript
 * const { refreshing, refreshControlProps } = usePullToRefresh({
 *   onRefresh: async () => {
 *     // Your refresh logic here
 *     await fetchData();
 *   },
 *   minRefreshTime: 1000,
 * });
 * 
 * return (
 *   <ScrollView refreshControl={<RefreshControl {...refreshControlProps} />}>
 *     {content}
 *   </ScrollView>
 * );
 * ```
 */
export const usePullToRefresh = ({
  onRefresh,
  minRefreshTime = 0,
  enableLogging = true,
  logPrefix = '🔄 Pull-to-Refresh',
}: PullToRefreshConfig): PullToRefreshResult => {
  const theme = usePaperTheme();
  const [refreshing, setRefreshing] = useState(false);

  const handleRefresh = useCallback(async () => {
    if (refreshing) return; // Prevent multiple simultaneous refreshes

    if (enableLogging) {
      console.log(`${logPrefix}: Refresh triggered`);
    }

    setRefreshing(true);
    const startTime = Date.now();

    try {
      // Execute the provided refresh function
      await Promise.resolve(onRefresh());

      // Apply minimum refresh time for better UX
      if (minRefreshTime > 0) {
        const elapsed = Date.now() - startTime;
        const remainingTime = minRefreshTime - elapsed;
        
        if (remainingTime > 0) {
          await new Promise(resolve => setTimeout(resolve, remainingTime));
        }
      }

      if (enableLogging) {
        console.log(`${logPrefix}: Refresh completed successfully`);
      }
    } catch (error) {
      if (enableLogging) {
        console.error(`${logPrefix}: Refresh failed`, error);
      }
      // Note: Error handling should be done in the onRefresh function
      // This hook doesn't handle UI error display to maintain flexibility
    } finally {
      setRefreshing(false);
    }
  }, [onRefresh, minRefreshTime, enableLogging, logPrefix, refreshing]);

  // Create RefreshControl props with theme integration
  const refreshControlProps = {
    refreshing,
    onRefresh: handleRefresh,
    tintColor: theme.colors.primary,
    colors: [theme.colors.primary],
    // iOS specific props
    title: 'Pull to refresh...',
    titleColor: theme.colors.onBackground,
  };

  return {
    refreshing,
    handleRefresh,
    refreshControlProps,
  };
};

/**
 * Hook variant specifically for home screen with common refresh operations
 * 
 * @example
 * ```typescript
 * const { refreshing, refreshControlProps } = useHomeScreenRefresh({
 *   updateGreeting: () => setGreeting(getTimeBasedGreeting()),
 *   updateUserData: async () => { await refetchUserData(); },
 *   updateTimestamp: () => setLastRefresh(new Date()),
 * });
 * ```
 */
export interface HomeScreenRefreshConfig {
  /** Function to update time-based greeting */
  updateGreeting?: () => void;
  /** Function to refresh user data */
  updateUserData?: () => void | Promise<void>;
  /** Function to update last refresh timestamp */
  updateTimestamp?: () => void;
  /** Additional custom refresh logic */
  additionalRefreshLogic?: () => void | Promise<void>;
}

export const useHomeScreenRefresh = ({
  updateGreeting,
  updateUserData,
  updateTimestamp,
  additionalRefreshLogic,
}: HomeScreenRefreshConfig = {}): PullToRefreshResult => {
  return usePullToRefresh({
    onRefresh: async () => {
      // Execute all refresh operations
      updateGreeting?.();
      await updateUserData?.();
      updateTimestamp?.();
      await additionalRefreshLogic?.();
    },
    minRefreshTime: 1000, // Minimum 1 second for good UX
    logPrefix: '🏠 Home Screen Refresh',
  });
};
/**
 * Feature Flags Hook for A/B Testing
 * 
 * Provides type-safe access to PostHog feature flags for gradual rollouts
 * and A/B testing throughout the app.
 */

import { useFeatureFlag } from 'posthog-react-native';

/**
 * Available feature flag names
 * Add new flags here as they're created in PostHog
 */
export type FeatureFlagName = 
  | 'new-onboarding-flow'
  | 'dark-mode-toggle'
  | 'beta-features'
  | 'enhanced-recipe-wizard'
  | 'advanced-analytics';

/**
 * Feature flags hook with type safety
 * 
 * @example
 * ```typescript
 * const { showNewOnboarding, enableDarkMode } = useFeatureFlags();
 * 
 * return (
 *   <View>
 *     {showNewOnboarding && <NewOnboardingFlow />}
 *     {enableDarkMode && <DarkModeToggle />}
 *   </View>
 * );
 * ```
 */
export const useFeatureFlags = () => {
  // Define feature flags with proper typing
  const showNewOnboarding = useFeatureFlag('new-onboarding-flow');
  const enableDarkMode = useFeatureFlag('dark-mode-toggle');
  const showBetaFeatures = useFeatureFlag('beta-features');
  const enhancedRecipeWizard = useFeatureFlag('enhanced-recipe-wizard');
  const advancedAnalytics = useFeatureFlag('advanced-analytics');

  return {
    /**
     * Controls new user onboarding experience
     * @default false
     */
    showNewOnboarding,
    
    /**
     * Controls dark mode availability in theme settings
     * @default true
     */
    enableDarkMode,
    
    /**
     * Enables experimental features for beta users
     * @default false
     */
    showBetaFeatures,
    
    /**
     * Tests improved recipe wizard UX and performance
     * @default false
     */
    enhancedRecipeWizard,
    
    /**
     * Enables detailed analytics collection and debugging
     * @default false
     */
    advancedAnalytics,
  };
};

/**
 * Individual feature flag hooks for specific use cases
 * Use these when you only need one flag to optimize performance
 */

/**
 * Hook for new onboarding flow flag
 */
export const useNewOnboardingFlag = () => {
  return useFeatureFlag('new-onboarding-flow');
};

/**
 * Hook for dark mode toggle flag
 */
export const useDarkModeFlag = () => {
  return useFeatureFlag('dark-mode-toggle');
};

/**
 * Hook for beta features flag
 */
export const useBetaFeaturesFlag = () => {
  return useFeatureFlag('beta-features');
};

/**
 * Hook for enhanced recipe wizard flag
 */
export const useEnhancedRecipeWizardFlag = () => {
  return useFeatureFlag('enhanced-recipe-wizard');
};

/**
 * Hook for advanced analytics flag
 */
export const useAdvancedAnalyticsFlag = () => {
  return useFeatureFlag('advanced-analytics');
};
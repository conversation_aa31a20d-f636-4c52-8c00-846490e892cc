import { useUserPreferences } from '@/shared/contexts/user-preferences-context';
import { designTokens } from '@/shared/styles/design-tokens';
import type { AppTheme } from '@/shared/utils';

/**
 * UNIFIED THEME HOOK - Single source for all theme and design token needs
 * 
 * Provides access to:
 * - Theme colors (React Native Paper)
 * - Design tokens (spacing, typography, etc.)
 * - Theme switching functionality
 * - Dark mode state
 * 
 * This is the ONLY theme hook you need to import in components.
 */
export const useTheme = () => {
  const { theme, toggleTheme, isDarkTheme, preferences, toggleHaptics, isUpdating } = useUserPreferences();
  
  return {
    // Theme object with colors
    theme,
    colors: theme.colors,
    
    // Design tokens (directly from source of truth)
    spacing: designTokens.spacing,
    typography: designTokens.typography,
    borderRadius: designTokens.borderRadius,
    elevation: designTokens.elevation,
    fontWeight: designTokens.fontWeight,
    duration: designTokens.duration,
    easing: designTokens.easing,
    iconSize: designTokens.iconSize,
    zIndex: designTokens.zIndex,
    breakpoints: designTokens.breakpoints,
    components: designTokens.components,
    gradients: designTokens.gradients,
    
    // Theme switching
    toggleTheme,
    themeMode: preferences.themeMode,
    isDarkTheme,
    
    // Haptics integration
    hapticsEnabled: preferences.hapticsEnabled,
    toggleHaptics,
    isHapticsUpdating: isUpdating,
  };
};

/**
 * LEGACY COMPATIBILITY HOOKS
 * Keep these for backward compatibility during migration
 * TODO: Remove after all components are updated to use useTheme()
 */
export const usePaperTheme = () => {
  const { theme } = useTheme();
  return theme;
};

export const useAppTheme = () => {
  const { theme } = useTheme();
  return theme;
};

export const useThemeToggle = () => {
  const { toggleTheme, themeMode, isDarkTheme } = useTheme();
  return { toggleTheme, themeMode, isDarkTheme };
};

/**
 * Hook for creating component styles with theme tokens
 * Usage: const styles = useThemedStyles((theme) => StyleSheet.create({...}))
 */
export const useThemedStyles = <T extends Record<string, any>>(
  createStyles: (theme: ReturnType<typeof useTheme>) => T
): T => {
  const theme = useTheme();
  return createStyles(theme);
};

// Re-export the type for convenience
export type { AppTheme };

import { useState, useCallback, useRef } from 'react';
import { readSseStream } from '@/shared/utils/sse-reader';

/**
 * Streaming item interface for real-time data processing
 */
export interface StreamingItem {
  id?: string;
  title: string;
  subtitle?: string;
  description?: string;
  timestamp?: Date;
  [key: string]: any; // Allow additional properties from API
}

/**
 * Streaming state interface
 */
export interface StreamingState {
  isStreaming: boolean;
  isComplete: boolean;
  error: string | null;
  items: StreamingItem[];
  totalReceived: number;
}

/**
 * Streaming configuration options
 */
export interface StreamingOptions {
  onItem?: (item: StreamingItem) => void;
  onComplete?: (items: StreamingItem[]) => void;
  onError?: (error: string) => void;
}

/**
 * Generic AI Streaming Hook (Modern & Universal)
 *
 * Provides true SSE streaming for Web, iOS, and Android by correctly
 * consuming the standard ReadableStream API.
 *
 * Features:
 * - Real-time item processing as data arrives
 * - Cross-platform compatibility
 * - Error handling and recovery
 * - Memory management and cleanup
 *
 * Requirements: 1.2, 1.5 - SSE streaming client with error handling
 */
export const useAiStreaming = (options: StreamingOptions = {}) => {
  const { onItem, onComplete, onError } = options;

  const [state, setState] = useState<StreamingState>({
    isStreaming: false,
    isComplete: false,
    error: null,
    items: [],
    totalReceived: 0,
  });

  const abortControllerRef = useRef<AbortController | null>(null);

  const resetState = useCallback(() => {
    setState({
      isStreaming: false,
      isComplete: false,
      error: null,
      items: [],
      totalReceived: 0,
    });
  }, []);

  const cleanup = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, []);

  /**
   * Parse streaming data line
   */
  const parseStreamLine = useCallback((line: string): StreamingItem | null => {
    if (!line.startsWith('data: ') || line.length <= 6) {
      return null;
    }

    try {
      const data = JSON.parse(line.slice(6));
      
      // Handle different data formats from the API
      if (data.type === 'structured_data') {
        const item: StreamingItem = {
          id: data.data?.cause_id || data.data?.symptom_id || data.data?.property_id || `item-${Date.now()}`,
          title: data.data?.name_localized || data.data?.name || 'Unknown',
          subtitle: data.data?.suggestion_localized || data.data?.suggestion || '',
          description: data.data?.description_localized || data.data?.description || '',
          timestamp: new Date(),
          ...data.data, // Include all original data
        };
        return item;
      }

      // Handle other data formats
      if (data.data) {
        const item: StreamingItem = {
          id: `item-${Date.now()}-${Math.random()}`,
          title: data.data.title || data.data.name || 'Item',
          subtitle: data.data.subtitle || data.data.description || '',
          description: data.data.description || '',
          timestamp: new Date(),
          ...data.data,
        };
        return item;
      }

      return null;
    } catch (error) {
      console.warn('Failed to parse streaming line:', line, error);
      return null;
    }
  }, []);

  /**
   * Process streaming response using true SSE streaming
   */
  const processStream = useCallback(async (response: Response): Promise<StreamingItem[]> => {
    const platform = typeof window !== 'undefined' ? 'web' : 'mobile';
    const hookId = `hook-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    console.log(`🎣 [HOOK-${platform}] Starting stream processing`, {
      hookId,
      platform,
      responseBodyAvailable: !!response.body,
      responseStatus: response.status,
      timestamp: new Date().toISOString()
    });

    if (!response.body) {
      console.error(`❌ [HOOK-${platform}] Response body not available`, { hookId });
      throw new Error('Response body is not available');
    }

    const items: StreamingItem[] = [];
    let itemCount = 0;

    try {
      console.log(`🔄 [HOOK-${platform}] Starting SSE stream iteration`, { hookId });
      
      // Use the universal SSE reader for true streaming
      for await (const dataString of readSseStream(response.body)) {
        console.log(`📨 [HOOK-${platform}] Received raw data string`, {
          hookId,
          dataLength: dataString.length,
          dataPreview: dataString.substring(0, 100) + (dataString.length > 100 ? '...' : ''),
          itemCount: itemCount + 1
        });

        const item = parseStreamLine(`data: ${dataString}`);
        if (item) {
          items.push(item);
          itemCount++;

          console.log(`✨ [HOOK-${platform}] Parsed item successfully`, {
            hookId,
            itemId: item.id,
            itemTitle: item.title,
            itemCount,
            totalItems: items.length
          });

          // Update state with new item in real-time
          setState(prev => {
            const newState = {
              ...prev,
              items: [...prev.items, item],
              totalReceived: prev.totalReceived + 1,
            };
            
            console.log(`🔄 [HOOK-${platform}] State updated`, {
              hookId,
              previousItemCount: prev.items.length,
              newItemCount: newState.items.length,
              totalReceived: newState.totalReceived
            });
            
            return newState;
          });

          // Call item callback
          console.log(`📞 [HOOK-${platform}] Calling onItem callback`, { hookId, itemId: item.id });
          onItem?.(item);
        } else {
          console.warn(`⚠️ [HOOK-${platform}] Failed to parse item from data string`, {
            hookId,
            dataString: dataString.substring(0, 200) + '...'
          });
        }
      }
      
      console.log(`🏁 [HOOK-${platform}] Stream processing completed`, {
        hookId,
        totalItemsProcessed: items.length,
        finalItemCount: itemCount
      });
    } catch (error) {
      console.error(`❌ [HOOK-${platform}] Stream processing failed`, {
        hookId,
        error: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined,
        itemsProcessedBeforeError: items.length
      });
      throw new Error(`Failed to process stream: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return items;
  }, [parseStreamLine, onItem]);

  /**
   * Start streaming (simplified, no retry logic)
   */
  const startStreaming = useCallback(async (
    url: string,
    requestInit: RequestInit
  ): Promise<StreamingItem[]> => {
    // Clean up any existing stream
    cleanup();

    // Reset state
    setState(prev => ({
      ...prev,
      isStreaming: true,
      isComplete: false,
      error: null,
      items: [],
      totalReceived: 0,
    }));

    try {
      // Create new abort controller
      abortControllerRef.current = new AbortController();

      const response = await fetch(url, {
        ...requestInit,
        signal: abortControllerRef.current.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Process the stream
      const items = await processStream(response);

      // Mark as complete
      setState(prev => ({
        ...prev,
        isStreaming: false,
        isComplete: true,
      }));

      onComplete?.(items);
      return items;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown streaming error';

      // Set error state
      setState(prev => ({
        ...prev,
        isStreaming: false,
        isComplete: false,
        error: errorMessage,
      }));

      onError?.(errorMessage);
      throw new Error(errorMessage);
    }
  }, [cleanup, processStream, onComplete, onError]);

  /**
   * Stop streaming
   */
  const stopStreaming = useCallback(() => {
    cleanup();
    setState(prev => ({
      ...prev,
      isStreaming: false,
    }));
  }, [cleanup]);

  return {
    // State
    ...state,

    // Actions
    startStreaming,
    stopStreaming,
    resetState,
  };
};
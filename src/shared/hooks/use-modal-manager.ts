import { useRef, useCallback } from 'react';
import { BottomSheetModal } from '@gorhom/bottom-sheet';

/**
 * Reusable hook for managing bottom sheet modals
 * Eliminates the need for withModalProvider HOC by providing consistent modal management
 * Uses the global BottomSheetModalProvider from _layout.tsx
 */
export const useModalManager = () => {
  const modalRef = useRef<BottomSheetModal>(null);
  
  const presentModal = useCallback(() => {
    modalRef.current?.present();
  }, []);
  
  const dismissModal = useCallback(() => {
    modalRef.current?.dismiss();
  }, []);
  
  return { 
    modalRef, 
    presentModal, 
    dismissModal 
  };
};
import React, { createContext, useContext, ReactNode, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { haptics } from '@/shared/utils/haptics';

// State managed by the context
interface EmailVerificationState {
  isVisible: boolean;
  isLoading: boolean;
  error: string;
  emailAddress: string;
  createdEmailAddress: any | null;
}

// Functions provided by the context
interface EmailVerificationContextType {
  state: EmailVerificationState;
  showVerificationModal: (emailAddress: string, createdEmailAddress: any) => void;
  hideVerificationModal: () => void;
  handleVerification: (code: string) => Promise<void>;
  handleResend: () => Promise<void>;
  resetState: () => void;
}

const initialState: EmailVerificationState = {
  isVisible: false,
  isLoading: false,
  error: '',
  emailAddress: '',
  createdEmailAddress: null,
};

const EmailVerificationContext = createContext<EmailVerificationContextType | undefined>(undefined);

interface EmailVerificationProviderProps {
  children: ReactNode;
  onVerificationSuccess?: () => void;
  onVerificationCancel?: () => void;
}

export const EmailVerificationProvider = ({
  children,
  onVerificationSuccess,
  onVerificationCancel
}: EmailVerificationProviderProps) => {
  const [state, setState] = useState<EmailVerificationState>(initialState);
  const { t } = useTranslation('common');

  const showVerificationModal = useCallback((emailAddress: string, createdEmailAddress: any) => {
    if (__DEV__) {
      console.log('📱 [EMAIL VERIFICATION] Showing verification modal');
      console.log('📱 [EMAIL VERIFICATION] Email:', emailAddress);
      console.log('📱 [EMAIL VERIFICATION] Created email address available:', !!createdEmailAddress);
    }

    setState(prev => ({
      ...prev,
      isVisible: true,
      emailAddress,
      createdEmailAddress,
      error: '',
      isLoading: false,
    }));
  }, []);

  const hideVerificationModal = useCallback(() => {
    if (__DEV__) {
      console.log('📱 [EMAIL VERIFICATION] Hiding verification modal');
    }

    setState(prev => ({
      ...prev,
      isVisible: false,
      error: '',
      isLoading: false,
    }));

    // Call cleanup callback if provided
    if (onVerificationCancel) {
      onVerificationCancel();
    }
  }, [onVerificationCancel]);

  const handleVerification = useCallback(async (code: string) => {
    if (__DEV__) {
      console.log('🔐 [EMAIL VERIFICATION] Starting verification process');
      console.log('🔐 [EMAIL VERIFICATION] Code length:', code.length);
      console.log('🔐 [EMAIL VERIFICATION] Email address available:', !!state.createdEmailAddress);
    }

    if (!state.createdEmailAddress) {
      const errorMessage = t('profile.verificationFailed');
      setState(prev => ({ ...prev, error: errorMessage }));
      haptics.error();
      throw new Error(errorMessage);
    }

    // Validate code format (6 digits)
    const trimmedCode = code.trim();
    if (!/^\d{6}$/.test(trimmedCode)) {
      const errorMessage = t('profile.invalidVerificationCode');
      setState(prev => ({ ...prev, error: errorMessage }));
      haptics.error();
      throw new Error(errorMessage);
    }

    setState(prev => ({ ...prev, isLoading: true, error: '' }));

    try {
      if (__DEV__) {
        console.log('🔐 [EMAIL VERIFICATION] Step 1: Calling attemptVerification()');
      }

      const verificationAttempt = await state.createdEmailAddress.attemptVerification({
        code: trimmedCode,
      });

      if (__DEV__) {
        console.log('🔐 [EMAIL VERIFICATION] Verification result:', {
          status: verificationAttempt.verification?.status,
          emailId: verificationAttempt.id
        });
      }

      if (verificationAttempt.verification?.status === 'verified') {
        if (__DEV__) {
          console.log('🔐 [EMAIL VERIFICATION] ✅ Email verified successfully');
        }

        // Reset state and hide modal
        setState(initialState);
        haptics.success();

        // Call success callback if provided
        if (onVerificationSuccess) {
          onVerificationSuccess();
        }

        return verificationAttempt;
      } else {
        throw new Error('Verification incomplete');
      }
    } catch (error: any) {
      if (__DEV__) {
        console.log('🔐 [EMAIL VERIFICATION] ❌ Verification failed:', error);
        console.log('🔐 [EMAIL VERIFICATION] Error details:', {
          message: error.message,
          stack: error.stack,
          name: error.name
        });
      }
      console.error('Email verification error:', error);

      // Handle specific verification errors
      let errorMessage: string;
      if (error.message?.includes('incorrect') || error.message?.includes('invalid')) {
        errorMessage = t('profile.invalidVerificationCode');
      } else if (error.message?.includes('expired')) {
        errorMessage = t('profile.verificationCodeExpired');
      } else {
        errorMessage = t('profile.verificationFailed');
      }

      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }));
      haptics.error();
      throw new Error(errorMessage);
    }
  }, [state.createdEmailAddress, t, onVerificationSuccess]);

  const handleResend = useCallback(async () => {
    if (__DEV__) {
      console.log('🔄 [EMAIL VERIFICATION] Starting verification code resend');
      console.log('🔄 [EMAIL VERIFICATION] Email address available:', !!state.createdEmailAddress);
    }

    if (!state.createdEmailAddress) {
      const errorMessage = t('profile.resendFailed');
      setState(prev => ({ ...prev, error: errorMessage }));
      haptics.error();
      throw new Error(errorMessage);
    }

    setState(prev => ({ ...prev, isLoading: true, error: '' }));

    try {
      if (__DEV__) {
        console.log('🔄 [EMAIL VERIFICATION] Step 1: Calling prepareVerification()');
      }

      await state.createdEmailAddress.prepareVerification({
        strategy: 'email_code',
      });

      if (__DEV__) {
        console.log('🔄 [EMAIL VERIFICATION] ✅ Verification code resent successfully');
      }

      setState(prev => ({ ...prev, isLoading: false }));
      haptics.success();
    } catch (error: any) {
      if (__DEV__) {
        console.log('🔄 [EMAIL VERIFICATION] ❌ Resend failed:', error);
        console.log('🔄 [EMAIL VERIFICATION] Error details:', {
          message: error.message,
          stack: error.stack,
          name: error.name
        });
      }
      console.error('Email resend error:', error);

      // Handle specific resend errors
      let errorMessage: string;
      if (error.message?.includes('rate limit')) {
        errorMessage = t('profile.resendRateLimited');
      } else {
        errorMessage = t('profile.resendFailed');
      }

      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }));
      haptics.error();
      throw new Error(errorMessage);
    }
  }, [state.createdEmailAddress, t]);

  const resetState = useCallback(() => {
    if (__DEV__) {
      console.log('🔄 [EMAIL VERIFICATION] Resetting state');
    }

    setState(initialState);
  }, []);

  return (
    <EmailVerificationContext.Provider value={{
      state,
      showVerificationModal,
      hideVerificationModal,
      handleVerification,
      handleResend,
      resetState,
    }}>
      {children}
    </EmailVerificationContext.Provider>
  );
};

export const useEmailVerificationContext = () => {
  const context = useContext(EmailVerificationContext);
  if (context === undefined) {
    throw new Error('useEmailVerificationContext must be used within an EmailVerificationProvider');
  }
  return context;
};
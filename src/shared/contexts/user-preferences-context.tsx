/**
 * @fileoverview Unified User Preferences Context using Clerk
 * Single source of truth for theme, haptics, and language preferences
 * Follows DRY, KISS, YAGNI principles
 * 
 * CRITICAL: Prevent infinite loops by:
 * - Only updating state when preferences actually change
 * - Using functional state updates for conditional changes
 * - Avoiding duplicate setPreferences() calls in useEffect
 */

import React, { createContext, useContext, useEffect, useState, ReactNode, useCallback, useMemo } from 'react';
import { useUser } from '@clerk/clerk-expo';
import { useColorScheme } from 'react-native';
import { MD3LightTheme } from 'react-native-paper';
import { createTheme } from '@/shared/utils';
// PostHog for theme analytics - import safely
let usePostHog: any = null;
try {
  const postHogModule = require('posthog-react-native');
  usePostHog = postHogModule.usePostHog;
} catch {
  // PostHog not available - create a no-op hook
  usePostHog = () => ({ capture: () => {} });
}

// Types
export type ThemeMode = 'light' | 'dark' | 'system';

export interface UserPreferences {
  themeMode: ThemeMode;
  hapticsEnabled: boolean;
}


// Default preferences
const DEFAULT_PREFERENCES: UserPreferences = {
  themeMode: 'system',
  hapticsEnabled: true,
};

// Context interface
interface UserPreferencesContextType {
  // Preferences
  preferences: UserPreferences;

  // Theme-related
  theme: typeof MD3LightTheme;
  isDarkTheme: boolean;
  toggleTheme: () => Promise<void>;

  // Haptics-related
  toggleHaptics: () => Promise<void>;

  // State
  isLoading: boolean;
  isUpdating: boolean;
}

// Create context
const UserPreferencesContext = createContext<UserPreferencesContextType | undefined>(undefined);

// Provider component
interface UserPreferencesProviderProps {
  children: ReactNode;
}

export function UserPreferencesProvider({ children }: UserPreferencesProviderProps) {
  const { user, isLoaded } = useUser();
  const systemColorScheme = useColorScheme();
  
  // PostHog analytics hook - always call to maintain hook order
  const posthogHook = usePostHog();

  const [preferences, setPreferences] = useState<UserPreferences>(DEFAULT_PREFERENCES);
  const [isUpdating, setIsUpdating] = useState(false);

  // Load preferences from Clerk user metadata
  useEffect(() => {
    if (isLoaded && user) {
      const userPrefs = user.unsafeMetadata as Partial<UserPreferences> || {};

      const newPreferences = {
        themeMode: userPrefs.themeMode || DEFAULT_PREFERENCES.themeMode,
        hapticsEnabled: userPrefs.hapticsEnabled ?? DEFAULT_PREFERENCES.hapticsEnabled,
      };

      // Only update state if preferences actually changed
      setPreferences(currentPrefs => {
        // Check if preferences are different
        const hasChanged = (
          currentPrefs.themeMode !== newPreferences.themeMode ||
          currentPrefs.hapticsEnabled !== newPreferences.hapticsEnabled
        );

        if (hasChanged) {
          // Track theme initialization if theme changed
          if (currentPrefs.themeMode !== newPreferences.themeMode) {
            try {
              const actualTheme = newPreferences.themeMode === 'system'
                ? (systemColorScheme === 'dark' ? 'dark' : 'light')
                : newPreferences.themeMode;

              posthogHook.capture('theme_initialized', {
                themeMode: newPreferences.themeMode,
                actualTheme,
                systemTheme: systemColorScheme,
                isSystemDefault: newPreferences.themeMode === 'system',
                timestamp: new Date().toISOString(),
              });

              if (__DEV__) {
                console.log('📊 PostHog: Theme initialized', {
                  themeMode: newPreferences.themeMode,
                  actualTheme,
                });
              }
            } catch (error) {
              if (__DEV__) {
                console.warn('Failed to track theme initialization:', error);
              }
            }
          }

          return newPreferences;
        }

        return currentPrefs;
      });
    }
  }, [isLoaded, user?.unsafeMetadata, user, systemColorScheme]);


  // Update preference in Clerk
  const updatePreference = useCallback(async <K extends keyof UserPreferences>(
    key: K,
    value: UserPreferences[K]
  ): Promise<void> => {
    if (!user) return;

    setIsUpdating(true);
    try {
      // Update local state immediately for responsive UI
      setPreferences(prev => ({ ...prev, [key]: value }));
      

      // Update Clerk metadata
      await user.update({
        unsafeMetadata: {
          ...user.unsafeMetadata,
          [key]: value,
        },
      });
    } catch (error) {
      console.error(`Failed to update ${key}:`, error);
      // Revert local state on error
      if (isLoaded && user) {
        const userPrefs = user.unsafeMetadata as Partial<UserPreferences> || {};
        setPreferences({
          themeMode: userPrefs.themeMode || DEFAULT_PREFERENCES.themeMode,
          hapticsEnabled: userPrefs.hapticsEnabled ?? DEFAULT_PREFERENCES.hapticsEnabled,
        });
      }
    } finally {
      setIsUpdating(false);
    }
  }, [user, isLoaded]);

  // Theme logic
  const isDarkTheme = preferences.themeMode === 'system'
    ? systemColorScheme === 'dark'
    : preferences.themeMode === 'dark';

  const theme = useMemo(() => createTheme(isDarkTheme), [isDarkTheme]);

  const toggleTheme = useCallback(async () => {
    // Safeguard 1: Prevent concurrent executions
    if (isUpdating) {
      if (__DEV__) {
        console.log('🔒 Theme toggle blocked - already updating');
      }
      return; // Early exit if already updating
    }

    setIsUpdating(true);

    try {
      const currentMode = preferences.themeMode;
      const previousTheme = isDarkTheme ? 'dark' : 'light';
      let newMode: ThemeMode;

      if (currentMode === 'system') {
        // If currently system, switch to opposite of current system theme
        newMode = systemColorScheme === 'dark' ? 'light' : 'dark';
      } else {
        // Otherwise, toggle between light and dark
        newMode = currentMode === 'light' ? 'dark' : 'light';
      }

      const newTheme = newMode === 'system' 
        ? (systemColorScheme === 'dark' ? 'dark' : 'light')
        : newMode;

      // Track theme toggle with PostHog
      try {
        posthogHook.capture('theme_toggled', {
          newTheme,
          previousTheme,
          newMode,
          previousMode: currentMode,
          timestamp: new Date().toISOString(),
          isSystemDefault: newMode === 'system',
          systemTheme: systemColorScheme,
        });

        if (__DEV__) {
          console.log('📊 PostHog: Theme toggled', {
            newTheme,
            previousTheme,
            newMode,
            previousMode: currentMode,
          });
        }
      } catch (error) {
        if (__DEV__) {
          console.warn('Failed to track theme toggle:', error);
        }
      }

      await updatePreference('themeMode', newMode);
    } catch (error) {
      if (__DEV__) {
        console.error('Theme toggle failed:', error);
      }
    } finally {
      // Safeguard 2: Always reset the updating state
      setIsUpdating(false);
    }
  }, [preferences.themeMode, systemColorScheme, updatePreference, isDarkTheme, isUpdating]);

  // Haptics logic
  const toggleHaptics = useCallback(async () => {
    // Safeguard: Prevent concurrent executions
    if (isUpdating) {
      if (__DEV__) {
        console.log('🔒 Haptics toggle blocked - already updating');
      }
      return;
    }

    setIsUpdating(true);

    try {
      await updatePreference('hapticsEnabled', !preferences.hapticsEnabled);
    } catch (error) {
      if (__DEV__) {
        console.error('Haptics toggle failed:', error);
      }
    } finally {
      setIsUpdating(false);
    }
  }, [preferences.hapticsEnabled, updatePreference, isUpdating]);


  const contextValue: UserPreferencesContextType = {
    preferences,
    theme,
    isDarkTheme,
    toggleTheme,
    toggleHaptics,
    isLoading: !isLoaded,
    isUpdating,
  };

  return (
    <UserPreferencesContext.Provider value={contextValue}>
      {children}
    </UserPreferencesContext.Provider>
  );
}

// Hook to use preferences context
export function useUserPreferences(): UserPreferencesContextType {
  const context = useContext(UserPreferencesContext);
  if (context === undefined) {
    throw new Error('useUserPreferences must be used within a UserPreferencesProvider');
  }
  return context;
}

// REMOVED: Duplicate useTheme hook - use the one from @/shared/hooks/use-theme instead
// This was causing naming conflicts and "Value is a number, expected an Object" errors

// DEPRECATED: useHaptics hook removed - use useTheme() from @/shared/hooks/use-theme instead
// This provides better consistency and follows the "Single Source of Truth" principle

export { useLanguage } from '@/shared/hooks/use-language';
/**
 * @fileoverview HapticsProvider - Syncs user preferences with haptics manager
 * Ensures haptics respect user settings from Clerk
 */

import React, { useEffect, ReactNode } from 'react';
import { useUserPreferences } from '@/shared/contexts/user-preferences-context';
import { haptics } from '@/shared/utils/haptics';

interface HapticsProviderProps {
  children: ReactNode;
}

export const HapticsProvider: React.FC<HapticsProviderProps> = ({ children }) => {
  const { preferences, isLoading } = useUserPreferences();

  useEffect(() => {
    if (!isLoading) {
      // Sync haptics manager with user preferences
      haptics.setEnabled(preferences.hapticsEnabled);
    }
  }, [isLoading, preferences.hapticsEnabled]);

  return <>{children}</>;
};
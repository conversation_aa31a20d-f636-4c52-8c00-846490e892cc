import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { useAuth, useUser } from '@clerk/clerk-expo';
import { createAuthenticatedSupabaseClient } from '@/shared/services/supabase/supabase-client';
import type { RealtimeChannel, SupabaseClient } from '@supabase/supabase-js';
import { processPresenceState, createUserTrackingPayload, PresenceUser } from '@/features/realtime-chat/utils';

// Import the existing types to maintain compatibility
export interface RealtimeChatMessage {
  id: string;
  content: string;
  user_id: string;
  user_name: string;
  user_avatar?: string;
  created_at: string;
}

// Complete interface matching current component expectations
interface RealtimeContextState {
  // Chat functionality
  messages: RealtimeChatMessage[];
  sendMessage: (content: string) => Promise<void>;
  clearMessages: () => void;
  
  // Presence functionality
  presentUsers: PresenceUser[];
  onlineCount: number;
  
  // Connection state
  isConnected: boolean;
  isLoading: boolean;
  isRetryingConnection: boolean;
  connectionRetryCount: number;
  connectionStatus: 'good' | 'poor' | 'disconnected';
  error: string | null;
  retry: () => Promise<void>;
  
  // Compatibility aliases
  onlineUsers: PresenceUser[]; // alias for presentUsers
  isReconnecting: boolean; // alias for isRetryingConnection  
  reconnectAttempts: number; // alias for connectionRetryCount
  connectionQuality: 'good' | 'poor' | 'disconnected'; // alias for connectionStatus
}

// Create the React Context
const RealtimeContext = createContext<RealtimeContextState | undefined>(undefined);

// Provider component
export const RealtimeProvider = ({ children }: { children: React.ReactNode }) => {
  const { getToken, isSignedIn } = useAuth();
  const { user } = useUser();

  // Core state
  const [supabaseClient, setSupabaseClient] = useState<SupabaseClient | null>(null);
  const [channel, setChannel] = useState<RealtimeChannel | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [presentUsers, setPresentUsers] = useState<PresenceUser[]>([]);
  const [messages, setMessages] = useState<RealtimeChatMessage[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [connectionRetryCount, setConnectionRetryCount] = useState(0);
  const [isRetryingConnection, setIsRetryingConnection] = useState(false);
  
  // Use a ref to prevent multiple connection attempts
  const connectionAttempted = useRef(false);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Constants
  const ROOM_NAME = 'demo-chat-messages';
  const TABLE_NAME = 'demo_chat_messages';
  const MAX_RETRY_ATTEMPTS = 5;
  const INITIAL_RETRY_DELAY = 1000;

  // Connection quality derived from connection state
  const connectionStatus: 'good' | 'poor' | 'disconnected' = 
    isConnected ? 'good' : isRetryingConnection ? 'poor' : 'disconnected';

  // Retry function
  const retry = useCallback(async () => {
    if (isRetryingConnection || !user || !isSignedIn) return;
    
    setIsRetryingConnection(true);
    setError(null);
    connectionAttempted.current = false;
    
    // Reset connection state
    if (channel) {
      channel.unsubscribe();
      setChannel(null);
    }
    if (supabaseClient) {
      supabaseClient.removeAllChannels();
      setSupabaseClient(null);
    }
    
    // Will trigger reconnection in useEffect
    setIsRetryingConnection(false);
  }, [isRetryingConnection, user, isSignedIn, channel, supabaseClient]);

  // Clear messages function
  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  // Main connection effect
  useEffect(() => {
    // Clean up any existing timeout
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    // Do nothing if the user is not signed in
    if (!isSignedIn || !user) {
      // Clean up existing connection on sign-out
      if (channel) {
        if (__DEV__) {
          console.log('🟠 [RealtimeProvider] User signed out. Cleaning up connection.');
        }
        channel.unsubscribe();
        supabaseClient?.removeAllChannels();
        setChannel(null);
        setSupabaseClient(null);
        setIsConnected(false);
        setIsLoading(false);
        setPresentUsers([]);
        setMessages([]);
        setError(null);
        setConnectionRetryCount(0);
        setIsRetryingConnection(false);
        connectionAttempted.current = false;
      }
      return;
    }

    // Prevent re-connecting if already connected or attempting
    if (channel || connectionAttempted.current) {
      return;
    }

    let localChannel: RealtimeChannel;
    let shouldRetry = true;

    const connect = async () => {
      connectionAttempted.current = true;
      setIsLoading(true);
      setError(null);
      
      if (__DEV__) {
        console.log('🔵 [RealtimeProvider] Establishing app-wide realtime connection...');
        console.log('👤 [RealtimeProvider] Connected User Debug Info:');
        console.log('  - User ID:', user?.id);
        console.log('  - Full Name:', user?.fullName);
        console.log('  - Email:', user?.primaryEmailAddress?.emailAddress);
        console.log('  - Image URL:', user?.imageUrl);
        console.log('  - Available props:', Object.keys(user || {}));
        console.log('  - Full user object:', user);
      }
      
      try {
        const token = await getToken({ template: 'supabase' });
        if (!token) {
          throw new Error('Could not get auth token');
        }
        
        const client = createAuthenticatedSupabaseClient(token);
        setSupabaseClient(client);

        localChannel = client.channel(ROOM_NAME);

        // Attach all event listeners
        localChannel
          .on('presence', { event: 'sync' }, () => {
            const users = processPresenceState(localChannel.presenceState());
            setPresentUsers(users);
          })
          .on('broadcast', { event: 'new_message' }, (payload) => {
            const newMessage = payload.payload as RealtimeChatMessage;
            // Prevent adding our own optimistic message again
            if (newMessage.user_id !== user.id) {
               setMessages((prev) => [newMessage, ...prev]);
            }
          });

        // Subscribe and track presence
        localChannel.subscribe(async (status) => {
          if (__DEV__) {
            console.log(`📡 [RealtimeProvider] Channel status: ${status}`);
          }
          
          if (status === 'SUBSCRIBED') {
            setIsConnected(true);
            setIsLoading(false);
            setConnectionRetryCount(0);
            setIsRetryingConnection(false);
            
            const trackingPayload = createUserTrackingPayload(user);
            if (__DEV__) {
              console.log('🎯 [RealtimeProvider] Tracking user presence with payload:', trackingPayload);
            }
            await localChannel.track(trackingPayload);
            
            // Load initial messages
            try {
              const { data } = await client
                .from(TABLE_NAME)
                .select('*')
                .order('created_at', { ascending: false })
                .limit(50);
              
              if (data) {
                setMessages(data as RealtimeChatMessage[]);
              }
            } catch (messageError) {
              console.error('🔴 [RealtimeProvider] Failed to load initial messages:', messageError);
            }
          } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
            setIsConnected(false);
            setIsLoading(false);
            
            if (shouldRetry && connectionRetryCount < MAX_RETRY_ATTEMPTS) {
              const retryDelay = INITIAL_RETRY_DELAY * Math.pow(2, connectionRetryCount);
              setConnectionRetryCount(prev => prev + 1);
              setIsRetryingConnection(true);
              
              if (__DEV__) {
                console.log(`🔄 [RealtimeProvider] Retrying connection in ${retryDelay}ms (attempt ${connectionRetryCount + 1})`);
              }
              
              reconnectTimeoutRef.current = setTimeout(() => {
                connectionAttempted.current = false;
                setIsRetryingConnection(false);
              }, retryDelay);
            } else {
              setError(`Connection failed: ${status}`);
              setIsRetryingConnection(false);
            }
          } else {
            setIsConnected(false);
          }
        });
        
        setChannel(localChannel);
        
      } catch (err) {
        setIsLoading(false);
        setIsConnected(false);
        connectionAttempted.current = false;
        
        const errorMessage = err instanceof Error ? err.message : 'Connection setup failed';
        setError(errorMessage);
        
        if (__DEV__) {
          console.error('🔴 [RealtimeProvider] Connection error:', err);
        }
        
        // Retry logic for setup errors
        if (shouldRetry && connectionRetryCount < MAX_RETRY_ATTEMPTS) {
          const retryDelay = INITIAL_RETRY_DELAY * Math.pow(2, connectionRetryCount);
          setConnectionRetryCount(prev => prev + 1);
          setIsRetryingConnection(true);
          
          reconnectTimeoutRef.current = setTimeout(() => {
            setIsRetryingConnection(false);
          }, retryDelay);
        }
      }
    };

    connect();

    // Cleanup function
    return () => {
      shouldRetry = false;
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }
      if (localChannel) {
        if (__DEV__) {
          console.log('🟠 [RealtimeProvider] Cleanup effect running.');
        }
        localChannel.unsubscribe();
        supabaseClient?.removeChannel(localChannel);
      }
      connectionAttempted.current = false;
    };
  }, [isSignedIn, user, getToken, connectionRetryCount, channel, supabaseClient]);

  // Send message function
  const sendMessage = useCallback(async (content: string) => {
    if (!channel || !supabaseClient || !user || !isConnected || !content.trim()) {
      throw new Error('Cannot send message. Not connected or missing user/content.');
    }
    
    const messageData = {
      content: content.trim(),
      user_id: user.id,
      user_name: user.fullName || user.primaryEmailAddress?.emailAddress || 'Anonymous',
      user_avatar: user.imageUrl,
    };
    
    if (__DEV__) {
      console.log('💬 [RealtimeProvider] Sending message with user data:');
      console.log('  - Message content:', content.trim());
      console.log('  - User ID:', user.id);
      console.log('  - User name used:', messageData.user_name);
      console.log('  - User avatar used:', messageData.user_avatar);
      console.log('  - Full message data:', messageData);
    }
    
    // Optimistic UI update
    const tempMessage: RealtimeChatMessage = {
      id: `temp-${Date.now()}`,
      ...messageData,
      created_at: new Date().toISOString(),
    };
    setMessages(prev => [tempMessage, ...prev]);

    try {
      // Save to database
      const { data: savedData, error } = await supabaseClient
        .from(TABLE_NAME)
        .insert(messageData)
        .select()
        .single();

      if (error || !savedData) {
        // Revert optimistic update on failure
        setMessages(prev => prev.filter(msg => msg.id !== tempMessage.id));
        throw error || new Error("Failed to save message");
      }

      // Replace temp message with real one from DB
      setMessages(prev => prev.map(msg => msg.id === tempMessage.id ? savedData as RealtimeChatMessage : msg));

      // Broadcast the real message to others
      await channel.send({
        type: 'broadcast',
        event: 'new_message',
        payload: savedData,
      });
      
    } catch (error) {
      // Revert optimistic update on error
      setMessages(prev => prev.filter(msg => msg.id !== tempMessage.id));
      throw error;
    }
  }, [channel, user, isConnected, supabaseClient]);

  // Provide the global state to all children
  const value: RealtimeContextState = {
    // Chat functionality
    messages,
    sendMessage,
    clearMessages,
    
    // Presence functionality
    presentUsers,
    onlineCount: presentUsers.length,
    
    // Connection state
    isConnected,
    isLoading,
    isRetryingConnection,
    connectionRetryCount,
    connectionStatus,
    error,
    retry,
    
    // Compatibility aliases
    onlineUsers: presentUsers,
    isReconnecting: isRetryingConnection,
    reconnectAttempts: connectionRetryCount,
    connectionQuality: connectionStatus,
  };

  return <RealtimeContext.Provider value={value}>{children}</RealtimeContext.Provider>;
};

// Custom hook for accessing the realtime context
export const useRealtime = () => {
  const context = useContext(RealtimeContext);
  if (context === undefined) {
    throw new Error('useRealtime must be used within a RealtimeProvider');
  }
  return context;
};
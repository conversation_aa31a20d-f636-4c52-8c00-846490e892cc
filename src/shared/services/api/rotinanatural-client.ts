/**
 * AromaChat API Client
 * 
 * Dedicated client for the AromaChat backend API at novo.rotinanatural.com.br
 * Handles authentication, request formatting, and response processing.
 * 
 * Requirements: 1.3, 1.4 - API client with authentication and error handling
 */

// API Configuration
// Mobile SSE Client for React Native platforms
import { MobileSSEClient } from '../../utils/mobile-sse-client';

const API_BASE_URL = 'https://novo.rotinanatural.com.br/api';
const API_ENDPOINTS = {
  streaming: '/ai/streaming',
  batchEnrichment: '/ai/batch-enrichment',
} as const;

/**
 * Base request data structure for all API calls
 */
export interface BaseApiRequest {
  health_concern: string;
  gender: 'male' | 'female';
  age_category: string;
  age_specific: string;
  user_language: string;
}

/**
 * Streaming API request structure
 */
export interface StreamingApiRequest {
  feature: 'create-recipe';
  step: 'potential-causes' | 'potential-symptoms' | 'therapeutic-properties' | 'final-recipes';
  data: BaseApiRequest & {
    selected_causes?: any[];
    selected_symptoms?: any[];
    selected_properties?: any[];
  };
}

/**
 * Streaming API response structure
 */
export interface StreamingApiResponse {
  data: {
    potential_causes?: any[];
    potential_symptoms?: any[];
    therapeutic_properties?: any[];
    enriched_oils?: any[];
    final_recipes?: any[];
  };
  meta: {
    step_name: string;
    request_id: string;
    timestamp_utc: string;
    status: string;
  };
}

/**
 * Batch enrichment request structure
 */
export interface BatchEnrichmentRequest {
  suggestedOils: {
    name: string;
    botanical_name: string;
    [key: string]: any;
  }[];
}

/**
 * API Error class for structured error handling
 */
export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public response?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

/**
 * Get API key from environment variables
 */
const getApiKey = (): string => {
  const apiKey = process.env.EXPO_PUBLIC_API_KEY;
  if (!apiKey) {
    throw new ApiError('API key not found. Please set EXPO_PUBLIC_API_KEY environment variable.');
  }
  return apiKey;
};

/**
 * Create request headers with authentication
 */
const createHeaders = (additionalHeaders: Record<string, string> = {}): Record<string, string> => {
  const apiKey = getApiKey();
  
  return {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${apiKey}`,
    ...additionalHeaders,
  };
};

/**
 * Handle API response and errors
 */
const handleResponse = async (response: Response) => {
  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
    
    try {
      const errorData = await response.json();
      if (errorData.error) {
        errorMessage = errorData.error;
      }
    } catch {
      // If we can't parse the error response, use the default message
    }
    
    throw new ApiError(errorMessage, response.status, response);
  }
  
  return response;
};

// Platform detection for API logging
const getPlatform = (): string => {
  try {
    // Try React Native first
    const Platform = require('react-native').Platform;
    return Platform.OS || 'unknown';
  } catch {
    // Fallback to web detection
    if (typeof window !== 'undefined') {
      return 'web';
    }
    return 'unknown';
  }
};

/**
 * Stream recipe step data from the API
 *
 * This function handles the streaming API calls for the recipe creation process.
 * It returns a Response object that can be processed with a streaming reader.
 * On mobile platforms (iOS/Android), it uses XMLHttpRequest-based streaming
 * to work around React Native's fetch() limitations with ReadableStream.
 *
 * @param request - The streaming API request data
 * @returns Promise<Response> - The streaming response
 */
export const streamRecipeStep = async (request: StreamingApiRequest): Promise<Response> => {
  const platform = getPlatform();
  const requestId = `api-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

  try {
    const headers = createHeaders();
    const requestBody = JSON.stringify(request);

    console.log(`🚀 [API-${platform}] Starting streaming request`, {
      requestId,
      platform,
      url: `${API_BASE_URL}${API_ENDPOINTS.streaming}`,
      step: request.step,
      feature: request.feature,
      timestamp: new Date().toISOString(),
      headers: Object.keys(headers),
      bodySize: requestBody.length,
      requestPreview: requestBody.substring(0, 200) + (requestBody.length > 200 ? '...' : '')
    });

    const startTime = performance.now();

    // Use mobile-specific streaming for ALL platforms (unified implementation)
    // This ensures consistent behavior across web, iOS, and Android
    console.log(`📱 [API-${platform}] Using unified mobile SSE client for platform: ${platform}`, { requestId });
    return createMobileStreamingResponse(request, headers, requestId, platform);

    // Note: All platforms now use unified mobile implementation above

  } catch (error) {
    console.error(`❌ [API-${platform}] Streaming API request failed`, {
      requestId,
      platform,
      error: error instanceof Error ? error.message : String(error),
      errorStack: error instanceof Error ? error.stack : undefined,
      errorType: error?.constructor.name,
      step: request.step,
      feature: request.feature
    });
    
    if (error instanceof ApiError) {
      throw error;
    }
    
    throw new ApiError(
      error instanceof Error ? error.message : 'Unknown API error occurred',
      undefined,
      error
    );
  }
};

/**
 * Stream potential causes based on health concern and demographics
 */
export const streamPotentialCauses = async (data: BaseApiRequest): Promise<Response> => {
  const request: StreamingApiRequest = {
    feature: 'create-recipe',
    step: 'potential-causes',
    data: {
      ...data,
      selected_causes: [],
      selected_symptoms: [],
    },
  };

  return streamRecipeStep(request);
};

/**
 * Stream potential symptoms based on selected causes
 */
export const streamPotentialSymptoms = async (
  baseData: BaseApiRequest,
  selectedCauses: any[]
): Promise<Response> => {
  const request: StreamingApiRequest = {
    feature: 'create-recipe',
    step: 'potential-symptoms',
    data: {
      ...baseData,
      selected_causes: selectedCauses,
      selected_symptoms: [],
    },
  };

  return streamRecipeStep(request);
};

/**
 * Stream therapeutic properties based on selected symptoms
 */
export const streamTherapeuticProperties = async (
  baseData: BaseApiRequest,
  selectedCauses: any[],
  selectedSymptoms: any[]
): Promise<Response> => {
  const request: StreamingApiRequest = {
    feature: 'create-recipe',
    step: 'therapeutic-properties',
    data: {
      ...baseData,
      selected_causes: selectedCauses,
      selected_symptoms: selectedSymptoms,
    },
  };

  return streamRecipeStep(request);
};

/**
 * Batch enrich oil data
 * 
 * This function handles the batch enrichment API for oil data.
 * Unlike streaming endpoints, this returns JSON data directly.
 */
export const batchEnrichOils = async (request: BatchEnrichmentRequest): Promise<any> => {
  try {
    console.log('Making batch enrichment API request:', {
      url: `${API_BASE_URL}${API_ENDPOINTS.batchEnrichment}`,
      oilCount: request.suggestedOils.length,
    });

    const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.batchEnrichment}`, {
      method: 'POST',
      headers: createHeaders(),
      body: JSON.stringify(request),
    });

    await handleResponse(response);
    return response.json();

  } catch (error) {
    console.error('Batch enrichment API request failed:', error);
    
    if (error instanceof ApiError) {
      throw error;
    }
    
    throw new ApiError(
      error instanceof Error ? error.message : 'Unknown API error occurred',
      undefined,
      error
    );
  }
};

// Removed getSuggestedOilsForProperties mock function
// Now using real streaming API with individual property requests following Next.js pattern

/**
 * Test API connectivity
 * 
 * Simple health check function to test API connectivity and authentication.
 * Returns basic connection information.
 */
export const testApiConnection = async (): Promise<{
  connected: boolean;
  responseTime: number;
  error?: string;
}> => {
  const startTime = performance.now();
  
  try {
    // Use a minimal request to test connectivity
    const testRequest: StreamingApiRequest = {
      feature: 'create-recipe',
      step: 'potential-causes',
      data: {
        health_concern: 'test connection',
        gender: 'female',
        age_category: 'adult',
        age_specific: '30',
        user_language: 'EN_US',
        selected_causes: [],
        selected_symptoms: [],
      },
    };

    const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.streaming}`, {
      method: 'POST',
      headers: createHeaders(),
      body: JSON.stringify(testRequest),
    });

    const responseTime = performance.now() - startTime;

    if (response.ok) {
      return {
        connected: true,
        responseTime,
      };
    } else {
      return {
        connected: false,
        responseTime,
        error: `HTTP ${response.status}: ${response.statusText}`,
      };
    }

  } catch (error) {
    const responseTime = performance.now() - startTime;
    
    return {
      connected: false,
      responseTime,
      error: error instanceof Error ? error.message : 'Unknown connection error',
    };
  }
};

/**
 * Universal streaming reader for processing SSE data
 *
 * Uses the modern ReadableStream API for true real-time streaming
 * on Web, iOS, and Android platforms.
 */
export const createStreamingReader = async function* (
  response: Response
): AsyncGenerator<any, void, unknown> {
  if (!response.body) {
    throw new ApiError('Response body is not available');
  }

  try {
    // Import the SSE reader utility
    const { readSseStream } = await import('@/shared/utils/sse-reader');

    // Use the universal SSE reader for true streaming
    for await (const dataString of readSseStream(response.body)) {
      try {
        const data = JSON.parse(dataString);
        yield data;
      } catch (error) {
        console.warn('Failed to parse streaming data:', dataString, error);
      }
    }
  } catch (error) {
    throw new ApiError(`Failed to read streaming response: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Create a mobile-compatible streaming response using XMLHttpRequest
 *
 * This function creates a Response object with a ReadableStream that internally
 * uses MobileSSEClient to handle SSE streaming on React Native platforms.
 *
 * @param request - The streaming API request data
 * @param headers - Request headers
 * @param requestId - Unique request identifier for logging
 * @param platform - Platform identifier (ios/android)
 * @returns Promise<Response> - A Response object compatible with readSseStream
 */
const createMobileStreamingResponse = async (
  request: StreamingApiRequest,
  headers: Record<string, string>,
  requestId: string,
  platform: string
): Promise<Response> => {
  console.log(`📱 [API-${platform}] Creating mobile streaming response`, { requestId });

  // Create a ReadableStream that bridges MobileSSEClient to the existing streaming infrastructure
  const stream = new ReadableStream({
    start(controller) {
      console.log(`📱 [API-${platform}] Starting mobile ReadableStream`, { requestId });

      const client = new MobileSSEClient({
        onMessage: (data: string) => {
          console.log(`📱 [API-${platform}] Mobile SSE message received`, {
            requestId,
            dataLength: data.length,
            dataPreview: data.substring(0, 100)
          });

          // Format as SSE data and enqueue to the stream
          const sseData = `data: ${data}\n\n`;
          const encoder = new TextEncoder();
          controller.enqueue(encoder.encode(sseData));
        },

        onError: (error: Error) => {
          console.error(`📱 [API-${platform}] Mobile SSE error`, {
            requestId,
            error: error.message,
            stack: error.stack
          });
          controller.error(error);
        },

        onOpen: () => {
          console.log(`📱 [API-${platform}] Mobile SSE connection opened`, { requestId });
        },

        onClose: () => {
          console.log(`📱 [API-${platform}] Mobile SSE connection closed`, { requestId });
          controller.close();
        },

        timeout: 120000 // 120 second timeout (2 minutes) for AI processing
      });

      // Start the mobile SSE connection
      client.connect(
        `${API_BASE_URL}${API_ENDPOINTS.streaming}`,
        request,
        headers
      ).catch((error) => {
        console.error(`📱 [API-${platform}] Failed to connect mobile SSE client`, {
          requestId,
          error: error.message
        });
        controller.error(error);
      });
    }
  });

  // Create a Response object with the mobile ReadableStream
  // Note: In React Native, we need to manually set the body property
  const response = new Response(null, {
    status: 200,
    statusText: 'OK',
    headers: new Headers({
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive'
    })
  });

  // Manually assign the ReadableStream to the body property
  // This is necessary because React Native's Response constructor doesn't properly handle ReadableStream
  Object.defineProperty(response, 'body', {
    value: stream,
    writable: false,
    enumerable: true,
    configurable: false
  });

  console.log(`📱 [API-${platform}] Mobile streaming response created`, {
    requestId,
    hasBody: !!response.body,
    bodyType: response.body?.constructor.name
  });

  return response;
};
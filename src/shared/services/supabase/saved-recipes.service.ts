/**
 * Protocol-Based Saved Recipes Service
 * 
 * Functional service for protocol-based saving operations. Redesigned to save
 * complete protocols (3 recipes + context) rather than individual recipes.
 * Framework-agnostic and stateless following recipe.service.ts patterns.
 */

import { createAuthenticatedSupabaseClient } from './supabase-client';
import { STORAGE_KEYS, ERROR_MESSAGES, SUCCESS_MESSAGES } from '@/features/create-recipe/constants/recipe.constants';
import type { 
  SaveProtocolPayload, 
  SavedProtocol,
  SavedRecipe,
  QueryOptions,
  ProtocolQueryOptions,
  HydratedSavedProtocol
} from './types/saved-recipe.types';
import type { 
  FinalRecipeProtocol, 
  RecipeTimeSlot,
  EnrichedEssentialOil 
} from '@/features/create-recipe/types';
import AsyncStorage from '@react-native-async-storage/async-storage';

// ============================================================================
// CORE PROTOCOL SAVE FUNCTIONS
// ============================================================================

/**
 * Save a complete protocol (3 recipes + context) to Supabase in atomic transaction
 * Follows functional pattern from recipe.service.ts
 */
export const saveProtocolToDatabase = async (
  payload: SaveProtocolPayload, 
  userId: string,
  clerkToken: string
): Promise<SavedProtocol> => {
  const startTime = performance.now();
  
  try {
    const client = createAuthenticatedSupabaseClient(clerkToken);
    const { 
      protocolName, 
      finalRecipes, 
      healthConcern, 
      demographics, 
      selectedCauses, 
      selectedSymptoms, 
      therapeuticProperties 
    } = payload;
    
    // Validate required fields
    if (!protocolName.trim()) {
      throw new Error('Protocol name is required');
    }
    
    const hasValidRecipes = Object.values(finalRecipes).some(slot => 
      slot.recipe && slot.recipe.selected_oils && slot.recipe.selected_oils.length > 0
    );
    
    if (!hasValidRecipes) {
      throw new Error('Protocol must have at least one recipe with oils');
    }

    // Begin atomic transaction by inserting protocol record
    const { data: protocolRecord, error: protocolError } = await client
      .from('user_saved_protocols')
      .insert({
        user_id: userId,
        protocol_name: protocolName.trim(),
        health_concern_original: healthConcern,
        demographics,
        selected_causes: selectedCauses,
        selected_symptoms: selectedSymptoms,
        therapeutic_properties: therapeuticProperties
      })
      .select()
      .single();
    
    if (protocolError) {
      console.error('Protocol save error:', protocolError);
      throw new Error(ERROR_MESSAGES.API_ERROR);
    }

    // Insert recipes for each time slot
    const recipeInserts = [];
    const timeSlots: RecipeTimeSlot[] = ['morning', 'midDay', 'night'];
    
    for (const timeSlot of timeSlots) {
      const slotData = finalRecipes[timeSlot];
      if (slotData?.recipe && slotData.recipe.selected_oils && slotData.recipe.selected_oils.length > 0) {
        const { selected_oils, ...recipeProtocol } = slotData.recipe;
        
        // Map midDay to mid-day for database constraint
        const dbTimeSlot = timeSlot === 'midDay' ? 'mid-day' : timeSlot;
        
        recipeInserts.push({
          protocol_id: protocolRecord.id,
          time_slot: dbTimeSlot,
          total_drops: slotData.recipe.total_drops,
          total_volume_ml: slotData.recipe.total_volume_ml,
          oil_count: selected_oils.length,
          recipe_protocol: recipeProtocol
        });
      }
    }

    if (recipeInserts.length === 0) {
      // Clean up protocol if no valid recipes
      await client.from('user_saved_protocols').delete().eq('id', protocolRecord.id);
      throw new Error('No valid recipes found in protocol');
    }

    const { data: recipeRecords, error: recipeError } = await client
      .from('user_saved_recipes')
      .insert(recipeInserts)
      .select();
    
    if (recipeError) {
      // Clean up protocol on recipe save failure
      await client.from('user_saved_protocols').delete().eq('id', protocolRecord.id);
      console.error('Recipe save error:', recipeError);
      throw new Error(ERROR_MESSAGES.API_ERROR);
    }

    // Insert oils for each recipe
    const allOilInserts = [];
    
    for (let i = 0; i < timeSlots.length; i++) {
      const timeSlot = timeSlots[i];
      const slotData = finalRecipes[timeSlot];
      // Map midDay to mid-day for database lookup
      const dbTimeSlot = timeSlot === 'midDay' ? 'mid-day' : timeSlot;
      const recipeRecord = recipeRecords?.find(r => r.time_slot === dbTimeSlot);
      
      if (slotData?.recipe && recipeRecord) {
        const { selected_oils } = slotData.recipe;
        
        const oilInserts = selected_oils.map((oil, oilIndex) => ({
          recipe_id: recipeRecord.id,
          oil_id: oil.oil_id,
          drops_count: oil.drops_count,
          oil_order: oilIndex + 1,
          // Preserve API-generated data for oil substitution
          name_localized: oil.name,
          rationale_localized: oil.rationale || '',
          final_relevance_score: oil.final_relevance_score || 0,
          specialization_score: oil.specialization_score || 0
        }));
        
        allOilInserts.push(...oilInserts);
      }
    }

    if (allOilInserts.length > 0) {
      const { error: oilError } = await client
        .from('user_saved_recipe_oils')
        .insert(allOilInserts);
      
      if (oilError) {
        // Clean up protocol and recipes on oil save failure
        await client.from('user_saved_protocols').delete().eq('id', protocolRecord.id);
        console.error('Recipe oils save error:', oilError);
        throw new Error(ERROR_MESSAGES.API_ERROR);
      }
    }

    const result: SavedProtocol = {
      ...protocolRecord,
      recipes: recipeRecords || []
    };

    const duration = performance.now() - startTime;
    if (__DEV__) {
      console.log(`✅ Protocol saved in ${duration.toFixed(2)}ms:`, protocolName);
    }

    return result;
    
  } catch (error) {
    const duration = performance.now() - startTime;
    console.error(`❌ Save protocol failed after ${duration.toFixed(2)}ms:`, error);
    throw error;
  }
};

/**
 * Get user's saved protocols from Supabase
 * Follows patterns from existing API functions
 */
export const getUserSavedProtocols = async (
  userId: string,
  clerkToken: string,
  options: ProtocolQueryOptions = {}
): Promise<SavedProtocol[]> => {
  try {
    const client = createAuthenticatedSupabaseClient(clerkToken);
    const {
      limit = 50,
      offset = 0,
      healthConcern,
      searchTerm,
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = options;
    
    let query = client
      .from('user_saved_protocols')
      .select(`
        *,
        recipes:user_saved_recipes(
          *,
          recipe_oils:user_saved_recipe_oils(*)
        )
      `)
      .eq('user_id', userId)
      .range(offset, offset + limit - 1)
      .order(sortBy, { ascending: sortOrder === 'asc' });
    
    // Apply filters
    if (healthConcern) query = query.ilike('health_concern_original', `%${healthConcern}%`);
    if (searchTerm) query = query.or(`protocol_name.ilike.%${searchTerm}%,health_concern_original.ilike.%${searchTerm}%`);
    
    const { data, error } = await query;
    
    if (error) {
      console.error('Get saved protocols error:', error);
      throw new Error(ERROR_MESSAGES.API_ERROR);
    }
    
    return data || [];
    
  } catch (error) {
    console.error('Get saved protocols failed:', error);
    throw error;
  }
};

/**
 * Get a specific protocol with complete details
 */
export const getProtocolDetails = async (
  protocolId: string,
  userId: string,
  clerkToken: string
): Promise<HydratedSavedProtocol | null> => {
  try {
    const client = createAuthenticatedSupabaseClient(clerkToken);
    
    const { data, error } = await client
      .from('user_saved_protocols')
      .select(`
        *,
        recipes:user_saved_recipes(
          *,
          recipe_oils:user_saved_recipe_oils(
            *,
            oil_details:essential_oils_with_safety_ids(
              id, name_english, name_scientific, name_portuguese,
              general_description, image_url, names_concatenated,
              internal_use, dilution, phototoxicity, 
              pregnancy_nursing_safety, child_safety,
              created_at, updated_at
            )
          )
        )
      `)
      .eq('id', protocolId)
      .eq('user_id', userId)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      console.error('Get protocol details error:', error);
      throw new Error(ERROR_MESSAGES.API_ERROR);
    }
    
    return data;
    
  } catch (error) {
    console.error('Get protocol details failed:', error);
    throw error;
  }
};

/**
 * Delete a saved protocol with CASCADE to recipes and oils
 * Following existing error handling patterns
 */
export const deleteSavedProtocol = async (
  protocolId: string, 
  userId: string,
  clerkToken: string
): Promise<void> => {
  try {
    const client = createAuthenticatedSupabaseClient(clerkToken);
    
    const { error } = await client
      .from('user_saved_protocols')
      .delete()
      .eq('id', protocolId)
      .eq('user_id', userId); // Security: user can only delete their own
    
    if (error) {
      console.error('Delete protocol error:', error);
      throw new Error(ERROR_MESSAGES.API_ERROR);
    }
    
    if (__DEV__) {
      console.log('✅ Protocol deleted:', protocolId);
    }
    
  } catch (error) {
    console.error('Delete protocol failed:', error);
    throw error;
  }
};

/**
 * Substitute oil in saved protocol recipe
 * Updates the oil record with new oil and preserves enrichment scores
 */
export const substituteOilInSavedRecipe = async (
  recipeId: string,
  originalOilId: string,
  newOil: EnrichedEssentialOil,
  userId: string,
  clerkToken: string
): Promise<void> => {
  try {
    const client = createAuthenticatedSupabaseClient(clerkToken);
    
    // Verify user owns this recipe via protocol
    const { data: recipe, error: recipeError } = await client
      .from('user_saved_recipes')
      .select(`
        id, 
        protocol:user_saved_protocols!inner(user_id)
      `)
      .eq('id', recipeId)
      .single();
      
    if (recipeError || !recipe || recipe.protocol.user_id !== userId) {
      throw new Error('Recipe not found or access denied');
    }
    
    // Update the oil record
    const { error: updateError } = await client
      .from('user_saved_recipe_oils')
      .update({
        oil_id: newOil.oil_id,
        drops_count: newOil.drops_count,
        name_localized: newOil.name,
        rationale_localized: newOil.rationale || '',
        final_relevance_score: newOil.final_relevance_score || 0,
        specialization_score: newOil.specialization_score || 0,
        updated_at: new Date().toISOString()
      })
      .eq('recipe_id', recipeId)
      .eq('oil_id', originalOilId);
    
    if (updateError) {
      console.error('Oil substitution error:', updateError);
      throw new Error(ERROR_MESSAGES.API_ERROR);
    }
    
    // Update recipe metrics
    await updateRecipeMetrics(recipeId, clerkToken);
    
    if (__DEV__) {
      console.log(`✅ Oil substituted in recipe ${recipeId}: ${originalOilId} → ${newOil.oil_id}`);
    }
    
  } catch (error) {
    console.error('Oil substitution failed:', error);
    throw error;
  }
};

/**
 * Update recipe metrics after oil changes
 */
const updateRecipeMetrics = async (recipeId: string, clerkToken: string): Promise<void> => {
  const client = createAuthenticatedSupabaseClient(clerkToken);
  
  // Get current oils for this recipe
  const { data: oils, error: oilsError } = await client
    .from('user_saved_recipe_oils')
    .select('drops_count')
    .eq('recipe_id', recipeId);
    
  if (oilsError || !oils) return;
  
  const totalDrops = oils.reduce((sum, oil) => sum + oil.drops_count, 0);
  const oilCount = oils.length;
  const totalVolumeMl = Math.round((totalDrops * 0.05) * 10) / 10; // 0.05ml per drop
  
  await client
    .from('user_saved_recipes')
    .update({
      total_drops: totalDrops,
      oil_count: oilCount,
      total_volume_ml: totalVolumeMl,
      updated_at: new Date().toISOString()
    })
    .eq('id', recipeId);
};

/**
 * Hydrate recipe oils with complete data from essential_oils_with_safety_ids
 * Excludes embedding column for performance (like existing oil enrichment)
 */
export const hydrateRecipeOils = async (
  oilIds: string[], 
  clerkToken: string
): Promise<any[]> => {
  if (oilIds.length === 0) return [];
  
  try {
    const client = createAuthenticatedSupabaseClient(clerkToken);
    
    const { data, error } = await client
      .from('essential_oils_with_safety_ids')
      .select(`
        id, name_english, name_scientific, name_portuguese, general_description,
        image_url, names_concatenated,
        internal_use, dilution, phototoxicity, pregnancy_nursing_safety, child_safety,
        created_at, updated_at
      `) // Excludes embedding column for performance
      .in('id', oilIds);
    
    if (error) {
      console.error('Oil hydration error:', error);
      throw new Error(ERROR_MESSAGES.API_ERROR);
    }
    
    return data || [];
    
  } catch (error) {
    console.error('Oil hydration failed:', error);
    throw error;
  }
};


// ============================================================================
// PROTOCOL RECONSTRUCTION FUNCTIONS
// ============================================================================

/**
 * Convert saved protocol back to finalRecipes format for UI consumption
 * Reconstructs the original finalRecipes structure from database entities
 */
export const reconstructProtocolForUI = (protocol: HydratedSavedProtocol): {
  protocolContext: {
    healthConcern: string;
    demographics: any;
    selectedCauses: string[];
    selectedSymptoms: string[];
    therapeuticProperties: any[];
  };
  finalRecipes: {
    morning: { recipe: FinalRecipeProtocol | null; status: 'success' };
    midDay: { recipe: FinalRecipeProtocol | null; status: 'success' };
    night: { recipe: FinalRecipeProtocol | null; status: 'success' };
  };
} => {
  const finalRecipes = {
    morning: { recipe: null as FinalRecipeProtocol | null, status: 'success' as const },
    midDay: { recipe: null as FinalRecipeProtocol | null, status: 'success' as const },
    night: { recipe: null as FinalRecipeProtocol | null, status: 'success' as const }
  };

  // Reconstruct each recipe
  protocol.recipes.forEach(savedRecipe => {
    const timeSlot = savedRecipe.time_slot as keyof typeof finalRecipes;
    
    // Reconstruct selected_oils array from oil records
    const selected_oils = savedRecipe.recipe_oils
      .sort((a, b) => a.oil_order - b.oil_order)
      .map(oil => ({
        oil_id: oil.oil_id,
        name: oil.name_localized || oil.oil_details?.name_english || 'Unknown Oil',
        rationale: oil.rationale_localized || '',
        drops_count: oil.drops_count,
        final_relevance_score: oil.final_relevance_score || 0,
        specialization_score: oil.specialization_score || 0,
        // Include oil details for complete reconstruction
        name_english: oil.oil_details?.name_english,
        name_scientific: oil.oil_details?.name_scientific,
        name_portuguese: oil.oil_details?.name_portuguese,
        general_description: oil.oil_details?.general_description,
        image_url: oil.oil_details?.image_url,
        safety: {
          internal_use: oil.oil_details?.internal_use,
          dilution: oil.oil_details?.dilution,
          phototoxicity: oil.oil_details?.phototoxicity,
          pregnancy_nursing_safety: oil.oil_details?.pregnancy_nursing_safety || [],
          child_safety: oil.oil_details?.child_safety || []
        }
      }));

    // Reconstruct complete recipe protocol
    const recipe: FinalRecipeProtocol = {
      ...savedRecipe.recipe_protocol,
      selected_oils,
      total_drops: savedRecipe.total_drops,
      total_volume_ml: savedRecipe.total_volume_ml
    };

    finalRecipes[timeSlot] = { recipe, status: 'success' };
  });

  return {
    protocolContext: {
      healthConcern: protocol.health_concern_original,
      demographics: protocol.demographics,
      selectedCauses: protocol.selected_causes,
      selectedSymptoms: protocol.selected_symptoms,
      therapeuticProperties: protocol.therapeutic_properties
    },
    finalRecipes
  };
};

// ============================================================================
// OFFLINE SUPPORT FUNCTIONS
// ============================================================================

/**
 * Cache saved protocols using existing storage patterns
 * Follows STORAGE_KEYS pattern from recipe.constants.ts
 */
export const cacheSavedProtocolsOffline = async (
  protocols: SavedProtocol[], 
  userId: string
): Promise<void> => {
  try {
    const cacheData = {
      protocols,
      userId,
      timestamp: new Date().toISOString(),
      version: '2.0.0' // Protocol version
    };
    
    const cacheKey = `${STORAGE_KEYS.SAVED_PROTOCOLS}_${userId}`;
    await AsyncStorage.setItem(cacheKey, JSON.stringify(cacheData));
    
    if (__DEV__) {
      console.log(`✅ Cached ${protocols.length} protocols offline`);
    }
    
  } catch (error) {
    console.error('Cache protocols failed:', error);
    // Non-critical, don't throw
  }
};

/**
 * Load cached protocols using existing storage patterns
 */
export const loadCachedSavedProtocols = async (userId: string): Promise<SavedProtocol[] | null> => {
  try {
    const cacheKey = `${STORAGE_KEYS.SAVED_PROTOCOLS}_${userId}`;
    const cachedData = await AsyncStorage.getItem(cacheKey);
    
    if (!cachedData) return null;
    
    const parsed = JSON.parse(cachedData);
    
    // Check cache age (1 hour TTL)
    const cacheAge = Date.now() - new Date(parsed.timestamp).getTime();
    if (cacheAge > 3600000) { // 1 hour
      await AsyncStorage.removeItem(cacheKey);
      return null;
    }
    
    return parsed.protocols;
    
  } catch (error) {
    console.error('Load cached protocols failed:', error);
    return null;
  }
};

// ============================================================================
// SERVICE OBJECT EXPORT
// ============================================================================

/**
 * Protocol-Based Saved Recipes Service
 * Groups all protocol operations following established service layer patterns
 */
export const savedRecipesService = {
  // Protocol CRUD operations
  saveProtocol: saveProtocolToDatabase,
  getUserProtocols: getUserSavedProtocols,
  getProtocolDetails: getProtocolDetails,
  deleteProtocol: deleteSavedProtocol,
  
  // Oil substitution in saved protocols
  substituteOil: substituteOilInSavedRecipe,
  
  // UI reconstruction utilities
  reconstructProtocol: reconstructProtocolForUI,
  hydrateOils: hydrateRecipeOils,
  
  // Offline support
  cacheOffline: cacheSavedProtocolsOffline,
  loadCached: loadCachedSavedProtocols,
} as const;

// Export individual functions for direct import if needed
export {
  saveProtocolToDatabase,
  getUserSavedProtocols,
  getProtocolDetails,
  deleteSavedProtocol,
  substituteOilInSavedRecipe,
  reconstructProtocolForUI,
  cacheSavedProtocolsOffline,
  loadCachedSavedProtocols,
};
/**
 * Supabase Client Configuration
 * 
 * Provides authenticated Supabase client integration with Clerk authentication.
 * Handles JWT token management and automatic authentication for all database operations.
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';

// Environment variables validation
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error(
    'Missing Supabase environment variables. Please add EXPO_PUBLIC_SUPABASE_URL and EXPO_PUBLIC_SUPABASE_ANON_KEY to your .env file.'
  );
}

/**
 * Global Supabase client instance
 * 
 * Configured for React Native/Expo with Clerk authentication.
 * Uses anonymous key for initial connection, then authenticates with Clerk JWT tokens.
 */
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    // Disable Supabase auth - we use Clerk
    storage: undefined,
    autoRefreshToken: false,
    persistSession: false,
    detectSessionInUrl: false,
  },
  // React Native optimizations
  realtime: {
    params: {
      eventsPerSecond: 2, // Throttle realtime events for mobile
    },
  },
});

/**
 * Creates an authenticated Supabase client using Clerk JWT token
 * 
 * @param clerkToken - JWT token from Clerk's getToken() method
 * @returns Configured Supabase client with authentication headers
 */
export function createAuthenticatedSupabaseClient(clerkToken: string): SupabaseClient {
  // Create a new client instance with the auth token
  const authenticatedClient = createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      storage: undefined,
      autoRefreshToken: false,
      persistSession: false,
      detectSessionInUrl: false,
    },
    global: {
      headers: {
        Authorization: `Bearer ${clerkToken}`,
      },
    },
    // React Native optimizations with JWT for realtime
    realtime: {
      params: {
        eventsPerSecond: 2,
      },
      headers: {
        Authorization: `Bearer ${clerkToken}`,
      },
    },
  });

  return authenticatedClient;
}

/**
 * Database schema type definitions
 * 
 * These match the tables created in the Supabase database schema.
 */
export interface Database {
  public: {
    Tables: {
      user_saved_recipes: {
        Row: {
          id: string;
          user_id: string;
          recipe_name: string;
          time_slot: 'morning' | 'mid-day' | 'night';
          created_at: string;
          updated_at: string;
          health_concern: string;
          health_concern_original?: string;
          demographics: {
            gender: 'male' | 'female';
            ageCategory: string;
            specificAge: number;
          };
          selected_causes: string[];
          selected_symptoms: string[];
          total_drops: number;
          total_volume_ml: number;
          oil_count: number;
          recipe_protocol: any; // JSONB - complete recipe data except selected_oils
        };
        Insert: {
          id?: string;
          user_id: string;
          recipe_name: string;
          time_slot: 'morning' | 'mid-day' | 'night';
          created_at?: string;
          updated_at?: string;
          health_concern: string;
          health_concern_original?: string;
          demographics: {
            gender: 'male' | 'female';
            ageCategory: string;
            specificAge: number;
          };
          selected_causes: string[];
          selected_symptoms: string[];
          total_drops: number;
          total_volume_ml: number;
          oil_count: number;
          recipe_protocol: any;
        };
        Update: {
          id?: string;
          user_id?: string;
          recipe_name?: string;
          time_slot?: 'morning' | 'mid-day' | 'night';
          created_at?: string;
          updated_at?: string;
          health_concern?: string;
          health_concern_original?: string;
          demographics?: {
            gender: 'male' | 'female';
            ageCategory: string;
            specificAge: number;
          };
          selected_causes?: string[];
          selected_symptoms?: string[];
          total_drops?: number;
          total_volume_ml?: number;
          oil_count?: number;
          recipe_protocol?: any;
        };
      };
      user_saved_recipe_oils: {
        Row: {
          id: string;
          recipe_id: string;
          oil_id: string;
          drops_count: number;
          oil_order: number;
          created_at: string;
        };
        Insert: {
          id?: string;
          recipe_id: string;
          oil_id: string;
          drops_count: number;
          oil_order: number;
          created_at?: string;
        };
        Update: {
          id?: string;
          recipe_id?: string;
          oil_id?: string;
          drops_count?: number;
          oil_order?: number;
          created_at?: string;
        };
      };
      essential_oils: {
        Row: {
          id: string;
          name_english: string;
          name_scientific: string;
          name_portuguese: string;
          general_description?: string;
          image_url?: string;
          names_concatenated?: string;
          created_at: string;
          updated_at: string;
          // Note: embedding column excluded for performance
        };
      };
    };
    Views: {
      essential_oils_with_safety_ids: {
        Row: {
          id: string;
          name_english: string;
          name_scientific: string;
          name_portuguese: string;
          general_description?: string;
          image_url?: string;
          names_concatenated?: string;
          // JSONB safety data
          internal_use: any;
          dilution: any;
          phototoxicity: any;
          pregnancy_nursing_safety: any[];
          child_safety: any[];
          created_at: string;
          updated_at: string;
          // Note: embedding column excluded for performance
        };
      };
    };
  };
}

/**
 * Type-safe Supabase client type
 */
export type TypedSupabaseClient = SupabaseClient<Database>;
/**
 * TypeScript types for the Save Recipe feature
 * 
 * These types define the data structures for saved recipes, integrating with
 * the existing recipe types and Supabase database schema.
 */

import type { 
  FinalRecipeProtocol, 
  RecipeTimeSlot,
  EnrichedEssentialOil,
  DemographicsData,
  TherapeuticProperty,
  PotentialCause,
  PotentialSymptom
} from '@/features/create-recipe/types';

// ============================================================================
// SAVE RECIPE PAYLOAD TYPES
// ============================================================================

/**
 * Payload for saving a recipe
 */
export interface SaveRecipePayload {
  recipeName: string;
  timeSlot: RecipeTimeSlot;
  recipeData: FinalRecipeProtocol;
  healthConcern: string;
  healthConcernOriginal?: string;
  demographics: DemographicsData;
  selectedCauses: string[];
  selectedSymptoms: string[];
}

/**
 * Payload for saving a complete protocol (3 recipes + context)
 */
export interface SaveProtocolPayload {
  protocolName: string;
  finalRecipes: {
    morning: { recipe: FinalRecipeProtocol | null; status: string };
    midDay: { recipe: FinalRecipeProtocol | null; status: string };
    night: { recipe: FinalRecipeProtocol | null; status: string };
  };
  healthConcern: string;
  demographics: DemographicsData;
  selectedCauses: string[];
  selectedSymptoms: string[];
  therapeuticProperties: TherapeuticProperty[];
}

// ============================================================================
// DATABASE RECORD TYPES
// ============================================================================

/**
 * Database record for saved recipe oils (user_saved_recipe_oils table)
 */
export interface SavedRecipeOil {
  id: string;
  recipe_id: string;
  oil_id: string; // UUID reference to essential_oils table
  drops_count: number;
  oil_order: number; // Preserve display order
  created_at: string;
}

/**
 * Database record for saved recipes (user_saved_recipes table)
 */
export interface SavedRecipe {
  id: string;
  user_id: string;
  recipe_name: string;
  time_slot: RecipeTimeSlot;
  created_at: string;
  updated_at: string;
  
  // Original recipe context for search/filtering
  health_concern: string;
  health_concern_original?: string;
  demographics: DemographicsData;
  selected_causes: string[];
  selected_symptoms: string[];
  
  // Computed metadata for performance
  total_drops: number;
  total_volume_ml: number;
  oil_count: number;
  
  // Complete recipe protocol (excluding selected_oils array)
  recipe_protocol: Omit<FinalRecipeProtocol, 'selected_oils'>;
  
  // Associated oil records (populated via join)
  recipe_oils: SavedRecipeOil[];
}

/**
 * Database record for saved recipe oils with enrichment data (user_saved_recipe_oils table)
 * Extended to include API-generated data for oil substitution
 */
export interface SavedProtocolRecipeOil extends SavedRecipeOil {
  // API-generated data preservation
  name_localized: string;
  rationale_localized: string;
  final_relevance_score: number;
  specialization_score: number;
}

/**
 * Database record for saved recipes in protocol context (user_saved_recipes table)
 */
export interface SavedProtocolRecipe {
  id: string;
  protocol_id: string;
  time_slot: RecipeTimeSlot;
  created_at: string;
  updated_at: string;
  
  // Recipe metrics
  total_drops: number;
  total_volume_ml: number;
  oil_count: number;
  
  // Complete recipe protocol (excluding selected_oils array)
  recipe_protocol: Omit<FinalRecipeProtocol, 'selected_oils'>;
  
  // Associated oil records
  recipe_oils: SavedProtocolRecipeOil[];
}

/**
 * Database record for saved protocols (user_saved_protocols table)
 */
export interface SavedProtocol {
  id: string;
  user_id: string;
  protocol_name: string;
  created_at: string;
  updated_at: string;
  
  // Complete workflow context
  health_concern_original: string;
  demographics: DemographicsData;
  selected_causes: string[];
  selected_symptoms: string[];
  therapeutic_properties: TherapeuticProperty[];
  
  // Associated recipe records
  recipes: SavedProtocolRecipe[];
}

// ============================================================================
// HYDRATED/ENRICHED TYPES
// ============================================================================

/**
 * Saved recipe oil with complete oil details from Supabase
 */
export interface HydratedRecipeOil extends SavedRecipeOil {
  oil_details: {
    id: string;
    name_english: string;
    name_scientific: string;
    name_portuguese: string;
    general_description?: string;
    image_url?: string;
    names_concatenated?: string;
    
    // JSONB safety data from essential_oils_with_safety_ids view
    internal_use: any;
    dilution: any;
    phototoxicity: any;
    pregnancy_nursing_safety: any[];
    child_safety: any[];
    
    created_at: string;
    updated_at: string;
  };
}

/**
 * Complete saved recipe with hydrated oil data
 */
export interface HydratedSavedRecipe extends Omit<SavedRecipe, 'recipe_oils'> {
  recipe_oils: HydratedRecipeOil[];
}

/**
 * Saved recipe converted back to FinalRecipeProtocol format for display
 */
export interface ReconstitutedRecipe extends SavedRecipe {
  // Full FinalRecipeProtocol with selected_oils populated from hydrated data
  full_recipe_protocol: FinalRecipeProtocol;
}

/**
 * Saved protocol oil with complete hydrated details
 */
export interface HydratedProtocolRecipeOil extends SavedProtocolRecipeOil {
  oil_details: {
    id: string;
    name_english: string;
    name_scientific: string;
    name_portuguese: string;
    general_description?: string;
    image_url?: string;
    names_concatenated?: string;
    
    // JSONB safety data
    internal_use: any;
    dilution: any;
    phototoxicity: any;
    pregnancy_nursing_safety: any[];
    child_safety: any[];
    
    created_at: string;
    updated_at: string;
  };
}

/**
 * Saved protocol recipe with hydrated oil details
 */
export interface HydratedSavedProtocolRecipe extends Omit<SavedProtocolRecipe, 'recipe_oils'> {
  recipe_oils: HydratedProtocolRecipeOil[];
}

/**
 * Complete saved protocol with hydrated oil data
 */
export interface HydratedSavedProtocol extends Omit<SavedProtocol, 'recipes'> {
  recipes: HydratedSavedProtocolRecipe[];
}

// ============================================================================
// QUERY OPTIONS TYPES
// ============================================================================

/**
 * Options for querying saved recipes
 */
export interface QueryOptions {
  limit?: number;
  offset?: number;
  timeSlot?: RecipeTimeSlot;
  healthConcern?: string;
  searchTerm?: string;
  sortBy?: 'created_at' | 'updated_at' | 'recipe_name' | 'total_drops' | 'oil_count';
  sortOrder?: 'asc' | 'desc';
}

/**
 * Options for querying saved protocols
 */
export interface ProtocolQueryOptions {
  limit?: number;
  offset?: number;
  healthConcern?: string;
  searchTerm?: string;
  sortBy?: 'created_at' | 'updated_at' | 'protocol_name';
  sortOrder?: 'asc' | 'desc';
}

/**
 * Pagination metadata for query results
 */
export interface PaginationMeta {
  total: number;
  page: number;
  limit: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

/**
 * Paginated query result
 */
export interface PaginatedResult<T> {
  data: T[];
  meta: PaginationMeta;
}

// ============================================================================
// SERVICE RESPONSE TYPES
// ============================================================================

/**
 * Result of oil validation operation
 */
export interface OilValidationResult {
  valid: string[];
  invalid: string[];
}

/**
 * Cache metadata for offline support
 */
export interface CacheMetadata {
  lastSync: Date | null;
  version: string;
  totalRecipes: number;
}

/**
 * Cached saved recipes data
 */
export interface CachedRecipesData {
  recipes: SavedRecipe[];
  metadata: CacheMetadata;
  timestamp: string;
}

// ============================================================================
// ERROR TYPES
// ============================================================================

/**
 * Specific error types for saved recipe operations
 */
export type SavedRecipeErrorType = 
  | 'authentication_required'
  | 'recipe_not_found'
  | 'oil_not_found'
  | 'database_error'
  | 'validation_error'
  | 'network_error'
  | 'cache_error';

/**
 * Structured error for saved recipe operations
 */
export interface SavedRecipeError extends Error {
  type: SavedRecipeErrorType;
  details?: Record<string, any>;
  retryable?: boolean;
}
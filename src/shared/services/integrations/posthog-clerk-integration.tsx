import { useEffect, useRef } from 'react';
import { Platform, AppState } from 'react-native';
import { useAuth, useUser } from '@clerk/clerk-expo';

// Safe PostHog hook that handles conditional loading
let posthogModule: any = null;

try {
  // Dynamic import to avoid require() style import
  if (Platform.OS !== 'web' || typeof window !== 'undefined') {
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    posthogModule = require('posthog-react-native');
  }
} catch {
  posthogModule = null;
}

/**
 * PostHog-Clerk Integration Component
 * 
 * Handles automatic user identification, session management, and authentication
 * event tracking between PostHog and Clerk.
 * 
 * This component renders nothing but manages side effects for analytics.
 */
export const PostHogClerkIntegration = () => {
  // Always call the hook - it's either the actual hook or null
  const posthog = posthogModule?.usePostHog?.() || null;
  const { isSignedIn, userId, isLoaded } = useAuth();
  const { user } = useUser();
  
  // Track if we've already identified this user to prevent duplicates
  const hasIdentifiedUser = useRef<string | null>(null);
  const hasTrackedAppLaunch = useRef(false);

  // Track app launch event once when component mounts
  useEffect(() => {
    if (!hasTrackedAppLaunch.current && posthog) {
      posthog.capture('app_launched', {
        isAuthenticated: isSignedIn,
        timestamp: new Date().toISOString(),
        platform: 'mobile',
      });
      hasTrackedAppLaunch.current = true;
      
      if (__DEV__) {
        console.log('📊 PostHog: App launched event tracked');
      }
    }
  }, [posthog, isSignedIn]);

  // Handle user identification and sign-in events
  useEffect(() => {
    if (!isLoaded || !posthog) return;

    if (isSignedIn && userId && user) {
      // Check if we've already identified this user
      if (hasIdentifiedUser.current === userId) {
        return;
      }

      // Identify user with PostHog
      posthog.identify(userId, {
        email: user.emailAddresses[0]?.emailAddress,
        firstName: user.firstName,
        lastName: user.lastName,
        fullName: user.fullName,
        createdAt: user.createdAt?.toISOString(),
        lastSignInAt: user.lastSignInAt?.toISOString(),
        userType: 'authenticated',
        platform: 'mobile',
      });

      // Track sign-in event
      posthog.capture('user_signed_in', {
        userId,
        email: user.emailAddresses[0]?.emailAddress,
        method: 'email',
        timestamp: new Date().toISOString(),
        isFirstTime: hasIdentifiedUser.current === null,
      });

      hasIdentifiedUser.current = userId;

      if (__DEV__) {
        console.log('📊 PostHog: User identified and sign-in tracked', {
          userId,
          email: user.emailAddresses[0]?.emailAddress,
        });
      }
    } else if (!isSignedIn && hasIdentifiedUser.current) {
      // User signed out - reset session
      posthog.capture('user_signed_out', {
        userId: hasIdentifiedUser.current,
        timestamp: new Date().toISOString(),
      });

      posthog.reset();
      hasIdentifiedUser.current = null;

      if (__DEV__) {
        console.log('📊 PostHog: User signed out and session reset');
      }
    }
  }, [isLoaded, isSignedIn, userId, user, posthog]);

  // Track app state changes for accurate DAU/MAU tracking
  useEffect(() => {
    if (!posthog) return;

    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'active') {
        posthog.capture('app_opened', {
          timestamp: new Date().toISOString(),
          source: 'foreground',
          isAuthenticated: isSignedIn,
          platform: 'mobile',
        });

        if (__DEV__) {
          console.log('📊 PostHog: App foregrounded event tracked');
        }
      } else if (nextAppState === 'background') {
        posthog.capture('app_backgrounded', {
          timestamp: new Date().toISOString(),
          isAuthenticated: isSignedIn,
          platform: 'mobile',
        });

        if (__DEV__) {
          console.log('📊 PostHog: App backgrounded event tracked');
        }
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [posthog, isSignedIn]);

  // This component renders nothing - it's purely for side effects
  return null;
};
import { MD3LightTheme, MD3DarkTheme } from 'react-native-paper';
import materialColors from '@/shared/styles/material-theme.json';
import { designTokens } from '@/shared/styles/design-tokens';

/**
 * Creates a comprehensive theme object that includes colors, typography, spacing, and all design tokens.
 * This is the SINGLE SOURCE OF TRUTH for theming in the app.
 * Uses the material-theme.json as the color palette source and design-tokens.ts for all other styling.
 */
export const createTheme = (isDark: boolean) => {
  const baseTheme = isDark ? MD3DarkTheme : MD3LightTheme;
  const colorScheme = isDark ? materialColors.schemes.dark : materialColors.schemes.light;

  return {
    ...baseTheme,
    colors: {
      ...baseTheme.colors,
      ...colorScheme,
    },
    // Add all design tokens to theme
    spacing: designTokens.spacing,
    typography: designTokens.typography,
    borderRadius: designTokens.borderRadius,
    elevation: designTokens.elevation,
    fontWeight: designTokens.fontWeight,
    duration: designTokens.duration,
    easing: designTokens.easing,
    iconSize: designTokens.iconSize,
    zIndex: designTokens.zIndex,
    breakpoints: designTokens.breakpoints,
    components: designTokens.components,
  };
};

/**
 * Type definition for the app theme
 */
export type AppTheme = ReturnType<typeof createTheme>;
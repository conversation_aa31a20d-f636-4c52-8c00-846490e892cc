/**
 * @fileoverview Reusable haptics utility following DRY, KISS, YAGNI principles
 * Provides common haptic feedback patterns used in popular apps
 * Now uses singleton pattern with Clerk-based preferences
 */

import * as Haptics from 'expo-haptics';
import { Platform } from 'react-native';

/**
 * Common haptic feedback patterns used in popular apps
 */
export const HapticPatterns = {
  // Button interactions (Instagram, Twitter, TikTok)
  buttonPress: () => Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light),

  // Tab switching (Instagram, Spotify, YouTube)
  tabSwitch: () => Haptics.selectionAsync(),

  // Success actions (WhatsApp message sent, Instagram like)
  success: () => Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success),

  // Error feedback (Failed login, network error)
  error: () => Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error),

  // Warning/caution (Delete confirmation, unsaved changes)
  warning: () => Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning),

  // Pull to refresh (Twitter, Instagram, Gmail)
  pullRefresh: () => Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium),

  // Long press actions (iOS context menu, Android long press)
  longPress: () => Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy),

  // Swipe actions (Tinder swipe, iOS mail swipe)
  swipeAction: () => Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium),

  // Toggle switches (Settings toggles, dark mode)
  toggle: () => Haptics.selectionAsync(),

  // Picker/scroll selection (iOS date picker, Spotify volume)
  selection: () => Haptics.selectionAsync(),

  // Achievement/milestone (Level up, streak milestone)
  achievement: () => Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success),

  // Destructive action (Delete, logout)
  destructive: () => Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy),
} as const;

/**
 * Haptics Manager - Singleton pattern for managing haptic feedback
 * Respects user preferences and platform capabilities
 */
class HapticsManager {
  private static instance: HapticsManager;
  private enabled: boolean = true;

  private constructor() { }

  static getInstance(): HapticsManager {
    if (!HapticsManager.instance) {
      HapticsManager.instance = new HapticsManager();
    }
    return HapticsManager.instance;
  }

  setEnabled(enabled: boolean) {
    this.enabled = enabled;
  }

  isEnabled(): boolean {
    return this.enabled;
  }

  private async executeHaptic(hapticFn: () => Promise<void>) {
    if (!this.enabled) return;

    try {
      // Only run haptics on iOS and Android
      if (Platform.OS === 'ios' || Platform.OS === 'android') {
        await hapticFn();
      }
    } catch (error) {
      // Silently fail if haptics not supported
      console.warn('Haptic feedback not available:', error);
    }
  }

  // All existing haptic methods - now respect the enabled setting
  light = () => this.executeHaptic(() => Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light));
  medium = () => this.executeHaptic(() => Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium));
  heavy = () => this.executeHaptic(() => Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy));

  success = () => this.executeHaptic(() => Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success));
  error = () => this.executeHaptic(() => Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error));
  warning = () => this.executeHaptic(() => Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning));

  select = () => this.executeHaptic(() => Haptics.selectionAsync());
  tab = () => this.light();
  toggle = () => this.select();
  swipe = () => this.light();

  achievement = () => this.success();
  destructive = () => this.warning();
}

// Export singleton instance
export const haptics = HapticsManager.getInstance();

export default haptics;
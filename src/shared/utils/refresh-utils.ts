/**
 * Refresh Utilities
 * 
 * Common utility functions for pull-to-refresh operations
 * across the AromaChat app.
 */

/**
 * Formats a timestamp for display in refresh-related UI
 * 
 * @param date - Date to format (defaults to current date)
 * @param options - Intl.DateTimeFormatOptions for customization
 * @returns Formatted timestamp string
 * 
 * @example
 * ```typescript
 * const timestamp = formatRefreshTimestamp();
 * // Returns: "2:30:45 PM" or similar
 * 
 * const fullTimestamp = formatRefreshTimestamp(new Date(), {
 *   year: 'numeric',
 *   month: 'short',
 *   day: 'numeric',
 *   hour: '2-digit',
 *   minute: '2-digit'
 * });
 * // Returns: "Jan 15, 2024, 2:30 PM"
 * ```
 */
export const formatRefreshTimestamp = (
  date: Date = new Date(),
  options: Intl.DateTimeFormatOptions = {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: true,
  }
): string => {
  return date.toLocaleString(undefined, options);
};

/**
 * Gets full timestamp with date for last refresh display
 * 
 * @param date - Date to format (defaults to current date)
 * @returns Full timestamp string with date and time
 * 
 * @example
 * ```typescript
 * const lastRefresh = getLastRefreshTimestamp();
 * // Returns: "Last updated: Jan 15, 2024 at 2:30:45 PM"
 * ```
 */
export const getLastRefreshTimestamp = (date: Date = new Date()): string => {
  const formattedDate = date.toLocaleDateString(undefined, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
  
  const formattedTime = date.toLocaleTimeString(undefined, {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: true,
  });

  return `Last updated: ${formattedDate} at ${formattedTime}`;
};

/**
 * Debounce utility for preventing rapid refresh triggers
 * 
 * @param func - Function to debounce
 * @param wait - Wait time in milliseconds
 * @returns Debounced function
 * 
 * @example
 * ```typescript
 * const debouncedRefresh = debounce(refreshFunction, 1000);
 * ```
 */
export const debounce = <T extends (...args: any[]) => void>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(null, args), wait);
  };
};

/**
 * Creates a simulated delay for better UX when refresh is too fast
 * 
 * @param minTime - Minimum time in milliseconds
 * @param actualTime - Time already elapsed
 * @returns Promise that resolves after remaining time
 * 
 * @example
 * ```typescript
 * const startTime = Date.now();
 * await performActualRefresh();
 * await simulateMinimumRefreshTime(1000, Date.now() - startTime);
 * ```
 */
export const simulateMinimumRefreshTime = async (
  minTime: number,
  actualTime: number = 0
): Promise<void> => {
  const remainingTime = minTime - actualTime;
  
  if (remainingTime > 0) {
    return new Promise(resolve => setTimeout(resolve, remainingTime));
  }
  
  return Promise.resolve();
};

/**
 * Common refresh actions for different screen types
 */
export const refreshActions = {
  /**
   * Updates timestamp for refresh operations
   */
  updateRefreshData: () => ({
    timestamp: new Date(),
    lastRefresh: getLastRefreshTimestamp(),
  }),

  /**
   * Logs refresh activity for debugging
   */
  logRefreshActivity: (screenName: string, action: string) => {
    console.log(`🔄 ${screenName}: ${action} at ${formatRefreshTimestamp()}`);
  },

  /**
   * Common error handler for refresh operations
   */
  handleRefreshError: (error: unknown, screenName: string) => {
    console.error(`❌ ${screenName} refresh failed:`, error);
    // Could integrate with error reporting service here
  },
} as const;

/**
 * Type definitions for refresh utilities
 */
export interface RefreshData {
  timestamp: Date;
  lastRefresh: string;
}

export interface RefreshOptions {
  enableLogging?: boolean;
  minRefreshTime?: number;
  screenName?: string;
}
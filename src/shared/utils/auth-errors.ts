/**
 * Centralized Clerk authentication error handling utility
 * Provides consistent error messages across all authentication flows
 * Following DRY principle - single source of truth for error handling
 */

export interface AuthError {
  message: string;
  shouldRetry: boolean;
  shouldResend?: boolean;
}

/**
 * Handles Clerk authentication errors and returns user-friendly messages
 * @param err - Clerk error object
 * @returns AuthError with user-friendly message and action hints
 */
export function handleAuthError(err: any): AuthError {
  const error = err?.errors?.[0];
  const code = error?.code;
  const message = error?.longMessage || error?.message;

  switch (code) {
    case 'form_code_incorrect':
      return {
        message: 'Invalid verification code. Please try again.',
        shouldRetry: true,
      };

    case 'form_code_expired':
      return {
        message: 'Code expired. A new code has been sent.',
        shouldRetry: true,
        shouldResend: true,
      };

    case 'verification_already_verified':
      return {
        message: 'This email has already been verified. Please try signing in.',
        shouldRetry: false,
      };

    case 'form_identifier_not_verified':
    case 'session_verification_needed':
      return {
        message: 'Account not verified. Please check your email.',
        shouldRetry: false,
      };

    case 'form_password_incorrect':
      return {
        message: 'Invalid credentials. Please check your email and password.',
        shouldRetry: true,
      };

    case 'rate_limit_exceeded':
      return {
        message: 'Too many attempts. Please wait before trying again.',
        shouldRetry: false,
      };

    case 'network_error':
    case 'oauth_network_error':
      return {
        message: 'Connection error. Please check your network.',
        shouldRetry: true,
      };

    case 'oauth_cancelled':
    case 'user_cancelled':
    case 'oauth_access_denied':
      return {
        message: 'Sign-in was cancelled.',
        shouldRetry: false,
      };

    default:
      // Generic fallback for unknown errors
      return {
        message: message || 'Something went wrong. Please try again.',
        shouldRetry: true,
      };
  }
}

/**
 * Logs authentication errors for debugging (only in development)
 * @param err - Error object to log
 * @param context - Additional context for the error
 */
export function logAuthError(err: any, context?: string) {
  // Force logging in development - check multiple dev indicators
  const isDev = __DEV__ || process.env.NODE_ENV === 'development' || process.env.EXPO_PUBLIC_ENVIRONMENT === 'development';
  
  if (isDev) {
    const prefix = `🚨 [Auth Error${context ? ` - ${context}` : ''}]`;
    console.error(`${prefix}:`, JSON.stringify(err, null, 2));
    console.error(`${prefix} - Raw error:`, err);
    console.error(`${prefix} - Error message:`, err?.message);
    console.error(`${prefix} - Clerk errors:`, err?.errors);
    console.error(`${prefix} - Error code:`, err?.code);
    console.error(`${prefix} - Error status:`, err?.status);
  }
}
/**
 * Mobile-specific SSE client for React Native
 * 
 * Uses XMLHttpRequest instead of fetch() because React Native's fetch()
 * doesn't properly expose ReadableStream body for SSE responses.
 */

export interface MobileSSEOptions {
  onMessage: (data: string) => void;
  onError: (error: Error) => void;
  onOpen?: () => void;
  onClose?: () => void;
  timeout?: number;
}

export class MobileSSEClient {
  private xhr: XMLHttpRequest | null = null;
  private options: MobileSSEOptions;
  private buffer = '';
  private isConnected = false;

  constructor(options: MobileSSEOptions) {
    this.options = options;
  }

  connect(url: string, requestData: any, headers: Record<string, string> = {}) {
    return new Promise<void>((resolve, reject) => {
      try {
        this.xhr = new XMLHttpRequest();
        
        // Configure for streaming
        this.xhr.open('POST', url, true);
        
        // Set headers
        Object.entries(headers).forEach(([key, value]) => {
          this.xhr!.setRequestHeader(key, value);
        });
        
        // Set response type for streaming
        this.xhr.responseType = 'text';
        
        let lastResponseLength = 0;
        
        this.xhr.onreadystatechange = () => {
          if (!this.xhr) return;
          
          if (this.xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
            console.log('🔍 [MobileSSE] Headers received:', {
              status: this.xhr.status,
              contentType: this.xhr.getResponseHeader('content-type'),
              headers: this.getAllResponseHeaders()
            });
            
            if (this.xhr.status === 200) {
              this.isConnected = true;
              this.options.onOpen?.();
              resolve();
            } else {
              reject(new Error(`HTTP ${this.xhr.status}: ${this.xhr.statusText}`));
            }
          }
          
          if (this.xhr.readyState === XMLHttpRequest.LOADING || this.xhr.readyState === XMLHttpRequest.DONE) {
            // Process new data
            const responseText = this.xhr.responseText || '';
            const newData = responseText.substring(lastResponseLength);
            lastResponseLength = responseText.length;
            
            if (newData) {
              console.log('🔍 [MobileSSE] New data chunk:', {
                chunkLength: newData.length,
                chunkPreview: newData.substring(0, 100)
              });
              this.processChunk(newData);
            }
          }
          
          if (this.xhr.readyState === XMLHttpRequest.DONE) {
            console.log('🔍 [MobileSSE] Connection completed');
            this.isConnected = false;
            this.options.onClose?.();
          }
        };
        
        this.xhr.onerror = () => {
          console.error('🔍 [MobileSSE] XHR error');
          this.isConnected = false;
          const error = new Error('XMLHttpRequest failed');
          this.options.onError(error);
          reject(error);
        };
        
        this.xhr.ontimeout = () => {
          console.error('🔍 [MobileSSE] XHR timeout');
          this.isConnected = false;
          const error = new Error('XMLHttpRequest timeout');
          this.options.onError(error);
          reject(error);
        };
        
        // Set timeout if specified
        if (this.options.timeout) {
          this.xhr.timeout = this.options.timeout;
        }
        
        // Send the request
        const requestBody = JSON.stringify(requestData);
        console.log('� [MobiloeSSE] Sending request:', {
          url,
          bodyLength: requestBody.length,
          headers
        });
        
        this.xhr.send(requestBody);
        
      } catch (error) {
        console.error('🔍 [MobileSSE] Connection error:', error);
        reject(error);
      }
    });
  }
  
  private getAllResponseHeaders(): Record<string, string> {
    if (!this.xhr) return {};
    
    const headers: Record<string, string> = {};
    const headerString = this.xhr.getAllResponseHeaders();
    
    if (headerString) {
      headerString.split('\r\n').forEach(line => {
        const parts = line.split(': ');
        if (parts.length === 2) {
          headers[parts[0].toLowerCase()] = parts[1];
        }
      });
    }
    
    return headers;
  }
  
  private processChunk(chunk: string) {
    this.buffer += chunk;
    
    // Process complete SSE messages (separated by \n\n)
    let boundaryIndex;
    while ((boundaryIndex = this.buffer.indexOf('\n\n')) >= 0) {
      const messageBlock = this.buffer.slice(0, boundaryIndex);
      this.buffer = this.buffer.slice(boundaryIndex + 2);
      
      console.log('🔍 [MobileSSE] Processing message block:', {
        messageLength: messageBlock.length,
        messagePreview: messageBlock.substring(0, 150)
      });
      
      const lines = messageBlock.split('\n');
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6).trim();
          console.log('🔍 [MobileSSE] Yielding data:', {
            dataLength: data.length,
            dataPreview: data.substring(0, 100)
          });
          this.options.onMessage(data);
        }
      }
    }
  }
  
  disconnect() {
    if (this.xhr) {
      console.log('🔍 [MobileSSE] Disconnecting');
      this.xhr.abort();
      this.xhr = null;
      this.isConnected = false;
      this.buffer = '';
    }
  }
  
  isActive(): boolean {
    return this.isConnected;
  }
}

/**
 * Helper function to create and use mobile SSE client
 */
export async function streamWithMobileSSE(
  url: string,
  requestData: any,
  headers: Record<string, string>,
  onMessage: (data: string) => void,
  timeout: number = 120000
): Promise<void> {
  return new Promise((resolve, reject) => {
    const client = new MobileSSEClient({
      onMessage,
      onError: (error) => {
        client.disconnect();
        reject(error);
      },
      onOpen: () => {
        console.log('🔍 [MobileSSE] Connection opened successfully');
      },
      onClose: () => {
        console.log('🔍 [MobileSSE] Connection closed');
        resolve();
      },
      timeout
    });
    
    client.connect(url, requestData, headers).catch(reject);
    
    // Auto-disconnect after timeout
    setTimeout(() => {
      if (client.isActive()) {
        console.log('🔍 [MobileSSE] Auto-disconnecting after timeout');
        client.disconnect();
        resolve();
      }
    }, timeout);
  });
}
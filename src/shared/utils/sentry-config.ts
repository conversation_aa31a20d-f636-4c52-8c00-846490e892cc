import * as Sentry from '@sentry/react-native';
import { isRunningInExpoGo } from 'expo';
import { Platform } from 'react-native';

// Create the navigation integration
export const navigationIntegration = Sentry.reactNavigationIntegration({
  enableTimeToInitialDisplay: !isRunningInExpoGo(),
  // Set timeout for route mounting (default 1000ms)
  routeChangeTimeoutMs: 1000,
  // Reduce clutter from back navigation (default true)
  ignoreEmptyBackNavigationTransactions: true,
});

// Initialize Sentry
export const initializeSentry = () => {
  Sentry.init({
    dsn: process.env.EXPO_PUBLIC_SENTRY_DSN,
    tracesSampleRate: __DEV__ ? 0.1 : 0.01, // Sample less in production
    integrations: [navigationIntegration],
    enableNativeFramesTracking: Platform.OS !== 'web' && !isRunningInExpoGo(),
    // Enable additional debugging in development
    debug: false,
    // Enable automatic error capturing
    attachStacktrace: true,
    // Performance monitoring
    enableAutoSessionTracking: Platform.OS !== 'web',
    // Release tracking
    release: process.env.EXPO_PUBLIC_APP_VERSION || '1.0.0',
    // Environment
    environment: __DEV__ ? 'development' : 'production',
  });

  if (__DEV__) {
    console.log('🔧 Sentry initialized with navigation integration');
  }
};
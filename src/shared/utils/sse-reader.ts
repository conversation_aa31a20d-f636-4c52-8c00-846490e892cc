/**
 * Universal Server-Sent Events (SSE) stream reader
 * 
 * Works with fetch's ReadableStream on Web, iOS, and Android.
 * Provides true real-time streaming by processing data as it arrives.
 */

// Platform detection for logging
const getPlatform = (): string => {
  try {
    // Try React Native first
    const Platform = require('react-native').Platform;
    return Platform.OS || 'unknown';
  } catch {
    // Fallback to web detection
    if (typeof window !== 'undefined') {
      return 'web';
    }
    return 'unknown';
  }
};

/**
 * A universal, spec-compliant Server-Sent Events (SSE) stream reader.
 * Works with `fetch`'s `ReadableStream` on Web, iOS, and Android.
 *
 * @param stream The ReadableStream from a fetch response body.
 * @returns An async generator that yields the `data` field from each SSE message as a string.
 */
export async function* readSseStream(
  stream: ReadableStream<Uint8Array>
): AsyncGenerator<string> {
  const platform = getPlatform();
  const sessionId = `sse-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  console.log(`🔍 [SSE-${platform}] Starting SSE stream reader`, {
    sessionId,
    platform,
    streamType: stream.constructor.name,
    timestamp: new Date().toISOString()
  });

  const reader = stream.getReader();
  const decoder = new TextDecoder();
  let buffer = '';
  let chunkCount = 0;
  let totalBytes = 0;
  let messageCount = 0;

  try {
    while (true) {
      console.log(`📥 [SSE-${platform}] Reading chunk ${chunkCount + 1}`, { sessionId });
      
      const { done, value } = await reader.read();
      
      if (done) {
        console.log(`✅ [SSE-${platform}] Stream completed`, {
          sessionId,
          totalChunks: chunkCount,
          totalBytes,
          totalMessages: messageCount,
          finalBufferLength: buffer.length,
          finalBuffer: buffer.length > 0 ? buffer.substring(0, 200) + '...' : 'empty'
        });
        
        // Process any remaining data in buffer
        if (buffer.length > 0 && buffer.startsWith('data: ')) {
          const finalData = buffer.slice(6).trim();
          console.log(`📤 [SSE-${platform}] Final buffer yield`, {
            sessionId,
            dataLength: finalData.length,
            data: finalData.substring(0, 100) + '...'
          });
          messageCount++;
          yield finalData;
        }
        break;
      }

      chunkCount++;
      const chunkSize = value?.length || 0;
      totalBytes += chunkSize;
      
      const chunk = decoder.decode(value, { stream: true });
      
      console.log(`📦 [SSE-${platform}] Chunk ${chunkCount} received`, {
        sessionId,
        chunkSize,
        totalBytes,
        chunkPreview: chunk.substring(0, 100) + (chunk.length > 100 ? '...' : ''),
        bufferLengthBefore: buffer.length
      });

      buffer += chunk;
      
      console.log(`🔄 [SSE-${platform}] Buffer updated`, {
        sessionId,
        bufferLength: buffer.length,
        bufferPreview: buffer.substring(0, 200) + (buffer.length > 200 ? '...' : '')
      });

      // Process complete SSE messages (separated by \n\n)
      let boundaryIndex;
      let processedMessages = 0;
      
      while ((boundaryIndex = buffer.indexOf('\n\n')) >= 0) {
        const messageBlock = buffer.slice(0, boundaryIndex);
        buffer = buffer.slice(boundaryIndex + 2);
        processedMessages++;

        console.log(`🔍 [SSE-${platform}] Processing message block ${processedMessages}`, {
          sessionId,
          messageBlock: messageBlock.substring(0, 150) + (messageBlock.length > 150 ? '...' : ''),
          remainingBufferLength: buffer.length
        });

        const lines = messageBlock.split('\n');
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6).trim();
            messageCount++;
            
            console.log(`📤 [SSE-${platform}] Yielding data message ${messageCount}`, {
              sessionId,
              dataLength: data.length,
              data: data.substring(0, 100) + (data.length > 100 ? '...' : ''),
              fullLine: line.substring(0, 150) + (line.length > 150 ? '...' : '')
            });
            
            yield data;
          } else if (line.trim()) {
            console.log(`ℹ️ [SSE-${platform}] Non-data line`, {
              sessionId,
              line: line.substring(0, 100) + (line.length > 100 ? '...' : '')
            });
          }
        }
      }
      
      if (processedMessages > 0) {
        console.log(`✨ [SSE-${platform}] Processed ${processedMessages} message blocks in chunk ${chunkCount}`, {
          sessionId,
          totalMessagesYielded: messageCount
        });
      }
    }
  } catch (error) {
    console.error(`❌ [SSE-${platform}] Stream error`, {
      sessionId,
      error: error instanceof Error ? error.message : String(error),
      errorStack: error instanceof Error ? error.stack : undefined,
      chunkCount,
      totalBytes,
      messageCount,
      bufferLength: buffer.length,
      bufferContent: buffer.substring(0, 200) + '...'
    });
    throw error;
  } finally {
    console.log(`🔒 [SSE-${platform}] Releasing reader lock`, { sessionId });
    reader.releaseLock();
  }
}

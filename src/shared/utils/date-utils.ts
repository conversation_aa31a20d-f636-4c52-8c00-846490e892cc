/**
 * Date and Time Utilities
 * 
 * Common utility functions for date/time operations and time-based content
 * across the AromaChat app.
 */

/**
 * Gets a time-based greeting message with proper time divisions
 * 
 * @param t - Translation function from useTranslation hook
 * @returns Greeting based on current time of day
 * 
 * Time divisions:
 * - Late Night: 00:00 – 03:59 → "Good Night" (or neutral greeting)
 * - Morning: 04:00 – 11:59 → "Good Morning"
 * - Afternoon: 12:00 – 17:59 → "Good Afternoon"
 * - Evening: 18:00 – 20:59 → "Good Evening"
 * - Night: 21:00 – 23:59 → "Good Night"
 * 
 * @example
 * ```typescript
 * const { t } = useTranslation(['homescreen']);
 * const greeting = getTimeBasedGreeting(t);
 * // Returns: "Good Morning", "Good Afternoon", etc. (localized)
 * ```
 */
export const getTimeBasedGreeting = (t: (key: string) => string): string => {
  const hour = new Date().getHours();
  
  if (hour >= 0 && hour < 4) {
    // Late Night: 00:00 – 03:59
    return t('homescreen:greetings.lateNight');
  } else if (hour >= 4 && hour < 12) {
    // Morning: 04:00 – 11:59
    return t('homescreen:greetings.morning');
  } else if (hour >= 12 && hour < 18) {
    // Afternoon: 12:00 – 17:59
    return t('homescreen:greetings.afternoon');
  } else if (hour >= 18 && hour < 21) {
    // Evening: 18:00 – 20:59
    return t('homescreen:greetings.evening');
  } else {
    // Night: 21:00 – 23:59
    return t('homescreen:greetings.night');
  }
};

/**
 * Formats a timestamp for display in UI components
 * 
 * @param date - Date to format (defaults to current date)
 * @param options - Intl.DateTimeFormatOptions for customization
 * @returns Formatted timestamp string
 * 
 * @example
 * ```typescript
 * const timestamp = formatTimestamp();
 * // Returns: "2:30:45 PM" or similar
 * 
 * const fullTimestamp = formatTimestamp(new Date(), {
 *   year: 'numeric',
 *   month: 'short',
 *   day: 'numeric',
 *   hour: '2-digit',
 *   minute: '2-digit'
 * });
 * // Returns: "Jan 15, 2024, 2:30 PM"
 * ```
 */
export const formatTimestamp = (
  date: Date = new Date(),
  options: Intl.DateTimeFormatOptions = {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: true,
  }
): string => {
  return date.toLocaleString(undefined, options);
};

/**
 * Gets full timestamp with date for display
 * 
 * @param date - Date to format (defaults to current date)
 * @returns Full timestamp string with date and time
 * 
 * @example
 * ```typescript
 * const lastUpdate = getFullTimestamp();
 * // Returns: "Last updated: Jan 15, 2024 at 2:30:45 PM"
 * ```
 */
export const getFullTimestamp = (date: Date = new Date()): string => {
  const formattedDate = date.toLocaleDateString(undefined, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
  
  const formattedTime = date.toLocaleTimeString(undefined, {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: true,
  });

  return `Last updated: ${formattedDate} at ${formattedTime}`;
};

/**
 * Gets the current time period as a string
 * 
 * @returns Current time period name
 * 
 * @example
 * ```typescript
 * const period = getCurrentTimePeriod();
 * // Returns: "morning", "afternoon", "evening", "night", or "lateNight"
 * ```
 */
export const getCurrentTimePeriod = (): 'lateNight' | 'morning' | 'afternoon' | 'evening' | 'night' => {
  const hour = new Date().getHours();
  
  if (hour >= 0 && hour < 4) {
    return 'lateNight';
  } else if (hour >= 4 && hour < 12) {
    return 'morning';
  } else if (hour >= 12 && hour < 18) {
    return 'afternoon';
  } else if (hour >= 18 && hour < 21) {
    return 'evening';
  } else {
    return 'night';
  }
};
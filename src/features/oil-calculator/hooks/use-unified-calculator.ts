import { useState, useCallback, useMemo, useEffect } from 'react';
import { haptics } from '@/shared/utils/haptics';

// --- TYPES ---
export interface Oil {
    id: string;
    name: string;
    drops: number;
}

// --- CONSTANTS ---
const DEFAULT_DROPS_PER_ML = 20;
const MIN_BOTTLE_SIZE = 5;
const MAX_BOTTLE_SIZE = 150;
const MIN_PERCENTAGE = 0.5;
const MAX_PERCENTAGE = 100.0;
const DEFAULT_BOTTLE_SIZE = 15;
const DEFAULT_TARGET_PERCENTAGE = 10.0;

// --- HELPER & CALCULATION FUNCTIONS ---
const generateId = () => `oil_${Date.now()}_${Math.random().toString().substr(2)}`;

const calculateTotalDrops = (oils: Oil[]): number => oils.reduce((sum, oil) => sum + oil.drops, 0);

const calculateDilutionPercentage = (totalDrops: number, bottleSize: number, mlPerDrop: number): number => {
    if (bottleSize <= 0) return 0;
    const eoVolume = totalDrops * mlPerDrop;
    return (eoVolume / bottleSize) * 100;
};

const calculateDropsForPercentage = (bottleSize: number, percentage: number, dropsPerMl: number): number => {
    if (bottleSize <= 0 || percentage <= 0) return 0;
    return Math.round((bottleSize * percentage / 100) * dropsPerMl);
};

const distributeDropsEqually = (totalDrops: number, oilCount: number): number[] => {
    if (oilCount <= 0) return [];
    const dropsPerOil = Math.floor(totalDrops / oilCount);
    const remainder = totalDrops % oilCount;
    return Array.from({ length: oilCount }, (_, i) => dropsPerOil + (i < remainder ? 1 : 0));
};

// --- UNIFIED CALCULATOR HOOK ---
export const useUnifiedCalculator = () => {
    const [bottleSize, setBottleSize] = useState(DEFAULT_BOTTLE_SIZE);
    const [targetPercentage, setTargetPercentage] = useState(DEFAULT_TARGET_PERCENTAGE);
    const [dropsPerMl, setDropsPerMl] = useState(DEFAULT_DROPS_PER_ML);
    
    // Derived values (single source of truth)
    const mlPerDrop = 1 / dropsPerMl;
    
    // Calculate initial drops for default 10% dilution in 15ml bottle
    const initialDrops = calculateDropsForPercentage(DEFAULT_BOTTLE_SIZE, DEFAULT_TARGET_PERCENTAGE, DEFAULT_DROPS_PER_ML);
    const [oils, setOils] = useState<Oil[]>([{ id: generateId(), name: '', drops: initialDrops }]);

    const totalDrops = useMemo(() => calculateTotalDrops(oils), [oils]);
    const dilutionPercentage = useMemo(() => calculateDilutionPercentage(totalDrops, bottleSize, mlPerDrop), [totalDrops, bottleSize, mlPerDrop]);

    const handleBottleSizeChange = useCallback((newSize: number) => {
        haptics.light();
        const clampedSize = Math.max(MIN_BOTTLE_SIZE, Math.min(MAX_BOTTLE_SIZE, newSize));
        
        // Calculate what percentage the current drops would be with the new bottle size
        const currentTotalDrops = calculateTotalDrops(oils);
        const wouldBePercentage = calculateDilutionPercentage(currentTotalDrops, clampedSize, mlPerDrop);
        
        // If it would exceed 100%, scale down the drops proportionally
        if (wouldBePercentage > 100) {
            const maxAllowedDrops = calculateDropsForPercentage(clampedSize, 100, dropsPerMl);
            const scalingFactor = maxAllowedDrops / currentTotalDrops;
            
            setOils(prevOils => prevOils.map(oil => ({
                ...oil,
                drops: Math.floor(oil.drops * scalingFactor)
            })));
        }
        
        setBottleSize(clampedSize);
    }, [oils, mlPerDrop, dropsPerMl]);
    
    const handleTargetPercentageChange = useCallback((newPercentage: number) => {
        haptics.select();
        setTargetPercentage(newPercentage);
        
        const newTotalDrops = calculateDropsForPercentage(bottleSize, newPercentage, dropsPerMl);
        const distributedDrops = distributeDropsEqually(newTotalDrops, oils.length);
        
        setOils(prevOils => prevOils.map((oil, i) => ({ ...oil, drops: distributedDrops[i] })));
    }, [bottleSize, oils.length, dropsPerMl]);
    
    const handleManualDropChange = useCallback((oilId: string, newDrops: number) => {
        haptics.light();
        setOils(prevOils => prevOils.map(oil => oil.id === oilId ? { ...oil, drops: Math.max(0, newDrops) } : oil));
    }, []);
    
    const addOil = useCallback(() => {
        haptics.light();
        setOils(prevOils => [...prevOils, { id: generateId(), name: '', drops: 0 }]);
    }, []);

    const removeOil = useCallback((oilId: string) => {
        haptics.destructive();
        setOils(prevOils => {
            if (prevOils.length <= 1) return prevOils;
            return prevOils.filter(oil => oil.id !== oilId);
        });
    }, []);

    const updateOilName = useCallback((oilId: string, name: string) => {
        setOils(prevOils => prevOils.map(oil => oil.id === oilId ? { ...oil, name } : oil));
    }, []);

    const handleDropsPerMlChange = useCallback((newDropsPerMl: number) => {
        haptics.light();
        setDropsPerMl(Math.max(1, Math.min(50, newDropsPerMl))); // Limit between 1-50 drops per ml
    }, []);

    const resetToDefaults = useCallback(() => {
        haptics.light();
        const defaultDrops = calculateDropsForPercentage(DEFAULT_BOTTLE_SIZE, DEFAULT_TARGET_PERCENTAGE, DEFAULT_DROPS_PER_ML);
        setBottleSize(DEFAULT_BOTTLE_SIZE);
        setTargetPercentage(DEFAULT_TARGET_PERCENTAGE);
        setDropsPerMl(DEFAULT_DROPS_PER_ML);
        setOils([{ id: generateId(), name: '', drops: defaultDrops }]);
    }, []);

    useEffect(() => {
        setTargetPercentage(dilutionPercentage);
    }, [dilutionPercentage]);

    return {
        bottleSize,
        targetPercentage,
        dropsPerMl,
        mlPerDrop,
        oils,
        totalDrops,
        dilutionPercentage,
        handleBottleSizeChange,
        handleTargetPercentageChange,
        handleManualDropChange,
        handleDropsPerMlChange,
        addOil,
        removeOil,
        updateOilName,
        resetToDefaults,
        // Export constants for components
        constants: {
            MIN_BOTTLE_SIZE,
            MAX_BOTTLE_SIZE,
            MIN_PERCENTAGE,
            MAX_PERCENTAGE
        }
    };
};
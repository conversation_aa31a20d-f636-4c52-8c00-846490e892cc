import React from 'react';
import { View } from 'react-native';
import { Text } from 'react-native-paper';
import { useTheme } from '@/shared/hooks/use-theme';
import { useTranslation } from 'react-i18next';
import { OilProportionBar } from './oil-proportion-bar';
import type { Oil } from '../../hooks/use-unified-calculator';

interface ResultsDisplayProps {
    dilutionPercentage: number;
    totalDrops: number;
    eoVolume: number;
    carrierVolume: number;
    oils: Oil[];
    bottleSize: number;
    mlPerDrop: number;
}

export const ResultsDisplay: React.FC<ResultsDisplayProps> = ({
    dilutionPercentage,
    totalDrops,
    eoVolume,
    carrierVolume,
    oils,
    bottleSize,
    mlPerDrop
}) => {
    const { theme, spacing } = useTheme();
    const { t } = useTranslation('oil-calculator');

    return (
        <View style={{ 
            paddingHorizontal: spacing.md,
            paddingTop: spacing.sm,
            paddingBottom: spacing.sm
        }}>
            {/* Main content layout */}
            <View style={{ 
                flexDirection: 'row', 
                alignItems: 'stretch',
                minHeight: 160
            }}>
                {/* Left Section - Clean metrics layout */}
                <View style={{ 
                    flex: 3, 
                    paddingRight: spacing.xl,
                    justifyContent: 'space-between',
                    paddingTop: spacing.xs + 12, // Match "Blend" text + spacing
                    paddingBottom: spacing.xs + 12 // Match "15ml" text + spacing
                }}>
                    {/* Top Row - Primary metrics */}
                    <View style={{ 
                        flexDirection: 'row', 
                        justifyContent: 'space-between'
                    }}>
                        {/* Strength */}
                        <View>
                            <Text variant="bodySmall" style={{ 
                                color: theme.colors.onSurfaceVariant,
                                marginBottom: spacing.sm,
                                fontWeight: '600',
                                textTransform: 'uppercase',
                                letterSpacing: 1.2
                            }}>
                                {t('strengthLabel')}
                            </Text>
                            <Text style={{ 
                                color: theme.colors.primary,
                                fontWeight: '800',
                                fontSize: 42,
                                lineHeight: 48
                            }}>
                                {dilutionPercentage.toFixed(0)}%
                            </Text>
                        </View>
                        
                        {/* Total Drops */}
                        <View style={{ alignItems: 'flex-end' }}>
                            <Text variant="bodySmall" style={{ 
                                color: theme.colors.onSurfaceVariant,
                                marginBottom: spacing.sm,
                                fontWeight: '600',
                                textTransform: 'uppercase',
                                letterSpacing: 1.2
                            }}>
                                {t('totalDropsLabel')}
                            </Text>
                            <Text style={{ 
                                color: theme.colors.onSurface,
                                fontWeight: '800',
                                fontSize: 42,
                                lineHeight: 48
                            }}>
                                {totalDrops}
                            </Text>
                        </View>
                    </View>
                    
                    {/* Bottom Row - Volume metrics */}
                    <View style={{ 
                        flexDirection: 'row', 
                        justifyContent: 'space-between'
                    }}>
                        <View>
                            <Text variant="bodySmall" style={{ 
                                color: theme.colors.onSurfaceVariant,
                                marginBottom: spacing.sm,
                                fontWeight: '500',
                                letterSpacing: 0.6
                            }}>
                                {t('essentialOil')}
                            </Text>
                            <View style={{ flexDirection: 'row', alignItems: 'baseline' }}>
                                <Text style={{ 
                                    color: theme.colors.onSurface,
                                    fontWeight: '600',
                                    fontSize: 22
                                }}>
                                    {eoVolume.toFixed(2)}
                                </Text>
                                <Text variant="titleMedium" style={{ 
                                    color: theme.colors.onSurfaceVariant,
                                    fontWeight: '400',
                                    marginLeft: spacing.xs
                                }}>
                                    ml
                                </Text>
                            </View>
                        </View>
                        
                        <View style={{ alignItems: 'flex-end' }}>
                            <Text variant="bodySmall" style={{ 
                                color: theme.colors.onSurfaceVariant,
                                marginBottom: spacing.sm,
                                fontWeight: '500',
                                letterSpacing: 0.6
                            }}>
                                {t('carrierOil')}
                            </Text>
                            <View style={{ flexDirection: 'row', alignItems: 'baseline' }}>
                                <Text style={{ 
                                    color: theme.colors.onSurface,
                                    fontWeight: '600',
                                    fontSize: 22
                                }}>
                                    {carrierVolume.toFixed(2)}
                                </Text>
                                <Text variant="titleMedium" style={{ 
                                    color: theme.colors.onSurfaceVariant,
                                    fontWeight: '400',
                                    marginLeft: spacing.xs
                                }}>
                                    ml
                                </Text>
                            </View>
                        </View>
                    </View>
                </View>
                
                {/* Right Section - Bottle visualization */}
                <View style={{ 
                    flex: 1, 
                    justifyContent: 'center',
                    alignItems: 'center',
                    paddingLeft: spacing.lg,
                    borderLeftWidth: 1,
                    borderLeftColor: theme.colors.primary,
                    borderLeftStyle: 'solid'
                }}>
                    {/* Blend label */}
                    <Text variant="bodySmall" style={{
                        color: theme.colors.primary,
                        marginBottom: spacing.xs,
                        fontWeight: '500',
                        letterSpacing: 1.2
                    }}>
                        {t('blendLabel')}
                    </Text>
                    
                    <OilProportionBar 
                        oils={oils} 
                        totalDrops={totalDrops} 
                        bottleSize={bottleSize} 
                        mlPerDrop={mlPerDrop}
                        orientation="vertical"
                        height={150}
                    />
                    
                    {/* Bottle size indicator */}
                    <Text variant="bodySmall" style={{
                        color: theme.colors.onSurfaceVariant,
                        marginTop: spacing.xs,
                        fontWeight: '500',
                        textAlign: 'center'
                    }}>
                        {bottleSize}ml
                    </Text>
                </View>
            </View>
        </View>
    );
};
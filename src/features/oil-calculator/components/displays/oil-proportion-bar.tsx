import React from 'react';
import { View } from 'react-native';
import { Text } from 'react-native-paper';
import { useTheme } from '@/shared/hooks/use-theme';
import { useTranslation } from 'react-i18next';
import type { Oil } from '../../hooks/use-unified-calculator';

interface OilProportionBarProps {
    oils: Oil[];
    totalDrops: number;
    bottleSize: number;
    mlPerDrop: number;
    orientation?: 'horizontal' | 'vertical';
    height?: number; // For vertical orientation
}

export const OilProportionBar: React.FC<OilProportionBarProps> = ({ 
    oils, 
    totalDrops, 
    bottleSize, 
    mlPerDrop, 
    orientation = 'horizontal', 
    height = 120 
}) => {
    const { theme, spacing } = useTheme();
    const { t } = useTranslation('oil-calculator');
    
    const eoVolume = totalDrops * mlPerDrop;
    const carrierVolume = bottleSize - eoVolume;
    
    // Primary color with varying opacities for subtle distinction
    const getOilColor = (index: number) => {
        const baseColor = theme.colors.primary;
        const opacities = [
            '100',  // 100% opacity
            '80',   // 80% opacity  
            '60',   // 60% opacity
            '40',   // 40% opacity
            '20',   // 20% opacity
        ];
        
        // For more than 5 oils, use secondary and tertiary colors with opacities
        if (index >= 5) {
            const secondaryColors = [
                theme.colors.secondary,
                theme.colors.tertiary,
            ];
            const colorIndex = Math.floor((index - 5) / 5);
            const opacityIndex = (index - 5) % 5;
            return `${secondaryColors[colorIndex % secondaryColors.length]}${opacities[opacityIndex]}`;
        }
        
        return `${baseColor}${opacities[index % opacities.length]}`;
    };
    
    // Carrier oil color (distinct color)
    const carrierColor = theme.colors.outline;
    
    if (orientation === 'vertical') {
        return (
            <View style={{ alignItems: 'center' }}>
                {/* Vertical Composition Bar */}
                <View style={{
                    width: 60,  // Made thicker - was 40, now 60
                    height: height,
                    backgroundColor: theme.colors.outlineVariant,
                    borderRadius: spacing.md,  // Increased border radius for thicker bar
                    flexDirection: 'column',
                    overflow: 'hidden'
                }}>
                    {/* Carrier Oil (top section) */}
                    {carrierVolume > 0 && (
                        <View
                            style={{
                                height: `${(carrierVolume / bottleSize) * 100}%`,
                                backgroundColor: '#F5C842', // Golden yellow for carrier oil
                                width: '100%'
                            }}
                        />
                    )}
                    
                    {/* Essential Oils (bottom section) - stacked from bottom */}
                    {oils.map((oil, index) => {
                        if (oil.drops === 0) return null;
                        const oilVolume = oil.drops * mlPerDrop;
                        const heightPercentage = (oilVolume / bottleSize) * 100;
                        
                        return (
                            <View
                                key={oil.id}
                                style={{
                                    height: `${heightPercentage}%`,
                                    backgroundColor: getOilColor(index),
                                    width: '100%'
                                }}
                            />
                        );
                    })}
                </View>
            </View>
        );
    }

    return (
        <View>
            {/* Horizontal 100% Composition Bar */}
            <View style={{
                height: 20,
                backgroundColor: theme.colors.outlineVariant,
                borderRadius: spacing.sm,
                flexDirection: 'row',
                overflow: 'hidden',
                marginBottom: spacing.sm
            }}>
                {/* Essential Oils */}
                {oils.map((oil, index) => {
                    if (oil.drops === 0) return null;
                    const oilVolume = oil.drops * mlPerDrop;
                    const widthPercentage = (oilVolume / bottleSize) * 100;
                    
                    return (
                        <View
                            key={oil.id}
                            style={{
                                width: `${widthPercentage}%`,
                                backgroundColor: getOilColor(index),
                                height: '100%'
                            }}
                        />
                    );
                })}
                
                {/* Carrier Oil */}
                {carrierVolume > 0 && (
                    <View
                        style={{
                            width: `${(carrierVolume / bottleSize) * 100}%`,
                            backgroundColor: carrierColor,
                            height: '100%'
                        }}
                    />
                )}
            </View>
            
            {/* Simplified legend */}
            <View style={{
                flexDirection: 'row',
                justifyContent: 'space-around',
                gap: spacing.md
            }}>
                {/* Essential Oils Total */}
                <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    gap: spacing.xs
                }}>
                    <View style={{
                        width: 8,
                        height: 8,
                        borderRadius: 4,
                        backgroundColor: theme.colors.primary
                    }} />
                    <Text variant="bodySmall" style={{ 
                        color: theme.colors.onSurface,
                        fontWeight: '500'
                    }}>
                        {t('essentialOils')} ({((eoVolume / bottleSize) * 100).toFixed(1)}%)
                    </Text>
                </View>
                
                {/* Carrier Oil */}
                {carrierVolume > 0 && (
                    <View style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        gap: spacing.xs
                    }}>
                        <View style={{
                            width: 8,
                            height: 8,
                            borderRadius: 4,
                            backgroundColor: carrierColor
                        }} />
                        <Text variant="bodySmall" style={{ 
                            color: theme.colors.onSurface,
                            fontWeight: '500'
                        }}>
                            {t('carrierOil')} ({((carrierVolume / bottleSize) * 100).toFixed(1)}%)
                        </Text>
                    </View>
                )}
            </View>
        </View>
    );
};
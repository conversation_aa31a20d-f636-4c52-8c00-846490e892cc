import React from 'react';
import { View } from 'react-native';
import { List, TextInput, TouchableRipple, Text, IconButton } from 'react-native-paper';
import { useTheme } from '@/shared/hooks/use-theme';
import { useTranslation } from 'react-i18next';
import type { Oil } from '../../hooks/use-unified-calculator';

interface OilInputRowProps {
    oil: Oil;
    onDropsChange: (oilId: string, drops: number) => void;
    onNameChange: (oilId: string, name: string) => void;
    onRemove: (oilId: string) => void;
    canRemove: boolean;
    totalDrops: number;
    bottleSize: number;
    dropsPerMl: number; // Pass centralized constant instead of hardcoding
}

export const OilInputRow: React.FC<OilInputRowProps> = ({ oil, onDropsChange, onNameChange, onRemove, canRemove, totalDrops, bottleSize, dropsPerMl }) => {
    const { theme, spacing } = useTheme();
    const { t } = useTranslation('oil-calculator');
    
    // Calculate individual oil percentages
    const totalBottleDrops = bottleSize * dropsPerMl;
    
    const partialPercentage = totalDrops > 0 ? (oil.drops / totalDrops) * 100 : 0;
    const mixPercentage = totalBottleDrops > 0 ? (oil.drops / totalBottleDrops) * 100 : 0;
    
    // Check if we're at bottle capacity (prevents going over 100% dilution)
    const isAtCapacity = totalDrops >= totalBottleDrops;
    
    const descriptionComponent = () => (
        <Text variant="bodySmall" style={{ 
            color: theme.colors.onSurfaceVariant,
            paddingLeft: spacing.md  // Align under input field (accounts for icon space)
        }}>
            <Text style={{ fontWeight: 'bold' }}>{t('essentialOilLabel')}</Text> {partialPercentage.toFixed(1)}% <Text style={{ fontWeight: 'bold' }}>{t('mixLabel')}</Text> {mixPercentage.toFixed(1)}%
        </Text>
    );
    
    return (
        <List.Item 
            style={{
                paddingRight: 8 // Remove default right padding to align delete button to edge
            }}
            title={() => (
                <View style={{ flexDirection: 'row', alignItems: 'center', gap: spacing.sm }}>
                    <TextInput
                        mode="outlined"
                        placeholder={t('oilNamePlaceholder')}
                        value={oil.name}
                        onChangeText={(text) => onNameChange(oil.id, text)}
                        style={{ flex: 1 }}
                        dense
                    />
                    <View style={{ flexDirection: 'row', alignItems: 'center', gap: spacing.xs }}>
                        <IconButton
                            icon="minus"
                            mode="contained-tonal"
                            size={20}
                            onPress={() => onDropsChange(oil.id, oil.drops - 1)}
                            disabled={oil.drops === 0}
                        />
                        
                        <Text variant="titleMedium" style={{ 
                            minWidth: 32, 
                            textAlign: 'center',
                            fontWeight: '600',
                            color: theme.colors.onSurface
                        }}>
                            {oil.drops}
                        </Text>
                        
                        <IconButton
                            icon="plus"
                            mode="contained-tonal"
                            size={20}
                            onPress={() => onDropsChange(oil.id, oil.drops + 1)}
                            disabled={isAtCapacity}
                        />
                    </View>
                </View>
            )}
            description={descriptionComponent}
            right={() => canRemove ? (
                <View style={{ paddingTop: spacing.sm }}>
                    <TouchableRipple 
                        onPress={() => onRemove(oil.id)}
                        style={{ 
                            borderRadius: 20,
                            width: 36,
                            height: 36,
                            alignItems: 'center',
                            justifyContent: 'center'
                        }}
                        borderless={true}
                        rippleColor={theme.colors.onSurfaceVariant + '20'}
                    >
                        <List.Icon 
                            icon="delete-outline" 
                            color={theme.colors.onSurfaceVariant}
                        />
                    </TouchableRipple>
                </View>
            ) : null}
        />
    );
};
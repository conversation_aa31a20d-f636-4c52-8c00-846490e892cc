import React from 'react';
import { View } from 'react-native';
import { Text, IconButton } from 'react-native-paper';
import { useTheme } from '@/shared/hooks/use-theme';
import { useTranslation } from 'react-i18next';

interface BottleSizeStepperProps {
    value: number;
    onChange: (value: number) => void;
}

export const BottleSizeStepper: React.FC<BottleSizeStepperProps> = ({ value, onChange }) => {
    const { theme, spacing } = useTheme();
    const { t } = useTranslation('oil-calculator');
    
    return (
        <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
            <IconButton
                icon="minus"
                mode="contained-tonal"
                size={20}
                onPress={() => onChange(value - 5)}
            />
            
            <View style={{ alignItems: 'center', flex: 1 }}>
                <Text variant="headlineMedium" style={{ 
                    color: theme.colors.onPrimary,
                    fontWeight: 'bold'
                }}>
                    {value}
                </Text>
                <Text variant="bodySmall" style={{ 
                    color: theme.colors.onPrimary, 
                    opacity: 0.7,
                    marginTop: spacing.xxs
                }}>
                    {t('millilitersUnit')}
                </Text>
            </View>
            
            <IconButton
                icon="plus"
                mode="contained-tonal"
                size={20}
                onPress={() => onChange(value + 5)}
            />
        </View>
    );
};
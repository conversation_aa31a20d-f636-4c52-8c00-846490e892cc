import React, { useState, useMemo, useEffect } from 'react';
import { View } from 'react-native';
import { Text, Button, IconButton, Icon } from 'react-native-paper';
import { BottomSheetView } from '@gorhom/bottom-sheet';
import { useTheme } from '@/shared/hooks/use-theme';
import { useTranslation } from 'react-i18next';
import { haptics } from '@/shared/utils/haptics';

// --- Constants for drop ratio range ---
const MIN_DROPS_PER_ML = 15;
const MAX_DROPS_PER_ML = 30;
const DECIMAL_PLACES = 3;
const ICON_SIZE_MULTIPLIER = 3;

interface DropRatioBottomSheetContentProps {
    onDismiss: () => void;
    initialDropsPerMl: number;
    onSave: (value: number) => void;
}

export const DropRatioBottomSheetContent: React.FC<DropRatioBottomSheetContentProps> = ({
    onDismiss,
    initialDropsPerMl,
    onSave
}) => {
    const { theme, spacing } = useTheme();
    const { t } = useTranslation('oil-calculator');
    
    // --- Internal state management until saved ---
    const [currentDrops, setCurrentDrops] = useState(initialDropsPerMl);

    // --- Calculate derived value within component ---
    const mlPerDrop = useMemo(() => 1 / currentDrops, [currentDrops]);

    // Reset internal state when component mounts with different initial value
    useEffect(() => {
        setCurrentDrops(initialDropsPerMl);
    }, [initialDropsPerMl]);

    const handleValueChange = (newValue: number) => {
        haptics.light();
        // Extra safeguard for range validation
        if (newValue >= MIN_DROPS_PER_ML && newValue <= MAX_DROPS_PER_ML) {
            setCurrentDrops(newValue);
        }
    };

    const handleSave = () => {
        onSave(currentDrops);
        onDismiss();
    };

    const styles = createStyles(theme, spacing);

    return (
        <BottomSheetView style={styles.container}>
            {/* Title Section */}
            <View style={styles.titleContainer}>
                <Text variant="headlineSmall" style={styles.title}>
                    {t('dropRatioSettings', 'Tamanho da Gota')}
                </Text>
            </View>
            {/* Content Section */}
            <View style={styles.contentSection}>
                {/* Background Icon */}
                <View style={styles.iconContainer}>
                    <Icon 
                        source="water"
                        size={spacing.xxl * ICON_SIZE_MULTIPLIER} // Use theme spacing with constant multiplier
                        color={theme.colors.primary}
                    />
                </View>

                {/* Stepper Controls */}
                <View style={styles.stepperContainer}>
                    {/* Decrease Button */}
                    <IconButton
                        icon="minus"
                        mode="contained"
                        size={32}
                        onPress={() => handleValueChange(currentDrops - 1)}
                        disabled={currentDrops <= MIN_DROPS_PER_ML}
                        style={styles.button}
                    />
                    
                    {/* Value Display */}
                    <View style={styles.valueContainer}>
                        <Text style={styles.valueText}>
                            {currentDrops}
                        </Text>
                        <Text variant="bodyMedium" style={styles.unitText}>
                            drops/ml
                        </Text>
                    </View>

                    {/* Increase Button */}
                    <IconButton
                        icon="plus"
                        mode="contained"
                        size={32}
                        onPress={() => handleValueChange(currentDrops + 1)}
                        disabled={currentDrops >= MAX_DROPS_PER_ML}
                        style={styles.button}
                    />
                </View>

                {/* Derived Value Display */}
                <Text variant="bodyMedium" style={styles.derivedValueText}>
                    1 gota ≈ {mlPerDrop.toFixed(DECIMAL_PLACES)} ml
                </Text>
            </View>

            {/* Action Buttons */}
            <View style={styles.actionsContainer}>
                <Button 
                    onPress={onDismiss} 
                    style={styles.cancelButton}
                    textColor={theme.colors.onSurface}
                >
                    {t('cancel', 'Cancelar')}
                </Button>
                <Button 
                    mode="contained" 
                    onPress={handleSave} 
                    style={styles.saveButton}
                >
                    {t('save', 'Salvar')}
                </Button>
            </View>
        </BottomSheetView>
    );
};

// --- StyleSheet for organization and performance ---
const createStyles = (theme: any, spacing: any) => ({
    container: {
        flex: 1,
        paddingHorizontal: spacing.md,
    },
    titleContainer: {
        alignItems: 'center' as const,
        paddingVertical: spacing.md,
        marginBottom: spacing.md,
    },
    title: {
        textAlign: 'center' as const,
        fontWeight: '600' as const,
        color: theme.colors.onSurface,
    },
    contentSection: {
        flex: 1,
    },
    iconContainer: {
        alignItems: 'center' as const,
        marginVertical: spacing.xs,
    },
    stepperContainer: {
        flexDirection: 'row' as const,
        alignItems: 'center' as const,
        justifyContent: 'center' as const,
        paddingVertical: spacing.sm,
        marginTop: spacing.xs,
    },
    button: {
        backgroundColor: theme.colors.primaryContainer,
    },
    valueContainer: {
        marginHorizontal: spacing.lg,
        minWidth: spacing.xxl * 2, // Use theme spacing instead of hardcoded 80
        alignItems: 'center' as const,
    },
    valueText: {
        fontSize: spacing.xxl + spacing.sm, // Use theme spacing for font size
        fontWeight: '800' as const,
        color: theme.colors.primary,
    },
    unitText: {
        color: theme.colors.onSurfaceVariant,
        fontWeight: '500' as const,
        marginTop: spacing.xxs,
    },
    derivedValueText: {
        textAlign: 'center' as const,
        color: theme.colors.onSurfaceVariant,
        marginTop: spacing.sm,
        fontWeight: '500' as const,
    },
    actionsContainer: {
        flexDirection: 'row' as const,
        gap: spacing.xs,
        paddingTop: spacing.md,
        paddingBottom: spacing.xl,
    },
    cancelButton: {
        flex: 1,
    },
    saveButton: {
        flex: 1,
    },
});
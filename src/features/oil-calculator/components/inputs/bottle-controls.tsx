import React from 'react';
import { View } from 'react-native';
import Slider from '@react-native-community/slider';
import { Text, List } from 'react-native-paper';
import { useTheme } from '@/shared/hooks/use-theme';
import { useTranslation } from 'react-i18next';
import { BottleSizeStepper } from './bottle-size-stepper';

interface BottleControlsProps {
    bottleSize: number;
    targetPercentage: number;
    minPercentage: number;
    maxPercentage: number;
    onBottleSizeChange: (size: number) => void;
    onTargetPercentageChange: (percentage: number) => void;
}

export const BottleControls: React.FC<BottleControlsProps> = ({
    bottleSize,
    targetPercentage,
    minPercentage,
    maxPercentage,
    onBottleSizeChange,
    onTargetPercentageChange
}) => {
    const { theme, spacing } = useTheme();
    const { t } = useTranslation('oil-calculator');

    return (
        <View style={{ paddingHorizontal: spacing.md, paddingVertical: spacing.sm }}>
            {/* Bottle Size Section */}
            <View style={{ marginBottom: spacing.md }}>
                <Text variant="bodyMedium" style={{ 
                    color: theme.colors.onPrimary,
                    marginBottom: spacing.sm,
                    opacity: 0.9
                }}>
                    {t('bottleSize')}
                </Text>
                <BottleSizeStepper value={bottleSize} onChange={onBottleSizeChange} />
            </View>

            {/* Target Dilution Section */}
            <View>
                <View style={{ 
                    flexDirection: 'row', 
                    justifyContent: 'space-between', 
                    alignItems: 'center',
                    marginBottom: spacing.sm 
                }}>
                    <Text variant="bodyMedium" style={{ 
                        color: theme.colors.onPrimary,
                        opacity: 0.9
                    }}>
                        {t('targetDilution')}
                    </Text>
                    <Text variant="titleMedium" style={{ 
                        color: theme.colors.onPrimary,
                        fontWeight: 'bold'
                    }}>
                        {targetPercentage.toFixed(1)}%
                    </Text>
                </View>
                <Slider
                    minimumValue={minPercentage}
                    maximumValue={maxPercentage}
                    step={0.5}
                    value={targetPercentage}
                    onValueChange={onTargetPercentageChange}
                    minimumTrackTintColor={theme.colors.surface}
                    maximumTrackTintColor={theme.colors.onPrimary + '40'}
                    thumbTintColor={theme.colors.surface}
                />
            </View>
        </View>
    );
};
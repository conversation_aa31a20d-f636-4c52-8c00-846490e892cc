/**
 * Recipe State Slice
 * 
 * Manages recipe-specific state (healthConcern, selectedCauses, etc.)
 * using Zustand store slicing pattern.
 */

import { StateCreator } from 'zustand';
import { RecipeStep } from '../types/recipe.types';
import { calculatePostEnrichmentScores } from '../utils/post-enrichment-scoring';
import type {
  HealthConcernData,
  DemographicsData,
  PotentialCause,
  PotentialSymptom,
  TherapeuticProperty,
  PropertyOilSuggestions,
  EnrichedEssentialOil,
  FinalRecipeProtocol,
  FinalRecipeStatus,
  RecipeTimeSlot
} from '../types/recipe.types';
import type { ScoredOil, SafetyLibrary } from '../utils/post-enrichment-scoring';

import {
  trackStepViewed,
  trackStepCompleted,
  trackWizardCompleted,
  trackWizardReset,
  trackWizardStarted
} from '../utils/recipe-analytics';

/**
 * Generates a UUID v4 string for React Native
 * Uses crypto.randomUUID if available, otherwise falls back to Math.random
 */
function generateUUID(): string {
  // React Native supports crypto.randomUUID in newer versions
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID();
  }
  // Fallback for React Native environments without crypto.randomUUID
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

export interface RecipeSliceState {
  // Step data
  healthConcern: HealthConcernData | null;
  demographics: DemographicsData | null;
  selectedCauses: PotentialCause[];
  selectedSymptoms: PotentialSymptom[];
  therapeuticProperties: TherapeuticProperty[];
  suggestedOils: PropertyOilSuggestions[];
  finalRecipes: {
    morning: {
      recipe: FinalRecipeProtocol | null;
      status: FinalRecipeStatus;
    };
    midDay: {
      recipe: FinalRecipeProtocol | null;
      status: FinalRecipeStatus;
    };
    night: {
      recipe: FinalRecipeProtocol | null;
      status: FinalRecipeStatus;
    };
    isGenerating: boolean;
    hasStartedGeneration: boolean;
    globalError: string | null;
  };

  // API response data
  potentialCauses: PotentialCause[];
  potentialSymptoms: PotentialSymptom[];

  // Navigation state
  currentStep: RecipeStep;
  completedSteps: RecipeStep[];

  // Oil enrichment states
  propertyEnrichmentStatus: Record<string, 'idle' | 'loading' | 'success' | 'error'>;

  // Post-enrichment scoring results (to avoid duplication)
  scoredOils: ScoredOil[] | null;
  safetyLibrary: SafetyLibrary | null;
  safetyLibraryFormatted: string | null;

  // Auto-analysis state
  shouldAutoAnalyzeProperties: boolean;

  // Metadata
  lastUpdated: Date;
  sessionId: string;
}

export interface RecipeSliceActions {
  // Step navigation actions
  setCurrentStep: (step: RecipeStep) => void;
  markStepCompleted: (step: RecipeStep) => void;
  canNavigateToStep: (step: RecipeStep) => boolean;

  // Data update actions
  updateHealthConcern: (data: HealthConcernData) => void;
  updateDemographics: (data: DemographicsData) => void;
  updateSelectedCauses: (causes: PotentialCause[]) => void;
  updateSelectedSymptoms: (symptoms: PotentialSymptom[]) => void;
  updateTherapeuticProperties: (properties: TherapeuticProperty[], source: string) => void;
  updateSuggestedOils: (oils: PropertyOilSuggestions[]) => void;
  updateFinalRecipes: (timeSlot: RecipeTimeSlot, recipe: FinalRecipeProtocol) => void;
  substituteOilInRecipe: (timeSlot: RecipeTimeSlot, originalOilId: string, substitutionOil: EnrichedEssentialOil) => void;

  // API data update actions
  setPotentialCauses: (causes: PotentialCause[]) => void;
  setPotentialSymptoms: (symptoms: PotentialSymptom[]) => void;

  // Data clearing actions
  clearStepsAfter: (currentStep: RecipeStep) => void;
  clearStepData: (step: RecipeStep) => void;
  resetWizard: () => void;

  // Oil enrichment state management
  setPropertyEnrichmentStatus: (propertyId: string, status: 'idle' | 'loading' | 'success' | 'error') => void;
  updatePropertyWithEnrichedOils: (propertyId: string, enrichedOils: EnrichedEssentialOil[]) => void;
  updatePropertyWithSuggestedOils: (propertyId: string, suggestedOils: any[]) => void;

  // Auto-analysis state management
  setShouldAutoAnalyzeProperties: (should: boolean) => void;

  // Final recipes state management
  setFinalRecipeStatus: (timeSlot: RecipeTimeSlot, status: FinalRecipeStatus) => void;
  setFinalRecipesGenerating: (isGenerating: boolean) => void;
  setFinalRecipesGlobalError: (error: string | null) => void;
  resetFinalRecipes: () => void;

  // Lookup helper functions
  getCauseNameById: (id: string) => string | null;
  getSymptomNameById: (id: string) => string | null;
  getCauseNamesByIds: (ids: string[]) => string[];
  getSymptomNamesByIds: (ids: string[]) => string[];
}

export type RecipeSlice = RecipeSliceState & RecipeSliceActions;

const DEFAULT_STEP = RecipeStep.HEALTH_CONCERN;

/**
 * Initial state for the recipe slice
 */
const initialRecipeState: RecipeSliceState = {
  // Step data
  healthConcern: null,
  demographics: null,
  selectedCauses: [],
  selectedSymptoms: [],
  therapeuticProperties: [],
  suggestedOils: [],
  finalRecipes: {
    morning: {
      recipe: null,
      status: { status: 'idle', retry_count: 0 }
    },
    midDay: {
      recipe: null,
      status: { status: 'idle', retry_count: 0 }
    },
    night: {
      recipe: null,
      status: { status: 'idle', retry_count: 0 }
    },
    isGenerating: false,
    hasStartedGeneration: false,
    globalError: null
  },

  // API response data
  potentialCauses: [],
  potentialSymptoms: [],

  // Navigation state
  currentStep: DEFAULT_STEP,
  completedSteps: [],

  // Oil enrichment states
  propertyEnrichmentStatus: {},

  // Post-enrichment scoring results (to avoid duplication)
  scoredOils: null,
  safetyLibrary: null,
  safetyLibraryFormatted: null,

  // Auto-analysis state
  shouldAutoAnalyzeProperties: false,

  // Metadata
  lastUpdated: new Date(),
  sessionId: generateUUID()
};

export const createRecipeSlice: StateCreator<
  RecipeSlice,
  [],
  [],
  RecipeSlice
> = (set, get) => ({
  ...initialRecipeState,

  // Step navigation actions - optimized to reduce unnecessary re-renders
  setCurrentStep: (step: RecipeStep) => {
    set((state) => {
      // Only update if step actually changed
      if (state.currentStep === step) return state;
      
      const previousStep = state.currentStep;
      
      // Track step navigation
      trackStepViewed(step, state.sessionId, previousStep);
      
      return {
        currentStep: step,
        lastUpdated: new Date()
      };
    });
  },

  markStepCompleted: (step: RecipeStep) => {
    set((state) => {
      // Only update if step isn't already completed
      if (state.completedSteps.includes(step)) return state;

      const completedSteps = [...state.completedSteps, step];
      
      // Track step completion
      trackStepCompleted(step, state.sessionId);
      
      // Check if this is the final step completion (wizard completed)
      if (step === RecipeStep.FINAL_RECIPES) {
        trackWizardCompleted(state.sessionId, completedSteps.length);
      }
      
      return {
        completedSteps,
        lastUpdated: new Date()
      };
    });
  },

  canNavigateToStep: (step: RecipeStep): boolean => {
    const state = get();

    // Always allow navigation to health concern (first step)
    if (step === RecipeStep.HEALTH_CONCERN) {
      return true;
    }

    // Check if previous steps are completed
    switch (step) {
      case RecipeStep.DEMOGRAPHICS:
        const canNavigateToDemographics = !!state.healthConcern;
        return canNavigateToDemographics;

      case RecipeStep.CAUSES:
        const canNavigateToCauses = !!state.healthConcern && !!state.demographics;
        return canNavigateToCauses;

      case RecipeStep.SYMPTOMS:
        const canNavigateToSymptoms = !!state.healthConcern && !!state.demographics && state.selectedCauses.length > 0;
        return canNavigateToSymptoms;

      case RecipeStep.PROPERTIES:
        // Can only navigate to properties when symptoms step is completed AND therapeutic properties exist
        // This prevents auto-navigation when just selecting symptoms - user must complete the step
        const canNavigateToProperties = !!state.healthConcern && !!state.demographics &&
          state.selectedCauses.length > 0 && state.selectedSymptoms.length > 0 &&
          state.therapeuticProperties.length > 0;
        return canNavigateToProperties;

      case RecipeStep.FINAL_RECIPES:
        // Can navigate to Final Recipes when all previous steps are completed
        // and therapeutic properties have been enriched with oils
        const hasBasicData = !!state.healthConcern && !!state.demographics &&
          state.selectedCauses.length > 0 && state.selectedSymptoms.length > 0;
        const hasProperties = state.therapeuticProperties.length > 0;
        const hasEnrichedProperties = state.therapeuticProperties.some(p => p.isEnriched);
        const canNavigateToFinalRecipes = hasBasicData && hasProperties && hasEnrichedProperties;

        return canNavigateToFinalRecipes;

      default:
        console.log(`❌ [Store Navigation] Unknown step: ${step}`);
        return false;
    }
  },

  // Data update actions
  updateHealthConcern: (data: HealthConcernData) => {
    set((state) => {
      // Track wizard started if this is the first health concern input
      if (!state.healthConcern) {
        trackWizardStarted(state.sessionId);
      }
      
      return {
        healthConcern: data,
        lastUpdated: new Date()
      };
    });
  },

  updateDemographics: (data: DemographicsData) => {
    set(() => ({
      demographics: data,
      lastUpdated: new Date()
    }));
  },

  updateSelectedCauses: (causes: PotentialCause[]) => {
    set(() => ({
      selectedCauses: causes,
      // Clear dependent data when causes change
      selectedSymptoms: [],
      therapeuticProperties: [],
      suggestedOils: [],
      potentialSymptoms: [],
      lastUpdated: new Date()
    }));
  },

  updateSelectedSymptoms: (symptoms: PotentialSymptom[]) => {
    set(() => ({
      selectedSymptoms: symptoms,
      // Clear dependent data when symptoms change
      therapeuticProperties: [],
      suggestedOils: [],
      lastUpdated: new Date()
    }));
  },

  updateTherapeuticProperties: (properties: TherapeuticProperty[], source: string) => {
    const currentState = get().therapeuticProperties;
    console.log('ACTION: updateTherapeuticProperties');
    console.log('PAYLOAD:', { properties, source });

    // Only keep canonical fields for each property
    const canonicalProperties = properties.map(newProp => {
      const existing = currentState.find(cp => cp.property_id === newProp.property_id);
      // If existing is enriched and has oils, keep it
      if (existing && existing.isEnriched && existing.suggested_oils && existing.suggested_oils.length > 0) {
        return existing;
      }
      // Otherwise, only keep canonical fields
      return {
        property_id: newProp.property_id,
        property_name_localized: newProp.property_name_localized,
        property_name_english: newProp.property_name_english,
        description_contextual_localized: newProp.description_contextual_localized,
        addresses_cause_ids: newProp.addresses_cause_ids || [],
        addresses_symptom_ids: newProp.addresses_symptom_ids || [],
        relevancy_score: newProp.relevancy_score,
        suggested_oils: newProp.suggested_oils || [],
        isLoadingOils: newProp.isLoadingOils ?? false,
        errorLoadingOils: newProp.errorLoadingOils ?? null,
        isEnriched: newProp.isEnriched ?? false,
      };
    });

    set(() => ({
      therapeuticProperties: canonicalProperties,
      // Clear dependent data when properties change
      suggestedOils: [],
      lastUpdated: new Date()
    }));
  },

  updateSuggestedOils: (oils: PropertyOilSuggestions[]) => {
    set(() => ({
      suggestedOils: oils,
      lastUpdated: new Date()
    }));
  },

  updateFinalRecipes: (timeSlot: RecipeTimeSlot, recipe: FinalRecipeProtocol) => {
    set((state) => ({
      finalRecipes: {
        ...state.finalRecipes,
        [timeSlot === 'mid-day' ? 'midDay' : timeSlot]: {
          recipe,
          status: { status: 'success' as const, retry_count: 0 }
        }
      },
      lastUpdated: new Date()
    }));
  },

  substituteOilInRecipe: (timeSlot: RecipeTimeSlot, originalOilId: string, substitutionOil: EnrichedEssentialOil) => {
    set((state) => {
      const slotKey = timeSlot === 'mid-day' ? 'midDay' : timeSlot;
      const currentRecipe = state.finalRecipes[slotKey].recipe;
      
      if (!currentRecipe) {
        if (__DEV__) {
          console.warn(`No recipe found for time slot: ${timeSlot}`);
        }
        return state;
      }

      // Find and replace the oil in selected_oils array
      const updatedSelectedOils = currentRecipe.selected_oils.map(oil => {
        if (oil.oil_id === originalOilId) {
          return {
            oil_id: substitutionOil.oil_id,
            name_localized: substitutionOil.name_localized,
            name_english: substitutionOil.name_english,
            name_botanical: substitutionOil.name_botanical,
            name_scientific: substitutionOil.name_scientific,
            drops_count: oil.drops_count, // Keep original drop count
            rationale_localized: substitutionOil.match_rationale_localized || `Substitute for therapeutic equivalence`
          };
        }
        return oil;
      });

      const updatedRecipe = {
        ...currentRecipe,
        selected_oils: updatedSelectedOils
      };

      if (__DEV__) {
        console.log(`✅ Substituted oil ${originalOilId} with ${substitutionOil.oil_id} in ${timeSlot} recipe`);
      }

      return {
        finalRecipes: {
          ...state.finalRecipes,
          [slotKey]: {
            ...state.finalRecipes[slotKey],
            recipe: updatedRecipe
          }
        },
        lastUpdated: new Date()
      };
    });
  },

  // API data update actions
  setPotentialCauses: (causes: PotentialCause[]) => {
    set(() => ({
      potentialCauses: causes,
      lastUpdated: new Date()
    }));
  },

  setPotentialSymptoms: (symptoms: PotentialSymptom[]) => {
    set(() => ({
      potentialSymptoms: symptoms,
      lastUpdated: new Date()
    }));
  },

  /**
   * Clears data for all steps after the specified step
   * Used when navigating backwards to ensure data consistency
   */
  clearStepsAfter: (currentStep: RecipeStep) => {
    console.log(`🧹 Clearing steps after: ${currentStep}`);

    set((state) => {
      const updates: Partial<RecipeSliceState> = {
        lastUpdated: new Date()
      };

      // Clear completed steps that come after the current step
      const stepOrder = [
        RecipeStep.HEALTH_CONCERN,
        RecipeStep.DEMOGRAPHICS,
        RecipeStep.CAUSES,
        RecipeStep.SYMPTOMS,
        RecipeStep.PROPERTIES,
        RecipeStep.FINAL_RECIPES
        // Note: OILS step removed - oils are now nested within PROPERTIES
      ];

      const currentStepIndex = stepOrder.indexOf(currentStep);
      const stepsToRemove = stepOrder.slice(currentStepIndex + 1);

      updates.completedSteps = state.completedSteps.filter(
        step => !stepsToRemove.includes(step)
      );

      // Clear data based on which step we're going back to
      switch (currentStep) {
        case RecipeStep.HEALTH_CONCERN:
          // Clear everything except health concern
          updates.demographics = null;
          updates.selectedCauses = [];
          updates.selectedSymptoms = [];
          updates.therapeuticProperties = [];
          updates.suggestedOils = [];
          updates.potentialCauses = [];
          updates.potentialSymptoms = [];
          break;

        case RecipeStep.DEMOGRAPHICS:
          // Clear causes and everything after
          updates.selectedCauses = [];
          updates.selectedSymptoms = [];
          updates.therapeuticProperties = [];
          updates.suggestedOils = [];
          updates.potentialCauses = [];
          updates.potentialSymptoms = [];
          break;

        case RecipeStep.CAUSES:
          // Clear symptoms and everything after
          updates.selectedSymptoms = [];
          updates.therapeuticProperties = [];
          updates.suggestedOils = [];
          updates.potentialSymptoms = [];
          break;

        case RecipeStep.SYMPTOMS:
          // Clear properties and oils
          updates.therapeuticProperties = [];
          updates.suggestedOils = [];
          break;

        case RecipeStep.PROPERTIES:
          // This is now the final step - nothing to clear after it
          // (oils are nested within properties, not a separate step)
          break;
      }

      console.log(`✅ Cleared data for steps after ${currentStep}:`, {
        clearedSteps: stepsToRemove,
        remainingCompletedSteps: updates.completedSteps
      });

      return { ...state, ...updates };
    });
  },

  /**
   * Clears specific step data
   * Used for targeted data clearing
   */
  clearStepData: (step: RecipeStep) => {
    console.log(`🧹 Clearing data for step: ${step}`);

    const stateToClear: Partial<RecipeSliceState> = {};
    switch (step) {
      case RecipeStep.HEALTH_CONCERN:
        stateToClear.healthConcern = null;
        break;
      case RecipeStep.DEMOGRAPHICS:
        stateToClear.demographics = null;
        break;
      case RecipeStep.CAUSES:
        stateToClear.selectedCauses = [];
        stateToClear.potentialCauses = [];
        break;
      case RecipeStep.SYMPTOMS:
        stateToClear.selectedSymptoms = [];
        stateToClear.potentialSymptoms = [];
        break;
      case RecipeStep.PROPERTIES:
        stateToClear.therapeuticProperties = [];
        stateToClear.suggestedOils = [];
        break;
    }
    set(stateToClear);
  },

  resetWizard: () => {
    console.log('🔄 Starting recipe wizard reset...');
    
    const currentState = get();
    
    // Track wizard reset
    trackWizardReset(currentState.sessionId, currentState.currentStep);

    // Reset to initial state with new session ID and explicitly clear all states
    const newSessionId = generateUUID();
    set(() => ({
      ...initialRecipeState,
      sessionId: newSessionId, // Generate new session ID
      lastUpdated: new Date()
    }));

    // Track new wizard session started
    trackWizardStarted(newSessionId);

    console.log('✅ Recipe wizard reset completed - all data and states cleared');
  },

  // Oil enrichment state management
  setPropertyEnrichmentStatus: (propertyId: string, status: 'idle' | 'loading' | 'success' | 'error') => {
    set((state) => ({
      propertyEnrichmentStatus: {
        ...state.propertyEnrichmentStatus,
        [propertyId]: status,
      },
      lastUpdated: new Date()
    }));
  },

  updatePropertyWithEnrichedOils: (propertyId: string, enrichedOils: EnrichedEssentialOil[]) => {
    set((state) => {
      console.log('ACTION: updatePropertyWithEnrichedOils');
      console.log('PAYLOAD:', { propertyId, enrichedOils });
      console.log('PRE-UPDATE STATE:', state.therapeuticProperties);

      const updatedProperties = state.therapeuticProperties.map(p => {
        if (p.property_id === propertyId) {
          // Consider an oil enriched if it has been processed (has an enrichment_status)
          const updatedOils = enrichedOils.map(oil => ({
            ...oil,
            isEnriched: !!oil.enrichment_status // Oil is enriched if it has been processed
          }));

          // Property is enriched if all oils have been processed
          const allOilsProcessed = updatedOils.every(oil => !!oil.enrichment_status);

          return {
            ...p,
            suggested_oils: updatedOils,
            isEnriched: allOilsProcessed
          };
        }
        return p;
      });

      // Log enrichment statistics
      console.log(`✅ [recipe-store] Updated property ${propertyId} with ${enrichedOils.length} oils:`, {
        enriched: enrichedOils.filter(o => o.enrichment_status === 'enriched').length,
        not_found: enrichedOils.filter(o => o.enrichment_status === 'not_found').length,
        discarded: enrichedOils.filter(o => o.enrichment_status === 'discarded').length
      });

      // 🚀 HYBRID SCORING TRIGGER: Calculate when all properties are enriched
      const allPropertiesEnriched = updatedProperties.every(p => p.isEnriched);

      let finalProperties = updatedProperties;
      let finalScoredOils = null;
      let finalSafetyLibrary = null;
      let finalSafetyLibraryFormatted = null;

      if (allPropertiesEnriched && updatedProperties.length > 0) {
        console.log('🎯 All properties enriched! Triggering hybrid scoring calculation...');

        try {
          // Calculate hybrid scores using imported function
          const scoringResult = calculatePostEnrichmentScores(updatedProperties);

          // Store the complete scoring result for later use (eliminates duplication)
          finalScoredOils = scoringResult.suggested_oils;
          finalSafetyLibrary = scoringResult.safety_library;
          finalSafetyLibraryFormatted = scoringResult.safety_library_formatted;

          // Create a map for quick lookup of hybrid scores AND specialization scores
          const hybridScoreMap = new Map<string, { final_relevance_score: number; specialization_score: number }>();
          scoringResult.suggested_oils.forEach(scoredOil => {
            if (scoredOil.oil_id) {
              hybridScoreMap.set(scoredOil.oil_id, {
                final_relevance_score: scoredOil.final_relevance_score,
                specialization_score: scoredOil.specialization_score
              });
            }
          });

          // Update properties with hybrid scores AND specialization scores
          finalProperties = updatedProperties.map(prop => ({
            ...prop,
            suggested_oils: (prop.suggested_oils || []).map(oil => {
              const scores = oil.oil_id ? hybridScoreMap.get(oil.oil_id) : undefined;
              return {
                ...oil,
                final_relevance_score: scores?.final_relevance_score,
                specialization_score: scores?.specialization_score
              };
            })
          }));

          console.log('✅ Hybrid scores applied to all properties!', {
            propertiesUpdated: finalProperties.length,
            oilsWithScores: Array.from(hybridScoreMap.entries()).length,
            scoredOilsStored: finalScoredOils.length,
            safetyLibraryStored: !!finalSafetyLibrary,
            sampleScores: Array.from(hybridScoreMap.entries()).slice(0, 3).map(([id, scores]) => ({
              oilId: id.substring(0, 8) + '...',
              hybridScore: scores.final_relevance_score.toFixed(2),
              specializationScore: (scores.specialization_score * 100).toFixed(1) + '%'
            }))
          });

        } catch (error) {
          console.error('❌ Failed to calculate hybrid scores:', error);
        }
      }

      console.log('POST-UPDATE STATE:', finalProperties);
      return {
        therapeuticProperties: finalProperties,
        // Store complete scoring result (eliminates service layer duplication)
        scoredOils: finalScoredOils,
        safetyLibrary: finalSafetyLibrary,
        safetyLibraryFormatted: finalSafetyLibraryFormatted,
        lastUpdated: new Date()
      };
    });
  },

  /**
   * Simple update function for suggested oils (YAGNI approach)
   * Updates a property with suggested oils data from API
   */
  updatePropertyWithSuggestedOils: (propertyId: string, suggestedOils: any[]) => {
    set((state) => {
      console.log('ACTION: updatePropertyWithSuggestedOils');
      console.log('PAYLOAD:', { propertyId, oilsCount: suggestedOils.length });

      const updatedProperties = state.therapeuticProperties.map(p => {
        if (p.property_id === propertyId) {
          return {
            ...p,
            suggested_oils: suggestedOils,
            // Don't mark as enriched - keep it simple for YAGNI
            isEnriched: false
          };
        }
        return p;
      });

      console.log(`✅ [recipe-store] Updated property ${propertyId} with ${suggestedOils.length} suggested oils`);

      return {
        therapeuticProperties: updatedProperties,
        lastUpdated: new Date()
      };
    });
  },

  // Auto-analysis state management
  /**
   * Sets the flag to trigger automatic oil analysis when navigating to properties page
   * @param should - Boolean flag indicating whether auto-analysis should be triggered
   * 
   * This flag is set to true when users navigate from symptoms to properties page,
   * and is automatically cleared after the auto-analysis is triggered to prevent
   * duplicate API calls.
   */
  setShouldAutoAnalyzeProperties: (should: boolean) => {
    set((state) => {
      // Only update if the flag actually changed
      if (state.shouldAutoAnalyzeProperties === should) return state;
      return {
        shouldAutoAnalyzeProperties: should,
        lastUpdated: new Date()
      };
    });
  },

  // Final recipes state management
  setFinalRecipeStatus: (timeSlot: RecipeTimeSlot, status: FinalRecipeStatus) => {
    set((state) => ({
      finalRecipes: {
        ...state.finalRecipes,
        [timeSlot === 'mid-day' ? 'midDay' : timeSlot]: {
          ...state.finalRecipes[timeSlot === 'mid-day' ? 'midDay' : timeSlot],
          status
        }
      },
      lastUpdated: new Date()
    }));
  },

  setFinalRecipesGenerating: (isGenerating: boolean) => {
    set((state) => ({
      finalRecipes: {
        ...state.finalRecipes,
        isGenerating,
        hasStartedGeneration: isGenerating || state.finalRecipes.hasStartedGeneration
      },
      lastUpdated: new Date()
    }));
  },

  setFinalRecipesGlobalError: (error: string | null) => {
    set((state) => ({
      finalRecipes: {
        ...state.finalRecipes,
        globalError: error
      },
      lastUpdated: new Date()
    }));
  },

  resetFinalRecipes: () => {
    set((state) => ({
      finalRecipes: {
        morning: {
          recipe: null,
          status: { status: 'idle', retry_count: 0 }
        },
        midDay: {
          recipe: null,
          status: { status: 'idle', retry_count: 0 }
        },
        night: {
          recipe: null,
          status: { status: 'idle', retry_count: 0 }
        },
        isGenerating: false,
        hasStartedGeneration: false,
        globalError: null
      },
      lastUpdated: new Date()
    }));
  },

  // Lookup helper functions to map IDs to names
  getCauseNameById: (id: string): string | null => {
    const state = get();
    const cause = state.selectedCauses.find(c => c.cause_id === id);
    return cause ? cause.cause_name : null;
  },

  getSymptomNameById: (id: string): string | null => {
    const state = get();
    const symptom = state.selectedSymptoms.find(s => s.symptom_id === id);
    return symptom ? symptom.symptom_name : null;
  },

  getCauseNamesByIds: (ids: string[]): string[] => {
    const state = get();
    console.log('🔍 getCauseNamesByIds called with UUIDs:', JSON.stringify(ids));
    console.log('🔍 Available selectedCauses:', JSON.stringify(state.selectedCauses.map(c => ({ id: c.cause_id, name: c.cause_name }))));

    const results = ids
      .map(id => {
        const cause = state.selectedCauses.find(c => c.cause_id === id);
        console.log(`🔍 Looking for cause UUID "${id}": ${cause ? `FOUND "${cause.cause_name}"` : 'NOT FOUND'}`);
        return cause ? cause.cause_name : null;
      })
      .filter((name): name is string => name !== null);

    console.log('🔍 getCauseNamesByIds final result:', results);
    return results;
  },

  getSymptomNamesByIds: (ids: string[]): string[] => {
    const state = get();
    console.log('🔍 getSymptomNamesByIds called with UUIDs:', JSON.stringify(ids));
    console.log('🔍 Available selectedSymptoms:', JSON.stringify(state.selectedSymptoms.map(s => ({ id: s.symptom_id, name: s.symptom_name }))));

    const results = ids
      .map(id => {
        const symptom = state.selectedSymptoms.find(s => s.symptom_id === id);
        console.log(`🔍 Looking for symptom UUID "${id}": ${symptom ? `FOUND "${symptom.symptom_name}"` : 'NOT FOUND'}`);
        return symptom ? symptom.symptom_name : null;
      })
      .filter((name): name is string => name !== null);

    console.log('🔍 getSymptomNamesByIds final result:', results);
    return results;
  }
});
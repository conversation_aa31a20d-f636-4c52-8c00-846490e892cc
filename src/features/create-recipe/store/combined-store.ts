/**
 * Combined Store Implementation
 * 
 * Merges recipe and UI slices into a single unified store
 * using Zustand store slicing pattern.
 */

import { create } from 'zustand';
import { useShallow } from 'zustand/react/shallow';
import { createRecipeSlice, RecipeSlice } from './recipe.slice';
import { createUISlice, UISlice } from './ui.slice';

/**
 * Combined store interface with both recipe and UI slices
 */
export interface CombinedStore extends RecipeSlice, UISlice {}

/**
 * Combined store using Zustand slicing pattern
 * Merges recipe slice and UI slice into a single store
 */
export const useCombinedRecipeStore = create<CombinedStore>()((...args) => ({
  ...createRecipeSlice(...args),
  ...createUISlice(...args),
}));

/**
 * Selector hooks for specific parts of the combined state
 * These hooks maintain the same interface as the original store
 */
export const useRecipeNavigationStore = () => useCombinedRecipeStore((state) => ({
  currentStep: state.currentStep,
  completedSteps: state.completedSteps,
  setCurrentStep: state.setCurrentStep,
  markStepCompleted: state.markStepCompleted,
  canNavigateToStep: state.canNavigateToStep
}));

export const useRecipeData = () => useCombinedRecipeStore((state) => ({
  healthConcern: state.healthConcern,
  demographics: state.demographics,
  selectedCauses: state.selectedCauses,
  selectedSymptoms: state.selectedSymptoms,
  therapeuticProperties: state.therapeuticProperties,
  suggestedOils: state.suggestedOils
}));

export const useRecipeApiData = () => useCombinedRecipeStore((state) => ({
  potentialCauses: state.potentialCauses,
  potentialSymptoms: state.potentialSymptoms,
  setPotentialCauses: state.setPotentialCauses,
  setPotentialSymptoms: state.setPotentialSymptoms
}));

export const useRecipeStatus = () => useCombinedRecipeStore((state) => ({
  isLoading: state.isLoading,
  error: state.error,
  setLoading: state.setLoading,
  setError: state.setError,
  clearError: state.clearError
}));

export const useRecipeStreaming = () => useCombinedRecipeStore(useShallow((state) => ({
  isStreamingCauses: state.isStreamingCauses,
  isStreamingSymptoms: state.isStreamingSymptoms,
  isStreamingProperties: state.isStreamingProperties,
  isStreamingOils: state.isStreamingOils,
  streamingError: state.streamingError,
  setStreamingCauses: state.setStreamingCauses,
  setStreamingSymptoms: state.setStreamingSymptoms,
  setStreamingProperties: state.setStreamingProperties,
  setStreamingOils: state.setStreamingOils,
  setStreamingError: state.setStreamingError,
  clearStreamingError: state.clearStreamingError
})));

/**
 * Utility function to clear all recipe data
 * Calls resetWizard on both slices
 */
export const clearRecipeData = () => {
  try {
    useCombinedRecipeStore.getState().resetWizard();
  } catch (error) {
    console.error('Error clearing recipe data:', error);
  }
};
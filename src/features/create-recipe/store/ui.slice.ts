/**
 * UI State Slice
 * 
 * Manages UI-specific state (isLoading, error, isStreaming, etc.)
 * using Zustand store slicing pattern.
 */

import { StateCreator } from 'zustand';

export interface UISliceState {
  // Loading and error states
  isLoading: boolean;
  error: string | null;

  // AI Streaming states
  isStreamingCauses: boolean;
  isStreamingSymptoms: boolean;
  isStreamingProperties: boolean;
  isStreamingOils: boolean;
  isStreamingFinalRecipes: boolean;
  streamingError: string | null;
}

export interface UISliceActions {
  // State management actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;

  // AI Streaming state management actions
  setStreamingCauses: (isStreaming: boolean) => void;
  setStreamingSymptoms: (isStreaming: boolean) => void;
  setStreamingProperties: (isStreaming: boolean) => void;
  setStreamingOils: (isStreaming: boolean) => void;
  setStreamingFinalRecipes: (isStreaming: boolean) => void;
  setStreamingError: (error: string | null) => void;
  clearStreamingError: () => void;

  // Batched update actions to minimize re-renders
  batchUpdateStreamingState: (updates: {
    isStreamingCauses?: boolean;
    isStreamingSymptoms?: boolean;
    isStreamingProperties?: boolean;
    isStreamingOils?: boolean;
    streamingError?: string | null;
  }) => void;
  batchUpdateLoadingAndError: (updates: {
    isLoading?: boolean;
    error?: string | null;
  }) => void;
}

export type UISlice = UISliceState & UISliceActions;

/**
 * Initial state for the UI slice
 */
const initialUIState: UISliceState = {
  // Loading and error states
  isLoading: false,
  error: null,

  // AI Streaming states
  isStreamingCauses: false,
  isStreamingSymptoms: false,
  isStreamingProperties: false,
  isStreamingOils: false,
  isStreamingFinalRecipes: false,
  streamingError: null,
};

export const createUISlice: StateCreator<
  UISlice,
  [],
  [],
  UISlice
> = (set, get) => ({
  ...initialUIState,

  // State management actions - optimized to prevent unnecessary re-renders
  setLoading: (loading: boolean) => {
    set((state) => {
      // Only update if loading state actually changed
      if (state.isLoading === loading) return state;
      return {
        isLoading: loading
      };
    });
  },

  setError: (error: string | null) => {
    set((state) => {
      // Only update if error actually changed
      if (state.error === error && !state.isLoading) return state;
      return {
        error,
        isLoading: false, // Clear loading when setting error
      };
    });
  },

  clearError: () => {
    set((state) => {
      // Only update if there's actually an error to clear
      if (!state.error) return state;
      return {
        error: null
      };
    });
  },

  // AI Streaming state management actions - optimized to reduce re-renders
  setStreamingCauses: (isStreaming: boolean) => {
    set((state) => {
      // Only update if streaming state actually changed
      if (state.isStreamingCauses === isStreaming) return state;
      return {
        isStreamingCauses: isStreaming,
        streamingError: isStreaming ? null : state.streamingError, // Clear error when starting new stream
      };
    });
  },

  setStreamingSymptoms: (isStreaming: boolean) => {
    set((state) => {
      // Only update if streaming state actually changed
      if (state.isStreamingSymptoms === isStreaming) return state;
      return {
        isStreamingSymptoms: isStreaming,
        streamingError: isStreaming ? null : state.streamingError, // Clear error when starting new stream
      };
    });
  },

  setStreamingProperties: (isStreaming: boolean) => {
    set((state) => {
      // Only update if streaming state actually changed
      if (state.isStreamingProperties === isStreaming) return state;
      return {
        isStreamingProperties: isStreaming,
        streamingError: isStreaming ? null : state.streamingError, // Clear error when starting new stream
      };
    });
  },

  setStreamingOils: (isStreaming: boolean) => {
    set((state) => {
      // Only update if streaming state actually changed
      if (state.isStreamingOils === isStreaming) return state;
      return {
        isStreamingOils: isStreaming,
        streamingError: isStreaming ? null : state.streamingError, // Clear error when starting new stream
      };
    });
  },

  setStreamingFinalRecipes: (isStreaming: boolean) => {
    set((state) => {
      // Only update if streaming state actually changed
      if (state.isStreamingFinalRecipes === isStreaming) return state;
      return {
        isStreamingFinalRecipes: isStreaming,
        streamingError: isStreaming ? null : state.streamingError, // Clear error when starting new stream
      };
    });
  },

  setStreamingError: (error: string | null) => {
    set(() => ({
      streamingError: error,
      isStreamingCauses: false, // Stop streaming on error
      isStreamingSymptoms: false, // Stop streaming on error
      isStreamingProperties: false, // Stop streaming on error
      isStreamingOils: false, // Stop streaming on error
      isStreamingFinalRecipes: false, // Stop streaming on error
    }));
  },

  clearStreamingError: () => {
    set(() => ({
      streamingError: null
    }));
  },

  // Batched update actions to minimize re-renders
  batchUpdateStreamingState: (updates: {
    isStreamingCauses?: boolean;
    isStreamingSymptoms?: boolean;
    isStreamingProperties?: boolean;
    isStreamingOils?: boolean;
    streamingError?: string | null;
  }) => {
    set((state) => {
      const newState = { ...state };
      let hasChanges = false;

      // Only update fields that have actually changed
      if (updates.isStreamingCauses !== undefined && state.isStreamingCauses !== updates.isStreamingCauses) {
        newState.isStreamingCauses = updates.isStreamingCauses;
        hasChanges = true;
      }
      if (updates.isStreamingSymptoms !== undefined && state.isStreamingSymptoms !== updates.isStreamingSymptoms) {
        newState.isStreamingSymptoms = updates.isStreamingSymptoms;
        hasChanges = true;
      }
      if (updates.isStreamingProperties !== undefined && state.isStreamingProperties !== updates.isStreamingProperties) {
        newState.isStreamingProperties = updates.isStreamingProperties;
        hasChanges = true;
      }
      if (updates.isStreamingOils !== undefined && state.isStreamingOils !== updates.isStreamingOils) {
        newState.isStreamingOils = updates.isStreamingOils;
        hasChanges = true;
      }
      if (updates.streamingError !== undefined && state.streamingError !== updates.streamingError) {
        newState.streamingError = updates.streamingError;
        hasChanges = true;
      }

      // Only return new state if there were actual changes
      if (hasChanges) {
        return newState;
      }

      return state; // No changes, return existing state
    });
  },

  batchUpdateLoadingAndError: (updates: {
    isLoading?: boolean;
    error?: string | null;
  }) => {
    set((state) => {
      const newState = { ...state };
      let hasChanges = false;

      if (updates.isLoading !== undefined && state.isLoading !== updates.isLoading) {
        newState.isLoading = updates.isLoading;
        hasChanges = true;
      }
      if (updates.error !== undefined && state.error !== updates.error) {
        newState.error = updates.error;
        hasChanges = true;
      }

      // Only return new state if there were actual changes
      if (hasChanges) {
        return newState;
      }

      return state; // No changes, return existing state
    });
  }
});
import React, { useState, useCallback, useEffect, useRef } from 'react';
import { View, TouchableWithoutFeedback, Keyboard } from 'react-native';
import { useTheme, useLanguage } from '@/shared/hooks';
import { useCombinedRecipeStore } from '@/features/create-recipe/store/combined-store';
import { SelectableDataList } from '@/shared/components';
import { useRecipeNavigation } from '@/features/create-recipe/hooks';
import { causesSelectionSchema } from '@/features/create-recipe/schemas';
import { SELECTION_REQUIREMENTS } from '@/features/create-recipe/constants';
import { StreamingBottomSheet } from '@/shared/components/modals/streaming';
import type { StreamingItem } from '@/shared/components/modals/streaming/types';
import type { PotentialCause } from '@/features/create-recipe/types';
import { useTranslation } from 'react-i18next';
import { EnhancedCausesHeader } from './enhanced-causes-header';
import { haptics } from '@/shared/utils/haptics';

interface CausesSelectionProps {
  onValidationChange?: (isValid: boolean) => void;
  onSubmitReady?: (submitFn: () => void) => void;
  onLoadingChange?: (isLoading: boolean) => void;
}

export const CausesSelection: React.FC<CausesSelectionProps> = ({
  onValidationChange,
  onSubmitReady,
  onLoadingChange
}) => {
  const { t } = useTranslation('create-recipe');
  const theme = useTheme();
  const { language: apiLanguage } = useLanguage();
  const { completeStepAndGoNext } = useRecipeNavigation();
  const {
    potentialCauses,
    selectedCauses,
    updateSelectedCauses
  } = useCombinedRecipeStore();

  // Handle selection validation changes from SelectableDataList (for future extensibility)
  const handleSelectionValidChange = useCallback((isValid: boolean) => {
    // Currently using existing validation system via useStepScreenCoordination
    // This callback is available for future enhancements
    console.log('🔍 [CausesSelection] Selection validity changed:', isValid);
  }, []);

  // Debug store subscription
  console.log('🔍 [CausesSelection] Store subscription debug:', {
    renderTimestamp: new Date().toISOString(),
    selectedCausesFromStore: selectedCauses.length,
    potentialCausesFromStore: potentialCauses.length
  });

  const causesToDisplay = potentialCauses;
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showStreamingModal, setShowStreamingModal] = useState(false);
  const [streamingItems, setStreamingItems] = useState<StreamingItem[]>([]);
  const [isStreaming, setIsStreaming] = useState(false);

  const selectionCount = selectedCauses.length;
  const minSelection = SELECTION_REQUIREMENTS.causes.min;
  const maxSelection = SELECTION_REQUIREMENTS.causes.max;

  // Debug logging for counter display issue
  console.log('🔍 [CausesSelection] Counter Debug:', {
    selectedCauses: selectedCauses.length,
    selectionCount,
    minSelection,
    maxSelection,
    SELECTION_REQUIREMENTS_CAUSES: SELECTION_REQUIREMENTS.causes,
    selectedCausesArray: selectedCauses.map(c => ({ id: c.cause_id, name: c.cause_name }))
  });
  
  const canSubmit = selectionCount >= minSelection && selectionCount <= maxSelection;
  const prevCanSubmit = useRef(canSubmit);
  const prevIsLoading = useRef(isSubmitting || isStreaming);
  const prevSubmitFn = useRef<(() => void) | null>(null);

  const validateSelection = useCallback(() => {
    try {
      causesSelectionSchema.parse({ selectedCauses });
      setErrors({});
      return true;
    } catch (error: any) {
      if (__DEV__) { console.error('❌ Validation error:', error); }
      const newErrors: Record<string, string> = {};
      if (error.errors) {
        error.errors.forEach((err: any) => { newErrors[err.path[0]] = err.message; });
      }
      setErrors(newErrors);
      return false;
    }
  }, [selectedCauses]);

  const handleCauseToggle = (cause: PotentialCause, isSelected: boolean) => {
    console.log('🔍 [CausesSelection] Toggle Debug - Before:', {
      causeId: cause.cause_id,
      causeName: cause.cause_name,
      isSelected,
      currentSelectionCount: selectionCount,
      maxSelection
    });
    
    if (isSelected) {
      // Add to selection (only if under limit)
      if (selectionCount < maxSelection) {
        const newSelectedCauses = [...selectedCauses, cause];
        console.log('🔍 [CausesSelection] Toggle Debug - Adding:', {
          addingCause: cause.cause_name,
          newSelectionCount: newSelectedCauses.length
        });
        updateSelectedCauses(newSelectedCauses);
      }
    } else {
      // Remove from selection
      const newSelectedCauses = selectedCauses.filter(c => c.cause_id !== cause.cause_id);
      console.log('🔍 [CausesSelection] Toggle Debug - Removing:', {
        removingCause: cause.cause_name,
        newSelectionCount: newSelectedCauses.length
      });
      updateSelectedCauses(newSelectedCauses);
    }
    
    // Clear errors when user makes changes
    if (errors.selectedCauses) {
      setErrors(prev => ({ ...prev, selectedCauses: '' }));
    }
  };

  const streamPotentialSymptoms = useCallback(async () => {
    const { healthConcern, demographics } = useCombinedRecipeStore.getState();
    if (!healthConcern || !demographics) {
      if (__DEV__) {
        console.error('❌ Missing health concern or demographics data for symptoms API call');
      }
      return;
    }
    setShowStreamingModal(true);
    setIsStreaming(true);
    setStreamingItems([]);
    try {
      const apiRequestData = {
        health_concern: healthConcern.healthConcern.trim(),
        gender: demographics.gender,
        age_category: demographics.ageCategory,
        age_specific: demographics.specificAge.toString(),
        user_language: apiLanguage,
      };
      const streamingRequest = {
        feature: 'create-recipe' as const,
        step: 'potential-symptoms' as const,
        data: { ...apiRequestData, selected_causes: selectedCauses, selected_symptoms: [] },
      };
      const { streamRecipeStep } = await import('@/shared/services/api/rotinanatural-client');
      const response = await streamRecipeStep(streamingRequest);
      if (!response.body) { throw new Error('No response body available for streaming'); }
      const { readSseStream } = await import('@/shared/utils/sse-reader');
      for await (const dataString of readSseStream(response.body)) {
        try {
          const parsedData = JSON.parse(dataString);
          if (parsedData.type === 'structured_data' && parsedData.field === 'potential_symptoms') {
            const item = {
              id: parsedData.data.symptom_id,
              symptom_id: parsedData.data.symptom_id,
              title: parsedData.data.name_localized,
              subtitle: parsedData.data.suggestion_localized,
              description: parsedData.data.explanation_localized,
              suggestion: parsedData.data.suggestion_localized,
            };
            setStreamingItems(prev => {
              const exists = prev.some(existingItem => existingItem.id === item.id);
              return exists ? prev : [...prev, item];
            });
          }
          if (parsedData.type === 'structured_complete') {
            setIsStreaming(false);
            const completePotentialSymptoms = parsedData.data?.data?.potential_symptoms || [];
            const potentialSymptoms = completePotentialSymptoms.map((item: any, index: number) => ({
              symptom_id: item.symptom_id || `symptom-${index}`,
              symptom_name: item.name_localized || '',
              symptom_suggestion: item.suggestion_localized || '',
              explanation: item.explanation_localized || '',
            }));
            const { setPotentialSymptoms } = useCombinedRecipeStore.getState();
            setPotentialSymptoms(potentialSymptoms);
            haptics.success(); // Success haptic when modal closes after completion
            setShowStreamingModal(false);
            completeStepAndGoNext();
            break;
          }
        } catch (parseError) { 
          if (__DEV__) {
            console.warn('Failed to parse symptoms streaming data:', dataString, parseError);
          }
        }
      }
    } catch (error) {
      if (__DEV__) {
        console.error('❌ Symptoms API streaming failed:', error);
      }
      setIsStreaming(false);
    }
  }, [apiLanguage, selectedCauses, completeStepAndGoNext]);

  const handleSubmit = useCallback(async () => {
    if (!validateSelection()) { return; }
    setIsSubmitting(true);
    try {
      await streamPotentialSymptoms();
    } catch (error) {
      console.error('❌ Error submitting causes selection:', error);
    } finally {
      setIsSubmitting(false);
    }
  }, [validateSelection, streamPotentialSymptoms]);
  
  useEffect(() => { if (onValidationChange && canSubmit !== prevCanSubmit.current) { prevCanSubmit.current = canSubmit; onValidationChange(canSubmit); } }, [onValidationChange, canSubmit]);
  useEffect(() => { if (onSubmitReady && prevSubmitFn.current !== handleSubmit) { prevSubmitFn.current = handleSubmit; onSubmitReady(handleSubmit); } }, [onSubmitReady, handleSubmit]);
  useEffect(() => { const currentLoading = isSubmitting || isStreaming; if (onLoadingChange && currentLoading !== prevIsLoading.current) { prevIsLoading.current = currentLoading; onLoadingChange(currentLoading); } }, [onLoadingChange, isSubmitting, isStreaming]);

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <View style={{ flex: 1, backgroundColor: theme.colors.background }}>
        
        <EnhancedCausesHeader />
        
        <SelectableDataList
          items={causesToDisplay}
          selectedItems={selectedCauses}
          onItemToggle={handleCauseToggle}
          maxSelection={maxSelection}
          minSelection={minSelection}
          onSelectionValidChange={handleSelectionValidChange}
          selectable={true}
          getItemId={(cause) => cause.cause_id}
          getItemTitle={(cause) => cause.cause_name}
          getItemDescription={(cause) => cause.cause_suggestion}
          getItemSubtitle={(cause) => cause.explanation}
          showSubtitle={true}
          showTitleBadges={false}
          showLeftComponent={false}
          translationKeys={{
            subtitlePrefix: '',
            subtitleSuffix: '',
            counterPrefix: t('causes.counterPrefix'),
            emptyStateTitle: t('common.noCausesAvailable'),
            emptyStateMessage: t('common.completeDemographics')
          }}
          errors={errors}
          errorKey="selectedCauses"
        />

        <StreamingBottomSheet
          visible={showStreamingModal}
          onDismiss={useCallback(() => setShowStreamingModal(false), [])}
          title={t('modals.analyzingSymptoms.title')}
          description={t('modals.analyzingSymptoms.description')}
          items={streamingItems}
          analysisType="symptoms"
          modalState={
            isStreaming && streamingItems.length === 0 ? 'loading' :
            isStreaming ? 'streaming' : 
            !isStreaming && streamingItems.length > 0 ? 'completed' : 'loading'
          }
          isComplete={!isStreaming && streamingItems.length > 0}
          hapticType="none"
          enableBlur={true}
          enableStackAnimation={true}
          enableSwipeToDismiss={false}
        />
      </View>
    </TouchableWithoutFeedback>
  );
};
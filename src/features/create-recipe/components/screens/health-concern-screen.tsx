/**
 * @fileoverview Health Concern Step Screen - V2.2 Direct Input (Logic Preserved)
 * * This is the final, corrected version. It passes all necessary logic (validation state,
 * character count) to the child component to ensure the UI swap preserves 100% of
 * the original feature's functionality.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { View, TouchableWithoutFeedback, Keyboard, KeyboardAvoidingView, Platform } from 'react-native';
import { Text, Chip } from 'react-native-paper';
import { useUser } from '@clerk/clerk-expo';
import { ScreenWrapper } from '@/shared/components/layout/screen-wrapper';
import { ChatInputBar } from '@/shared/components/layout/chat-input-bar';
import { useCombinedRecipeStore } from '@/features/create-recipe/store/combined-store';
import { useRecipeNavigation } from '@/features/create-recipe/hooks';
import { healthConcernSchema } from '@/features/create-recipe/schemas';
import { HEALTH_CONCERN_VALIDATION } from '@/features/create-recipe/constants';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/shared/hooks/use-theme';
import { haptics } from '@/shared/utils/haptics';
import type { HealthConcernData } from '@/features/create-recipe/types';

export default function HealthConcernScreen() {
  const { completeStepAndGoNext } = useRecipeNavigation();
  const { healthConcern, updateHealthConcern } = useCombinedRecipeStore();
  const { user } = useUser();
  const { t } = useTranslation('create-recipe');
  const theme = useTheme();

  // PRESERVED LOGIC: All original state management is kept.
  const [inputText, setInputText] = useState(healthConcern?.healthConcern || '');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasStartedTyping, setHasStartedTyping] = useState(!!healthConcern?.healthConcern);

  useEffect(() => {
    setInputText(healthConcern?.healthConcern || '');
    setHasStartedTyping(!!healthConcern?.healthConcern);
    setIsSubmitting(false);
  }, [healthConcern]);

  // PRESERVED LOGIC: Validation function remains the same.
  const isInputValid = (text: string) => {
    try {
      healthConcernSchema.parse({ healthConcern: text });
      return text.trim().length >= 3;
    } catch {
      return false;
    }
  };

  const handleInputChange = (text: string) => {
    setInputText(text);
    if (!hasStartedTyping && text.length > 0) {
      setHasStartedTyping(true);
    }
  };
  
  // PRESERVED LOGIC: The core submission and navigation logic is identical.
  const handleSendMessage = useCallback(async () => {
    if (!isInputValid(inputText) || isSubmitting) return;

    setIsSubmitting(true);
    try {
      const formData: HealthConcernData = { healthConcern: inputText.trim() };
      updateHealthConcern(formData);
      completeStepAndGoNext();
    } catch (error) {
      console.error('Error submitting health concern:', error);
      haptics.error();
    } finally {
      setIsSubmitting(false);
    }
  }, [inputText, isSubmitting, updateHealthConcern, completeStepAndGoNext]);
  
  const handleSuggestionPress = useCallback((suggestionText: string) => {
    haptics.light();
    handleInputChange(suggestionText);
  }, []);

  const suggestionData = t('healthConcern.suggestions', { returnObjects: true }) as { text: string; expanded: string }[];
  const suggestions = Array.isArray(suggestionData) ? suggestionData : [];

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? -20 : 20}
    >
      <ScreenWrapper
        showAppBar={true}
        appBarProps={{
          title: t('healthConcern.title'),
          showBackButton: true,
          titleStyle: { color: theme.colors.primary },
        }}
        scrollable={false}
      >
        <View style={{ flex: 1 }}>
          <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
            <View style={{ flex: 1, justifyContent: 'center', paddingHorizontal: theme.spacing.screenPadding }}>
              <Text 
                variant="headlineLarge" 
                style={{ textAlign: 'center', color: theme.colors.primary, marginBottom: theme.spacing.xl }}
              >
                {t('healthConcern.greeting')}{user?.firstName ? `, ${user.firstName}` : ''}!
              </Text>
              <Text 
                variant="titleMedium" 
                style={{ textAlign: 'center', color: theme.colors.onSurfaceVariant }}
              >
                {t('healthConcern.subtitle')}
              </Text>
            </View>
          </TouchableWithoutFeedback>

          {/* UPDATED UI: Wrapping View for suggestions */}
          {!hasStartedTyping && (
            <View style={{ paddingBottom: theme.spacing.sm, paddingHorizontal: theme.spacing.md, flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'center', gap: theme.spacing.sm }}>
              {suggestions.map((suggestion, index) => (
                <Chip 
                  key={index} 
                  onPress={() => handleSuggestionPress(suggestion.expanded)}
                >
                  {suggestion.text}
                </Chip>
              ))}
            </View>
          )}
        </View>

        <ChatInputBar
          placeholder={t('healthConcern.placeholder')}
          value={inputText}
          onChangeText={handleInputChange}
          onSendMessage={handleSendMessage}
          disabled={isSubmitting}
          maxLength={HEALTH_CONCERN_VALIDATION.maxLength}
          isSendEnabled={isInputValid(inputText)}
          // **RESTORED: Pass character count to preserve original logic**
          characterCount={inputText.length}
        />
      </ScreenWrapper>
    </KeyboardAvoidingView>
  );
}
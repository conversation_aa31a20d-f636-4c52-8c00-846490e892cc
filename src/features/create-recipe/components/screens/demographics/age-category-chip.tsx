import React from 'react';
import { View } from 'react-native';
import { Chip } from 'react-native-paper';
import { useTheme } from '@/shared/hooks/use-theme';
import { useTranslation } from 'react-i18next';
import { createAgeCategoryOptions } from '@/features/create-recipe/constants';

interface AgeCategoryChipProps {
  /**
   * The determined age category value
   */
  ageCategory: string;
  /**
   * Whether to show the age category chip
   */
  visible?: boolean;
  /**
   * Optional style overrides
   */
  style?: any;
}

/**
 * AgeCategoryChip Component
 * 
 * Displays a read-only chip showing the auto-determined age category
 * with Material Design 3 styling that matches React Native Paper theme.
 */
export const AgeCategoryChip: React.FC<AgeCategoryChipProps> = ({
  ageCategory,
  visible = true,
  style
}) => {
  const theme = useTheme();
  const { t } = useTranslation('create-recipe');

  if (!visible || !ageCategory) {
    return null;
  }

  // Get age category options with translated labels
  const ageCategoryOptions = createAgeCategoryOptions(t);
  const categoryOption = ageCategoryOptions.find(option => option.value === ageCategory);
  const displayLabel = categoryOption?.label || t('ageCategoryOptions.unknown');

  return (
    <View style={[{ overflow: 'hidden' }, style]}>
      <Chip 
        icon="information-outline"
        mode="outlined"
        style={{
          alignSelf: 'flex-start',
          marginTop: 8,
        }}
        textStyle={{ 
          color: theme.colors.onSurfaceVariant,
          fontSize: 12
        }}
        disabled
        compact
      >
        {t('demographics.category')}: {displayLabel}
      </Chip>
    </View>
  );
};
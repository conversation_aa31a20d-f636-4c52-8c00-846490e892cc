import React, { useState, useCallback } from 'react';
import { View, TouchableWithoutFeedback, Keyboard } from 'react-native';
import Slider from '@react-native-community/slider';
import { 
  Text, 
  IconButton,
  Surface
} from 'react-native-paper';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslation } from 'react-i18next';

import { useTheme } from '@/shared/hooks/use-theme';
import { StandardizedSegmentedButtons, SegmentedButtonPresets } from '@/shared/components/ui';
import { useLanguage } from '@/shared/hooks';
import { useCombinedRecipeStore } from '@/features/create-recipe/store/combined-store';
import { useRecipeNavigation } from '@/features/create-recipe/hooks';
import { demographicsSchema } from '@/features/create-recipe/schemas';
import { createGenderOptions, determineAgeCategory, TOUCH_TARGETS, AGE_VALIDATION } from '@/features/create-recipe/constants';
import { haptics } from '@/shared/utils/haptics';
import type { StreamingItem } from '@/shared/components/modals/streaming/types';
import { StreamingBottomSheet } from '@/shared/components/modals/streaming';
import { AgeCategoryChip } from './age-category-chip';

import type { DemographicsData } from '@/features/create-recipe/types';

/**
 * Demographics Form Component
 * Second step in the recipe creation wizard
 * 
 * Uses React Native Paper components with Material Design 3 theming
 * Integrates Zod validation with Paper form components
 */

interface DemographicsFormProps {
  onValidationChange?: (isValid: boolean, data?: Pick<DemographicsData, 'gender' | 'specificAge'>) => void;
  onSubmitReady?: (submitFn: () => void) => void;
  onLoadingChange?: (isLoading: boolean) => void;
}

export const DemographicsForm: React.FC<DemographicsFormProps> = ({
  onValidationChange,
  onSubmitReady,
  onLoadingChange
}) => {
  const { t } = useTranslation('create-recipe');
  const theme = useTheme();
  const { apiLanguage } = useLanguage();
  const { completeStepAndGoNext } = useRecipeNavigation();
  const { 
    healthConcern,
    demographics, 
    updateDemographics,
    setPotentialCauses 
  } = useCombinedRecipeStore();

  // React Hook Form setup - PRESERVED FROM ORIGINAL
  const {
    control,
    handleSubmit,
    watch,
    formState: { errors: formErrors, isSubmitting, isValid },
  } = useForm<Pick<DemographicsData, 'gender' | 'specificAge'>>({
    resolver: zodResolver(demographicsSchema),
    defaultValues: {
      gender: demographics?.gender || 'female',
      specificAge: demographics?.specificAge || (AGE_VALIDATION.min + (AGE_VALIDATION.max - AGE_VALIDATION.min) / 5),
    },
    mode: 'onTouched',
  });

  // Watch age to auto-determine category - PRESERVED FROM ORIGINAL
  const currentAge = watch('specificAge');
  
  // Streaming modal state - PRESERVED FROM ORIGINAL (Complex streaming requires custom modal)
  const [showStreamingModal, setShowStreamingModal] = useState(false);
  const [streamingItems, setStreamingItems] = useState<StreamingItem[]>([]);
  const [isStreaming, setIsStreaming] = useState(false);
  
  // Track modal visibility state changes - PRESERVED FROM ORIGINAL
  React.useEffect(() => {
    console.log('🚀 MODAL VISIBILITY CHANGED:', {
      showStreamingModal,
      timestamp: new Date().toISOString(),
      component: 'DemographicsFormV2'
    });
  }, [showStreamingModal]);
  
  // NEW: Notify parent of validation state changes
  React.useEffect(() => {
    const gender = watch('gender');
    const specificAge = watch('specificAge');
    if (onValidationChange) {
      onValidationChange(isValid, { gender, specificAge });
      console.log('Form validation changed:', { isValid, gender, specificAge });
    }
  }, [isValid, watch, onValidationChange]);
  
  // NEW: Provide form submit function to parent
  React.useEffect(() => {
    if (onSubmitReady) {
      const submitWrapper = () => {
        console.log('Form submission triggered from parent');
        handleSubmit(onSubmit)();
      };
      onSubmitReady(submitWrapper);
    }
  }, [onSubmitReady, handleSubmit, onSubmit]);
  
  // NEW: Notify parent of loading state changes
  React.useEffect(() => {
    if (onLoadingChange) {
      onLoadingChange(isSubmitting || isStreaming);
      console.log('Form loading state changed:', { isSubmitting, isStreaming });
    }
  }, [onLoadingChange, isSubmitting, isStreaming]);
  
  // Auto-determine age category from current age - PRESERVED FROM ORIGINAL
  const determinedAgeCategory = currentAge ? determineAgeCategory(currentAge) : null;

  // Real API streaming functionality for potential causes - PRESERVED FROM ORIGINAL
  const streamPotentialCauses = async () => {
    if (!healthConcern) {
      console.error('❌ No health concern data available for API call');
      return;
    }

    console.log('🚀 Starting real API streaming for potential causes...');
    console.log('🎬 SETTING MODAL VISIBLE - BEFORE:', { showStreamingModal });
    setShowStreamingModal(true);
    console.log('🎬 SETTING MODAL VISIBLE - AFTER setShowStreamingModal(true) called');
    setIsStreaming(true);
    setStreamingItems([]); // Clear previous items

    try {
      console.log('🌐 [DemographicsFormV2] Using user language:', { apiLanguage });

      // Create API request data with auto-determined age category
      const apiRequestData = {
        health_concern: healthConcern.healthConcern.trim(),
        gender: currentAge ? watch('gender') : 'female',
        age_category: determinedAgeCategory || 'adult',
        age_specific: currentAge ? currentAge.toString() : '25',
        user_language: apiLanguage,
      };

      console.log('📤 API Request Data:', JSON.stringify(apiRequestData, null, 2));

      // Use the platform-aware streamRecipeStep directly (same as debug screen)
      const { streamRecipeStep } = await import('@/shared/services/api/rotinanatural-client');
      const response = await streamRecipeStep({
        feature: 'create-recipe',
        step: 'potential-causes',
        data: apiRequestData,
      });

      console.log('📡 Response received:', {
        status: response.status,
        bodyAvailable: !!response.body,
        bodyType: response.body?.constructor.name
      });

      if (!response.body) {
        throw new Error('Response body not available for streaming');
      }

      // Process the stream using the same approach as debug screen
      const { readSseStream } = await import('@/shared/utils/sse-reader');
      let itemCount = 0;
      const receivedItems: StreamingItem[] = [];

      for await (const dataString of readSseStream(response.body)) {
        itemCount++;

        console.log(`📨 Received streaming item ${itemCount}:`, {
          dataLength: dataString.length,
          dataPreview: dataString.substring(0, 100)
        });

        try {
          const parsedData = JSON.parse(dataString);

          // Handle structured data items
          if (parsedData.type === 'structured_data' && parsedData.field === 'potential_causes') {
            const item: StreamingItem = {
              id: parsedData.data.cause_id,
              cause_id: parsedData.data.cause_id,
              title: parsedData.data.name_localized,
              subtitle: parsedData.data.suggestion_localized,  // suggestion goes to subtitle for display
              description: parsedData.data.explanation_localized,  // explanation for detailed info
              suggestion: parsedData.data.suggestion_localized,  // keep suggestion for store compatibility
            };

            receivedItems.push(item);

            // Update UI immediately
            setStreamingItems(prev => {
              const exists = prev.some(existingItem =>
                existingItem.id === item.id ||
                (existingItem.title === item.title && existingItem.subtitle === item.subtitle)
              );
              if (exists) return prev;
              return [...prev, item];
            });
          }

          // Handle completion
          if (parsedData.type === 'structured_complete') {
            console.log('✅ Streaming completed with structured_complete');
            console.log('🔍 STRUCTURED_COMPLETE DATA:', JSON.stringify(parsedData.data, null, 2));
            setIsStreaming(false);

            // Use structured_complete data instead of receivedItems to get complete data
            const completePotentialCauses = parsedData.data?.data?.potential_causes || [];
            console.log('✅ DemographicsFormV2: Streaming completed with', completePotentialCauses.length, 'items');
            console.log('📋 Final complete items (from structured_complete):', completePotentialCauses);

            // Use structured_complete data to update the store with correct PotentialCause interface mapping
            const potentialCauses = completePotentialCauses.map((item: any, index: number) => ({
              cause_id: item.cause_id || `cause-${index}`,
              cause_name: item.name_localized || '',
              cause_suggestion: item.suggestion_localized || '',
              explanation: item.explanation_localized || '',
            }));

            setPotentialCauses(potentialCauses);
            console.log('💾 Stored potential causes with correct interface mapping:', potentialCauses);
            console.log('🔍 First cause example:', potentialCauses[0]);

            // Close modal immediately and navigate (same pattern as symptoms selection)
            haptics.success(); // Success haptic when modal closes after completion
            setShowStreamingModal(false);
            completeStepAndGoNext();

            break;
          }
        } catch (parseError) {
          console.warn('Failed to parse streaming data:', dataString, parseError);
        }
      }

      console.log('🎯 Streaming completed successfully');

    } catch (error) {
      console.error('❌ API streaming failed:', error);
      setIsStreaming(false);
    }
  };

  // Handle form submission with React Hook Form - PRESERVED FROM ORIGINAL
  const onSubmit = async (formData: Pick<DemographicsData, 'gender' | 'specificAge'>) => {
    try {
      // Create complete demographics data with auto-determined age category
      const completeData: DemographicsData = {
        ...formData,
        ageCategory: determinedAgeCategory || 'adult',
      };
      
      // Update store with complete data including computed age category
      updateDemographics(completeData);
      
      // Real API: Show streaming modal for potential causes analysis
      await streamPotentialCauses();
      
    } catch (error) {
      console.error('Error submitting demographics:', error);
    }
  };


  // UI Demo state - REMOVED: now connected to React Hook Form
  const [demoAge, setDemoAge] = useState(AGE_VALIDATION.min + (AGE_VALIDATION.max - AGE_VALIDATION.min) / 5); // Start at ~25 (20% through range)
  const MIN_AGE = AGE_VALIDATION.min;
  const MAX_AGE = AGE_VALIDATION.max;

  return (
    <View style={{ flex: 1 }}>
      <TouchableWithoutFeedback onPress={() => {
        haptics.light();
        Keyboard.dismiss();
      }}>
        <View style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          paddingHorizontal: theme.spacing.screenPadding,
        }}>
          {/* Simple centered greeting - Modern chat style */}
          <Text 
            variant="headlineLarge" 
            style={{
              textAlign: 'center',
              color: theme.colors.primary,
              marginBottom: theme.spacing.xl,
            }}
          >
            {t('forms.tellUsAboutYourself')}
          </Text>
          
          {/* Gender Selection */}
          <View style={{ marginBottom: theme.spacing.xl, width: '100%', maxWidth: 300 }}>
            <Text 
              variant="titleMedium" 
              style={{
                textAlign: 'center',
                color: theme.colors.onSurface,
                marginBottom: theme.spacing.md,
              }}
            >
              {t('forms.gender')}
            </Text>
            <Controller
              name="gender"
              control={control}
              render={({ field: { onChange, value } }) => (
                <StandardizedSegmentedButtons
                  value={value}
                  onValueChange={(selectedValue) => {
                    haptics.light();
                    onChange(selectedValue);
                  }}
                  buttons={SegmentedButtonPresets.gender(t)}
                />
              )}
            />
          </View>

          {/* Age Selection - Dual Control System */}
          <View style={{ marginBottom: theme.spacing.xl, width: '100%', maxWidth: 300 }}>
            <Text 
              variant="titleMedium" 
              style={{
                textAlign: 'center',
                color: theme.colors.onSurface,
                marginBottom: theme.spacing.md,
              }}
            >
              {t('forms.yourAge')}
            </Text>
            
            {/* Micro Control - Stepper Buttons */}
            <View style={{
              flexDirection: 'row', 
              alignItems: 'center', 
              justifyContent: 'center', 
              gap: theme.spacing.lg,
              marginBottom: theme.spacing.md,
            }}>
              <IconButton 
                icon="minus" 
                mode="contained-tonal"
                style={{ 
                  minHeight: TOUCH_TARGETS.MINIMUM_SIZE, 
                  minWidth: TOUCH_TARGETS.MINIMUM_SIZE
                }}
                onPress={() => {
                  haptics.light();
                  setDemoAge(Math.max(MIN_AGE, demoAge - 1));
                }}
                disabled={demoAge <= MIN_AGE}
              />
              
              <View style={{ alignItems: 'center' }}>
                <Surface 
                  mode="flat"
                  style={{
                    paddingVertical: theme.spacing.lg,
                    paddingHorizontal: theme.spacing.xl, // More horizontal space for 3 digits
                    width: theme.spacing.xl * 5, // 5x xl spacing = enough room for "120" in displaySmall
                    alignItems: 'center', 
                    borderRadius: theme.spacing.md,
                    backgroundColor: theme.colors.surfaceVariant,
                  }}
                >
                  <Text 
                    variant="displaySmall" 
                    style={{ 
                      color: theme.colors.onSurfaceVariant,
                      fontWeight: 'bold',
                      textAlign: 'center', // Center text within fixed container
                      fontVariant: ['tabular-nums'] // Monospace numbers prevent width changes
                    }}
                  >
                    {demoAge}
                  </Text>
                </Surface>
                <Text variant="bodyMedium" style={{ 
                  color: theme.colors.onSurfaceVariant, 
                  marginTop: theme.spacing.xs,
                  fontWeight: '500'
                }}>
                  {t('ageUnit')}
                </Text>
              </View>
              
              <IconButton 
                icon="plus" 
                mode="contained-tonal"
                style={{ 
                  minHeight: TOUCH_TARGETS.MINIMUM_SIZE, 
                  minWidth: TOUCH_TARGETS.MINIMUM_SIZE
                }}
                onPress={() => {
                  haptics.light();
                  setDemoAge(Math.min(MAX_AGE, demoAge + 1));
                }}
                disabled={demoAge >= MAX_AGE}
              />
            </View>

            {/* Macro Control - Slider */}
            <Slider
              style={{ width: '100%', height: 40 }}
              minimumValue={MIN_AGE}
              maximumValue={MAX_AGE}
              value={demoAge}
              onValueChange={(value) => {
                const roundedValue = Math.round(value);
                if (roundedValue !== demoAge) {
                  haptics.light();
                  setDemoAge(roundedValue);
                }
              }}
              onSlidingComplete={() => haptics.medium()}
              step={1}
              minimumTrackTintColor={theme.colors.primary}
              maximumTrackTintColor={theme.colors.outline}
              thumbStyle={{ backgroundColor: theme.colors.primary }}
            />
            
            {/* Auto-determined Age Category Chip */}
            <View style={{ alignItems: 'center', marginTop: theme.spacing.md }}>
              <AgeCategoryChip 
                ageCategory={determineAgeCategory(demoAge) || ''}
                visible={!!demoAge && !!determineAgeCategory(demoAge)}
                style={{ alignSelf: 'center' }}
              />
            </View>
          </View>
        </View>
      </TouchableWithoutFeedback>
      
      {/* AI Streaming Modal - REFACTORED TO BOTTOM SHEET */}
      <StreamingBottomSheet
        visible={showStreamingModal}
        onDismiss={useCallback(() => {
          console.log('🎭 DemographicsFormV2 - StreamingBottomSheet onDismiss called - only closing modal');
          setShowStreamingModal(false);
          // Navigation is handled in structured_complete callback, not here
        }, [setShowStreamingModal])}
        title={t('errors.analyzingCauses')}
        description={t('errors.processingDemographics')}
        items={streamingItems}
        analysisType="causes"
        modalState={
          isStreaming && streamingItems.length === 0 ? 'loading' :
          isStreaming ? 'streaming' : 
          !isStreaming && streamingItems.length > 0 ? 'completed' : 'loading'
        }
        isComplete={!isStreaming && streamingItems.length > 0}
        hapticType="none"
        enableBlur={true}
        enableStackAnimation={true}
        enableSwipeToDismiss={false} // Standard: auto-close on completion, no swipe dismiss
      />
    </View>
  );
};

export default DemographicsForm;
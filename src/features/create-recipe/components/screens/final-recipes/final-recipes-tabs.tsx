import { View } from 'react-native';
import { Text, Chip, IconButton } from 'react-native-paper';
import { useTheme } from '@/shared/hooks/use-theme';
import type { RecipeTimeSlot } from '@/features/create-recipe/types';
import { ProtocolSummaryCard } from './final-recipes-cards';
import { useTranslation } from 'react-i18next';

// Overview Tab Component
interface OverviewTabProps {
  healthConcern: any;
  demographics: any;
  selectedCauses: any[];
  selectedSymptoms: any[];
  finalRecipes: any;
  onSwitchToRecipes: (protocol?: RecipeTimeSlot) => void;
}

export function OverviewTab({ 
  healthConcern, 
  demographics, 
  selectedCauses, 
  selectedSymptoms, 
  finalRecipes, 
  onSwitchToRecipes 
}: OverviewTabProps) {
  const theme = useTheme();
  const { t } = useTranslation('create-recipe');
  
  return (
    <View style={{ flex: 1 }}>
      {/* User Profile Section */}
      <View style={{ marginBottom: theme.spacing.lg }}>
        <Text 
          variant="titleLarge" 
          style={{
            fontWeight: 'bold',
            marginBottom: theme.spacing.lg,
            color: theme.colors.onSurface,
            paddingHorizontal: theme.spacing.screenPadding
          }}
        >
          {t('finalRecipes.sections.userProfile')}
        </Text>
        <View style={{ paddingHorizontal: theme.spacing.screenPadding, marginBottom: theme.spacing.lg }}>
          <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, marginBottom: theme.spacing.sm }}>
            <Text style={{ fontWeight: 'bold' }}>{t('finalRecipes.labels.condition')}:</Text> {healthConcern?.healthConcern || t('finalRecipes.labels.notProvided')}
          </Text>
          <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, marginBottom: theme.spacing.sm }}>
            <Text style={{ fontWeight: 'bold' }}>{t('finalRecipes.labels.age')}:</Text> {demographics?.specificAge || t('finalRecipes.labels.notProvided')} {t('finalRecipes.labels.years')}
          </Text>
          <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, marginBottom: theme.spacing.sm }}>
            <Text style={{ fontWeight: 'bold' }}>{t('finalRecipes.labels.gender')}:</Text> {demographics?.gender || t('finalRecipes.labels.notProvided')}
          </Text>
        </View>

        {selectedCauses.length > 0 && (
          <View style={{ paddingHorizontal: theme.spacing.screenPadding, marginTop: theme.spacing.md }}>
            <Text 
              variant="bodySmall" 
              style={{
                fontWeight: 'bold',
                marginBottom: theme.spacing.sm,
                textTransform: 'uppercase',
                letterSpacing: 0.5,
                color: theme.colors.onSurfaceVariant
              }}
            >
              {t('finalRecipes.labels.identifiedCauses')}
            </Text>
            <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: theme.spacing.sm }}>
              {selectedCauses.map((cause) => (
                <Chip 
                  key={cause.cause_id} 
                  style={{ 
                    backgroundColor: theme.colors.primaryContainer,
                    marginBottom: theme.spacing.xs 
                  }}
                >
                  {cause.cause_name}
                </Chip>
              ))}
            </View>
          </View>
        )}

        {selectedSymptoms.length > 0 && (
          <View style={{ paddingHorizontal: theme.spacing.screenPadding, marginTop: theme.spacing.md }}>
            <Text 
              variant="bodySmall" 
              style={{
                fontWeight: 'bold',
                marginBottom: theme.spacing.sm,
                textTransform: 'uppercase',
                letterSpacing: 0.5,
                color: theme.colors.onSurfaceVariant
              }}
            >
              {t('finalRecipes.labels.identifiedSymptoms')}
            </Text>
            <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: theme.spacing.sm }}>
              {selectedSymptoms.map((symptom) => (
                <Chip 
                  key={symptom.symptom_id} 
                  style={{ 
                    backgroundColor: theme.colors.tertiaryContainer,
                    marginBottom: theme.spacing.xs 
                  }}
                >
                  {symptom.symptom_name}
                </Chip>
              ))}
            </View>
          </View>
        )}
      </View>

      {/* Protocol Summary Cards */}
      <View>
        <Text 
          variant="titleLarge" 
          style={{
            fontWeight: 'bold',
            marginBottom: theme.spacing.lg,
            color: theme.colors.onSurface,
            paddingHorizontal: theme.spacing.screenPadding
          }}
        >
          {t('finalRecipes.sections.protocolSummary')}
        </Text>
        <View style={{ gap: theme.spacing.md, paddingHorizontal: theme.spacing.screenPadding }}>
          <ProtocolSummaryCard
            timeSlot="morning"
            recipe={finalRecipes.morning.recipe}
            onViewDetails={() => onSwitchToRecipes('morning')}
          />
          <ProtocolSummaryCard
            timeSlot="midday"
            recipe={finalRecipes.midDay.recipe}
            onViewDetails={() => onSwitchToRecipes('midday')}
          />
          <ProtocolSummaryCard
            timeSlot="night"
            recipe={finalRecipes.night.recipe}
            onViewDetails={() => onSwitchToRecipes('night')}
          />
        </View>
      </View>
    </View>
  );
}

// RecipesTab removed - now using modal system

// Safety Tab Component
interface SafetyTabProps {
  finalRecipes: any;
}

export function SafetyTab({ finalRecipes }: SafetyTabProps) {
  const theme = useTheme();

  // Aggregate safety warnings from all recipes
  const allSafetyWarnings = [
    ...(finalRecipes.morning.recipe?.safety_warnings || []),
    ...(finalRecipes.midDay.recipe?.safety_warnings || []),
    ...(finalRecipes.night.recipe?.safety_warnings || [])
  ];

  return (
    <View style={{ flex: 1, paddingHorizontal: theme.spacing.screenPadding }}>
      <SafetyWarningsComponent warnings={allSafetyWarnings} />
    </View>
  );
}

// Safety Warnings Component
interface SafetyWarningsComponentProps {
  warnings: any[];
}

function SafetyWarningsComponent({ warnings }: SafetyWarningsComponentProps) {
  const theme = useTheme();
  const { t } = useTranslation('create-recipe');

  if (!warnings || warnings.length === 0) {
    return (
      <View 
        style={{ 
          padding: theme.spacing.xl,
          borderRadius: theme.spacing.md,
          backgroundColor: theme.colors.primaryContainer,
          marginBottom: theme.spacing.lg
        }}
      >
        <View 
          style={{ 
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: theme.spacing.md 
          }}
        >
          <IconButton icon="shield-check" iconColor={theme.colors.primary} size={24} />
          <Text 
            variant="titleMedium" 
            style={{ 
              marginLeft: theme.spacing.sm,
              color: theme.colors.primary,
              fontWeight: 'bold'
            }}
          >
            {t('finalRecipes.states.safetyProfile')}
          </Text>
        </View>
        <Text 
          variant="bodyMedium" 
          style={{ 
            color: theme.colors.onPrimaryContainer,
            lineHeight: 20 
          }}
        >
          {t('finalRecipes.states.safetyBasedOnProfile')}
        </Text>
      </View>
    );
  }

  return (
    <View style={{ gap: theme.spacing.lg }}>
      <Text 
        variant="titleLarge" 
        style={{
          fontWeight: 'bold',
          marginBottom: theme.spacing.sm,
          color: theme.colors.onSurface
        }}
      >
        {t('finalRecipes.states.safetyInformation')}
      </Text>
      
      {warnings.map((warning, index) => (
        <View 
          key={index} 
          style={{
            padding: theme.spacing.lg,
            borderRadius: theme.spacing.md,
            marginBottom: theme.spacing.sm,
            backgroundColor: theme.colors.surfaceVariant
          }}
        >
          <View 
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: theme.spacing.sm
            }}
          >
            <IconButton 
              icon="alert-circle" 
              iconColor={theme.colors.error} 
              size={20} 
            />
            <Text 
              variant="titleSmall" 
              style={{
                marginLeft: theme.spacing.sm,
                flex: 1,
                color: theme.colors.error,
                fontWeight: 'bold'
              }}
            >
              {warning.message_localized}
            </Text>
          </View>
          <Text 
            variant="bodyMedium" 
            style={{
              lineHeight: 18,
              color: theme.colors.onSurface
            }}
          >
            {warning.guidance_localized}
          </Text>
        </View>
      ))}

      {/* General Safety Guidelines */}
      <View 
        style={{
          padding: theme.spacing.xl,
          borderRadius: theme.spacing.md,
          backgroundColor: theme.colors.surfaceVariant
        }}
      >
        <Text 
          variant="titleMedium" 
          style={{
            fontWeight: 'bold',
            marginBottom: theme.spacing.lg,
            color: theme.colors.onSurface
          }}
        >
          {t('finalRecipes.states.generalSafetyGuidelines')}
        </Text>
        
        <View style={{ gap: theme.spacing.md }}>
          <View 
            style={{
              flexDirection: 'row',
              alignItems: 'center'
            }}
          >
            <IconButton icon="test-tube" size={16} iconColor={theme.colors.onSurfaceVariant} />
            <Text 
              variant="bodySmall" 
              style={{
                flex: 1,
                marginLeft: theme.spacing.sm,
                lineHeight: 18,
                color: theme.colors.onSurface
              }}
            >
              {t('finalRecipes.states.patchTestAdvice')}
            </Text>
          </View>
          
          <View 
            style={{
              flexDirection: 'row',
              alignItems: 'center'
            }}
          >
            <IconButton icon="eye-off" size={16} iconColor={theme.colors.onSurfaceVariant} />
            <Text 
              variant="bodySmall" 
              style={{
                flex: 1,
                marginLeft: theme.spacing.sm,
                lineHeight: 18,
                color: theme.colors.onSurface
              }}
            >
              {t('finalRecipes.states.avoidEyesAdvice')}
            </Text>
          </View>
          
          <View 
            style={{
              flexDirection: 'row',
              alignItems: 'center'
            }}
          >
            <IconButton icon="baby" size={16} iconColor={theme.colors.onSurfaceVariant} />
            <Text 
              variant="bodySmall" 
              style={{
                flex: 1,
                marginLeft: theme.spacing.sm,
                lineHeight: 18,
                color: theme.colors.onSurface
              }}
            >
              {t('finalRecipes.states.keepAwayFromChildrenAdvice')}
            </Text>
          </View>
          
          <View 
            style={{
              flexDirection: 'row',
              alignItems: 'center'
            }}
          >
            <IconButton icon="thermometer" size={16} iconColor={theme.colors.onSurfaceVariant} />
            <Text 
              variant="bodySmall" 
              style={{
                flex: 1,
                marginLeft: theme.spacing.sm,
                lineHeight: 18,
                color: theme.colors.onSurface
              }}
            >
              {t('finalRecipes.states.storageAdvice')}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
}
/**
 * Final Recipe List Components - Container/Presentational Pattern
 * Following the established pattern from PropertiesListUI and EnhancedPropertiesHeader
 * Separates header logic from list logic for better reusability and consistency
 */

import React, { useState, useMemo } from 'react';
import { View } from 'react-native';
import { Text, List, Divider, TouchableRipple, IconButton } from 'react-native-paper';
import { useTheme } from '@/shared/hooks/use-theme';
import { useTranslation } from 'react-i18next';
import { haptics } from '@/shared/utils/haptics';
import { useModalManager } from '@/shared/hooks/use-modal-manager';
import { useCombinedRecipeStore } from '@/features/create-recipe/store/combined-store';
import { OilSubstitutionBottomSheet } from '../../modals/oil-substitution-bottom-sheet';
import type { FinalRecipeProtocol, RecipeTimeSlot, EnrichedEssentialOil } from '@/features/create-recipe/types';

// Part 1: Recipe Details Header Component (like EnhancedPropertiesHeader)
interface RecipeDetailsHeaderProps {
  recipe: FinalRecipeProtocol;
  timeSlot: RecipeTimeSlot;
}

export function RecipeDetailsHeader({ recipe, timeSlot }: RecipeDetailsHeaderProps) {
  const theme = useTheme();
  const { t } = useTranslation('create-recipe');

  const getTimeSlotColor = (slot: RecipeTimeSlot) => {
    switch (slot) {
      case 'morning': return theme.colors.primary;
      case 'mid-day': return theme.colors.secondary; 
      case 'night': return theme.colors.tertiary;
      default: return theme.colors.primary;
    }
  };

  return (
    <View style={{ paddingHorizontal: theme.spacing.screenPadding, marginBottom: theme.spacing.lg }}>
      {/* Protocol Chip */}
      <View style={{
        paddingHorizontal: theme.spacing.md,
        paddingVertical: theme.spacing.sm,
        borderRadius: theme.spacing.lg,
        alignSelf: 'flex-start',
        backgroundColor: getTimeSlotColor(timeSlot),
        marginBottom: theme.spacing.md
      }}>
        <Text variant="labelMedium" style={{ color: theme.colors.onPrimary, fontWeight: 'bold' }}>
          {timeSlot.charAt(0).toUpperCase() + timeSlot.slice(1)} Protocol
        </Text>
      </View>

      {/* Recipe Name & Description */}
      <Text variant="headlineSmall" style={{ 
        fontWeight: 'bold', 
        marginBottom: theme.spacing.sm, 
        color: theme.colors.onSurface 
      }}>
        {recipe.recipe_name_localized}
      </Text>
      
      <Text variant="bodyMedium" style={{ 
        marginBottom: theme.spacing.lg, 
        lineHeight: 20, 
        color: theme.colors.onSurface 
      }}>
        {recipe.description_localized}
      </Text>

      {/* Quick Info Stats Block */}
      <View style={{
        flexDirection: 'row',
        justifyContent: 'space-around',
        padding: theme.spacing.lg,
        backgroundColor: theme.colors.surfaceVariant,
        borderRadius: theme.spacing.md,
      }}>
        {/* Total Drops */}
        <View style={{ alignItems: 'center' }}>
          <Text variant="bodySmall" style={{ 
            color: theme.colors.onSurfaceVariant, 
            marginBottom: theme.spacing.xs, 
            textTransform: 'uppercase', 
            letterSpacing: 0.5 
          }}>
            {t('finalRecipes.stats.totalDrops')}
          </Text>
          <Text variant="titleMedium" style={{ 
            fontWeight: 'bold', 
            color: theme.colors.onSurface 
          }}>
            {recipe.total_drops}
          </Text>
        </View>
        
        {/* Volume */}
        <View style={{ alignItems: 'center' }}>
          <Text variant="bodySmall" style={{ 
            color: theme.colors.onSurfaceVariant, 
            marginBottom: theme.spacing.xs, 
            textTransform: 'uppercase', 
            letterSpacing: 0.5 
          }}>
            {t('finalRecipes.stats.volume')}
          </Text>
          <Text variant="titleMedium" style={{ 
            fontWeight: 'bold', 
            color: theme.colors.onSurface 
          }}>
            {recipe.total_volume_ml}ml
          </Text>
        </View>
        
        {/* Method */}
        <View style={{ alignItems: 'center' }}>
          <Text variant="bodySmall" style={{ 
            color: theme.colors.onSurfaceVariant, 
            marginBottom: theme.spacing.xs, 
            textTransform: 'uppercase', 
            letterSpacing: 0.5 
          }}>
            {t('finalRecipes.stats.method')}
          </Text>
          <Text variant="titleMedium" style={{ 
            fontWeight: 'bold', 
            color: theme.colors.onSurface 
          }}>
            {t('finalRecipes.labels.topical')}
          </Text>
        </View>
      </View>
    </View>
  );
}

// Part 2: Final Recipe Accordion List (like PropertiesListUI)
interface FinalRecipeListProps {
  recipe: FinalRecipeProtocol;
  timeSlot: RecipeTimeSlot; // Add this prop
}

export function FinalRecipeList({ recipe, timeSlot }: FinalRecipeListProps) {
  const theme = useTheme();
  const { t } = useTranslation('create-recipe');
  const [expandedSections, setExpandedSections] = useState<string[]>(['oils']); // Default open oils

  // Early return if recipe is null or undefined
  if (!recipe) {
    return (
      <View style={{ padding: theme.spacing.lg, alignItems: 'center' }}>
        <Text variant="bodyLarge" style={{ color: theme.colors.onSurfaceVariant }}>
          {t('finalRecipes.states.noRecipeData')}
        </Text>
      </View>
    );
  }

  // Ensure selected_oils array exists
  if (!recipe.selected_oils || !Array.isArray(recipe.selected_oils)) {
    return (
      <View style={{ padding: theme.spacing.lg, alignItems: 'center' }}>
        <Text variant="bodyLarge" style={{ color: theme.colors.onSurfaceVariant }}>
          {t('finalRecipes.states.incompleteRecipeData')}
        </Text>
      </View>
    );
  }

  const toggleSection = (sectionId: string) => {
    haptics.light();
    setExpandedSections(prev =>
      prev.includes(sectionId)
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  // Add substitution modal state
  const { modalRef, presentModal, dismissModal } = useModalManager();
  const [selectedOilForSubstitution, setSelectedOilForSubstitution] = useState<{
    oil_id: string;
    name_localized: string;
  } | null>(null);
  
  // Get store actions and state
  const { therapeuticProperties, substituteOilInRecipe } = useCombinedRecipeStore();

  // Get all alternative oils from therapeutic properties
  const getAllAlternativeOils = useMemo(() => {
    const allOils: EnrichedEssentialOil[] = [];
    therapeuticProperties.forEach(property => {
      if (property.suggested_oils) {
        allOils.push(...property.suggested_oils);
      }
    });
    
    if (__DEV__) {
      console.log('🔍 [FinalRecipeList] Collecting alternative oils:', {
        propertiesCount: therapeuticProperties.length,
        totalOilsBeforeDedup: allOils.length,
        enrichedOilsCount: allOils.filter(oil => oil.enrichment_status === 'enriched').length,
        oilsWithFinalScore: allOils.filter(oil => oil.final_relevance_score !== undefined).length,
        oilsWithSpecializationScore: allOils.filter(oil => oil.specialization_score !== undefined).length
      });
    }
    
    // Deduplicate by oil_id and filter enriched oils with scores
    const uniqueOils = new Map<string, EnrichedEssentialOil>();
    const filteredOut: { reason: string; oil: Partial<EnrichedEssentialOil> }[] = [];
    
    allOils.forEach(oil => {
      if (!oil.oil_id) {
        filteredOut.push({ reason: 'missing oil_id', oil: { name_localized: oil.name_localized } });
        return;
      }
      if (!oil.final_relevance_score) {
        filteredOut.push({ reason: 'missing final_relevance_score', oil: { oil_id: oil.oil_id, name_localized: oil.name_localized } });
        return;
      }
      if (oil.enrichment_status !== 'enriched') {
        filteredOut.push({ reason: 'not enriched', oil: { oil_id: oil.oil_id, name_localized: oil.name_localized, enrichment_status: oil.enrichment_status } });
        return;
      }
      
      if (!uniqueOils.has(oil.oil_id) || 
          (oil.final_relevance_score > (uniqueOils.get(oil.oil_id)?.final_relevance_score || 0))) {
        uniqueOils.set(oil.oil_id, oil);
      }
    });
    
    const finalOils = Array.from(uniqueOils.values());
    
    if (__DEV__) {
      console.log('🔍 [FinalRecipeList] Alternative oils processing complete:', {
        totalOilsBeforeDedup: allOils.length,
        filteredOutCount: filteredOut.length,
        finalUniqueOilsCount: finalOils.length,
        filteredOutReasons: filteredOut.reduce((acc, item) => {
          acc[item.reason] = (acc[item.reason] || 0) + 1;
          return acc;
        }, {} as Record<string, number>)
      });
      
      if (filteredOut.length > 0) {
        console.warn('⚠️ [FinalRecipeList] Some oils were filtered out:', {
          count: filteredOut.length,
          examples: filteredOut.slice(0, 3)
        });
      }
      
      // Check for missing specialization scores in final oils
      const missingSpecialization = finalOils.filter(oil => oil.specialization_score === undefined);
      if (missingSpecialization.length > 0) {
        console.warn('⚠️ [FinalRecipeList] Alternative oils missing specialization_score:', {
          count: missingSpecialization.length,
          percentage: Math.round((missingSpecialization.length / finalOils.length) * 100),
          examples: missingSpecialization.slice(0, 3).map(oil => ({
            oil_id: oil.oil_id,
            name: oil.name_localized
          }))
        });
      }
    }
    
    return finalOils;
  }, [therapeuticProperties]);

  const handleSubstituteOil = (oil: { oil_id: string; name_localized: string }) => {
    haptics.light();
    setSelectedOilForSubstitution(oil);
    
    if (__DEV__) {
      console.log('🔄 [FinalRecipeList] Opening substitution modal:', {
        timeSlot,
        originalOil: {
          oil_id: oil.oil_id,
          name_localized: oil.name_localized
        },
        availableAlternativesCount: getAllAlternativeOils.length,
        recipeSelectedOilsCount: recipe.selected_oils.length,
        recipeSelectedOilIds: recipe.selected_oils.map(o => o.oil_id)
      });
      
      // Validate that the oil being substituted is actually in the recipe
      const isOilInRecipe = recipe.selected_oils.some(selectedOil => selectedOil.oil_id === oil.oil_id);
      if (!isOilInRecipe) {
        console.error('❌ [FinalRecipeList] CRITICAL: Oil being substituted is not in recipe!', {
          targetOilId: oil.oil_id,
          recipeOilIds: recipe.selected_oils.map(o => o.oil_id)
        });
      }
    }
    
    presentModal();
  };

  const handleSelectSubstitute = (substitutionOil: EnrichedEssentialOil) => {
    if (selectedOilForSubstitution) {
      substituteOilInRecipe(timeSlot, selectedOilForSubstitution.oil_id, substitutionOil);
      setSelectedOilForSubstitution(null);
    }
  };

  const handleDismissModal = () => {
    setSelectedOilForSubstitution(null);
    dismissModal();
  };

  const sections = [
    { 
      id: 'oils', 
      title: t('finalRecipes.sections.essentialOils'), 
      icon: 'leaf', 
      content: (
        <>
          {recipe.selected_oils.map((oil, index) => (
            <List.Item
              key={index}
              title={oil.name_localized}
              description={oil.name_scientific || oil.name_botanical}
              left={() => <List.Icon icon="leaf" color={theme.colors.primary} />}
              right={() => (
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <Text variant="labelLarge" style={{ 
                    fontWeight: 'bold', 
                    color: theme.colors.onSurface,
                    marginRight: theme.spacing.sm
                  }}>
                    {oil.drops_count} {t('finalRecipes.stats.drops')}
                  </Text>
                  <IconButton
                    icon="swap-horizontal"
                    size={20}
                    onPress={() => handleSubstituteOil({
                      oil_id: oil.oil_id,
                      name_localized: oil.name_localized
                    })}
                    style={{ margin: 0 }}
                  />
                </View>
              )}
            />
          ))}
        </>
      )
    },
    { 
      id: 'carrierOil', 
      title: t('finalRecipes.sections.carrierOil'), 
      icon: 'water', 
      content: (
        <>
          <List.Item
            title={`${t('finalRecipes.sections.recommended')}: ${recipe.carrier_oil.name_localized}`}
            description={`${recipe.carrier_oil.properties_localized} • ${recipe.carrier_oil.amount_ml}ml`}
            titleStyle={{ fontWeight: 'bold', color: theme.colors.primary }}
          />
          {recipe.carrier_oil.alternative && (
            <List.Item
              title={`${t('finalRecipes.sections.alternative')}: ${recipe.carrier_oil.alternative.name_localized}`}
              description={recipe.carrier_oil.alternative.properties_localized}
              titleStyle={{ fontWeight: 'bold', color: theme.colors.onSurfaceVariant }}
            />
          )}
        </>
      )
    },
    { 
      id: 'preparation', 
      title: t('finalRecipes.sections.preparation'), 
      icon: 'flask-outline', 
      content: (
        <>
          {recipe.preparation_steps_localized.map((step, index) => (
            <List.Item
              key={index}
              title={`Step ${index + 1}`}
              description={step}
              left={() => (
                <View style={{ 
                  backgroundColor: theme.colors.primaryContainer, 
                  paddingHorizontal: theme.spacing.sm, 
                  paddingVertical: theme.spacing.xs, 
                  borderRadius: theme.spacing.md, 
                  minWidth: 24, 
                  alignItems: 'center', 
                  justifyContent: 'center' 
                }}>
                  <Text variant="bodySmall" style={{ 
                    fontWeight: 'bold', 
                    color: theme.colors.onPrimaryContainer, 
                    textAlign: 'center' 
                  }}>
                    {index + 1}
                  </Text>
                </View>
              )}
            />
          ))}
        </>
      )
    },
    { 
      id: 'application', 
      title: t('finalRecipes.sections.application'), 
      icon: 'hand-heart', 
      content: (
        <>
          {recipe.usage_instructions_localized.map((instruction, index) => (
            <List.Item
              key={index}
              title={instruction.method}
              description={`${instruction.description} • ${instruction.frequency}`}
              titleStyle={{ fontWeight: 'bold' }}
            />
          ))}
        </>
      )
    },
    { 
      id: 'rationales', 
      title: t('finalRecipes.sections.rationales'), 
      icon: 'lightbulb-outline', 
      content: (
        <>
          {recipe.selected_oils.map((oil, index) => (
            <List.Item 
              key={index} 
              title={oil.name_localized} 
              description={oil.rationale_localized} 
              titleStyle={{ fontWeight: 'bold' }} 
            />
          ))}
        </>
      )
    },
    { 
      id: 'safety', 
      title: t('finalRecipes.sections.safety'), 
      icon: 'shield-check-outline', 
      content: (
        <>
          {recipe.safety_warnings?.length > 0 ? recipe.safety_warnings.map((warning, index) => (
            <List.Item
              key={index}
              title={warning.message_localized}
              description={warning.guidance_localized}
              titleStyle={{ fontWeight: 'bold', color: theme.colors.error }}
              left={() => <List.Icon icon="alert-circle" color={theme.colors.error} />}
            />
          )) : (
            <List.Item 
              title={t('finalRecipes.states.safetyProfile')} 
              description={t('finalRecipes.states.safetyBasedOnProfile')}
              left={() => <List.Icon icon="shield-check" color={theme.colors.primary} />}
            />
          )}
        </>
      )
    },
  ];

  return (
    <View style={{ flex: 1 }}>
      <List.Section>
        {sections.map((section, index) => {
          const isExpanded = expandedSections.includes(section.id);
          return (
            <React.Fragment key={section.id}>
              <List.Accordion
                title={section.title}
                left={() => <List.Icon icon={section.icon} color={isExpanded ? theme.colors.onPrimary : theme.colors.primary} />}
                expanded={isExpanded}
                onPress={() => toggleSection(section.id)}
                style={{ backgroundColor: isExpanded ? theme.colors.primary : 'transparent' }}
                titleStyle={{ 
                  color: isExpanded ? theme.colors.onPrimary : theme.colors.onSurface, 
                  fontWeight: 'bold' 
                }}
                right={() => (
                  <List.Icon 
                    icon={isExpanded ? 'chevron-up' : 'chevron-down'} 
                    color={isExpanded ? theme.colors.onPrimary : theme.colors.onSurfaceVariant} 
                  />
                )}
              >
                <Divider />
                <View style={{ paddingHorizontal: theme.spacing.screenPadding, paddingVertical: theme.spacing.md }}>
                  {section.content}
                </View>
              </List.Accordion>
              {index < sections.length - 1 && <Divider />}
            </React.Fragment>
          );
        })}
      </List.Section>
      
      {/* Oil Substitution Modal */}
      <OilSubstitutionBottomSheet
        modalRef={modalRef}
        originalOil={selectedOilForSubstitution}
        alternativeOils={getAllAlternativeOils}
        onSelectSubstitute={handleSelectSubstitute}
        onDismiss={handleDismissModal}
        selectedOils={recipe.selected_oils}
        timeSlot={timeSlot}
      />
    </View>
  );
}
/**
 * Final Recipes Components - Barrel Exports
 * Follows DRY principle - single import source for all final recipes components
 */

// Tab Components
export { OverviewTab, RecipesTab, SafetyTab } from './final-recipes-tabs';

// Card Components
export { ProtocolSummaryCard } from './final-recipes-cards';

// NEW: Container/Presentational Components (following properties-selection pattern)
export { RecipeDetailsHeader, FinalRecipeList } from './final-recipes-list';

// Enhanced Hook (NEW - implements post-enrichment scoring and 3 parallel API calls)
export { useFinalRecipes } from '../../../hooks/use-final-recipes';
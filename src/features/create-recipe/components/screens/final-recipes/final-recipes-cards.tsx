import { View } from 'react-native';
import { Text, Surface, TouchableRipple, Divider } from 'react-native-paper';
import { useTheme } from '@/shared/hooks/use-theme';
import { haptics } from '@/shared/utils/haptics';
import type { FinalRecipeProtocol, RecipeTimeSlot } from '@/features/create-recipe/types';
import { useTranslation } from 'react-i18next';

// Protocol Summary Card Component
interface ProtocolSummaryCardProps {
  timeSlot: RecipeTimeSlot;
  recipe: FinalRecipeProtocol | null;
  onViewDetails: () => void;
}

export function ProtocolSummaryCard({ timeSlot, recipe, onViewDetails }: ProtocolSummaryCardProps) {
  const theme = useTheme();
  const { t } = useTranslation('create-recipe');

  const getTimeSlotConfig = (slot: RecipeTimeSlot) => {
    switch (slot) {
      case 'morning':
        return {
          color: theme.colors.primary,
          label: t('finalRecipes.protocols.morning'),
          emoji: '🌅'
        };
      case 'mid-day':
        return {
          color: theme.colors.secondary,
          label: t('finalRecipes.protocols.midDay'),
          emoji: '☀️'
        };
      case 'night':
        return {
          color: theme.colors.tertiary,
          label: t('finalRecipes.protocols.night'),
          emoji: '🌙'
        };
      default:
        return {
          color: theme.colors.primary,
          label: 'Protocol',
          emoji: '🧪'
        };
    }
  };

  const config = getTimeSlotConfig(timeSlot);

  if (!recipe) {
    return (
      <Surface 
        style={{ 
          marginBottom: theme.spacing.md,
          borderRadius: theme.spacing.md,
          padding: theme.spacing.lg,
          backgroundColor: theme.colors.surface,
          opacity: 0.6
        }} 
        elevation={1}
      >
        <Text 
          variant="bodyMedium" 
          style={{
            textAlign: 'center',
            fontStyle: 'italic',
            color: theme.colors.onSurface
          }}
        >
          {t('finalRecipes.states.generating', { protocol: config.label.toLowerCase() })}
        </Text>
      </Surface>
    );
  }

  return (
    <TouchableRipple
      onPress={() => {
        haptics.light();
        onViewDetails();
      }}
      style={{ 
        borderRadius: theme.spacing.md,
        marginBottom: theme.spacing.md 
      }}
    >
      <Surface 
        style={{ 
          padding: theme.spacing.lg,
          backgroundColor: theme.colors.surface
        }} 
        elevation={1}
      >
        <View 
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: theme.spacing.sm
          }}
        >
          <Text style={{ fontSize: theme.typography.headlineSmall.fontSize, marginRight: theme.spacing.sm }}>{config.emoji}</Text>
          <Text 
            variant="titleSmall" 
            style={{ 
              color: config.color,
              fontWeight: 'bold'
            }}
          >
            {config.label}
          </Text>
        </View>

        <Text 
          variant="bodyMedium" 
          style={{ 
            fontWeight: '600',
            marginBottom: theme.spacing.xs,
            color: theme.colors.onSurface
          }}
        >
          {recipe.recipe_theme_localized}
        </Text>

        <Text 
          variant="bodySmall" 
          style={{ 
            color: theme.colors.onSurfaceVariant,
            fontStyle: 'italic',
            marginBottom: theme.spacing.md
          }}
        >
          {recipe.holistic_benefit_localized}
        </Text>

        <Divider style={{ marginVertical: theme.spacing.sm }} />

        <View style={{ marginBottom: theme.spacing.md }}>
          <Text 
            variant="bodySmall" 
            style={{ 
              color: theme.colors.onSurfaceVariant
            }}
          >
            {recipe.selected_oils.length} {t('finalRecipes.stats.oils')} • {recipe.total_drops} {t('finalRecipes.stats.drops')}
          </Text>
        </View>

        <Text 
          variant="labelLarge" 
          style={{
            color: theme.colors.primary,
            textAlign: 'center',
            fontWeight: 'bold'
          }}
        >
          {t('finalRecipes.actions.viewRecipe')}
        </Text>
      </Surface>
    </TouchableRipple>
  );
}

// EnhancedRecipeCard has been deprecated - now using direct List.Accordion pattern in RecipesTab


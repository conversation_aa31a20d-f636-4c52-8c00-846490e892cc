import React from 'react';
import { View } from 'react-native';
import { Text } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/shared/hooks';

interface EnhancedSymptomsHeaderProps {}

/**
 * EnhancedSymptomsHeader - Rich header component showing "Please select" message
 * Follows the same pattern as EnhancedPropertiesHeader for consistency
 */
export const EnhancedSymptomsHeader: React.FC<EnhancedSymptomsHeaderProps> = () => {
  const { t } = useTranslation('create-recipe');
  const theme = useTheme();

  return (
    <View style={{
      paddingTop: theme.spacing.xl,
      marginBottom: theme.spacing.xs,
    }}>
      {/* Please select message */}
      <View style={{
        paddingHorizontal: theme.spacing.screenPadding,
        marginBottom: theme.spacing.md,
      }}>
        <Text
          variant="titleLarge"
          style={{
            textAlign: 'left',
            color: theme.colors.onSurface,
            fontWeight: '600',
          }}
        >
          {t('symptoms.pleaseSelect')}
        </Text>
      </View>
    </View>
  );
};
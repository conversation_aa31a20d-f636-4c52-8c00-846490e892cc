import React, { useState, useCallback, useEffect, useRef } from 'react';
import { View, TouchableWithoutFeedback, Keyboard } from 'react-native';
import { useTheme, useLanguage } from '@/shared/hooks';
import { useCombinedRecipeStore } from '@/features/create-recipe/store/combined-store';
import { SelectableDataList } from '@/shared/components';
import { useRecipeNavigation } from '@/features/create-recipe/hooks';
import { symptomsSelectionSchema } from '@/features/create-recipe/schemas';
import { SELECTION_REQUIREMENTS } from '@/features/create-recipe/constants';
import { useTranslation } from 'react-i18next';

import { StreamingBottomSheet } from '@/shared/components/modals/streaming';
import type { StreamingItem } from '@/shared/components/modals/streaming/types';
import type { PotentialSymptom, TherapeuticProperty } from '@/features/create-recipe/types';
import { EnhancedSymptomsHeader } from './enhanced-symptoms-header';
import { haptics } from '@/shared/utils/haptics';

/**
 * Symptoms Selection Component - Simplified V4
 * Fourth step in the recipe creation wizard
 * Simple multi-select interface for potential symptoms
 * Uses React Native Paper components with Material Design 3 theming
 * Follows KISS, DRY, YAGNI principles - no search, no categories, no complex features
 */

interface SymptomsSelectionProps {
  onValidationChange?: (isValid: boolean) => void;
  onSubmitReady?: (submitFn: () => void) => void;
  onLoadingChange?: (isLoading: boolean) => void;
}

export const SymptomsSelection: React.FC<SymptomsSelectionProps> = ({
  onValidationChange,
  onSubmitReady,
  onLoadingChange
}) => {
  const { t } = useTranslation('create-recipe');
  const theme = useTheme();
  const { language: apiLanguage } = useLanguage();
  const { completeStepAndGoNext } = useRecipeNavigation();
  const { 
    potentialSymptoms, 
    selectedSymptoms, 
    updateSelectedSymptoms,
    healthConcern,
    demographics,
    selectedCauses,
    updateTherapeuticProperties
  } = useCombinedRecipeStore();

  // Handle selection validation changes from SelectableDataList (for future extensibility)
  const handleSelectionValidChange = useCallback((isValid: boolean) => {
    // Currently using existing validation system via useStepScreenCoordination
    // This callback is available for future enhancements
    console.log('🩺 [SymptomsSelection] Selection validity changed:', isValid);
  }, []);

  // Real data from API - no fallback to demo data
  const symptomsToDisplay = potentialSymptoms;

  // Simplified form state - only what's needed
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Streaming modal state for therapeutic properties (following demographics pattern)
  const [showStreamingModal, setShowStreamingModal] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamingItems, setStreamingItems] = useState<StreamingItem[]>([]);

  // Simple selection state
  const selectedSymptomIds = selectedSymptoms.map(symptom => symptom.symptom_id);
  const selectionCount = selectedSymptoms.length;
  const minSelection = SELECTION_REQUIREMENTS.symptoms.min;
  const maxSelection = SELECTION_REQUIREMENTS.symptoms.max;
  const canSubmit = selectionCount >= minSelection && selectionCount <= maxSelection;

  // Refs to track previous values and prevent redundant callback calls
  const prevCanSubmit = useRef(canSubmit);
  const prevIsLoading = useRef(isSubmitting || isStreaming);
  const prevSubmitFn = useRef<(() => void) | null>(null);

  // Debug logging for development
  console.log('🩺 SymptomsSelection Debug:', {
    totalSymptoms: symptomsToDisplay.length,
    selectedCount: selectionCount,
    canSubmit,
    selectedIds: selectedSymptomIds
  });

  // Track modal visibility changes (following demographics pattern)
  React.useEffect(() => {
    console.log('🚀 MODAL VISIBILITY CHANGED:', {
      showStreamingModal,
      timestamp: new Date().toISOString(),
      component: 'SymptomsSelection'
    });
  }, [showStreamingModal]);

  // Debug modal state (matching standardized pattern)
  console.log('🎭 SymptomsSelection - Modal State:', {
    showStreamingModal,
    isStreaming,
    streamingItemsCount: streamingItems.length,
    timestamp: new Date().toISOString()
  });

  // Debug the actual data structure
  if (symptomsToDisplay.length > 0) {
    console.log('🔍 SymptomsSelection: First symptom data structure:', symptomsToDisplay[0]);
    console.log('🔍 SymptomsSelection: Available fields:', Object.keys(symptomsToDisplay[0]));
  }

  // Validation - memoized to prevent recreation and infinite loops
  const validateSelection = useCallback(() => {
    try {
      symptomsSelectionSchema.parse({ selectedSymptoms });
      setErrors({});
      return true;
    } catch (error: any) {
      const newErrors: Record<string, string> = {};
      if (error.errors) {
        error.errors.forEach((err: any) => {
          newErrors[err.path[0]] = err.message;
        });
      }
      setErrors(newErrors);
      return false;
    }
  }, [selectedSymptoms]);

  // Handle symptom selection
  const handleSymptomToggle = (symptom: PotentialSymptom, isSelected: boolean) => {
    if (isSelected) {
      // Add to selection (only if under limit)
      if (selectionCount < maxSelection) {
        const newSelection = [...selectedSymptoms, symptom];
        updateSelectedSymptoms(newSelection);
      }
    } else {
      // Remove from selection
      const newSelection = selectedSymptoms.filter(s => s.symptom_id !== symptom.symptom_id);
      updateSelectedSymptoms(newSelection);
    }
    
    // Clear errors when user makes changes
    if (errors.selectedSymptoms) {
      setErrors(prev => ({ ...prev, selectedSymptoms: '' }));
    }
  };

  // Real API streaming functionality for therapeutic properties - memoized to prevent recreation
  const streamTherapeuticPropertiesData = useCallback(async () => {
    console.log('🔥 streamTherapeuticPropertiesData called! This should ONLY happen when user clicks Complete Selection button');
    console.log('🔥 Call stack:', new Error().stack);
    
    if (!healthConcern || !demographics || selectedCauses.length === 0 || selectedSymptoms.length === 0) {
      console.error('❌ Missing required data for therapeutic properties API call');
      return;
    }

    console.log('🚀 Starting real API streaming for therapeutic properties...');
    console.log('📊 Health Concern Data:', healthConcern);
    console.log('📊 Demographics Data:', demographics);
    console.log('📊 Selected Causes:', selectedCauses);
    console.log('📊 Selected Symptoms:', selectedSymptoms);
    
    console.log('🎬 SETTING MODAL VISIBLE - BEFORE:', { showStreamingModal });
    setShowStreamingModal(true);
    console.log('🎬 SETTING MODAL VISIBLE - AFTER setShowStreamingModal(true) called');
    setIsStreaming(true);
    setStreamingItems([]);

    try {
      console.log('🌐 [SymptomsSelection] Using user language:', { apiLanguage });

      // Create API request data following the exact same pattern as causes selection
      const apiRequestData = {
        health_concern: healthConcern.healthConcern.trim(),
        gender: demographics.gender,
        age_category: demographics.ageCategory,
        age_specific: demographics.specificAge.toString(),
        user_language: apiLanguage,
      };

      console.log('📤 Properties API Request Data:', JSON.stringify(apiRequestData, null, 2));

      // Create the streaming request following the exact same pattern
      const streamingRequest = {
        feature: 'create-recipe' as const,
        step: 'therapeutic-properties' as const,
        data: {
          ...apiRequestData,
          selected_causes: selectedCauses,
          selected_symptoms: selectedSymptoms,
        },
      };

      console.log('📤 Full Properties Streaming Request:', JSON.stringify(streamingRequest, null, 2));

      // Import and use the API client (same as causes selection)
      const { streamRecipeStep } = await import('@/shared/services/api/rotinanatural-client');
      
      console.log('🌐 Making properties API call to streaming endpoint...');
      const response = await streamRecipeStep(streamingRequest);
      
      console.log('📡 Properties response received:', {
        ok: response.ok,
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        bodyUsed: response.bodyUsed,
        hasBody: !!response.body,
      });

      if (!response.body) {
        throw new Error('No response body available for streaming');
      }

      // Process the stream using the same approach as causes selection
      const { readSseStream } = await import('@/shared/utils/sse-reader');
      let itemCount = 0;
      const receivedItems: any[] = [];

      for await (const dataString of readSseStream(response.body)) {
        itemCount++;

        console.log(`📨 Received streaming item ${itemCount}:`, {
          dataLength: dataString.length,
          dataPreview: dataString.substring(0, 100)
        });

        try {
          const parsedData = JSON.parse(dataString);

          // Handle structured data items (therapeutic properties)
          if (parsedData.type === 'structured_data' && parsedData.field === 'therapeutic_properties') {
            console.log('🔍 RAW parsedData.data:', JSON.stringify(parsedData.data));
            console.log('🔍 addresses_cause_ids in raw data:', JSON.stringify(parsedData.data.addresses_cause_ids));
            console.log('🔍 addresses_symptom_ids in raw data:', JSON.stringify(parsedData.data.addresses_symptom_ids));
            console.log('🔍 RAW dataString length:', dataString.length);
            if (__DEV__) {
              console.log('🔍 RAW dataString preview:', dataString.substring(0, 100));
            }
            
            const item = {
              id: parsedData.data.property_id,
              property_id: parsedData.data.property_id,
              title: parsedData.data.property_name_localized,
              subtitle: parsedData.data.property_name_english,
              description: parsedData.data.description_contextual_localized,
              // Keep all original data for proper interface mapping
              ...parsedData.data,
            };
            
            console.log('🔍 CONSTRUCTED item:', JSON.stringify(item));
            console.log('🔍 addresses_cause_ids in constructed item:', item.addresses_cause_ids);
            console.log('🔍 addresses_symptom_ids in constructed item:', item.addresses_symptom_ids);

            receivedItems.push(item);

            // Update UI immediately
            setStreamingItems(prev => {
              const exists = prev.some(existingItem =>
                existingItem.id === item.id ||
                (existingItem.title === item.title && existingItem.subtitle === item.subtitle)
              );
              if (exists) return prev;
              return [...prev, item];
            });
          }

          // Handle completion
          if (parsedData.type === 'structured_complete') {
            console.log('✅ Streaming completed with structured_complete');
            console.log('🔍 STRUCTURED_COMPLETE DATA:', JSON.stringify(parsedData.data, null, 2));
            console.log('🔍 STRUCTURED_COMPLETE therapeutic_properties:', parsedData.data?.data?.therapeutic_properties);
            
            // DEBUG: Check first property's addresses_cause_ids in structured_complete
            const firstProperty = parsedData.data?.data?.therapeutic_properties?.[0];
            if (firstProperty) {
              console.log('🔍 STRUCTURED_COMPLETE first property addresses_cause_ids:', firstProperty.addresses_cause_ids);
              console.log('🔍 STRUCTURED_COMPLETE first property addresses_symptom_ids:', firstProperty.addresses_symptom_ids);
            }
            setIsStreaming(false);

            // Use structured_complete data instead of receivedItems to get full UUIDs
            const completeProperties = parsedData.data?.data?.therapeutic_properties || [];
            console.log('✅ SymptomsSelection: Streaming completed with', completeProperties.length, 'properties');
            console.log('📋 Final complete properties (from structured_complete):', completeProperties);
            
            // DEBUG: Check if structured_complete actually has full UUIDs
            if (completeProperties.length > 0) {
              const firstProp = completeProperties[0];
              console.log('🔍 STRUCTURED_COMPLETE first property full data:', JSON.stringify(firstProp, null, 2));
              console.log('🔍 STRUCTURED_COMPLETE addresses_cause_ids:', firstProp.addresses_cause_ids);
              console.log('🔍 STRUCTURED_COMPLETE addresses_symptom_ids:', firstProp.addresses_symptom_ids);
            }

            // Transform data to TherapeuticProperty interface using complete data with full UUIDs
            const therapeuticProperties: TherapeuticProperty[] = completeProperties.map((item: any, index: number) => ({
              property_id: item.property_id || `property-${index}`,
              property_name_localized: item.property_name_localized || '',
              property_name_english: item.property_name_english || '',
              description_contextual_localized: item.description_contextual_localized || '',
              addresses_cause_ids: item.addresses_cause_ids || [],
              addresses_symptom_ids: item.addresses_symptom_ids || [],
              relevancy_score: item.relevancy_score || 0,
              suggested_oils: item.suggested_oils || [],
              isLoadingOils: false,
              errorLoadingOils: null,
              isEnriched: false,
            }));

            // Update store with therapeutic properties
            updateTherapeuticProperties(therapeuticProperties, 'symptoms-selection-streaming');
            console.log('💾 Stored therapeutic properties with correct interface mapping:', therapeuticProperties);
            console.log('🔍 First property example:', therapeuticProperties[0]);

            // Close modal immediately and navigate (same pattern as demographics and causes)
            haptics.success(); // Success haptic when modal closes after completion
            setShowStreamingModal(false);
            completeStepAndGoNext();

            break;
          }
        } catch (parseError) {
          console.warn('Failed to parse streaming data:', dataString, parseError);
        }
      }

      console.log('🎯 Therapeutic properties streaming completed successfully');

    } catch (error) {
      console.error('❌ Therapeutic properties streaming failed:', error);
      setIsStreaming(false);
      setShowStreamingModal(false);
      setErrors(prev => ({ ...prev, api: 'Failed to load therapeutic properties. Please try again.' }));
    }
  }, [healthConcern, demographics, selectedCauses, selectedSymptoms, apiLanguage, completeStepAndGoNext, updateTherapeuticProperties, showStreamingModal]);

  // Handle form submission - memoized to prevent infinite loops
  const handleSubmit = useCallback(async () => {
    console.log('🔥 handleSubmit called! This should ONLY happen when user clicks Complete Selection button');
    console.log('🔥 Call stack:', new Error().stack);
    
    if (!validateSelection()) {
      return;
    }

    console.log('🚀 Submitting symptoms selection:', selectedSymptoms.map(s => s.symptom_name));
    setIsSubmitting(true);
    
    try {
      // Call therapeutic properties API with streaming modal
      await streamTherapeuticPropertiesData();
    } catch (error) {
      console.error('❌ Error submitting symptoms selection:', error);
      setErrors(prev => ({ ...prev, submit: 'Failed to process symptoms. Please try again.' }));
    } finally {
      setIsSubmitting(false);
    }
  }, [validateSelection, selectedSymptoms, streamTherapeuticPropertiesData]);

  // NEW: Notify parent of validation state changes - with safeguards
  useEffect(() => {
    if (onValidationChange && canSubmit !== prevCanSubmit.current) {
      prevCanSubmit.current = canSubmit;
      onValidationChange(canSubmit);
      console.log('Symptoms validation changed:', { canSubmit, selectionCount });
    }
  }, [onValidationChange, canSubmit, selectionCount]);
  
  // NEW: Provide submit function to parent - with safeguards
  useEffect(() => {
    if (onSubmitReady && prevSubmitFn.current !== handleSubmit) {
      prevSubmitFn.current = handleSubmit;
      onSubmitReady(handleSubmit);
      console.log('Symptoms submit function ready');
    }
  }, [onSubmitReady, handleSubmit]);
  
  // NEW: Notify parent of loading state changes - with safeguards
  useEffect(() => {
    const currentLoading = isSubmitting || isStreaming;
    if (onLoadingChange && currentLoading !== prevIsLoading.current) {
      prevIsLoading.current = currentLoading;
      onLoadingChange(currentLoading);
      console.log('Symptoms loading state changed:', { isSubmitting, isStreaming, currentLoading });
    }
  }, [onLoadingChange, isSubmitting, isStreaming]);


  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <View style={{ flex: 1, backgroundColor: theme.colors.background }}>
        
        <EnhancedSymptomsHeader />
        
        <SelectableDataList
          items={symptomsToDisplay}
          selectedItems={selectedSymptoms}
          onItemToggle={handleSymptomToggle}
          maxSelection={maxSelection}
          minSelection={minSelection}
          onSelectionValidChange={handleSelectionValidChange}
          selectable={true}
          getItemId={(symptom) => symptom.symptom_id}
          getItemTitle={(symptom) => symptom.symptom_name}
          getItemDescription={(symptom) => symptom.symptom_suggestion}
          getItemSubtitle={(symptom) => symptom.explanation}
          showSubtitle={true}
          showTitleBadges={false}
          showLeftComponent={false}
          translationKeys={{
            subtitlePrefix: '',
            subtitleSuffix: '',
            counterPrefix: t('symptoms.counterPrefix'),
            emptyStateTitle: t('common.noSymptomsAvailable'),
            emptyStateMessage: t('common.completeCauses')
          }}
          errors={errors}
          errorKey="selectedSymptoms"
        />

        <StreamingBottomSheet
          visible={showStreamingModal}
          onDismiss={useCallback(() => {
            console.log('🎭 SymptomsSelection - StreamingBottomSheet onDismiss called - only closing modal');
            setShowStreamingModal(false);
            // Navigation is handled in structured_complete callback, not here
          }, [setShowStreamingModal])}
          title={t('modals.analyzingProperties.title')}
          description={t('modals.analyzingProperties.description')}
          items={streamingItems}
          analysisType="properties"
          modalState={
            isStreaming && streamingItems.length === 0 ? 'loading' :
            isStreaming ? 'streaming' : 
            !isStreaming && streamingItems.length > 0 ? 'completed' : 'loading'
          }
          isComplete={!isStreaming && streamingItems.length > 0}
          hapticType="none"
          enableBlur={true}
          enableStackAnimation={true}
          enableSwipeToDismiss={false}
        />
      </View>
    </TouchableWithoutFeedback>
  );
};

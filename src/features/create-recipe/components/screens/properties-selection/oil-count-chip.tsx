import React, { useEffect, useRef } from 'react';
import { View, Animated } from 'react-native';
import { Chip } from 'react-native-paper';
import { useTheme } from '@/shared/hooks';

interface OilCountChipProps {
  isLoading: boolean;
  uniqueCount: number;
  hasAnyOils: boolean;
}

/**
 * OilCountChip - Displays unique essential oil count with loading states
 * States: Shimmer (loading) → Live count updates → Final count
 * Uses outlined Material Design chip style
 */
export const OilCountChip: React.FC<OilCountChipProps> = ({ 
  isLoading, 
  uniqueCount, 
  hasAnyOils 
}) => {
  const theme = useTheme();
  const shimmerAnimation = useRef(new Animated.Value(0)).current;

  // Start shimmer animation when loading
  useEffect(() => {
    if (isLoading && !hasAnyOils) {
      const shimmer = Animated.loop(
        Animated.sequence([
          Animated.timing(shimmerAnimation, {
            toValue: 1,
            duration: 1200,
            useNativeDriver: true,
          }),
          Animated.timing(shimmerAnimation, {
            toValue: 0,
            duration: 1200,
            useNativeDriver: true,
          }),
        ])
      );
      shimmer.start();
      
      return () => shimmer.stop();
    }
  }, [isLoading, hasAnyOils, shimmerAnimation]);

  const getChipText = () => {
    if (isLoading && !hasAnyOils) {
      return 'Loading oils...';
    }
    
    if (uniqueCount === 0) {
      return '0 Essential oils';
    }
    
    return `${uniqueCount} Essential oil${uniqueCount === 1 ? '' : 's'}`;
  };

  const getChipStyle = () => {
    // Add shimmer effect when loading
    if (isLoading && !hasAnyOils) {
      return {
        opacity: shimmerAnimation.interpolate({
          inputRange: [0, 1],
          outputRange: [0.3, 0.8], // eslint-disable-line no-magic-numbers
        }),
      };
    }

    return {};
  };

  return (
    <View style={{
      flexDirection: 'row',
      justifyContent: 'flex-start',
      paddingHorizontal: theme.spacing.screenPadding,
      marginBottom: theme.spacing.lg,
    }}>
      {isLoading && !hasAnyOils ? (
        <Animated.View style={getChipStyle()}>
          <Chip
            mode="outlined"
            onPress={() => {}}
            style={{
              elevation: 0,
              shadowOpacity: 0,
              shadowOffset: { width: 0, height: 0 },
              shadowRadius: 0,
            }}
            textStyle={{
              fontSize: 14,
              fontWeight: '500',
            }}
            compact
          >
            {getChipText()}
          </Chip>
        </Animated.View>
      ) : (
        <Chip
          mode="outlined"
          onPress={() => {}}
          style={{}}
          textStyle={{
            fontSize: 14,
            fontWeight: '500',
          }}
          compact
        >
          {getChipText()}
        </Chip>
      )}
    </View>
  );
};
import React, { useEffect, useMemo, useCallback, useState, useRef } from 'react';
import { View, ScrollView } from 'react-native';
import { Text, Icon } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { usePropertiesSelection } from '@/features/create-recipe/hooks/use-properties-selection';
import ParallelStreamingBottomSheet from '@/shared/components/modals/streaming/parallel-streaming-bottom-sheet';
import { EnhancedPropertiesHeader } from './enhanced-properties-header';
import { useTheme } from '@/shared/hooks';
import { PropertiesListUI } from './properties-list-ui';

/**
 * Properties Selection Container Component
 * Refactored to Container/Presentational pattern following accordion-demo.tsx design
 * Maintains all existing business logic while presenting a clean, simplified UI
 */

interface PropertiesSelectionProps {
  onValidationChange?: (isValid: boolean) => void;
  onSubmitReady?: (submitFn: () => void) => void;
  onLoadingChange?: (isLoading: boolean) => void;
}

export const PropertiesSelectionMinimal: React.FC<PropertiesSelectionProps> = ({
  onValidationChange,
  onSubmitReady,
  onLoadingChange
}) => {
  const hookResult = usePropertiesSelection();
  const theme = useTheme();
  const { t } = useTranslation('create-recipe');

  // Defensive destructuring with fallbacks to prevent "undefined to object" error
  const {
    selectedPropertyIds = [],
    isSubmitting = false,
    showStreamingModal = false,
    parallelStreamingState = { isStreaming: false, results: new Map(), errors: new Map(), completedCount: 0 },
    handleSubmit = () => { },
    getCauseNamesByIds = () => [],
    getSymptomNamesByIds = () => [],
    therapeuticProperties = [],
    // Enrichment state tracking (for debugging only)
    propertyEnrichmentStates = {},
    // Navigation function
    completeStepAndGoNext = () => { },
    // Recipe generation functionality (NEW)
    showRecipeStreamingModal = false,
    isGeneratingRecipes = false,
    handleGenerateFinalRecipes = () => { }
  } = hookResult || {};

  // State for controlled accordion expansion
  const [expandedAccordions, setExpandedAccordions] = useState<string[]>([]);
  
  // Ref for ScrollView to control scroll position
  const scrollViewRef = useRef<ScrollView>(null);

  const handleAccordionPress = (id: string) => {
    setExpandedAccordions(currentExpanded => {
      const isExpanded = currentExpanded.includes(id);
      if (isExpanded) {
        // Close the current accordion
        return [];
      } else {
        // Open only this accordion (close any others)
        // Calculate scroll position to bring clicked accordion to top
        const clickedIndex = therapeuticProperties.findIndex(prop => prop.property_id === id);
        if (clickedIndex >= 0) {
          // Estimate scroll position: header + progress + (accordion height * index)
          // Header section ≈ 120px, Progress ≈ 80px, Each accordion ≈ 140px
          const headerHeight = 120;
          const progressHeight = 80;
          const accordionHeight = 140;
          const targetY = headerHeight + progressHeight + (accordionHeight * clickedIndex);
          
          // Smooth scroll to position with small delay to ensure state update
          setTimeout(() => {
            const SCROLL_OFFSET = 20; // Small offset from top
            scrollViewRef.current?.scrollTo({
              y: Math.max(0, targetY - SCROLL_OFFSET),
              animated: true
            });
          }, 100);
        }
        return [id];
      }
    });
  };

  // Memoized computed states to prevent infinite re-renders
  const isAnyPropertyEnriching = useMemo(() =>
    Object.values(propertyEnrichmentStates || {}).some(state => state.isEnriching),
    [propertyEnrichmentStates]
  );

  // Memoized navigation logic to prevent re-render loops
  const allPropertiesEnriched = useMemo(() =>
    therapeuticProperties.length > 0 &&
    therapeuticProperties.every(prop => prop.isEnriched && prop.suggested_oils && prop.suggested_oils.length > 0),
    [therapeuticProperties]
  );


  const enrichedPropertiesCount = useMemo(() =>
    therapeuticProperties.filter(prop => prop.isEnriched).length,
    [therapeuticProperties]
  );

  const totalPropertiesCount = useMemo(() =>
    therapeuticProperties.length,
    [therapeuticProperties.length]
  );

  // Pre-compute name mappings to avoid store calls during render
  const namesMappings = useMemo(() => {
    const causeNames = new Map<string, string>();
    const symptomNames = new Map<string, string>();

    // Collect all unique IDs first
    const allCauseIds = new Set<string>();
    const allSymptomIds = new Set<string>();

    therapeuticProperties.forEach(prop => {
      (prop.addresses_cause_ids || []).forEach(id => allCauseIds.add(id));
      (prop.addresses_symptom_ids || []).forEach(id => allSymptomIds.add(id));
    });

    // Get names for all unique IDs in batches to avoid repeated store calls
    if (allCauseIds.size > 0) {
      const batchCauseNames = getCauseNamesByIds(Array.from(allCauseIds));
      Array.from(allCauseIds).forEach((id, index) => {
        if (batchCauseNames[index]) {
          causeNames.set(id, batchCauseNames[index]);
        }
      });
    }

    if (allSymptomIds.size > 0) {
      const batchSymptomNames = getSymptomNamesByIds(Array.from(allSymptomIds));
      Array.from(allSymptomIds).forEach((id, index) => {
        if (batchSymptomNames[index]) {
          symptomNames.set(id, batchSymptomNames[index]);
        }
      });
    }

    return { causeNames, symptomNames };
  }, [therapeuticProperties, getCauseNamesByIds, getSymptomNamesByIds]);

  // Helper functions that use pre-computed mappings
  const getCachedCauseNames = useCallback((ids: string[]) => {
    return ids.map(id => namesMappings.causeNames.get(id)).filter((name): name is string => name !== undefined);
  }, [namesMappings.causeNames]);

  const getCachedSymptomNames = useCallback((ids: string[]) => {
    return ids.map(id => namesMappings.symptomNames.get(id)).filter((name): name is string => name !== undefined);
  }, [namesMappings.symptomNames]);

  // Simple V4 Callback Pattern - Single useEffect to prevent infinite loops
  useEffect(() => {
    if (onValidationChange) {
      // Button validation: Enable when we have properties to work with
      // The actual action (oil suggestions vs final recipes) is determined by allPropertiesEnriched
      onValidationChange(therapeuticProperties.length > 0);
    }
    if (onSubmitReady) {
      const currentAction = !allPropertiesEnriched ? handleSubmit : handleGenerateFinalRecipes;
      onSubmitReady(currentAction);
    }
    if (onLoadingChange) {
      const isLoading = isSubmitting || parallelStreamingState.isStreaming || isGeneratingRecipes;
      onLoadingChange(isLoading);
    }
  }, [
    onValidationChange,
    onSubmitReady,
    onLoadingChange,
    therapeuticProperties.length,
    allPropertiesEnriched,
    handleSubmit,
    handleGenerateFinalRecipes,
    isSubmitting,
    parallelStreamingState.isStreaming,
    isGeneratingRecipes
  ]);

  return (
    <View style={{ flex: 1, backgroundColor: theme.colors.background }}>
      {/* Enhanced Properties Header with chips and live oil count */}
      <EnhancedPropertiesHeader therapeuticProperties={therapeuticProperties} />
      <ScrollView
        ref={scrollViewRef}
        style={{ flex: 1 }}
        contentContainerStyle={{}}
        showsVerticalScrollIndicator={false}
      >
        {therapeuticProperties.length > 0 && (
          <>
            {/* New Presentational Component - Replaces complex nested UI */}
            <PropertiesListUI 
              properties={therapeuticProperties}
              expandedAccordions={expandedAccordions}
              onAccordionToggle={handleAccordionPress}
              parallelStreamingState={parallelStreamingState}
              propertyEnrichmentStates={propertyEnrichmentStates}
            />
          </>
        )}
      </ScrollView>

      {/* REMOVED: Oil Suggestions Modal - Now using in-place loading states */}

      {/* PRODUCTION Streaming Modal for Final Recipe Generation - Now using @gorhom/bottom-sheet */}
      <ParallelStreamingBottomSheet
        visible={showRecipeStreamingModal}
        title={t('modals.generatingRecipes.title')}
        description={t('modals.generatingRecipes.description')}
        selectedProperties={[
          { 
            property_name_localized: 'Morning Recipe', 
            property_id: 'morning', 
            isEnriched: true,
            property_name_english: 'Morning Recipe',
            description_contextual_localized: 'Morning recipe generation',
            addresses_cause_ids: [],
            addresses_symptom_ids: [],
            relevancy_score: 5,
            suggested_oils: []
          },
          { 
            property_name_localized: 'Mid-Day Recipe', 
            property_id: 'mid-day', 
            isEnriched: true,
            property_name_english: 'Mid-Day Recipe',
            description_contextual_localized: 'Mid-day recipe generation',
            addresses_cause_ids: [],
            addresses_symptom_ids: [],
            relevancy_score: 5,
            suggested_oils: []
          },
          { 
            property_name_localized: 'Night Recipe', 
            property_id: 'night', 
            isEnriched: true,
            property_name_english: 'Night Recipe',
            description_contextual_localized: 'Night recipe generation',
            addresses_cause_ids: [],
            addresses_symptom_ids: [],
            relevancy_score: 5,
            suggested_oils: []
          }
        ]}
        parallelStreamingState={parallelStreamingState}
        onDismiss={() => {
          console.log('🎭 Properties Final Recipes - Enhanced Modal onDismiss called - navigating to next step');
          completeStepAndGoNext();
        }}
        hapticType="none"
        enableBlur={true}
        enableStackAnimation={true}
        enableSwipeToDismiss={false}
      />

    </View>
  );
};
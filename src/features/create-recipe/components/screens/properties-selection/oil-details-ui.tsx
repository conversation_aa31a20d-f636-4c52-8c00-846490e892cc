/**
 * @fileoverview Oil Details UI - Refactored into separate Title and Description components
 * Clean, simplified oil display matching the accordion-demo.tsx pattern
 * Derives simplified safety indicators from complex safety data
 */

import React from 'react';
import { View } from 'react-native';
import { Text, Chip } from 'react-native-paper';
import { useTheme } from '@/shared/hooks';
import { useTranslation } from 'react-i18next';

interface SafetyData {
  internal_use?: {
    id?: string;
    name?: string;
    description?: string;
    guidance?: string;
  };
  dilution?: {
    name?: string;
    description?: string;
    percentage_min?: number;
    percentage_max?: number;
    ratio?: string;
  };
  phototoxicity?: {
    id?: string;
    status?: string;
    description?: string;
    guidance?: string;
  };
  pregnancy_nursing?: {
    name?: string;
    status_description?: string;
    code?: string;
    description?: string;
    usage_guidance?: string;
  }[];
  child_safety?: {
    age_range_id?: string;
    age_range?: string;
    safety_notes?: string;
  }[];
}

interface OilData {
  oil_id?: string;
  oil_name_localized?: string;
  name_localized?: string;
  oil_name_english?: string;
  name_english?: string;
  name_botanical?: string;
  botanical_name?: string;
  name_scientific?: string;  // Scientific name from safety database
  match_rationale_localized?: string;
  relevancy_to_property_score?: number;
  relevancy_score?: number;
  similarity_score?: number;
  isEnriched?: boolean;
  enrichment_status?: 'enriched' | 'not_found' | 'discarded';
  safety?: SafetyData;
  final_relevance_score?: number;  // Hybrid score across all properties (0-5 range)
}


// Helper functions
const getDilutionInfoForOil = (oil: OilData, t: Function) => {
  if (!oil.safety?.dilution) {
    return { 
      range: 'N/A', 
      ratio: 'N/A', 
      description: t('propertiesSelection.safety.dilutionNotAvailable'),
      min: 0,
      max: 5
    };
  }
  
  const { percentage_min, percentage_max, ratio, description: dilutionDesc } = oil.safety.dilution;
  
  const range = percentage_min !== undefined && percentage_max !== undefined 
    ? `${(percentage_min * 100)}% - ${(percentage_max * 100)}%`
    : percentage_min !== undefined 
      ? `${(percentage_min * 100)}%+`
      : 'N/A';
  
  return {
    range,
    ratio: ratio || '1:10',
    description: dilutionDesc || t('propertiesSelection.safety.followStandardGuidelines'),
    min: percentage_min !== undefined ? percentage_min * 100 : undefined,
    max: percentage_max !== undefined ? percentage_max * 100 : undefined
  };
};

const getSafetyIndicatorsForOil = (oil: OilData) => {
  const indicators: { icon: string; text: string; description: string; priority: 'high' | 'medium' | 'low' }[] = [];

  // 1. CHILD SAFETY ANALYSIS - Parse age-based restrictions (following safety-filter.ts pattern)
  if (oil.safety?.child_safety && Array.isArray(oil.safety.child_safety)) {
    oil.safety.child_safety.forEach(childSafety => {
      if (childSafety.age_range && childSafety.safety_notes) {
        // Parse age range (e.g., "0-10", "under 6") - following reference project regex pattern
        const ageMatch = childSafety.age_range.match(/(\d+)/g);
        if (ageMatch) {
          const maxAge = parseInt(ageMatch[ageMatch.length - 1]);
          // Check for avoid/contraindication keywords (following reference patterns)
          if (childSafety.safety_notes.toLowerCase().includes('avoid') || 
              childSafety.safety_notes.toLowerCase().includes('not recommended') ||
              childSafety.safety_notes.toLowerCase().includes('contraindicated')) {
            indicators.push({
              icon: 'alert',
              text: `Child Safety: Ages ${childSafety.age_range}`,
              description: childSafety.safety_notes,
              priority: 'high'
            });
          } else {
            indicators.push({
              icon: 'baby',
              text: `Child Guidance: Ages ${childSafety.age_range}`,
              description: childSafety.safety_notes,
              priority: 'medium'
            });
          }
        }
      }
    });
  }

  // 2. PREGNANCY/NURSING ANALYSIS - Process contraindications (following safety-filter.ts pattern)
  if (oil.safety?.pregnancy_nursing && Array.isArray(oil.safety.pregnancy_nursing)) {
    oil.safety.pregnancy_nursing.forEach(pregnancySafety => {
      if (pregnancySafety.status_description?.toLowerCase().includes('avoid') ||
          pregnancySafety.code === 'contraindicated') {
        indicators.push({
          icon: 'account-heart',
          text: 'Pregnancy/Nursing Warning',
          description: pregnancySafety.usage_guidance || pregnancySafety.description || pregnancySafety.status_description || 'Not recommended during pregnancy or nursing',
          priority: 'high'
        });
      } else if (pregnancySafety.usage_guidance || pregnancySafety.description) {
        indicators.push({
          icon: 'information',
          text: 'Pregnancy/Nursing Guidance',
          description: pregnancySafety.usage_guidance || pregnancySafety.description || 'Consult healthcare provider',
          priority: 'medium'
        });
      }
    });
  }

  // 3. PHOTOTOXICITY ANALYSIS - Database ID-based detection (standardized records)
  if (oil.safety?.phototoxicity?.id === '9a987a49-f246-4aa2-99d7-87d189a01d00') {
    // This is the database record for "Phototoxic" oils
    const photo = oil.safety.phototoxicity;
    indicators.push({
      icon: 'weather-sunny-alert',
      text: 'Phototoxic',
      description: photo.description || 'Avoid sun exposure after application',
      priority: 'high'
    });
  } else if (oil.safety?.phototoxicity?.id === 'ae18d720-4473-479e-b9f3-7fa65192d639') {
    // This is the database record for "Non-Phototoxic" oils
    const photo = oil.safety.phototoxicity;
    indicators.push({
      icon: 'weather-sunny',
      text: 'Sun Safe',
      description: photo.description || 'Safe for sun exposure after application',
      priority: 'low'
    });
  }

  // 4. INTERNAL USE SAFETY - Database ID-based detection (standardized records)
  if (oil.safety?.internal_use?.id === '247f091e-d55c-4a76-aaef-ea4485457b63') {
    // This is the database record for "Not for Internal Use" oils
    const internalUse = oil.safety.internal_use;
    indicators.push({
      icon: 'close-circle',
      text: 'Not for Internal Use',
      description: internalUse.description || 'Do not ingest - topical use only',
      priority: 'high'
    });
  } else if (oil.safety?.internal_use?.id === '2343a180-7dd2-45b1-a402-065b1bf2bd7c') {
    // This is the database record for "Safe for Internal Use" oils  
    const internalUse = oil.safety.internal_use;
    indicators.push({
      icon: 'check-circle',
      text: 'Safe for Internal Use',
      description: internalUse.description || 'May be used internally with proper guidance',
      priority: 'low'
    });
  }

  // 5. DEFAULT SAFETY GUIDANCE - If no specific data available
  if (indicators.length === 0) {
    indicators.push({
      icon: 'shield-check',
      text: 'General Safety',
      description: 'Always dilute properly and perform patch test before use',
      priority: 'low'
    });
  }

  // Sort by priority: high -> medium -> low
  const priorityOrder = { high: 0, medium: 1, low: 2 };
  indicators.sort((a, b) => priorityOrder[a.priority] - priorityOrder[b.priority]);

  // Return only top 2 most important indicators for UI display
  return indicators.slice(0, 2).map(({ priority, ...indicator }) => indicator);
};

const getStyles = (theme: any) => ({
  watchTitle: {
    flex: 1,
  },
  watchTitleRow: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
    marginBottom: theme.spacing.xs / 2,
  },
  watchName: {
    fontSize: 15,
    fontWeight: '600' as const,
    flex: 1,
  },
  watchSpecs: {
    fontSize: 12,
    marginTop: 1,
  },
  watchDescription: {
    marginTop: theme.spacing.sm,
  },
  watchFeatureRow: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    marginBottom: theme.spacing.md,
    paddingTop: theme.spacing.sm,
  },
  leftGroup: {
    flexDirection: 'row' as const,
    alignItems: 'flex-start' as const,
    gap: theme.spacing.sm,
  },
  rightGroup: {
    alignItems: 'flex-end' as const,
  },
  itemCircle: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    marginRight: theme.spacing.sm,
    backgroundColor: theme.colors.primaryContainer
  },
  itemCircleNumber: {
    fontSize: 14,
    fontWeight: '700' as const,
    textAlign: 'center' as const,
    color: theme.colors.onPrimaryContainer
  },
  featureLabel: {
    fontSize: 10,
    textAlign: 'center' as const,
    color: theme.colors.onSurfaceVariant
  },
  featureValue: {
    fontSize: 11,
    fontWeight: '500' as const,
    textAlign: 'center' as const,
    marginTop: theme.spacing.xs / 2,
    color: theme.colors.onSurface
  },
  fullWidthDescription: {
    fontSize: 12,
    lineHeight: 16,
    marginTop: theme.spacing.sm,
    marginBottom: theme.spacing.sm,
    paddingHorizontal: 0,
    textAlign: 'left' as const,
    color: theme.colors.onSurfaceVariant
  },
  fullWidthRangeBar: {
    marginTop: theme.spacing.sm,
    marginBottom: theme.spacing.sm,
  },
  rangeBarContainer: {
    width: '100%',
    height: 6,
    borderRadius: 3,
    marginBottom: 4,
    position: 'relative' as const,
    backgroundColor: theme.colors.outlineVariant
  },
  rangeBarFill: {
    position: 'absolute' as const,
    height: '100%',
    borderRadius: 3,
    backgroundColor: theme.colors.primary
  },
  safetyContainer: {
    flexDirection: 'row' as const,
    marginVertical: theme.spacing.md,
    gap: theme.spacing.md,
  },
  safetyColumn: {
    flex: 1,
    alignItems: 'stretch' as const,
  },
  safetyChip: {
    marginBottom: theme.spacing.xs,
  },
  safetyText: {
    fontSize: 10,
    lineHeight: 14,
    textAlign: 'left' as const,
    paddingHorizontal: 4,
    color: theme.colors.onSurfaceVariant
  },
});

// NEW COMPONENT 1: The Title
export const OilDetailsTitle: React.FC<{ oil: OilData }> = ({ oil }) => {
  const theme = useTheme();
  const { t } = useTranslation('create-recipe');
  const styles = getStyles(theme);

  const oilName = oil.oil_name_localized || oil.name_localized || 'Essential Oil';
  const englishName = oil.name_english || oil.oil_name_english || '';
  const scientificName = oil.name_scientific || '';
  const relevancyScore = oil.relevancy_to_property_score || oil.relevancy_score || 0;
  const description = oil.match_rationale_localized || '';

  return (
    <View style={styles.watchTitle}>
      <Text variant="titleMedium" style={[{ color: theme.colors.onSurface, marginBottom: scientificName ? theme.spacing.xs : theme.spacing.sm, fontWeight: 'bold' }]}>
        {oilName}
        {englishName && (
          <Text variant="titleMedium" style={{ fontWeight: 'normal' }}>
            {` (${englishName})`}
          </Text>
        )}
      </Text>
      
      {scientificName && (
        <Text variant="bodyMedium" style={[{ color: theme.colors.onSurfaceVariant, marginBottom: theme.spacing.sm }]}>
          Scientific: {scientificName}
        </Text>
      )}
      
      <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: theme.spacing.sm }}>
        <Chip 
          icon="star" 
          style={styles.safetyChip}
        >
          {t('propertiesSelection.chips.property')}: {relevancyScore.toFixed(1)}
        </Chip>
        <Chip 
          icon="star" 
          style={styles.safetyChip}
        >
          {t('propertiesSelection.chips.holistic')}: {oil.final_relevance_score?.toFixed(1) || 'N/A'}
        </Chip>
      </View>
      
      {description && (
        <Text variant="bodyMedium" style={[{ color: theme.colors.onSurfaceVariant, marginTop: theme.spacing.sm }]}>
          {description}
        </Text>
      )}
    </View>
  );
};

// NEW COMPONENT 2: The Description
export const OilDetailsDescription: React.FC<{ oil: OilData }> = ({ oil }) => {
  const theme = useTheme();
  const { t } = useTranslation('create-recipe');
  const styles = getStyles(theme);

  const dilutionInfo = getDilutionInfoForOil(oil, t);
  const safetyIndicators = getSafetyIndicatorsForOil(oil);

  return (
    <View style={styles.watchDescription}>
      <View style={styles.watchFeatureRow}>
        <View style={styles.leftGroup}>
          <View style={styles.itemCircle}>
            <Text variant="labelMedium" style={[styles.itemCircleNumber]}>
              {dilutionInfo.description.charAt(0).toUpperCase()}
            </Text>
          </View>
          <View>
            <Text variant="labelSmall" style={styles.featureLabel}>{t('propertiesSelection.safety.dilutionRange')}</Text>
            <Text variant="bodySmall" style={[styles.featureValue]}>{dilutionInfo.range}</Text>
          </View>
        </View>
        
        <View style={styles.rightGroup}>
          <Text variant="labelSmall" style={styles.featureLabel}>{t('propertiesSelection.safety.ratio')}</Text>
          <Text variant="bodySmall" style={[styles.featureValue]}>{dilutionInfo.ratio}</Text>
        </View>
      </View>
      
      <Text variant="bodyMedium" style={[styles.fullWidthDescription]}>
        {dilutionInfo.description}
      </Text>
      
      <View style={styles.fullWidthRangeBar}>
        <View style={styles.rangeBarContainer}>
          {dilutionInfo.min !== undefined && dilutionInfo.max !== undefined && (
            <View 
              style={[
                styles.rangeBarFill, 
                { 
                  left: `${dilutionInfo.min}%`,
                  width: `${Math.max(1, dilutionInfo.max - dilutionInfo.min)}%`
                }
              ]} 
            />
          )}
        </View>
      </View>
      
      {safetyIndicators && safetyIndicators.length > 0 && (
        <View style={styles.safetyContainer}>
          {safetyIndicators.slice(0, 2).map((indicator, index) => (
            <View key={index} style={styles.safetyColumn}>
              <Chip 
                icon={indicator.icon}
                style={styles.safetyChip}
              >
                {indicator.text}
              </Chip>
              <Text variant="labelSmall" style={styles.safetyText}>
                {indicator.description}
              </Text>
            </View>
          ))}
        </View>
      )}
    </View>
  );
};


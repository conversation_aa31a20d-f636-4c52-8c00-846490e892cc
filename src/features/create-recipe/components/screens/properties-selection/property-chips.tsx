import React from 'react';
import { View } from 'react-native';
import { Chip } from 'react-native-paper';
import { useTheme } from '@/shared/hooks';
import { TherapeuticProperty } from '../../types';

interface PropertyChipsProps {
  therapeuticProperties: TherapeuticProperty[];
  maxVisible?: number;
}

/**
 * PropertyChips - Displays therapeutic property names as flat Material Design chips
 * Shows max 5 properties with "+X more" overflow handling
 * Uses existing accordion colors for consistency
 */
export const PropertyChips: React.FC<PropertyChipsProps> = ({ 
  therapeuticProperties, 
  maxVisible = 5 
}) => {
  const theme = useTheme();

  if (therapeuticProperties.length === 0) {
    return null;
  }

  const visibleProperties = therapeuticProperties.slice(0, maxVisible);
  const hiddenCount = Math.max(0, therapeuticProperties.length - maxVisible);

  return (
    <View style={{
      flexDirection: 'row',
      flexWrap: 'wrap',
      alignItems: 'flex-start',
      justifyContent: 'flex-start',
      paddingHorizontal: theme.spacing.screenPadding,
      marginBottom: theme.spacing.sm,
    }}>
      {visibleProperties.map((property, index) => (
        <Chip
          key={property.property_id}
          onPress={() => {}}
          style={{
            margin: theme.spacing.xs / 2,
            backgroundColor: theme.colors.surfaceVariant,
          }}
          textStyle={{
            fontSize: 12,
          }}
          compact
        >
          {property.property_name_localized || property.property_name_english || `Property ${index + 1}`}
        </Chip>
      ))}
      
      {hiddenCount > 0 && (
        <Chip
          onPress={() => {}}
          style={{
            margin: theme.spacing.xs / 2,
            backgroundColor: theme.colors.surfaceVariant,
          }}
          textStyle={{
            fontSize: 12,
          }}
          compact
        >
          +{hiddenCount} more
        </Chip>
      )}
    </View>
  );
};
import React from 'react';
import { View } from 'react-native';
import { Text } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/shared/hooks';
import { useUniqueOilCount } from '../../../hooks/use-unique-oil-count';
import { PropertyChips } from './property-chips';
import { OilCountChip } from './oil-count-chip';
import { TherapeuticProperty } from '../../types';

interface EnhancedPropertiesHeaderProps {
  therapeuticProperties: TherapeuticProperty[];
}

/**
 * EnhancedPropertiesHeader - Rich header component showing property preview and live oil count
 * Replaces the simple subtitle text with engaging visual preview
 */
export const EnhancedPropertiesHeader: React.FC<EnhancedPropertiesHeaderProps> = ({
  therapeuticProperties
}) => {
  const { t } = useTranslation('create-recipe');
  const theme = useTheme();
  const { uniqueCount, hasAnyOils, isLoading } = useUniqueOilCount(therapeuticProperties);

  return (
    <View style={{
      paddingTop: theme.spacing.xl,
      marginBottom: theme.spacing.xs,
    }}>
      {/* Title with count */}
      <View style={{
        paddingHorizontal: theme.spacing.screenPadding,
        marginBottom: theme.spacing.md,
      }}>
        <Text
          variant="titleLarge"
          style={{
            textAlign: 'left',
            color: theme.colors.onSurface,
            fontWeight: '600',
          }}
        >
          {therapeuticProperties.length}{t('propertiesSelection.subtitleSuffix')}
        </Text>
      </View>

      {/* Property chips preview */}
      <PropertyChips therapeuticProperties={therapeuticProperties} />

      {/* Essential oils count with real-time updates */}
      <OilCountChip 
        isLoading={isLoading}
        uniqueCount={uniqueCount}
        hasAnyOils={hasAnyOils}
      />
    </View>
  );
};
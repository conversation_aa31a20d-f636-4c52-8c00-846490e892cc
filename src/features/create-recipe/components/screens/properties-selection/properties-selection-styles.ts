import { StyleSheet } from 'react-native';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerCard: {
    padding: 16,
    margin: 16,
    marginBottom: 8,
    borderRadius: 12,
  },
  scrollView: {
    flex: 1,
  },
  listContainer: {
    paddingHorizontal: 16,
    gap: 12,
  },
  propertyCard: {
    borderRadius: 12,
  },
  propertyHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  propertyContent: {
    flex: 1,
    marginRight: 12,
  },
  propertyDetails: {
    marginTop: 4,
  },
  bottomBar: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  enrichmentBar: {
    position: 'absolute',
    bottom: 70, // Above the bottom bar
    left: 0,
    right: 0,
  },
  // Nested oils styles
  oilsContainer: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  oilsHeader: {
    marginBottom: 8,
  },
  oilCard: {
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    borderLeftWidth: 3,
    borderLeftColor: 'transparent',
  },
  oilItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  oilBullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    marginTop: 6,
    marginRight: 8,
  },
  oilContent: {
    flex: 1,
  },
  oilNames: {
    marginBottom: 4,
  },
  relevancyBadge: {
    alignSelf: 'flex-start',
    marginBottom: 4,
  },
  rationaleContainer: {
    marginTop: 4,
  },
  enrichmentStatus: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.05)',
  },
  // Debug enrichment section
  enrichmentDebugSection: {
    borderRadius: 12,
  },
});
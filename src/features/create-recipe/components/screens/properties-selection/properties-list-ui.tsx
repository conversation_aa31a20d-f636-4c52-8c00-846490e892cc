/**
 * @fileoverview Properties List UI - Presentational Component
 * Clean accordion list matching the accordion-demo.tsx pattern
 * Shows properties that expand to reveal their suggested oils
 */

import React from 'react';
import { View } from 'react-native';
import { Text, List, Divider, IconButton, Badge } from 'react-native-paper';
import { useTheme } from '@/shared/hooks';
import { useTranslation } from 'react-i18next';
import { OilDetailsTitle, OilDetailsDescription } from './oil-details-ui';
import { UniversalList } from '@/shared/components/data-display/universal-list';

interface OilData {
  oil_id?: string;
  oil_name_localized?: string;
  name_localized?: string;
  oil_name_english?: string;
  name_english?: string;
  name_botanical?: string;
  botanical_name?: string;
  name_scientific?: string;  // Scientific name from safety database
  match_rationale_localized?: string;
  relevancy_to_property_score?: number;
  relevancy_score?: number;
  similarity_score?: number;
  isEnriched?: boolean;
  enrichment_status?: 'enriched' | 'not_found' | 'discarded';
  safety?: {
    internal_use?: {
      name?: string;
      description?: string;
      guidance?: string;
    };
    dilution?: {
      name?: string;
      description?: string;
      percentage_min?: number;
      percentage_max?: number;
      ratio?: string;
    };
    phototoxicity?: {
      status?: string;
      description?: string;
      guidance?: string;
    };
    pregnancy_nursing?: {
      name?: string;
      status_description?: string;
      code?: string;
      description?: string;
      usage_guidance?: string;
    }[];
    child_safety?: {
      age_range_id?: string;
      age_range?: string;
      safety_notes?: string;
    }[];
  };
}

interface TherapeuticProperty {
  property_id: string;
  property_name_localized: string;
  property_name_english?: string;
  description_contextual_localized: string;
  addresses_cause_ids?: string[];
  addresses_symptom_ids?: string[];
  relevancy_score?: number;
  isEnriched?: boolean;
  suggested_oils?: OilData[];
}

interface PropertiesListUIProps {
  properties: TherapeuticProperty[];
  expandedAccordions: string[];
  onAccordionToggle: (propertyId: string) => void;
  // New props for loading states
  parallelStreamingState?: {
    isStreaming: boolean;
    results: Map<string, any>;
    errors: Map<string, any>;
    completedCount: number;
  };
  propertyEnrichmentStates?: Record<string, {
    isEnriching: boolean;
    retryCount: number;
    error?: string;
  }>;
}

export const PropertiesListUI: React.FC<PropertiesListUIProps> = ({
  properties,
  expandedAccordions,
  onAccordionToggle,
  // New props for loading states
  parallelStreamingState = { isStreaming: false, results: new Map(), errors: new Map(), completedCount: 0 },
  propertyEnrichmentStates = {}
}) => {
  const theme = useTheme();
  const { t } = useTranslation('create-recipe');

  const getStyles = (theme: any) => ({
    dividerLine: {
      height: 1,
      marginVertical: 6,
    },
    relevancyRow: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      marginTop: 4,
    },
    starsContainer: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
    },
    starIcon: {
      margin: 0,
      width: 16,
      height: 16,
    },
    leftIconButton: {
      width: 32,
      height: 32,
      marginLeft: 16,
      marginRight: 8,
    },
    leftBadge: {
      width: 32,
      height: 32,
      borderRadius: 16,
      marginLeft: 16,
      marginRight: 8,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
      fontSize: 14,
      fontWeight: '600' as const,
      textAlign: 'center' as const,
      textAlignVertical: 'center' as const,
      lineHeight: 32,
    },
  });

  const styles = getStyles(theme);

  const isAccordionExpanded = (propertyId: string) => expandedAccordions.includes(propertyId);

  // Helper function to determine visual state for each property
  const getPropertyVisualState = (propertyId: string, property: any) => {
    const enrichmentState = propertyEnrichmentStates[propertyId];
    const hasStreamingResult = parallelStreamingState.results.has(propertyId);
    const hasStreamingError = parallelStreamingState.errors.has(propertyId);
    
    // Phase 1: Suggested oils streaming (during modal phase)
    if (parallelStreamingState.isStreaming && !hasStreamingResult && !hasStreamingError) {
      return 'streaming';
    }
    
    // Phase 2: Enrichment loading (after streaming completes)
    if (enrichmentState?.isEnriching) {
      return 'enriching';
    }
    
    // Error state (max retries reached)
    const MAX_RETRIES = 3;
    if (enrichmentState?.error && enrichmentState.retryCount >= MAX_RETRIES) {
      return 'error';
    }
    
    // Success state (enriched oils available)
    const enrichedOils = property.suggested_oils?.filter(oil => oil.enrichment_status === 'enriched') || [];
    if (property.isEnriched && enrichedOils.length > 0) {
      return 'success';
    }
    
    // Initial state (no oils yet)
    return 'initial';
  };

  const renderLeftIcon = (propertyId: string, property: any, isExpanded: boolean) => {
    const visualState = getPropertyVisualState(propertyId, property);
    const enrichedOils = property.suggested_oils?.filter(oil => oil.enrichment_status === 'enriched') || [];
    const oilCount = enrichedOils.length;

    switch (visualState) {
      case 'streaming':
      case 'enriching':
        return (
          <IconButton
            icon=""
            mode="contained"
            loading={true}
            size={24}
            disabled
            style={styles.leftIconButton}
            iconColor={theme.colors.onPrimary}
            containerColor={isExpanded ? theme.colors.onPrimary : theme.colors.primary}
          />
        );
      
      case 'error':
        return (
          <IconButton
            icon="alert-circle"
            mode="contained-tonal"
            size={24}
            style={styles.leftIconButton}
            iconColor={theme.colors.error}
            containerColor={theme.colors.errorContainer}
          />
        );
      
      case 'success':
      case 'initial':
      default:
        return (
          <Badge 
            visible={true}
            style={[
              styles.leftBadge,
              { 
                backgroundColor: isExpanded ? theme.colors.onPrimary : theme.colors.primary,
                color: isExpanded ? theme.colors.primary : theme.colors.onPrimary,
              }
            ]}
          >
            {oilCount}
          </Badge>
        );
    }
  };

  const getAccordionContent = (property: any, hasOils: boolean, enrichedOils: any[]) => {
    const visualState = getPropertyVisualState(property.property_id, property);
    
    if (visualState === 'streaming') {
      return (
        <List.Item
          title={t('propertiesSelection.loadingSuggestedOils', { default: 'Loading suggested oils...' })}
          description={t('propertiesSelection.loadingSuggestedOilsDescription', { default: 'Analyzing properties to find the best essential oils' })}
          left={() => <List.Icon icon="loading" />}
        />
      );
    }
    
    if (visualState === 'enriching') {
      return (
        <List.Item
          title={t('propertiesSelection.loadingEnrichment', { default: 'Enriching oil details...' })}
          description={t('propertiesSelection.loadingEnrichmentDescription', { default: 'Getting safety information and usage details' })}
          left={() => <List.Icon icon="loading" />}
        />
      );
    }
    
    if (visualState === 'error') {
      return (
        <List.Item
          title={t('propertiesSelection.loadingError', { default: 'Unable to load oil suggestions' })}
          description={t('propertiesSelection.loadingErrorDescription', { default: 'Please check your connection and try again' })}
          left={() => <List.Icon icon="alert-circle" color={theme.colors.error} />}
        />
      );
    }
    
    // Success state - show oils with the PERFECT flatMap solution
    if (hasOils && enrichedOils.length > 0) {
      return enrichedOils.flatMap((oil, oilIndex) => {
        const key = oil.oil_id || oilIndex;
        
        const listItem = (
          <List.Item
            key={key}
            title={() => <OilDetailsTitle oil={oil} />}
            description={() => <OilDetailsDescription oil={oil} />}
            style={{ 
              paddingHorizontal: theme.spacing.screenPadding, 
              paddingVertical: theme.spacing.md 
            }}
          />
        );

        if (oilIndex < enrichedOils.length - 1) {
          return [listItem, <Divider key={`divider-${key}`} leftInset />];
        }
        return [listItem];
      });
    }
    
    // Initial/no oils state
    return (
      <List.Item
        title={t('propertiesSelection.noOilsMessage')}
        description={t('propertiesSelection.noOilsDescription')}
      />
    );
  };


  // This function encapsulates the entire rendering of ONE accordion item.
  // The logic is COPIED DIRECTLY from the old .map() block to ensure nothing is lost.
  const renderPropertyItem = (property: TherapeuticProperty, index: number) => {
    const propertyId = property.property_id;
    const isExpanded = isAccordionExpanded(propertyId);
    const enrichedOils = property.suggested_oils?.filter(oil => oil.enrichment_status === 'enriched') || [];
    const hasOils = enrichedOils.length > 0;
    const relevancyScore = property.relevancy_score || 0;
    
    // This is the CRITICAL part: return the exact, unmodified List.Accordion JSX.
    return (
      <List.Accordion
        left={() => (
          <View style={{ justifyContent: 'center' }}>
            {renderLeftIcon(propertyId, property, isExpanded)}
          </View>
        )}
        title={property.property_name_localized}
        titleStyle={{
          color: isExpanded ? theme.colors.onPrimary : theme.colors.onSurface,
          fontWeight: 'bold'
        }}
        description={
          <View>
            <View style={styles.relevancyRow}>
              <View style={styles.starsContainer}>
                {Array.from({ length: 5 }, (_, i) => i + 1).map((star) => (
                  <IconButton
                    key={star}
                    icon={star <= relevancyScore ? 'star' : 'star-outline'}
                    iconColor={
                      star <= relevancyScore 
                        ? (isExpanded ? theme.colors.onPrimary : theme.colors.tertiary)
                        : (isExpanded ? theme.colors.onPrimary : theme.colors.onSurfaceVariant)
                    }
                    size={12}
                    style={styles.starIcon}
                  />
                ))}
              </View>
            </View>
            <Text style={{ 
              color: isExpanded ? theme.colors.onPrimary : theme.colors.onSurfaceVariant, 
              marginBottom: 6
            }}>
              {property.description_contextual_localized}
            </Text>
          </View>
        }
        expanded={isExpanded}
        onPress={() => onAccordionToggle(propertyId)}
        style={{
          backgroundColor: isExpanded ? theme.colors.primary : 'transparent'
        }}
        right={() => (
          <List.Icon 
            icon={isExpanded ? 'chevron-up' : 'chevron-down'} 
            color={isExpanded ? theme.colors.onPrimary : theme.colors.onSurfaceVariant}
          />
        )}
      >
        <Divider />
        {getAccordionContent(property, hasOils, enrichedOils)}
      </List.Accordion>
    );
  };

  return (
    <UniversalList<TherapeuticProperty>
      items={properties}
      getItemId={(item) => item.property_id}
      renderItem={renderPropertyItem}
      listHeaderComponent={<List.Subheader>{t('propertiesSelection.title')}</List.Subheader>}
      useInsetDividers={false} // Use full-width dividers between accordions
    />
  );
};
/**
 * Oil Substitution Bottom Sheet Modal - ENHANCED VERSION
 * 
 * Displays a list of therapeutically equivalent alternative oils
 * for substitution in final recipes. Uses existing scored data
 * from the hybrid scoring algorithm.
 * 
 * ENHANCEMENTS:
 * - Original oil scoring: Shows relevance score AND specialist fit for the oil being substituted
 * - Smart filtering: Excludes oils already selected in the current recipe
 * - Specialist Fit display: Shows specialization score as percentage for informed decisions
 * - Therapeutic properties display for original oil AND all substitution candidates
 * - Semantic icon system for instant safety recognition
 * - Color-coded safety indicators (green/tertiary for safe, red/error for caution)
 * - Clean information hierarchy using description prop
 * - Improved scannability with visual cues
 * - Educational comparison: users can see category overlap between oils
 * - Follows "content-first" design principles from CLAUDE.md
 */

import React, { useMemo, useCallback } from 'react';
import { View } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { Text, List, Chip, Divider } from 'react-native-paper';
import { 
  BottomSheetModal, 
  BottomSheetScrollView,
  BottomSheetBackdrop
} from '@gorhom/bottom-sheet';
import { useTheme } from '@/shared/hooks/use-theme';
import { useTranslation } from 'react-i18next';
import { haptics } from '@/shared/utils/haptics';
import { useCombinedRecipeStore } from '@/features/create-recipe/store/combined-store';
import type { EnrichedEssentialOil, RecipeTimeSlot } from '../../types/recipe.types';

interface OilSubstitutionBottomSheetProps {
  modalRef: React.RefObject<BottomSheetModal>;
  originalOil: {
    oil_id: string;
    name_localized: string;
  } | null;
  alternativeOils: EnrichedEssentialOil[];
  onSelectSubstitute: (oil: EnrichedEssentialOil) => void;
  onDismiss: () => void;
  selectedOils: EnrichedEssentialOil[];
  timeSlot: RecipeTimeSlot;
}

// --- ENHANCED COMPONENTS FOR IMPROVED UX ---


/**
 * Adapted dilution information display from oil-details-ui.tsx patterns
 * Works with EnrichedEssentialOil data structure
 */
const getDilutionInfoForOil = (oil: EnrichedEssentialOil, t: Function) => {
  if (!oil.safety?.dilution) {
    return { 
      range: 'N/A', 
      ratio: 'N/A', 
      description: t('oilSubstitution.dilutionNotAvailable', { defaultValue: 'Standard dilution guidelines apply' }),
      min: 0,
      max: 5
    };
  }
  
  const { percentage_min, percentage_max, ratio, description: dilutionDesc } = oil.safety.dilution;
  
  const range = percentage_min !== undefined && percentage_min !== null && percentage_max !== undefined && percentage_max !== null
    ? `${(percentage_min * 100)}% - ${(percentage_max * 100)}%`
    : percentage_min !== undefined && percentage_min !== null
      ? `${(percentage_min * 100)}%+`
      : 'N/A';
  
  return {
    range,
    ratio: ratio || '1:10',
    description: dilutionDesc || t('oilSubstitution.followStandardGuidelines', { defaultValue: 'Always dilute properly and perform patch test before use' }),
    min: percentage_min !== undefined && percentage_min !== null ? percentage_min * 100 : undefined,
    max: percentage_max !== undefined && percentage_max !== null ? percentage_max * 100 : undefined
  };
};

/**
 * Adapted safety indicators from oil-details-ui.tsx patterns
 * Works with EnrichedEssentialOil data structure using ID-based mappings
 */
const getSafetyIndicatorsForOil = (oil: EnrichedEssentialOil) => {
  const indicators: { icon: string; text: string; description: string; priority: 'high' | 'medium' | 'low' }[] = [];

  // 1. CHILD SAFETY ANALYSIS - Parse age-based restrictions (following safety-filter.ts pattern)
  if (oil.safety?.child_safety && Array.isArray(oil.safety.child_safety)) {
    oil.safety.child_safety.forEach(childSafety => {
      if (childSafety.age_range && childSafety.safety_notes) {
        // Parse age range (e.g., "0-10", "under 6") - following reference project regex pattern
        const ageMatch = childSafety.age_range.match(/(\d+)/g);
        if (ageMatch) {
          // Check for avoid/contraindication keywords (following reference patterns)
          if (childSafety.safety_notes.toLowerCase().includes('avoid') || 
              childSafety.safety_notes.toLowerCase().includes('not recommended') ||
              childSafety.safety_notes.toLowerCase().includes('contraindicated')) {
            indicators.push({
              icon: 'alert',
              text: `Child Safety: Ages ${childSafety.age_range}`,
              description: childSafety.safety_notes,
              priority: 'high'
            });
          } else {
            indicators.push({
              icon: 'baby',
              text: `Child Guidance: Ages ${childSafety.age_range}`,
              description: childSafety.safety_notes,
              priority: 'medium'
            });
          }
        }
      }
    });
  }

  // 2. PREGNANCY/NURSING ANALYSIS - Process contraindications (following safety-filter.ts pattern)
  if (oil.safety?.pregnancy_nursing && Array.isArray(oil.safety.pregnancy_nursing)) {
    oil.safety.pregnancy_nursing.forEach(pregnancySafety => {
      if (pregnancySafety.status_description?.toLowerCase().includes('avoid') ||
          pregnancySafety.code === 'contraindicated') {
        indicators.push({
          icon: 'account-heart',
          text: 'Pregnancy/Nursing Warning',
          description: pregnancySafety.usage_guidance || pregnancySafety.description || pregnancySafety.status_description || 'Not recommended during pregnancy or nursing',
          priority: 'high'
        });
      } else if (pregnancySafety.usage_guidance || pregnancySafety.description) {
        indicators.push({
          icon: 'information',
          text: 'Pregnancy/Nursing Guidance',
          description: pregnancySafety.usage_guidance || pregnancySafety.description || 'Consult healthcare provider',
          priority: 'medium'
        });
      }
    });
  }

  // 3. PHOTOTOXICITY ANALYSIS - Database ID-based detection (adapted for EnrichedEssentialOil)
  if (oil.safety?.phototoxicity_id === '9a987a49-f246-4aa2-99d7-87d189a01d00') {
    // This is the database record for "Phototoxic" oils
    indicators.push({
      icon: 'weather-sunny-alert',
      text: 'Phototoxic',
      description: 'Avoid sun exposure after application',
      priority: 'high'
    });
  } else if (oil.safety?.phototoxicity_id === 'ae18d720-4473-479e-b9f3-7fa65192d639') {
    // This is the database record for "Non-Phototoxic" oils
    indicators.push({
      icon: 'weather-sunny',
      text: 'Sun Safe',
      description: 'Safe for sun exposure after application',
      priority: 'low'
    });
  }

  // 4. INTERNAL USE SAFETY - Database ID-based detection (adapted for EnrichedEssentialOil)
  if (oil.safety?.internal_use_id === '247f091e-d55c-4a76-aaef-ea4485457b63') {
    // This is the database record for "Not for Internal Use" oils
    indicators.push({
      icon: 'close-circle',
      text: 'Not for Internal Use',
      description: 'Do not ingest - topical use only',
      priority: 'high'
    });
  } else if (oil.safety?.internal_use_id === '2343a180-7dd2-45b1-a402-065b1bf2bd7c') {
    // This is the database record for "Safe for Internal Use" oils  
    indicators.push({
      icon: 'check-circle',
      text: 'Safe for Internal Use',
      description: 'May be used internally with proper guidance',
      priority: 'low'
    });
  }

  // 5. DEFAULT SAFETY GUIDANCE - If no specific data available
  if (indicators.length === 0) {
    indicators.push({
      icon: 'shield-check',
      text: 'General Safety',
      description: 'Always dilute properly and perform patch test before use',
      priority: 'low'
    });
  }

  // Sort by priority: high -> medium -> low
  const priorityOrder = { high: 0, medium: 1, low: 2 };
  indicators.sort((a, b) => priorityOrder[a.priority] - priorityOrder[b.priority]);

  // Return only top 4 most important indicators for UI display
  return indicators.slice(0, 4).map(({ priority, ...indicator }) => indicator);
};


/**
 * Oil title component adapted from oil-details-ui.tsx OilDetailsTitle
 */
const OilListItemTitle = ({ oil, index, theme, t }: {
  oil: EnrichedEssentialOil;
  index: number;
  theme: any;
  t: Function;
}) => {
  const styles = {
    watchTitle: { flex: 1 },
    safetyChip: { marginBottom: theme.spacing.xs }
  };

  const oilName = oil.name_localized || 'Essential Oil';
  const englishName = oil.name_english;
  const scientificName = oil.name_scientific || oil.name_botanical;
  const relevancyScore = oil.final_relevance_score || 0;
  const description = oil.match_rationale_localized || '';

  return (
    <View style={styles.watchTitle}>
      <Text variant="titleMedium" style={{ 
        color: theme.colors.onSurface, 
        marginBottom: theme.spacing.xs, 
        fontWeight: 'bold' 
      }}>
        {oilName}
      </Text>
      
      {englishName && oilName.toLowerCase() !== englishName.toLowerCase() && (
        <Text variant="bodyMedium" style={{
          color: theme.colors.onSurfaceVariant,
          marginBottom: theme.spacing.xs,
        }}>
          (English: {englishName})
        </Text>
      )}
      
      {scientificName && (
        <Text variant="bodyMedium" style={{ 
          color: theme.colors.onSurfaceVariant, 
          marginBottom: theme.spacing.xs,
          fontStyle: 'italic'
        }}>
          {scientificName}
        </Text>
      )}
            
      {description && (
        <Text variant="bodyMedium" style={{ 
          color: theme.colors.onSurfaceVariant, 
          marginBottom: theme.spacing.md 
        }}>
          {description}
        </Text>
      )}
      
      <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: theme.spacing.sm }}>
        <Chip icon="star" style={styles.safetyChip}>
          Relevance: {relevancyScore.toFixed(1)}
        </Chip>
        {oil.specialization_score !== undefined && (
          <Chip icon="target" style={styles.safetyChip}>
            {`${(oil.specialization_score * 100).toFixed(0)}% Fit`}
          </Chip>
        )}
        {index === 0 && (
          <Chip 
            style={[styles.safetyChip, { backgroundColor: theme.colors.primaryContainer }]}
            textStyle={{ color: theme.colors.onPrimaryContainer, fontSize: 10, fontWeight: 'bold' }}
          >
            Best Match
          </Chip>
        )}
      </View>
    </View>
  );
};

/**
 * Displays therapeutic properties (categories) for a given oil
 */
const TherapeuticPropertiesDisplay = ({ oil, therapeuticProperties, theme }: {
  oil: EnrichedEssentialOil;
  therapeuticProperties: any[];
  theme: any;
}) => {
  const oilCategories = useMemo(() => {
    if (!oil.oil_id || !therapeuticProperties) return [];
    
    return therapeuticProperties.filter(property => 
      property.suggested_oils?.some((suggestedOil: any) => suggestedOil.oil_id === oil.oil_id)
    );
  }, [oil.oil_id, therapeuticProperties]);

  if (oilCategories.length === 0) return null;

  return (
    <View style={{ marginVertical: theme.spacing.md }}>
      <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: theme.spacing.xs }}>
        {oilCategories.map((property) => (
          <Chip 
            key={property.property_id}
            compact
            style={{ backgroundColor: theme.colors.secondaryContainer }}
            textStyle={{ fontSize: 10 }}
          >
            {property.property_name_localized}
          </Chip>
        ))}
      </View>
    </View>
  );
};

/**
 * Oil description component adapted from oil-details-ui.tsx OilDetailsDescription
 */
const OilListItemDescription = ({ oil, t, theme, therapeuticProperties }: {
  oil: EnrichedEssentialOil;
  t: Function;
  theme: any;
  therapeuticProperties: any[];
}) => {
  const dilutionInfo = useMemo(() => getDilutionInfoForOil(oil, t), [oil, t]);
  const safetyIndicators = useMemo(() => getSafetyIndicatorsForOil(oil), [oil]);

  const styles = {
    watchDescription: { marginTop: theme.spacing.sm },
    watchFeatureRow: {
      flexDirection: 'row' as const,
      justifyContent: 'space-between' as const,
      marginBottom: theme.spacing.md,
      paddingTop: theme.spacing.sm,
    },
    leftGroup: {
      flexDirection: 'row' as const,
      alignItems: 'flex-start' as const,
      gap: theme.spacing.sm,
    },
    rightGroup: { alignItems: 'flex-end' as const },
    itemCircle: {
      width: 28,
      height: 28,
      borderRadius: 14,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
      marginRight: theme.spacing.sm,
      backgroundColor: theme.colors.primaryContainer
    },
    itemCircleNumber: {
      fontSize: 14,
      fontWeight: '700' as const,
      textAlign: 'center' as const,
      color: theme.colors.onPrimaryContainer
    },
    featureLabel: {
      fontSize: 10,
      textAlign: 'center' as const,
      color: theme.colors.onSurfaceVariant
    },
    featureValue: {
      fontSize: 11,
      fontWeight: '500' as const,
      textAlign: 'center' as const,
      marginTop: theme.spacing.xs / 2,
      color: theme.colors.onSurface
    },
    fullWidthDescription: {
      fontSize: 12,
      lineHeight: 16,
      marginTop: theme.spacing.sm,
      marginBottom: theme.spacing.sm,
      paddingHorizontal: 0,
      textAlign: 'left' as const,
      color: theme.colors.onSurfaceVariant
    },
    fullWidthRangeBar: {
      marginTop: theme.spacing.sm,
      marginBottom: theme.spacing.sm,
    },
    rangeBarContainer: {
      width: '100%',
      height: 6,
      borderRadius: 3,
      marginBottom: 4,
      position: 'relative' as const,
      backgroundColor: theme.colors.outlineVariant
    },
    rangeBarFill: {
      position: 'absolute' as const,
      height: '100%',
      borderRadius: 3,
      backgroundColor: theme.colors.primary
    },
    safetyContainer: {
      flexDirection: 'row' as const,
      marginVertical: theme.spacing.md,
      gap: theme.spacing.md,
    },
    safetyColumn: {
      flex: 1,
      alignItems: 'stretch' as const,
    },
    safetyChip: { marginBottom: theme.spacing.xs },
    safetyText: {
      fontSize: 10,
      lineHeight: 14,
      textAlign: 'left' as const,
      paddingHorizontal: 4,
      color: theme.colors.onSurfaceVariant
    },
  };

  return (
    <View style={styles.watchDescription}>
      {/* Therapeutic Properties - add back for comparison */}
      <TherapeuticPropertiesDisplay oil={oil} therapeuticProperties={therapeuticProperties} theme={theme} />
      
      <View style={styles.watchFeatureRow}>
        <View style={styles.leftGroup}>
          <View style={styles.itemCircle}>
            <Text variant="labelMedium" style={styles.itemCircleNumber}>
              {dilutionInfo.description.charAt(0).toUpperCase()}
            </Text>
          </View>
          <View>
            <Text variant="labelSmall" style={styles.featureLabel}>
              {t('oilSubstitution.dilutionRange', { defaultValue: 'Dilution Range' })}
            </Text>
            <Text variant="bodySmall" style={styles.featureValue}>{dilutionInfo.range}</Text>
          </View>
        </View>
        
        <View style={styles.rightGroup}>
          <Text variant="labelSmall" style={styles.featureLabel}>
            {t('oilSubstitution.ratio', { defaultValue: 'Ratio' })}
          </Text>
          <Text variant="bodySmall" style={styles.featureValue}>{dilutionInfo.ratio}</Text>
        </View>
      </View>
      
      
      <View style={styles.fullWidthRangeBar}>
        <View style={styles.rangeBarContainer}>
          {dilutionInfo.min !== undefined && dilutionInfo.max !== undefined && (
            <View 
              style={[
                styles.rangeBarFill, 
                { 
                  left: `${dilutionInfo.min}%`,
                  width: `${Math.max(1, dilutionInfo.max - dilutionInfo.min)}%`
                }
              ]} 
            />
          )}
        </View>
      </View>
      
      {safetyIndicators && safetyIndicators.length > 0 && (
        <View style={styles.safetyContainer}>
          {safetyIndicators.slice(0, 2).map((indicator, index) => (
            <View key={index} style={styles.safetyColumn}>
              <Chip 
                icon={indicator.icon}
                style={styles.safetyChip}
              >
                {indicator.text}
              </Chip>
            </View>
          ))}
        </View>
      )}
    </View>
  );
};

export const OilSubstitutionBottomSheet: React.FC<OilSubstitutionBottomSheetProps> = ({
  modalRef,
  originalOil,
  alternativeOils,
  onSelectSubstitute,
  onDismiss,
  selectedOils,
  timeSlot
}) => {
  const theme = useTheme();
  const { t } = useTranslation('create-recipe');
  
  // Access therapeutic properties from Zustand store
  const { therapeuticProperties } = useCombinedRecipeStore();

  // Sort alternatives by final_relevance_score (descending) and exclude oils already in recipe
  const sortedAlternatives = useMemo(() => {
    if (__DEV__) {
      console.log('🔍 [OilSubstitution] Starting filtering process:', {
        totalAlternatives: alternativeOils.length,
        originalOilId: originalOil?.oil_id,
        selectedOilsCount: selectedOils?.length || 0,
        timeSlot
      });
    }

    // Get array of oil IDs already selected in the recipe
    const selectedOilIds = selectedOils?.map(oil => oil.oil_id) || [];
    
    if (__DEV__) {
      console.log('🔍 [OilSubstitution] Selected oil IDs in recipe:', selectedOilIds);
      
      // --- ENHANCED DEBUGGING ---
      // The following logs inspect the data structures being compared.
      // The filtering issue occurs if the objects in `selectedOils` are partial 
      // and do not have the same properties as the full objects in `alternativeOils`.
      console.log('🕵️‍♂️ [OilSubstitution] Inspecting data structures for filtering...');
      if (selectedOils && selectedOils.length > 0) {
        console.log('Sample from `selectedOils` (in recipe):', JSON.stringify(selectedOils[0], null, 2));
      }
      if (alternativeOils && alternativeOils.length > 0) {
        // Find a corresponding oil to compare, if possible
        const alternativeExample = alternativeOils.find(alt => alt.oil_id === selectedOils?.[0]?.oil_id) || alternativeOils[0];
        console.log('Sample from `alternativeOils` (master list):', JSON.stringify(alternativeExample, null, 2));
      }
      // --- END ENHANCED DEBUGGING ---
    }

    // Filter alternatives
    const afterOriginalFilter = alternativeOils.filter(oil => oil.oil_id !== originalOil?.oil_id);
    const afterRecipeFilter = afterOriginalFilter.filter(oil => !selectedOilIds.includes(oil.oil_id));
    
    // Sort by relevance score
    const sorted = afterRecipeFilter.sort((a, b) => (b.final_relevance_score || 0) - (a.final_relevance_score || 0));

    if (__DEV__) {
      console.log('🔍 [OilSubstitution] Filtering results:', {
        totalAlternatives: alternativeOils.length,
        afterExcludingOriginal: afterOriginalFilter.length,
        afterExcludingRecipeOils: afterRecipeFilter.length,
        finalSortedCount: sorted.length,
        excludedByOriginal: alternativeOils.length - afterOriginalFilter.length,
        excludedByRecipe: afterOriginalFilter.length - afterRecipeFilter.length
      });

      // Debug specialization_score issues
      const oilsWithoutSpecialization = sorted.filter(oil => oil.specialization_score === undefined);
      if (oilsWithoutSpecialization.length > 0) {
        console.warn('⚠️ [OilSubstitution] Oils missing specialization_score:', {
          count: oilsWithoutSpecialization.length,
          examples: oilsWithoutSpecialization.slice(0, 3).map(oil => ({
            oil_id: oil.oil_id,
            name: oil.name_localized,
            final_relevance_score: oil.final_relevance_score,
            specialization_score: oil.specialization_score,
            enrichment_status: oil.enrichment_status
          }))
        });
      }

      // Debug final_relevance_score issues
      const oilsWithoutFinalScore = sorted.filter(oil => oil.final_relevance_score === undefined);
      if (oilsWithoutFinalScore.length > 0) {
        console.warn('⚠️ [OilSubstitution] Oils missing final_relevance_score:', {
          count: oilsWithoutFinalScore.length,
          examples: oilsWithoutFinalScore.slice(0, 3).map(oil => ({
            oil_id: oil.oil_id,
            name: oil.name_localized,
            final_relevance_score: oil.final_relevance_score,
            enrichment_status: oil.enrichment_status
          }))
        });
      }
    }

    return sorted;
  }, [alternativeOils, originalOil?.oil_id, selectedOils, timeSlot]);

  // Find the original oil with scoring data from alternatives list
  const originalOilWithScore = useMemo(() => {
    if (!originalOil?.oil_id) {
      if (__DEV__) {
        console.warn('⚠️ [OilSubstitution] No originalOil provided to modal');
      }
      return null;
    }
    
    const foundOil = alternativeOils.find(oil => oil.oil_id === originalOil.oil_id);
    
    if (__DEV__) {
      if (!foundOil) {
        console.warn('⚠️ [OilSubstitution] Original oil not found in alternatives list:', {
          originalOilId: originalOil.oil_id,
          originalOilName: originalOil.name_localized,
          alternativeOilsCount: alternativeOils.length,
          alternativeOilIds: alternativeOils.slice(0, 5).map(oil => oil.oil_id)
        });
      } else {
        console.log('✅ [OilSubstitution] Original oil found with scores:', {
          oil_id: foundOil.oil_id,
          name: foundOil.name_localized,
          final_relevance_score: foundOil.final_relevance_score,
          specialization_score: foundOil.specialization_score,
          enrichment_status: foundOil.enrichment_status
        });
        
        // Check for missing scores on original oil
        if (foundOil.specialization_score === undefined) {
          console.warn('⚠️ [OilSubstitution] Original oil missing specialization_score');
        }
        if (foundOil.final_relevance_score === undefined) {
          console.warn('⚠️ [OilSubstitution] Original oil missing final_relevance_score');
        }
      }
    }
    
    return foundOil || null;
  }, [originalOil?.oil_id, alternativeOils]);

  // Find therapeutic properties that contain the original oil
  const originalOilCategories = useMemo(() => {
    if (!originalOil?.oil_id || !therapeuticProperties) return [];
    
    return therapeuticProperties.filter(property => 
      property.suggested_oils?.some(oil => oil.oil_id === originalOil.oil_id)
    );
  }, [originalOil?.oil_id, therapeuticProperties]);

  const handleSelectOil = (oil: EnrichedEssentialOil) => {
    haptics.light();
    onSelectSubstitute(oil);
    modalRef.current?.dismiss();
  };

  // Render backdrop according to guide recommendations
  const renderBackdrop = useCallback((props: any) => (
    <BottomSheetBackdrop
      {...props}
      disappearsOnIndex={-1}
      appearsOnIndex={0}
      opacity={0.5}
      pressBehavior="close"
    />
  ), []);

  return (
    <BottomSheetModal
      ref={modalRef}
      snapPoints={['95%']}
      onDismiss={onDismiss}
      backdropComponent={renderBackdrop}
      enableOverDrag={false}
      enableDynamicSizing={false}
      enableContentPanningGesture={true}
      backgroundStyle={{ backgroundColor: theme.colors.surface }}
      handleIndicatorStyle={{ backgroundColor: theme.colors.onSurfaceVariant }}
    >
      <View style={{ flex: 1 }}>
        {/* Header */}
        <View style={{ 
          paddingHorizontal: theme.spacing.screenPadding,
          paddingBottom: theme.spacing.md,
          borderBottomWidth: 1,
          borderBottomColor: theme.colors.outline
        }}>
          {/* Title */}
          <Text variant="headlineSmall" style={{ 
            fontWeight: 'bold',
            color: theme.colors.onSurface,
            marginBottom: theme.spacing.sm
          }}>
            {t('oilSubstitution.title', { oilName: originalOil?.name_localized })}
          </Text>

          {/* Original Oil Info - matching list item structure */}
          <View style={{ marginBottom: theme.spacing.sm }}>
            <Text variant="titleMedium" style={{ 
              color: theme.colors.onSurface, 
              marginBottom: theme.spacing.xs, 
              fontWeight: 'bold' 
            }}>
              {originalOil?.name_localized}
            </Text>
            
            {/* Scientific name */}
            {(originalOilWithScore?.name_scientific || originalOilWithScore?.name_botanical) && (
              <Text variant="bodyMedium" style={{ 
                color: theme.colors.onSurfaceVariant, 
                marginBottom: theme.spacing.sm,
                fontStyle: 'italic'
              }}>
                Scientific: {originalOilWithScore.name_scientific || originalOilWithScore.name_botanical}
              </Text>
            )}
            
            {/* Score chips - matching list item style */}
            <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: theme.spacing.sm, marginBottom: theme.spacing.sm }}>
              {originalOilWithScore?.final_relevance_score && (
                <Chip icon="star" compact style={{ marginBottom: theme.spacing.xs }}>
                  Relevance: {originalOilWithScore.final_relevance_score.toFixed(1)}
                </Chip>
              )}
              {originalOilWithScore?.specialization_score !== undefined && (
                <Chip icon="target" compact style={{ marginBottom: theme.spacing.xs }}>
                  {`${(originalOilWithScore.specialization_score * 100).toFixed(0)}% Fit`}
                </Chip>
              )}
            </View>
          </View>
          
          {/* Therapeutic Properties Categories */}
          {originalOilCategories.length > 0 && (
            <View style={{ 
              flexDirection: 'row', 
              flexWrap: 'wrap', 
              gap: theme.spacing.xs,
              marginBottom: theme.spacing.sm
            }}>
              {originalOilCategories.map((property) => (
                <Chip 
                  key={property.property_id}
                  compact
                  icon="leaf"
                  style={{ backgroundColor: theme.colors.primaryContainer }}
                  textStyle={{ fontSize: 9 }}
                >
                  {property.property_name_localized}
                </Chip>
              ))}
            </View>
          )}
          
          <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
            {t('oilSubstitution.subtitle')}
          </Text>
        </View>

        {/* Alternatives List */}
        <BottomSheetScrollView style={{ flex: 1 }} focusHook={useFocusEffect}>
          {sortedAlternatives.map((oil, index) => (
            <React.Fragment key={oil.oil_id}>
              <List.Item
                title={() => <OilListItemTitle oil={oil} index={index} theme={theme} t={t} />}
                description={() => <OilListItemDescription oil={oil} t={t} theme={theme} therapeuticProperties={therapeuticProperties} />}
                onPress={() => handleSelectOil(oil)}
                right={() => null}
                style={{ 
                  paddingHorizontal: theme.spacing.screenPadding,
                  paddingVertical: theme.spacing.md // Increase padding for richer content
                }}
              />
              {index < sortedAlternatives.length - 1 && <Divider />}
            </React.Fragment>
          ))}
          
          {sortedAlternatives.length === 0 && (
            <View style={{ 
              padding: theme.spacing.screenPadding,
              alignItems: 'center' 
            }}>
              <Text variant="bodyLarge" style={{ 
                color: theme.colors.onSurfaceVariant,
                textAlign: 'center'
              }}>
                {t('oilSubstitution.noAlternatives')}
              </Text>
              {__DEV__ && (
                <Text variant="bodySmall" style={{ 
                  color: theme.colors.error,
                  textAlign: 'center',
                  marginTop: theme.spacing.sm,
                  fontFamily: 'monospace'
                }}>
                  DEBUG: {alternativeOils.length} total alternatives, {selectedOils?.length || 0} selected oils
                </Text>
              )}
            </View>
          )}
        </BottomSheetScrollView>
      </View>
    </BottomSheetModal>
  );
};
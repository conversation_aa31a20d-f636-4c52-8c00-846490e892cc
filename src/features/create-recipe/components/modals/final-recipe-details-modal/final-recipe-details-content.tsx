/**
 * @fileoverview Final Recipe Details Content Component
 * List.AccordionGroup implementation with exclusive behavior and flat design
 * Located in create-recipe feature as it's specific to recipe creation workflow
 * Previously located in shared/components/recipe-modal/recipe-modal-content.tsx
 */

import React, { useState } from 'react';
import { View } from 'react-native';
import { Text, List, Chip } from 'react-native-paper';
import { useTheme } from '@/shared/hooks/use-theme';
import { haptics } from '@/shared/utils/haptics';

interface FinalRecipeDetailsContentProps {
  recipe: any;
}

export function FinalRecipeDetailsContent({ recipe }: FinalRecipeDetailsContentProps) {
  const { theme, spacing } = useTheme();
  const [expandedId, setExpandedId] = useState<string>('essential-oils');

  // Debug: Log the recipe data to understand what's being passed
  if (__DEV__) {
    console.log('🔍 FinalRecipeDetailsContent received recipe:', {
      recipe: recipe ? 'defined' : 'undefined',
      selected_oils: recipe?.selected_oils ? `array with ${recipe.selected_oils.length} items` : 'undefined',
      carrier_oil: recipe?.carrier_oil ? 'defined' : 'undefined'
    });
  }

  // Early return if recipe is undefined or incomplete
  if (!recipe || !recipe.selected_oils || !Array.isArray(recipe.selected_oils)) {
    return (
      <View style={{ padding: spacing.lg, alignItems: 'center' }}>
        <Text variant="bodyLarge" style={{ color: theme.colors.onSurfaceVariant }}>
          No recipe data available
        </Text>
      </View>
    );
  }

  const renderDropQuantity = (drops: number) => (
    <View style={{ alignItems: 'center' }}>
      <Chip style={{
        backgroundColor: theme.colors.primaryContainer,
      }}>
        {drops}
      </Chip>
      <Text variant="bodySmall" style={{
        color: theme.colors.onSurfaceVariant,
        marginTop: 2
      }}>
        drops
      </Text>
    </View>
  );


  const isExpanded = (id: string) => expandedId === id;

  const getAccordionStyle = (id: string) => ({
    backgroundColor: isExpanded(id) ? theme.colors.primary : theme.colors.surface
  });

  const getTitleStyle = (id: string) => ({
    color: isExpanded(id) ? theme.colors.onPrimary : theme.colors.primary
  });

  const getDescriptionStyle = (id: string) => ({
    color: isExpanded(id) ? theme.colors.onPrimary : theme.colors.onSurfaceVariant
  });

  return (
      <List.AccordionGroup
        expandedId={expandedId}
        onAccordionPress={(newId) => {
          setExpandedId(expandedId === newId ? '' : newId as string);
          haptics?.light();
        }}
      >
        {/* Essential Oils - Expanded by default */}
        <List.Accordion
          id="essential-oils"
          title="Essential Oils"
          description={`${recipe.selected_oils.length} oils selected`}
          style={getAccordionStyle('essential-oils')}
          titleStyle={getTitleStyle('essential-oils')}
          descriptionStyle={getDescriptionStyle('essential-oils')}
        >
          {recipe.selected_oils.map((oil: any, index: number) => (
            <List.Item
              key={index}
              title={oil.name_localized}
              description={oil.name_botanical}
              right={() => renderDropQuantity(oil.drops_count)}
            />
          ))}
        </List.Accordion>

        {/* Carrier Oil Options */}
        {recipe.carrier_oil && (
          <List.Accordion
            id="carrier-oils"
            title="Carrier Oil Options"
            description="Recommended and alternative options"
            style={getAccordionStyle('carrier-oils')}
            titleStyle={getTitleStyle('carrier-oils')}
            descriptionStyle={getDescriptionStyle('carrier-oils')}
          >
            <List.Item
              title={recipe.carrier_oil.name_localized}
              description={recipe.carrier_oil.properties_localized}
              left={(props) => (
                <Chip {...props} style={{ 
                  backgroundColor: theme.colors.secondaryContainer,
                  marginLeft: spacing.md,
                  alignSelf: 'center'
                }}>
                  Recommended
                </Chip>
              )}
            />
            {recipe.carrier_oil.alternative && (
              <List.Item
                title={recipe.carrier_oil.alternative.name_localized}
                description={recipe.carrier_oil.alternative.properties_localized}
                left={(props) => (
                  <Chip {...props} style={{ 
                    backgroundColor: theme.colors.tertiaryContainer,
                    marginLeft: spacing.md,
                    alignSelf: 'center'
                  }}>
                    Alternative
                  </Chip>
                )}
              />
            )}
          </List.Accordion>
        )}

        {/* Preparation Steps */}
        {recipe.preparation_steps_localized && Array.isArray(recipe.preparation_steps_localized) && (
          <List.Accordion
            id="preparation"
            title="Preparation Steps"
            description="Step-by-step preparation guide"
            style={getAccordionStyle('preparation')}
            titleStyle={getTitleStyle('preparation')}
            descriptionStyle={getDescriptionStyle('preparation')}
          >
            {recipe.preparation_steps_localized.map((step: string, index: number) => (
              <List.Item
                key={index}
                title={`Step ${index + 1}`}
                description={step}
                left={(props) => (
                  <View {...props} style={{
                    width: 32,
                    height: 32,
                    borderRadius: 16,
                    backgroundColor: theme.colors.primaryContainer,
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginLeft: spacing.md,
                    alignSelf: 'center'
                  }}>
                    <Text variant="bodyMedium" style={{
                      color: theme.colors.onPrimaryContainer,
                      fontWeight: 'bold'
                    }}>
                      {index + 1}
                    </Text>
                  </View>
                )}
              />
            ))}
          </List.Accordion>
        )}

        {/* Application Methods with integrated ritual */}
        {recipe.usage_instructions_localized && Array.isArray(recipe.usage_instructions_localized) && (
          <List.Accordion
            id="application"
            title="Application Methods"
            description="Usage instructions and ritual"
            style={getAccordionStyle('application')}
            titleStyle={getTitleStyle('application')}
            descriptionStyle={getDescriptionStyle('application')}
          >
            {recipe.usage_instructions_localized.map((instruction: any, index: number) => (
              <List.Item
                key={index}
                title={instruction.method}
                description={`${instruction.description}\n\nFrequency: ${instruction.frequency}`}
              />
            ))}
            
            {/* Integrated Ritual Suggestion */}
            {recipe.ritual_suggestion_localized && (
              <View style={{ 
                padding: spacing.md,
                backgroundColor: theme.colors.surfaceVariant,
                marginTop: spacing.sm,
                borderRadius: 8,
                marginHorizontal: spacing.md
              }}>
                <Text variant="titleSmall" style={{
                  color: theme.colors.primary,
                  fontWeight: 'bold',
                  marginBottom: spacing.xs
                }}>
                  Ritual Suggestion
                </Text>
                <Text variant="bodyMedium" style={{
                  color: theme.colors.onSurfaceVariant,
                  lineHeight: 20
                }}>
                  {recipe.ritual_suggestion_localized}
                </Text>
              </View>
            )}
          </List.Accordion>
        )}

        {/* Why These Oils Work - with embedded rationales */}
        {(recipe.synergy_rationale_localized || recipe.selected_oils.some((oil: any) => oil.rationale_localized)) && (
          <List.Accordion
            id="rationales"
            title="Why These Oils Work"
            description="Scientific rationale and properties"
            style={getAccordionStyle('rationales')}
            titleStyle={getTitleStyle('rationales')}
            descriptionStyle={getDescriptionStyle('rationales')}
          >
            {/* Overall synergy section */}
            {recipe.synergy_rationale_localized && (
              <View style={{ 
                padding: spacing.md,
                backgroundColor: theme.colors.surfaceVariant,
                marginBottom: spacing.sm,
                borderRadius: 8,
                marginHorizontal: spacing.md
              }}>
                <Text variant="titleSmall" style={{
                  color: theme.colors.primary,
                  fontWeight: 'bold',
                  marginBottom: spacing.xs
                }}>
                  Overall Synergy
                </Text>
                <Text variant="bodyMedium" style={{
                  color: theme.colors.onSurfaceVariant,
                  lineHeight: 20
                }}>
                  {recipe.synergy_rationale_localized}
                </Text>
              </View>
            )}

            {/* Individual oil rationales from selected_oils */}
            {recipe.selected_oils.filter((oil: any) => oil.rationale_localized).map((oil: any, index: number) => (
              <View key={index} style={{ paddingHorizontal: spacing.md, paddingVertical: spacing.sm }}>
                <Text variant="titleSmall" style={{
                  color: theme.colors.primary,
                  fontWeight: 'bold',
                  marginBottom: spacing.xs
                }}>
                  {oil.name_localized}
                </Text>
                <Text variant="bodyMedium" style={{
                  color: theme.colors.onSurface,
                  lineHeight: 20,
                  marginBottom: spacing.xs
                }}>
                  {oil.rationale_localized}
                </Text>
              </View>
            ))}
          </List.Accordion>
        )}

      </List.AccordionGroup>
  );
}
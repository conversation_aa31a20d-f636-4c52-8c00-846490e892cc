/**
 * @fileoverview Final Recipe Details Header Component
 * Clean header without Surface containers, displays recipe title, time chip, and formulation info
 * Located in create-recipe feature as it's specific to recipe creation workflow
 * Previously located in shared/components/recipe-modal/RecipeModalHeader.tsx
 */

import React from 'react';
import { View } from 'react-native';
import { Text, Chip } from 'react-native-paper';
import { useTheme } from '@/shared/hooks/use-theme';
import type { RecipeTimeSlot } from '@/features/create-recipe/types';

interface FinalRecipeDetailsHeaderProps {
  recipe: any;
  timeSlot: RecipeTimeSlot;
}

export function FinalRecipeDetailsHeader({ recipe, timeSlot }: FinalRecipeDetailsHeaderProps) {
  const { theme, spacing } = useTheme();

  // Early return if recipe is undefined
  if (!recipe) {
    return (
      <View style={{ paddingHorizontal: spacing.screenPadding, marginBottom: spacing.lg }}>
        <Text variant="bodyLarge" style={{ color: theme.colors.onSurfaceVariant }}>
          No recipe data available
        </Text>
      </View>
    );
  }

  return (
    <View style={{ paddingHorizontal: spacing.screenPadding, marginBottom: spacing.lg }}>
      {/* Recipe title - left aligned, no emoji */}
      {recipe.recipe_theme_localized && (
        <Text variant="headlineSmall" style={{
          color: theme.colors.primary,
          fontWeight: 'bold',
          marginBottom: spacing.sm,
          textAlign: 'left'
        }}>
          {recipe.recipe_theme_localized}
        </Text>
      )}
      
      {/* Time range chip - left aligned */}
      {recipe.time_range_localized && (
        <Chip style={{
          backgroundColor: theme.colors.primaryContainer,
          alignSelf: 'flex-start',
          marginBottom: spacing.md
        }}>
          {recipe.time_range_localized}
        </Chip>
      )}

      {/* Recipe description */}
      {recipe.description_localized && (
        <Text variant="bodyMedium" style={{
          color: theme.colors.onSurface,
          lineHeight: 20,
          marginBottom: spacing.md
        }}>
          {recipe.description_localized}
        </Text>
      )}

      {/* Holistic Benefits */}
      {recipe.holistic_benefit_localized && (
        <>
          <Text variant="titleSmall" style={{
            color: theme.colors.primary,
            fontWeight: 'bold',
            marginBottom: spacing.sm
          }}>
            Holistic Benefits
          </Text>
          <Text variant="bodyMedium" style={{
            color: theme.colors.onSurface,
            lineHeight: 20,
            marginBottom: spacing.lg
          }}>
            {recipe.holistic_benefit_localized}
          </Text>
        </>
      )}

      {/* Formulation chips */}
      {recipe.formulation && (
        <View style={{
          flexDirection: 'row',
          flexWrap: 'wrap',
          gap: spacing.sm,
          marginBottom: spacing.lg
        }}>
          {recipe.formulation.total_drops && (
            <Chip style={{ backgroundColor: theme.colors.primaryContainer }}>
              {recipe.formulation.total_drops} drops total
            </Chip>
          )}
          {recipe.formulation.dilution_percentage && (
            <Chip style={{ backgroundColor: theme.colors.secondaryContainer }}>
              {recipe.formulation.dilution_percentage}% dilution
            </Chip>
          )}
          {recipe.formulation.bottle_size_ml && (
            <Chip style={{ backgroundColor: theme.colors.tertiaryContainer }}>
              {recipe.formulation.bottle_size_ml}ml bottle
            </Chip>
          )}
        </View>
      )}
    </View>
  );
}
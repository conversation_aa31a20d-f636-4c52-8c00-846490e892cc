/**
 * @fileoverview Final Recipe Details Modal Component
 * Main modal container with time slot navigation and BottomSheetModal wrapper
 * Located in create-recipe feature as it's specific to recipe creation workflow
 * Previously located in shared/components/recipe-modal/
 */

import React, { useState, useCallback } from 'react';
import { View } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { Portal, Text } from 'react-native-paper';
import {
  BottomSheetModal,
  BottomSheetScrollView,
  BottomSheetBackdrop,
} from '@gorhom/bottom-sheet';
import { useHeaderHeight } from '@react-navigation/elements';
import { useTheme } from '@/shared/hooks/use-theme';
import { StandardizedSegmentedButtons, SegmentedButtonPresets } from '@/shared/components/ui';
import { haptics } from '@/shared/utils/haptics';
import { FinalRecipeDetailsHeader } from './final-recipe-details-header';
import { FinalRecipeList } from '@/features/create-recipe/components/screens/final-recipes';
import type { RecipeTimeSlot } from '@/features/create-recipe/types';

interface FinalRecipeDetailsModalProps {
  visible: boolean;
  onDismiss: () => void;
  initialTimeSlot: RecipeTimeSlot;
  finalRecipes: any;
  bottomSheetModalRef: React.RefObject<BottomSheetModal>;
}

export function FinalRecipeDetailsModal({
  visible,
  onDismiss,
  initialTimeSlot,
  finalRecipes,
  bottomSheetModalRef,
}: FinalRecipeDetailsModalProps) {
  const { theme, spacing } = useTheme();
  const [activeTimeSlot, setActiveTimeSlot] =
    useState<RecipeTimeSlot>(initialTimeSlot);
  const headerHeight = useHeaderHeight();

  const timeSlotButtons = SegmentedButtonPresets.timeSlots();

  const currentRecipe = finalRecipes[activeTimeSlot];

  const handleTimeSlotChange = (newTimeSlot: string) => {
    setActiveTimeSlot(newTimeSlot as RecipeTimeSlot);
    haptics?.light();
  };

  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
      />
    ),
    []
  );

  // Don't render modal if no recipes available
  if (!finalRecipes || !Object.keys(finalRecipes).length) {
    return null;
  }

  return (
    <Portal>
      <BottomSheetModal
        ref={bottomSheetModalRef}
        snapPoints={['90%']}
        enablePanDownToClose={true}
        enableOverDrag={false}
        enableContentPanningGesture={true}
        enableDynamicSizing={false}
        topInset={headerHeight}
        onDismiss={onDismiss}
        backgroundStyle={{ backgroundColor: theme.colors.surface }}
        backdropComponent={renderBackdrop}
        handleIndicatorStyle={{ backgroundColor: theme.colors.onSurfaceVariant }}
      >
        <View style={{ flex: 1 }}>
          {/* Modal Header */}
          <View
            style={{
              paddingHorizontal: spacing.screenPadding,
              paddingTop: spacing.md,
              paddingBottom: spacing.sm,
            }}
          >
            <Text
              variant="headlineSmall"
              style={{
                textAlign: 'center' as const,
                color: theme.colors.onSurface,
                fontWeight: 'bold',
                marginBottom: spacing.md,
              }}
            >
              🧪 Recipe Details
            </Text>
          </View>

          {/* Time Slot Segmented Buttons (Fixed) */}
          <View
            style={{
              paddingHorizontal: spacing.screenPadding,
              paddingBottom: spacing.md,
            }}
          >
            <StandardizedSegmentedButtons
              value={activeTimeSlot}
              onValueChange={handleTimeSlotChange}
              buttons={timeSlotButtons}
            />
          </View>

          {/* Scrollable Content Area */}
          <BottomSheetScrollView style={{ flex: 1 }} focusHook={useFocusEffect}>
            <FinalRecipeDetailsHeader recipe={currentRecipe} timeSlot={activeTimeSlot} />
            <FinalRecipeList recipe={currentRecipe} timeSlot={activeTimeSlot} />
          </BottomSheetScrollView>
        </View>
      </BottomSheetModal>
    </Portal>
  );
}
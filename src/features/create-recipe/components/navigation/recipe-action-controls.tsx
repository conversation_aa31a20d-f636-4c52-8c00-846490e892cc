/**
 * Recipe Action Controls Component
 * Progressive action controls for enhanced BottomNavigation in V4 implementation
 * Provides step-specific actions with validation states and loading handling
 */

import React from 'react';
import { View } from 'react-native';
import { 
  Text, 
  Button 
} from 'react-native-paper';
import { useTranslation } from 'react-i18next';

import { useTheme } from '@/shared/hooks';
import { RecipeStep } from '@/features/create-recipe/types';
import { haptics } from '@/shared/utils/haptics';
import type { AppTheme } from '@/shared/utils/theme-utils';

// Minimum touch target size for accessibility compliance
const MIN_TOUCH_TARGET = 44;
const BUTTON_PADDING_OFFSET = 8;

interface RecipeActionControlsProps {
  step: RecipeStep | string;
  isValid: boolean;
  isLoading: boolean;
  currentSelections?: number;
  recommendedSelections?: number;
  onContinue: () => void;
  onBack?: () => void;
  onSkip?: () => void;
}

interface StepConfig {
  title: string;
  subtitle: string;
  buttonText: string;
  buttonIcon: string;
  progressText: string;
}

const getStyles = (theme: AppTheme) => ({
  container: {
    gap: theme.spacing.sm,
  },
  actionContainer: {
    alignItems: 'center',
    gap: theme.spacing.xs,
  },
  title: {
    marginBottom: theme.spacing.xs,
  },
  subtitle: {
    marginBottom: theme.spacing.md,
    textAlign: 'center',
  },
  continueButton: {
    borderRadius: theme.spacing.sm,
    elevation: theme.elevation.small,
    minWidth: '100%',
  },
  buttonContent: {
    paddingVertical: theme.spacing.sm,
  },
  secondaryActions: {
    flexDirection: 'row',
    gap: theme.spacing.sm,
    marginTop: theme.spacing.sm,
  },
  secondaryButton: {
    flex: 1,
  },
});

export const RecipeActionControls: React.FC<RecipeActionControlsProps> = ({
  step,
  isValid,
  isLoading,
  currentSelections = 0,
  recommendedSelections = 1,
  onContinue,
  onBack,
  onSkip,
}) => {
  const { t } = useTranslation('create-recipe');
  const theme = useTheme();
  
  const getStepConfig = (): StepConfig => {
    switch (step) {
      case 'health-concern':
      case RecipeStep.HEALTH_CONCERN:
        return {
          title: t('actionControls.healthConcern.title'),
          subtitle: isValid ? t('actionControls.healthConcern.subtitleValid') : t('actionControls.healthConcern.subtitleInvalid'),
          buttonText: t('actionControls.healthConcern.buttonText'),
          buttonIcon: 'account-search',
          progressText: '', // No progress indicators for discovery
        };
      case 'demographics':
      case RecipeStep.DEMOGRAPHICS:
        return {
          title: currentSelections >= recommendedSelections ? t('actionControls.demographics.titleReady') : t('actionControls.demographics.titleContinue'),
          subtitle: `${currentSelections} ${currentSelections === 1 ? t('actionControls.demographics.subtitleSingle') : t('actionControls.demographics.subtitleMultiple')}`,
          buttonText: t('actionControls.demographics.buttonText'),
          buttonIcon: 'brain',
          progressText: '', // No progress indicators for discovery
        };
      case 'causes':
      case RecipeStep.CAUSES:
        return {
          title: currentSelections >= recommendedSelections ? t('actionControls.causes.titleReady') : t('actionControls.causes.titleMore'),
          subtitle: `${currentSelections} ${currentSelections === 1 ? t('actionControls.causes.subtitleSingle') : t('actionControls.causes.subtitleMultiple')}`,
          buttonText: t('actionControls.causes.buttonText'),
          buttonIcon: 'clipboard-pulse',
          progressText: '', // No progress indicators for discovery
        };
      case 'symptoms':
      case RecipeStep.SYMPTOMS:
        return {
          title: currentSelections >= recommendedSelections ? t('actionControls.symptoms.titleReady') : t('actionControls.symptoms.titleMore'),
          subtitle: `${currentSelections} ${currentSelections === 1 ? t('actionControls.symptoms.subtitleSingle') : t('actionControls.symptoms.subtitleMultiple')}`,
          buttonText: t('actionControls.symptoms.buttonText'),
          buttonIcon: 'leaf',
          progressText: '', // No progress indicators for discovery
        };
      case 'properties':
      case RecipeStep.PROPERTIES:
        return {
          title: t('actionControls.properties.title'),
          subtitle: t('actionControls.properties.subtitle'),
          buttonText: t('actionControls.properties.buttonText'),
          buttonIcon: 'bottle-tonic',
          progressText: '', // No progress indicators for discovery
        };
      case 'recipes':
      case RecipeStep.FINAL_RECIPES:
        return {
          title: t('actionControls.recipes.title'),
          subtitle: t('actionControls.recipes.subtitle'),
          buttonText: t('actionControls.recipes.buttonText'),
          buttonIcon: 'eye',
          progressText: '', // No progress indicators for discovery
        };
      default:
        return {
          title: t('actionControls.default.title'),
          subtitle: t('actionControls.default.subtitle'),
          buttonText: t('actionControls.default.buttonText'),
          buttonIcon: 'arrow-right',
          progressText: t('actionControls.default.progressText'),
        };
    }
  };

  const config = getStepConfig();

  // Enhanced continue handler with haptic feedback
  const handleContinueWithHaptics = () => {
    if (__DEV__) {
      console.log('🔊 [RecipeActionControls] Continue button pressed - triggering success haptic');
    }
    haptics.success(); // Success haptic for progressing to next step (like completing a level)
    onContinue();
  };

  // Enhanced back handler with haptic feedback
  const handleBackWithHaptics = () => {
    if (onBack) {
      if (__DEV__) {
        console.log('🔊 [RecipeActionControls] Back button pressed - triggering light haptic');
      }
      haptics.light(); // Light feedback for navigation back
      onBack();
    }
  };

  // Enhanced skip handler with haptic feedback  
  const handleSkipWithHaptics = () => {
    if (onSkip) {
      if (__DEV__) {
        console.log('🔊 [RecipeActionControls] Skip button pressed - triggering medium haptic');
      }
      haptics.medium(); // Medium feedback for skipping step
      onSkip();
    }
  };

  const styles = getStyles(theme);

  return (
    <View style={styles.container}>
      {/* Discovery Action - No Progress Indicators */}
      <View style={styles.actionContainer}>
        <Text variant="titleSmall" style={[styles.title, { color: theme.colors.onSurface }]}>
          {config.title}
        </Text>
        <Text variant="bodySmall" style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}>
          {config.subtitle}
        </Text>
        
        <Button
          mode="contained"
          onPress={handleContinueWithHaptics}
          loading={isLoading}
          disabled={!isValid || isLoading}
          style={[styles.continueButton, { minHeight: MIN_TOUCH_TARGET }]}
          contentStyle={[styles.buttonContent, { minHeight: MIN_TOUCH_TARGET - BUTTON_PADDING_OFFSET }]} // Account for button padding
          icon={config.buttonIcon}
          accessibilityLabel={`${config.buttonText}. ${config.subtitle}`}
          accessibilityHint={t('actionControls.accessibility.continueHint')}
          accessibilityState={{
            disabled: !isValid || isLoading,
            busy: isLoading,
          }}
        >
          {isLoading ? t('actionControls.healthConcern.processing') : config.buttonText}
        </Button>
        
        {/* Optional secondary actions */}
        {(onBack || onSkip) && (
          <View style={styles.secondaryActions}>
            {onBack && (
              <Button
                mode="outlined"
                onPress={handleBackWithHaptics}
                disabled={isLoading}
                style={styles.secondaryButton}
                contentStyle={styles.buttonContent}
                icon="arrow-left"
                compact
              >
                {t('actionControls.secondaryActions.back')}
              </Button>
            )}
            {onSkip && (
              <Button
                mode="text"
                onPress={handleSkipWithHaptics}
                disabled={isLoading}
                style={styles.secondaryButton}
                contentStyle={styles.buttonContent}
                icon="skip-forward"
                compact
              >
                {t('actionControls.secondaryActions.skip')}
              </Button>
            )}
          </View>
        )}
      </View>
    </View>
  );
};

export default RecipeActionControls;
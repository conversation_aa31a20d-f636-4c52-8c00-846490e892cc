import React from 'react';
import { Alert } from 'react-native';
import { Appbar } from 'react-native-paper';
import { usePaperTheme } from '@/shared/hooks/use-theme';
import { useRecipeReset } from '@/features/create-recipe/utils/navigation-utils';
import { haptics } from '@/shared/utils/haptics';
import { useTranslation } from 'react-i18next';

export const StartOverButton = () => {
  const handleResetWizard = useRecipeReset();
  const theme = usePaperTheme();
  const { t } = useTranslation('create-recipe');
  
  const showConfirmationModal = () => {
    // Warning haptic for potentially destructive action
    haptics.warning();
    
    Alert.alert(
      t('startOver.title', 'Start Over?'),
      t('startOver.message', 'This will clear all your progress and restart the recipe wizard from the beginning. This action cannot be undone.'),
      [
        {
          text: t('startOver.cancel', 'Cancel'),
          style: 'cancel',
        },
        {
          text: t('startOver.confirm', 'Start Over'),
          style: 'destructive',
          onPress: () => {
            console.log('🔄 User confirmed recipe wizard reset');
            handleResetWizard();
          },
        },
      ]
    );
  };

  return (
    <Appbar.Action
      icon="refresh"
      onPress={showConfirmationModal}
      color={theme.colors.onSurface}
    />
  );
};

import { useCallback } from 'react';
import { useParallelStreamingEngine, ParallelStreamRequest } from './use-parallel-streaming-engine';
import { useLanguage } from '@/shared/hooks';
import {
  fetchOilEnrichmentData,
  createOilSuggestionStreamingRequests,
  createFinalRecipeStreamingRequests
} from '../services';
import type {
  TherapeuticProperty,
  PropertyOilSuggestions,
  HealthConcernData,
  DemographicsData,
  PotentialCause,
  PotentialSymptom,
  ContainerRecommendation,
  SafetyWarning
} from '../types/recipe.types';
import type { ScoredOil, SafetyLibrary } from '../utils/post-enrichment-scoring';

/**
 * React Native version of create recipe streaming hook
 * Adapted from Next.js original with parallel streaming engine
 */
export function useCreateRecipeStreaming() {
  const { streamingState, startStreams, resetState } = useParallelStreamingEngine<any>();
  const { apiLanguage } = useLanguage();

  /**
   * Start parallel oil suggestion streaming for all properties
   * Uses service layer for business logic
   */
  const startOilSuggestionStreaming = useCallback(async (
    properties: TherapeuticProperty[],
    healthConcern: HealthConcernData,
    demographics: DemographicsData,
    selectedCauses: PotentialCause[],
    selectedSymptoms: PotentialSymptom[]
  ) => {
    console.log('🚀 [RN-Streaming] Starting parallel oil suggestion streaming for', properties.length, 'properties');

    // Use service layer to create streaming requests
    const requests = createOilSuggestionStreamingRequests(
      properties,
      healthConcern,
      demographics,
      selectedCauses,
      selectedSymptoms,
      apiLanguage
    );

    console.log('📤 [RN-Streaming] Created', requests.length, 'parallel streaming requests');
    return await startStreams(requests);
  }, [startStreams, apiLanguage]);

  /**
   * Fetch oil enrichment data for properties with suggested oils
   * Uses service layer for business logic
   */
  const fetchOilEnrichmentDataWrapper = useCallback(async (
    propertySuggestions: PropertyOilSuggestions[],
    healthConcern: HealthConcernData,
    demographics: DemographicsData,
    selectedCauses: PotentialCause[],
    selectedSymptoms: PotentialSymptom[]
  ) => {
    // Use service layer for business logic
    return await fetchOilEnrichmentData(
      propertySuggestions,
      healthConcern,
      demographics,
      selectedCauses,
      selectedSymptoms,
      apiLanguage
    );
  }, [apiLanguage]);

  /**
   * Start parallel final recipe streaming for 3 time slots using pre-calculated scoring data
   * Uses service layer for business logic
   * UPDATED: Now accepts pre-calculated scoring data to eliminate duplication
   */
  const startFinalRecipeStreaming = useCallback(async (
    scoredOils: ScoredOil[],
    safetyLibrary: SafetyLibrary,
    safetyLibraryFormatted: string,
    healthConcern: HealthConcernData,
    demographics: DemographicsData,
    selectedCauses: PotentialCause[],
    selectedSymptoms: PotentialSymptom[]
  ) => {
    console.log('🚀 [RN-FinalRecipes] Starting parallel final recipe streaming for 3 time slots');

    // Use service layer to create streaming requests with pre-calculated data
    const requests = createFinalRecipeStreamingRequests(
      scoredOils,
      safetyLibrary,
      safetyLibraryFormatted,
      healthConcern,
      demographics,
      selectedCauses,
      selectedSymptoms,
      apiLanguage
    );

    console.log('📤 [RN-FinalRecipes] Created', requests.length, 'parallel streaming requests for time slots');
    return await startStreams(requests);
  }, [startStreams, apiLanguage]);

  return {
    streamingState,
    startOilSuggestionStreaming,
    startFinalRecipeStreaming,
    fetchOilEnrichmentData: fetchOilEnrichmentDataWrapper,
    resetState
  };
}
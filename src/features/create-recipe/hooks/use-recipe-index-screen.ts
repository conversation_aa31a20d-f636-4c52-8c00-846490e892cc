import { useMemo, useEffect } from 'react';
import { useRouter } from 'expo-router';
import { useCombinedRecipeStore } from '../store/combined-store';
import { WIZARD_STEPS, TOTAL_STEPS } from '../constants';

export const useRecipeIndexScreen = () => {
  const router = useRouter();
  
  // Store state
  const {
    completedSteps,
    healthConcern,
    demographics,
    selectedCauses,
    selectedSymptoms,
    resetWizard,
  } = useCombinedRecipeStore();

  // Calculate progress
  const progress = useMemo(() => {
    return completedSteps.length / TOTAL_STEPS;
  }, [completedSteps.length]);

  // Navigation helpers
  const handleStartWizard = () => {
    router.push('/(tabs)/create-recipe/health-concern');
  };

  const handleContinueWizard = () => {
    // Navigate to the next incomplete step
    const nextStep = WIZARD_STEPS.find(step => !completedSteps.includes(step.key));
    if (nextStep) {
      router.push(nextStep.path as any);
    }
  };

  const handleResetWizard = () => {
    resetWizard();
  };

  // Get progress summary - extracted business logic
  const progressSummary = useMemo(() => {
    const summaries = [];
    
    if (healthConcern) {
      summaries.push({
        title: 'Health Concern',
        value: healthConcern.healthConcern.length > 50 
          ? `${healthConcern.healthConcern.substring(0, 50)}...`
          : healthConcern.healthConcern,
        icon: 'heart-pulse'
      });
    }
    
    if (demographics) {
      summaries.push({
        title: 'Demographics',
        value: `${demographics.gender}, ${demographics.ageCategory}`,
        icon: 'account'
      });
    }
    
    if (selectedCauses.length > 0) {
      summaries.push({
        title: 'Potential Causes',
        value: `${selectedCauses.length} selected`,
        icon: 'format-list-bulleted'
      });
    }
    
    if (selectedSymptoms.length > 0) {
      summaries.push({
        title: 'Symptoms',
        value: `${selectedSymptoms.length} selected`,
        icon: 'medical-bag'
      });
    }
    
    return summaries;
  }, [healthConcern, demographics, selectedCauses.length, selectedSymptoms.length]);

  const hasProgress = progressSummary.length > 0;

  // Smart routing: redirect to health-concern if no recipe in progress
  useEffect(() => {
    if (!hasProgress) {
      router.replace('/(tabs)/create-recipe/health-concern');
    }
  }, [hasProgress, router]);

  return {
    // State
    progress,
    progressSummary,
    hasProgress,
    
    // Actions
    handleStartWizard,
    handleContinueWizard,
    handleResetWizard,
  };
};
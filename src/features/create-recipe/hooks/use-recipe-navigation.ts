import { useCallback } from 'react';
import { useRouter } from 'expo-router';
import { useCombinedRecipeStore } from '@/features/create-recipe/store/combined-store';
import { RecipeStep } from '@/features/create-recipe/types';

/**
 * Hook for managing recipe wizard navigation
 * Integrates with Expo Router and Zustand store
 * Provides navigation functions adapted for React Native
 */
export const useRecipeNavigation = () => {
  const router = useRouter();
  const {
    currentStep,
    completedSteps,
    setCurrentStep,
    markStepCompleted,
    canNavigateToStep,
    clearStepsAfter,
  } = useCombinedRecipeStore();

  // Define available steps and their paths
  const stepPaths = {
    [RecipeStep.HEALTH_CONCERN]: '/(tabs)/create-recipe/health-concern',
    [RecipeStep.DEMOGRAPHICS]: '/(tabs)/create-recipe/demographics',
    [RecipeStep.CAUSES]: '/(tabs)/create-recipe/causes',
    [RecipeStep.SYMPTOMS]: '/(tabs)/create-recipe/symptoms',
    // Properties and final recipes routes don't exist yet
    [RecipeStep.PROPERTIES]: '/(tabs)/create-recipe/properties',
    [RecipeStep.FINAL_RECIPES]: '/(tabs)/create-recipe/final-recipes',
  };

  const stepOrder = [
    RecipeStep.HEALTH_CONCERN,
    RecipeStep.DEMOGRAPHICS,
    RecipeStep.CAUSES,
    RecipeStep.SYMPTOMS,
    RecipeStep.PROPERTIES,
    RecipeStep.FINAL_RECIPES,
  ];

  // Navigation functions
  const goToStep = useCallback((step: RecipeStep) => {
    if (!canNavigateToStep(step)) {
      console.warn(`Cannot navigate to step: ${step}`);
      return false;
    }

    const path = stepPaths[step];
    if (!path) {
      console.error(`Path not found for step: ${step}`);
      return false;
    }

    setCurrentStep(step);
    router.push(path as any);
    return true;
  }, [canNavigateToStep, setCurrentStep, router]);

  const goToNext = useCallback(() => {
    const currentIndex = stepOrder.indexOf(currentStep);
    if (currentIndex === -1 || currentIndex >= stepOrder.length - 1) {
      console.warn('Already at the last step or step not found');
      return false;
    }

    // Mark current step as completed
    markStepCompleted(currentStep);

    // Navigate to next step
    const nextStep = stepOrder[currentIndex + 1];
    return goToStep(nextStep);
  }, [currentStep, markStepCompleted, goToStep]);

  const goToPrevious = useCallback(() => {
    const currentIndex = stepOrder.indexOf(currentStep);
    if (currentIndex <= 0) {
      // Go back to index if at first step
      router.push('/(tabs)/create-recipe/' as any);
      return true;
    }

    // Clear steps after the previous step to maintain data consistency
    const previousStep = stepOrder[currentIndex - 1];
    clearStepsAfter(previousStep);

    return goToStep(previousStep);
  }, [currentStep, clearStepsAfter, goToStep, router]);

  const goToIndex = useCallback(() => {
    router.push('/(tabs)/create-recipe/' as any);
    return true;
  }, [router]);

  // Progress calculations
  const getProgress = useCallback(() => {
    const currentIndex = stepOrder.indexOf(currentStep);
    return {
      currentStepIndex: currentIndex,
      totalSteps: stepOrder.length,
      progress: currentIndex >= 0 ? (currentIndex + 1) / stepOrder.length : 0,
      completedSteps: completedSteps.length,
      isFirstStep: currentIndex === 0,
      isLastStep: currentIndex === stepOrder.length - 1,
    };
  }, [currentStep, completedSteps.length]);

  // Validation helpers
  const canGoNext = useCallback(() => {
    const currentIndex = stepOrder.indexOf(currentStep);
    return currentIndex >= 0 && currentIndex < stepOrder.length - 1;
  }, [currentStep]);

  const canGoPrevious = useCallback(() => {
    const currentIndex = stepOrder.indexOf(currentStep);
    return currentIndex > 0;
  }, [currentStep]);

  // Step completion helpers
  const completeCurrentStep = useCallback(() => {
    markStepCompleted(currentStep);
  }, [markStepCompleted, currentStep]);

  const completeStepAndGoNext = useCallback(() => {
    completeCurrentStep();
    return goToNext();
  }, [completeCurrentStep, goToNext]);

  return {
    // Current state
    currentStep,
    completedSteps,

    // Navigation functions
    goToStep,
    goToNext,
    goToPrevious,
    goToIndex,
    setCurrentStep,

    // Progress information
    getProgress,

    // Validation
    canNavigateToStep,
    canGoNext,
    canGoPrevious,

    // Step completion
    completeCurrentStep,
    completeStepAndGoNext,
  };
};
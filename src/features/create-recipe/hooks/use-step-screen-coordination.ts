import { useState, useCallback } from 'react';

interface StepScreenCoordinationState {
  isValid: boolean;
  formData: any;
}

/**
 * Shared hook for managing screen-to-component coordination pattern
 * Used by demographics, causes, and symptoms screens
 * Handles validation state, submit function management, and loading coordination
 */
export const useStepScreenCoordination = () => {
  const [validationState, setValidationState] = useState<StepScreenCoordinationState>({
    isValid: false,
    formData: null
  });
  const [submitFn, setSubmitFn] = useState<(() => void) | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Callback to receive form validation state from child component
  const handleValidationChange = useCallback((isValid: boolean, formData?: any) => {
    setValidationState({ isValid, formData });
    console.log('Step validation state updated:', { isValid, formData });
  }, []);
  
  // Callback to receive form submit function from child component
  const handleSubmitReady = useCallback((submitFunction: () => void) => {
    setSubmitFn(() => submitFunction);
    console.log('Step submit function ready');
  }, []);
  
  // Callback to receive loading state from child component
  const handleLoadingChange = useCallback((loading: boolean) => {
    setIsLoading(loading);
    console.log('Step loading state:', loading);
  }, []);

  // Validation helper
  const isFormValid = useCallback(() => {
    return validationState.isValid;
  }, [validationState.isValid]);

  // Handle step continuation - trigger the stored submit function
  const handleContinue = useCallback(async () => {
    if (!isFormValid() || !submitFn) {
      console.log('Cannot continue step:', { 
        isValid: isFormValid(), 
        hasSubmitFn: !!submitFn 
      });
      return;
    }
    
    console.log('Triggering step form submission');
    try {
      // Trigger the child component's submit function which handles:
      // 1. Store update with step data
      // 2. API streaming with modal (if applicable)
      // 3. Navigation after completion
      // Loading state managed by child via handleLoadingChange callback
      submitFn();
    } catch (error) {
      console.error('Error triggering step form submission:', error);
    }
  }, [isFormValid, submitFn]);

  return {
    // State
    validationState,
    isLoading,
    
    // Computed
    isFormValid: isFormValid(),
    
    // Callbacks for child components
    handleValidationChange,
    handleSubmitReady, 
    handleLoadingChange,
    
    // Actions
    handleContinue,
  };
};
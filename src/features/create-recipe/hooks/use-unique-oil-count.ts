import { useMemo } from 'react';
import { TherapeuticProperty } from '../types';

/**
 * Custom hook for counting unique essential oils across all therapeutic properties
 * Reuses existing deduplication logic from use-properties-selection.ts
 * 
 * @param therapeuticProperties - Array of therapeutic properties with suggested oils
 * @returns Object with unique oil count and loading state
 */
export const useUniqueOilCount = (therapeuticProperties: TherapeuticProperty[]) => {
  // Calculate unique oils count by deduplicating across all properties by oil_id
  const uniqueOilsData = useMemo(() => {
    const uniqueOilIds = new Set<string>();
    let totalEnrichedProperties = 0;
    let hasAnyOils = false;

    // Iterate through all properties and collect unique oil IDs
    therapeuticProperties.forEach(property => {
      if (property.isEnriched && property.suggested_oils && property.suggested_oils.length > 0) {
        totalEnrichedProperties++;
        hasAnyOils = true;
        
        // Add each oil's ID to the set (automatic deduplication)
        property.suggested_oils.forEach(oil => {
          if (oil.oil_id) {
            uniqueOilIds.add(oil.oil_id);
          }
        });
      }
    });

    return {
      uniqueCount: uniqueOilIds.size,
      totalEnrichedProperties,
      hasAnyOils,
      isLoading: totalEnrichedProperties < therapeuticProperties.length && therapeuticProperties.length > 0
    };
  }, [therapeuticProperties]);

  return uniqueOilsData;
};
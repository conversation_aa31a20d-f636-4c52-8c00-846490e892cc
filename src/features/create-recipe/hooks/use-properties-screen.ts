import { useState, useCallback, useRef, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useCombinedRecipeStore } from '../store/combined-store';

/**
 * Hook for managing Properties screen business logic
 * Handles enrichment state detection, dynamic UI calculations, and form coordination
 */
export const usePropertiesScreen = () => {
  const { t } = useTranslation('create-recipe');
  const { therapeuticProperties } = useCombinedRecipeStore();
  
  // Enhanced state management with refs for performance optimization
  const [isValid, setIsValid] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [currentSubmitFn, setCurrentSubmitFn] = useState<(() => void) | null>(null);
  
  // Refs to prevent redundant updates
  const prevIsValid = useRef(isValid);
  const prevIsLoading = useRef(isLoading);
  const prevSubmitFn = useRef<(() => void) | null>(null);

  // Callback handlers with performance optimization
  const handleValidationChange = useCallback((valid: boolean) => {
    if (valid !== prevIsValid.current) {
      prevIsValid.current = valid;
      setIsValid(valid);
    }
  }, []);

  const handleSubmitReady = useCallback((submitFn: () => void) => {
    if (submitFn !== prevSubmitFn.current) {
      prevSubmitFn.current = submitFn;
      setCurrentSubmitFn(() => submitFn);
    }
  }, []);

  const handleLoadingChange = useCallback((loading: boolean) => {
    if (loading !== prevIsLoading.current) {
      prevIsLoading.current = loading;
      setIsLoading(loading);
    }
  }, []);

  // Business rules - enrichment state detection
  const allPropertiesEnriched = useMemo(() => {
    return therapeuticProperties.length > 0 &&
      therapeuticProperties.every(prop => 
        prop.isEnriched && 
        prop.suggested_oils && 
        prop.suggested_oils.length > 0
      );
  }, [therapeuticProperties]);

  // Validation helper
  const hasProperties = useCallback(() => {
    return therapeuticProperties.length > 0;
  }, [therapeuticProperties.length]);

  // Dynamic UI calculations
  const getButtonText = useCallback(() => {
    if (isLoading) {
      return t('propertiesSelection.loading.button');
    }
    return allPropertiesEnriched 
      ? t('propertiesSelection.buttons.generateFinalRecipes') 
      : t('propertiesSelection.buttons.getOilSuggestions');
  }, [allPropertiesEnriched, isLoading, t]);

  const getButtonIcon = useCallback(() => {
    return allPropertiesEnriched ? 'creation' : 'leaf';
  }, [allPropertiesEnriched]);

  // Dynamic title text
  const getTitle = useCallback(() => {
    if (isLoading) {
      return t('propertiesSelection.loading.title');
    }
    if (allPropertiesEnriched) {
      return t('propertiesSelection.buttons.generateFinalRecipes');
    }
    return therapeuticProperties.length > 0 
      ? t('propertiesSelection.buttons.getOilSuggestions') 
      : t('propertiesSelection.buttons.selectProperties');
  }, [isLoading, allPropertiesEnriched, therapeuticProperties.length, t]);

  // Dynamic subtitle text  
  const getSubtitle = useCallback(() => {
    if (isLoading) {
      return t('propertiesSelection.loading.subtitle');
    }
    if (allPropertiesEnriched) {
      return t('propertiesSelection.messages.propertiesEnrichedCreateRecipes');
    }
    return therapeuticProperties.length > 0 
      ? t('propertiesSelection.messages.propertiesSelectedGetSuggestions', { count: therapeuticProperties.length })
      : t('propertiesSelection.messages.selectPropertiesToContinue');
  }, [isLoading, allPropertiesEnriched, therapeuticProperties.length, t]);

  // Action handler
  const handleAction = useCallback(() => {
    if (currentSubmitFn) {
      currentSubmitFn();
    }
  }, [currentSubmitFn]);

  return {
    // State
    isValid,
    isLoading,
    allPropertiesEnriched,
    
    // Computed
    hasProperties: hasProperties(),
    buttonText: getButtonText(),
    buttonIcon: getButtonIcon(),
    title: getTitle(),
    subtitle: getSubtitle(),
    
    // Callbacks for child components
    handleValidationChange,
    handleSubmitReady,
    handleLoadingChange,
    
    // Actions
    handleAction,
  };
};
import { useState, useRef, useMemo, useEffect, useCallback } from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '@/shared/hooks';
import { useCombinedRecipeStore } from '@/features/create-recipe/store/combined-store';
import { useRecipeNavigation } from './use-recipe-navigation';
import { useCreateRecipeStreaming } from './use-create-recipe-streaming';
import { batchEnrichOils } from '@/shared/services/api/rotinanatural-client';
import { RecipeStep } from '@/features/create-recipe/types';



export const usePropertiesSelection = () => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();

  // Recipe store integration
  const {
    therapeuticProperties,
    updateTherapeuticProperties,
    updatePropertyWithEnrichedOils,
    setPropertyEnrichmentStatus,
    healthConcern,
    demographics,
    selectedCauses,
    selectedSymptoms,
    getCauseNamesByIds,
    getSymptomNamesByIds,
    updateFinalRecipes,
    // Post-enrichment scoring data (to eliminate duplication)
    scoredOils,
    safetyLibrary,
    safetyLibraryFormatted
  } = useCombinedRecipeStore();

  // Navigation integration
  const { completeStepAndGoNext, setCurrentStep } = useRecipeNavigation();

  // Streaming integration
  const {
    streamingState: parallelStreamingState,
    startOilSuggestionStreaming,
    startFinalRecipeStreaming,
    fetchOilEnrichmentData: _fetchOilEnrichmentData,
    resetState: resetParallelStreams
  } = useCreateRecipeStreaming();

  // State management (8 state variables from original)
  const [selectedPropertyIds, setSelectedPropertyIds] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showStreamingModal, setShowStreamingModal] = useState(false);

  // Recipe generation state management (separate from oil suggestions)
  const [showRecipeStreamingModal, setShowRecipeStreamingModal] = useState(false);
  const [isGeneratingRecipes, setIsGeneratingRecipes] = useState(false);

  // Per-property automatic enrichment state tracking
  const [propertyEnrichmentStates, setPropertyEnrichmentStates] = useState<Record<string, {
    isEnriching: boolean;
    retryCount: number;
    error?: string;
  }>>({});
  

  // Refs for infinite loop protection
  const processedResultsRef = useRef(new Set<string>());
  const processedErrorsRef = useRef(new Set<string>());

  // REMOVED: Enrichment infinite loop protection refs - No longer needed for immediate enrichment

  // Computed values
  const propertyProgressCount = useMemo(() => {
    if (!parallelStreamingState.isStreaming) return { completed: 0, total: 0 };
    
    // Count properties that have completed (either with results or errors)
    const completedCount = parallelStreamingState.results.size + parallelStreamingState.errors.size;
    const totalProperties = selectedPropertyIds.length;
    
    return {
      completed: completedCount,
      total: totalProperties
    };
  }, [parallelStreamingState.isStreaming, parallelStreamingState.results.size, parallelStreamingState.errors.size, selectedPropertyIds.length]);

  const selectionCount = selectedPropertyIds.length;
  const canSubmit = selectionCount > 0;

  // Debugging logs (preserve from original)
  console.log('🔍 LAYER 11P-1: therapeuticProperties count:', therapeuticProperties.length);
  console.log('🔍 LAYER 11P-1: selectedPropertyIds count:', selectionCount);
  console.log('🔍 LAYER 11P-1: canSubmit:', canSubmit);
  console.log('🔍 LAYER 11P-1: parallelStreamingState.isStreaming:', parallelStreamingState.isStreaming);
  console.log('🔍 LAYER 11P-1: propertyProgressCount:', propertyProgressCount);
  console.log('🔍 LAYER 11P-1: showStreamingModal:', showStreamingModal);

  // Use real data if available, fallback to mock
  const mockProperties = [
    {
      property_id: '1',
      property_name_localized: 'Anti-inflammatory',
      property_name_english: 'Anti-inflammatory',
      description_contextual_localized: 'Helps reduce inflammation'
    },
    {
      property_id: '2', 
      property_name_localized: 'Antimicrobial',
      property_name_english: 'Antimicrobial',
      description_contextual_localized: 'Fights harmful microorganisms'
    }
  ];

  const propertiesToShow = therapeuticProperties.length > 0 ? therapeuticProperties : mockProperties;

  // Auto-enrichment function with exponential backoff retry logic
  const autoTriggerEnrichment = useCallback(async (
    propertyId: string, 
    suggestedOils: any[], 
    retryCount: number = 0
  ) => {
    const maxRetries = 3;
    const baseDelay = 1000; // 1 second base delay
    
    console.log(`🤖 AUTO-ENRICH: Starting auto-enrichment for property ${propertyId} (attempt ${retryCount + 1}/${maxRetries + 1})`);
    
    // Update enrichment state to show loading
    setPropertyEnrichmentStates(prev => ({
      ...prev,
      [propertyId]: {
        isEnriching: true,
        retryCount,
        error: undefined
      }
    }));

    try {
      if (!healthConcern || !demographics || !selectedCauses?.length || !selectedSymptoms?.length) {
        throw new Error('Missing required recipe data for auto-enrichment');
      }

      // Prepare oils for batch enrichment (single property)
      const preparedOils = suggestedOils.map(oil => ({
        name: oil.oil_name_localized || oil.name,
        botanical_name: oil.botanical_name || '',
        oil_id: oil.oil_id,
        ...oil
      }));

      // Call batch enrichment for this property's oils
      const enrichmentResponse = await batchEnrichOils({
        suggestedOils: preparedOils
      });

      console.log(`✅ AUTO-ENRICH: Successfully enriched property ${propertyId} with ${enrichmentResponse?.enriched_oils?.length || 0} oils`);
      
      // 🔍 RAW DATA LOGGING - Log the complete API response structure
      console.log(`🔍 RAW API RESPONSE for property ${propertyId}:`, JSON.stringify(enrichmentResponse, null, 2));
      
      // 🔍 DETAILED SAFETY DATA LOGGING - Log each oil's safety data structure
      if (enrichmentResponse?.enriched_oils) {
        enrichmentResponse.enriched_oils.forEach((oil: any, index: number) => {
          console.log(`🔍 OIL ${index + 1} RAW DATA (${oil.name_english || oil.oil_name_english || 'Unknown'}):`, {
            oil_id: oil.oil_id,
            name_english: oil.name_english,
            name_botanical: oil.name_botanical,
            enrichment_status: oil.enrichment_status,
            isEnriched: oil.isEnriched,
            hasSafety: !!oil.safety,
            safetyRawData: oil.safety ? JSON.stringify(oil.safety, null, 2) : 'No safety data'
          });
          
          // 🔍 PREGNANCY DATA SPECIFIC LOGGING
          if (oil.safety?.pregnancy_nursing) {
            console.log(`🔍 PREGNANCY DATA for ${oil.name_english}:`, {
              pregnancyNursingRaw: JSON.stringify(oil.safety.pregnancy_nursing, null, 2),
              pregnancyNursingType: typeof oil.safety.pregnancy_nursing,
              pregnancyNursingIsArray: Array.isArray(oil.safety.pregnancy_nursing),
              pregnancyNursingLength: Array.isArray(oil.safety.pregnancy_nursing) ? oil.safety.pregnancy_nursing.length : 'Not an array',
              pregnancyNursingKeys: typeof oil.safety.pregnancy_nursing === 'object' ? Object.keys(oil.safety.pregnancy_nursing) : 'Not an object'
            });
          } else {
            console.log(`🔍 NO PREGNANCY DATA for ${oil.name_english}`);
          }
          
          // 🔍 CHILD SAFETY DATA SPECIFIC LOGGING
          if (oil.safety?.child_safety) {
            console.log(`🔍 CHILD SAFETY DATA for ${oil.name_english}:`, {
              childSafetyRaw: JSON.stringify(oil.safety.child_safety, null, 2),
              childSafetyType: typeof oil.safety.child_safety,
              childSafetyIsArray: Array.isArray(oil.safety.child_safety),
              childSafetyLength: Array.isArray(oil.safety.child_safety) ? oil.safety.child_safety.length : 'Not an array'
            });
          } else {
            console.log(`🔍 NO CHILD SAFETY DATA for ${oil.name_english}`);
          }
        });
      }

      // Update store with enriched oils
      if (enrichmentResponse?.enriched_oils) {
        updatePropertyWithEnrichedOils(propertyId, enrichmentResponse.enriched_oils);
        setPropertyEnrichmentStatus(propertyId, 'success');
      }

      // Mark enrichment as completed
      setPropertyEnrichmentStates(prev => ({
        ...prev,
        [propertyId]: {
          isEnriching: false,
          retryCount,
          error: undefined
        }
      }));

    } catch (error) {
      console.error(`❌ AUTO-ENRICH: Error enriching property ${propertyId} (attempt ${retryCount + 1}):`, error);
      
      if (retryCount < maxRetries) {
        // Calculate exponential backoff delay: 1s, 2s, 4s
        const delay = baseDelay * Math.pow(2, retryCount);
        console.log(`🔄 AUTO-ENRICH: Retrying property ${propertyId} in ${delay}ms...`);
        
        setTimeout(() => {
          autoTriggerEnrichment(propertyId, suggestedOils, retryCount + 1);
        }, delay);
      } else {
        // Max retries reached, mark as failed
        console.error(`💥 AUTO-ENRICH: Property ${propertyId} failed after ${maxRetries + 1} attempts`);
        setPropertyEnrichmentStates(prev => ({
          ...prev,
          [propertyId]: {
            isEnriching: false,
            retryCount,
            error: error instanceof Error ? error.message : 'Enrichment failed'
          }
        }));
        setPropertyEnrichmentStatus(propertyId, 'error');
      }
    }
  }, [
    healthConcern, 
    demographics, 
    selectedCauses, 
    selectedSymptoms, 
    updatePropertyWithEnrichedOils, 
    setPropertyEnrichmentStatus
  ]);

  // Set current step when component mounts
  useEffect(() => {
    setCurrentStep(RecipeStep.PROPERTIES);
  }, [setCurrentStep]);

  // LAYER 4: Add auto-selection useEffect
  useEffect(() => {
    if (therapeuticProperties.length > 0 && selectedPropertyIds.length === 0) {
      const allIds = therapeuticProperties.map(p => p.property_id);
      setSelectedPropertyIds(allIds);
    }
  }, [therapeuticProperties, selectedPropertyIds.length]);

  // AUTO-TRIGGER: Start oil suggestion streaming automatically when properties are loaded
  useEffect(() => {
    const shouldAutoTrigger = (
      therapeuticProperties.length > 0 &&
      selectedPropertyIds.length > 0 &&
      healthConcern &&
      demographics &&
      selectedCauses?.length > 0 &&
      selectedSymptoms?.length > 0 &&
      !parallelStreamingState.isStreaming &&
      !isSubmitting &&
      // Only trigger if no properties have suggested oils yet (first load)
      !therapeuticProperties.some(prop => prop.suggested_oils && prop.suggested_oils.length > 0)
    );

    if (shouldAutoTrigger) {
      console.log('🤖 AUTO-TRIGGER: Conditions met, starting oil suggestion streaming automatically...');
      console.log('🤖 AUTO-TRIGGER: Properties:', therapeuticProperties.length);
      console.log('🤖 AUTO-TRIGGER: Selected:', selectedPropertyIds.length);
      console.log('🤖 AUTO-TRIGGER: Has required data:', {
        healthConcern: !!healthConcern,
        demographics: !!demographics,
        selectedCauses: selectedCauses?.length || 0,
        selectedSymptoms: selectedSymptoms?.length || 0
      });
      
      // Call handleSubmit to trigger the streaming process
      handleSubmit();
    }
  }, [
    therapeuticProperties.length,
    selectedPropertyIds.length,
    healthConcern,
    demographics,
    selectedCauses?.length,
    selectedSymptoms?.length,
    parallelStreamingState.isStreaming,
    isSubmitting,
    therapeuticProperties,
    handleSubmit
  ]);

  // LAYER 7B: Add complex object logging in modal visibility useEffect (testing object creation)
  useEffect(() => {
    console.log('🚀 LAYER 7B: MODAL VISIBILITY CHANGED:', {
      showStreamingModal,
      timestamp: new Date().toISOString(),
      component: 'PropertiesSelection'
    });
  }, [showStreamingModal]);

  // LAYER 8B: Add streaming progress useEffect WITH TEMPLATE LITERALS (THE BIG TEST!)
  useEffect(() => {
    if (parallelStreamingState.isStreaming) {
      console.log(`📊 LAYER 8B: Property Analysis Progress: ${propertyProgressCount.completed}/${propertyProgressCount.total} properties completed`);
      console.log(`📊 LAYER 8B: Tool Calls Made: ${parallelStreamingState.completedCount} total | Results: ${parallelStreamingState.results.size} | Errors: ${parallelStreamingState.errors.size}`);
    }
  }, [parallelStreamingState.isStreaming, propertyProgressCount.completed, propertyProgressCount.total, parallelStreamingState.completedCount, parallelStreamingState.results.size, parallelStreamingState.errors.size]);

  // LAYER 8C: Test Map/Set size dependencies in isolation (testing if .size properties cause issues)
  useEffect(() => {
    console.log('📊 LAYER 8C: Map/Set sizes changed');
    console.log('📊 LAYER 8C: Results size:', parallelStreamingState.results.size);
    console.log('📊 LAYER 8C: Errors size:', parallelStreamingState.errors.size);
  }, [parallelStreamingState.results.size, parallelStreamingState.errors.size]);

  // LAYER 9B: Add individual streaming results processing useEffect WITH STORE OPERATIONS (THE PRIME SUSPECT!)
  useEffect(() => {
    if (parallelStreamingState.results.size === 0) return;

    console.log('🔄 LAYER 9B: Processing individual streaming results with STORE OPERATIONS...');

    // Update properties individually for each result that arrived
    parallelStreamingState.results.forEach((result, propertyId) => {
      // INFINITE LOOP FIX: Skip if already processed
      if (processedResultsRef.current.has(propertyId)) return;
      
      if (result && result.suggested_oils) {
        console.log(`✅ LAYER 9B: Updating property ${propertyId} with ${result.suggested_oils.length} oils via STORE`);
        
        // THE PRIME SUSPECT: Update store immediately with this single property update
        const currentProperties = useCombinedRecipeStore.getState().therapeuticProperties;
        const updatedProperties = currentProperties.map(property => {
          if (property.property_id === propertyId) {
            return {
              ...property,
              suggested_oils: result.suggested_oils,
              isLoadingOils: false,
              errorLoadingOils: null,
              isEnriched: false, // Will be enriched in next step
            };
          }
          return property;
        });

        // STORE METHOD CALL: This could trigger the error!
        updateTherapeuticProperties(updatedProperties, `individual-result-${propertyId}`);
        
        // AUTO-ENRICHMENT: Trigger enrichment immediately when suggested oils arrive
        console.log(`🤖 AUTO-ENRICH: Suggested oils arrived for property ${propertyId}, triggering auto-enrichment...`);
        autoTriggerEnrichment(propertyId, result.suggested_oils);
        
        // INFINITE LOOP FIX: Mark as processed
        processedResultsRef.current.add(propertyId);
      }
    });
  }, [parallelStreamingState.results, updateTherapeuticProperties, autoTriggerEnrichment]);

  // LAYER 10B: Add individual streaming errors processing useEffect WITH STORE OPERATIONS
  useEffect(() => {
    if (parallelStreamingState.errors.size === 0) return;

    console.log('❌ LAYER 10B: Processing individual streaming errors WITH STORE OPERATIONS...');

    // Handle errors individually for each property that failed WITH STORE UPDATES
    parallelStreamingState.errors.forEach((error, propertyId) => {
      // INFINITE LOOP FIX: Skip if already processed
      if (processedErrorsRef.current.has(propertyId)) return;
      
      console.log(`❌ LAYER 10B: Handling error for property ${propertyId}:`, error);
      
      // Update just this property with error state immediately via STORE
      const currentProperties = useCombinedRecipeStore.getState().therapeuticProperties;
      const updatedProperties = currentProperties.map(property => {
        if (property.property_id === propertyId) {
          return {
            ...property,
            isLoadingOils: false,
            errorLoadingOils: 'Failed to get suggested oils',
          };
        }
        return property;
      });

      // STORE METHOD CALL: Update store immediately with this single property error
      updateTherapeuticProperties(updatedProperties, `individual-error-${propertyId}`);
      
      // INFINITE LOOP FIX: Mark as processed
      processedErrorsRef.current.add(propertyId);
    });
  }, [parallelStreamingState.errors, updateTherapeuticProperties]);

  // REMOVED: ENRICHMENT LAYER 9B and 10B - No longer needed since enrichment is now immediate batch processing

  // COMPLETION LOGIC: Reset isSubmitting when streaming finishes (no modal needed)
  useEffect(() => {
    const totalSelected = selectedPropertyIds.length;
    const totalCompleted = parallelStreamingState.results.size + parallelStreamingState.errors.size;
    
    console.log('🎯 AUTO-COMPLETION: Checking streaming completion:');
    console.log('🎯 AUTO-COMPLETION: totalSelected:', totalSelected);
    console.log('🎯 AUTO-COMPLETION: totalCompleted:', totalCompleted);
    console.log('🎯 AUTO-COMPLETION: isStreaming:', parallelStreamingState.isStreaming);
    console.log('🎯 AUTO-COMPLETION: isSubmitting:', isSubmitting);
    
    // Reset isSubmitting when streaming completes and we're still in submitting state
    if (totalSelected > 0 && totalCompleted >= totalSelected && !parallelStreamingState.isStreaming && isSubmitting) {
      console.log('🎯 AUTO-COMPLETION: ✅ Streaming finished, resetting isSubmitting');
      console.log('🎯 AUTO-COMPLETION: Results count:', parallelStreamingState.results.size);
      console.log('🎯 AUTO-COMPLETION: Errors count:', parallelStreamingState.errors.size);
      
      setIsSubmitting(false);
      
      // Only show error if ALL properties failed
      if (parallelStreamingState.results.size === 0 && parallelStreamingState.errors.size > 0) {
        console.log('❌ AUTO-COMPLETION: All properties failed, showing error');
        setErrors(prev => ({ ...prev, api: 'Failed to get oils for all properties. Please try again.' }));
      }
    }
  }, [selectedPropertyIds.length, parallelStreamingState.results.size, parallelStreamingState.errors.size, parallelStreamingState.isStreaming, isSubmitting]);

  // REMOVED: ENRICHMENT LAYER 11B - No longer needed since enrichment is now immediate with built-in completion handling

  // Business logic functions
  const handlePropertyToggle = (propertyId: string) => {
    setSelectedPropertyIds(prev => {
      const isSelected = prev.includes(propertyId);
      if (isSelected) {
        const newSelection = prev.filter(id => id !== propertyId);
        console.log('🔄 Deselected property, count:', newSelection.length);
        return newSelection;
      } else {
        const newSelection = [...prev, propertyId];
        console.log('🔄 Selected property, count:', newSelection.length);
        return newSelection;
      }
    });
  };

  const handleSubmit = useCallback(async () => {
    console.log('🔥 LAYER 11P-1: PRODUCTION submit pressed - starting REAL API streaming!');
    if (!canSubmit) {
      console.log('❌ LAYER 11P-1: Cannot submit, no properties selected');
      return;
    }
    
    setIsSubmitting(true);
    // REMOVED: setShowStreamingModal(true) - No longer using modal for in-place loading
    
    try {
      // REAL API STREAMING - this sets isStreaming: true
      console.log('🚀 LAYER 11P-1: Starting REAL oil suggestion streaming for', selectionCount, 'properties...');
      console.log('🚀 LAYER 11P-1: API Dependencies check:', {
        healthConcern: !!healthConcern,
        demographics: !!demographics,
        selectedCauses: selectedCauses?.length || 0,
        selectedSymptoms: selectedSymptoms?.length || 0
      });
      
      if (!healthConcern || !demographics || !selectedCauses?.length || !selectedSymptoms?.length) {
        throw new Error('Missing required recipe data - healthConcern, demographics, selectedCauses, or selectedSymptoms');
      }
      
      // Get selected property objects (not just IDs)
      const selectedProperties = therapeuticProperties.filter(p => selectedPropertyIds.includes(p.property_id));
      
      await startOilSuggestionStreaming(
        selectedProperties,
        healthConcern,
        demographics,
        selectedCauses,
        selectedSymptoms
      );
      console.log('🚀 LAYER 11P-1: Streaming initiated! parallelStreamingState.isStreaming should now be TRUE');
    } catch (error) {
      console.error('❌ LAYER 11P-1: Error starting streaming:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to start oil suggestion streaming';
      setIsSubmitting(false);
      console.error('❌ LAYER 11P-1: Streaming failed with error:', errorMessage);
    }
  }, [canSubmit, healthConcern, demographics, selectedCauses, selectedSymptoms, selectedPropertyIds, therapeuticProperties, startOilSuggestionStreaming]);

  // Handle final recipe generation using pre-calculated scoring data (eliminates duplication)
  const handleGenerateFinalRecipes = useCallback(async () => {
    console.log('🚀 [Properties-FinalRecipes] Generate Final Recipes button clicked');

    // Verify we have pre-calculated scoring data
    if (!scoredOils || scoredOils.length === 0) {
      console.error('❌ [Properties-FinalRecipes] No scored oils available');
      setErrors(prev => ({ ...prev, recipes: 'No scored oils available for recipe generation. Please ensure all properties are enriched.' }));
      return;
    }

    if (!safetyLibrary || !safetyLibraryFormatted) {
      console.error('❌ [Properties-FinalRecipes] Missing safety library data');
      setErrors(prev => ({ ...prev, recipes: 'Safety library data not available. Please try regenerating the recipes.' }));
      return;
    }

    console.log('✅ [Properties-FinalRecipes] Starting final recipe generation with pre-calculated data:', {
      scoredOilsCount: scoredOils.length,
      topOilScore: scoredOils[0]?.final_relevance_score,
      hasSafetyLibrary: !!safetyLibrary,
      hasSafetyLibraryFormatted: !!safetyLibraryFormatted
    });
    setIsGeneratingRecipes(true);
    setShowRecipeStreamingModal(true);

    try {
      // Use pre-calculated scoring data (eliminates service layer duplication)
      const results = await startFinalRecipeStreaming(
        scoredOils,
        safetyLibrary,
        safetyLibraryFormatted,
        healthConcern,
        demographics,
        selectedCauses,
        selectedSymptoms
      );

      console.log('🎯 [Properties-FinalRecipes] Final recipe streaming completed:', results);
      
      // Store results in the final recipes store (following pattern from use-final-recipes)
      results.forEach((recipeData, timeSlot) => {
        if (recipeData && recipeData.recipe) {
          console.log(`💾 [Properties-FinalRecipes] Storing ${timeSlot} recipe:`, recipeData.recipe.recipe_name_localized);
          updateFinalRecipes(timeSlot, recipeData.recipe);
        }
      });

      // Close modal and navigate (following symptoms -> properties pattern)
      setShowRecipeStreamingModal(false);
      setIsGeneratingRecipes(false);
      
      // Navigate to final recipes screen (following completion pattern)
      console.log('✅ [Properties-FinalRecipes] Navigation to final recipes...');
      await completeStepAndGoNext();

    } catch (error) {
      console.error('❌ [Properties-FinalRecipes] Error generating final recipes:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to generate final recipes';
      setShowRecipeStreamingModal(false);
      setIsGeneratingRecipes(false);
      setErrors(prev => ({ ...prev, recipes: errorMessage }));
    }
  }, [scoredOils, safetyLibrary, safetyLibraryFormatted, healthConcern, demographics, selectedCauses, selectedSymptoms, startFinalRecipeStreaming, updateFinalRecipes, completeStepAndGoNext]);


  // Return all state, handlers, and computed values
  return {
    theme,
    insets,
    therapeuticProperties,
    selectedPropertyIds,
    isSubmitting,
    errors,
    showStreamingModal,
    propertyProgressCount,
    selectionCount,
    canSubmit,
    propertiesToShow,
    parallelStreamingState,
    handlePropertyToggle,
    handleSubmit,
    getCauseNamesByIds,
    getSymptomNamesByIds,
    // Auto-enrichment state tracking
    propertyEnrichmentStates,
    // Additional refs and functions that might be needed
    processedResultsRef,
    processedErrorsRef,
    // REMOVED: processedEnrichmentResultsRef, processedEnrichmentErrorsRef - No longer needed for immediate enrichment
    completeStepAndGoNext,
    setCurrentStep,
    resetParallelStreams,
    setSelectedPropertyIds,
    setIsSubmitting,
    setErrors,
    setShowStreamingModal,
    
    // Recipe generation functionality (NEW)
    showRecipeStreamingModal,
    isGeneratingRecipes,
    handleGenerateFinalRecipes,
    
    // Auto-enrichment state tracking for internal use only
    // Note: propertyEnrichmentStates is already returned above for component debugging
  };
};
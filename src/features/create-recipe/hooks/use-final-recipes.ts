 import { useEffect, useState, useCallback } from 'react';
import { useCombinedRecipeStore } from '@/features/create-recipe/store/combined-store';
import { useCreateRecipeStreaming } from './use-create-recipe-streaming';
import { useParallelStreamingEngine } from './use-parallel-streaming-engine';
import { calculatePostEnrichmentScores } from '@/features/create-recipe/utils/post-enrichment-scoring';
import type { FinalRecipeProtocol, RecipeTimeSlot } from '@/features/create-recipe/types';
import { RecipeStep } from '@/features/create-recipe/types';

/**
 * Custom hook for final recipes business logic
 * FOLLOWS DRY PRINCIPLES: Reuses existing streaming infrastructure
 * 
 * ENHANCED: Adds post-enrichment scoring and 3 parallel API calls
 * REUSES: useCreateRecipeStreaming for consistent streaming patterns
 */
export function useFinalRecipes() {
  const {
    healthConcern,
    demographics,
    selectedCauses,
    selectedSymptoms,
    therapeuticProperties,
    finalRecipes,
    setStreamingFinalRecipes,
    setFinalRecipesGenerating,
    setFinalRecipesGlobalError,
    updateFinalRecipes,
    setCurrentStep
  } = useCombinedRecipeStore();

  // DRY: Reuse existing streaming infrastructure instead of duplicating
  const { streamingState: createRecipeStreamingState, resetState } = useCreateRecipeStreaming();
  const { streamingState, startStreams } = useParallelStreamingEngine<FinalRecipeProtocol>();

  // Local state for streaming modal (following existing patterns)
  const [showStreamingModal, setShowStreamingModal] = useState(false);
  const [streamingItems, setStreamingItems] = useState<any[]>([]);
  const [streamingCompleted, setStreamingCompleted] = useState(false);


  // DRY: Follow existing pattern from usePropertiesSelection for setting current step
  useEffect(() => {
    setCurrentStep(RecipeStep.FINAL_RECIPES);
  }, [setCurrentStep]);

  // Enhanced final recipes generation following existing streaming patterns
  const generateFinalRecipes = useCallback(async () => {
    if (!healthConcern || !demographics || selectedCauses.length === 0 || 
        selectedSymptoms.length === 0 || therapeuticProperties.length === 0) {
      console.error('❌ Missing required data for final recipes API call');
      setFinalRecipesGlobalError('Missing required data. Please complete all previous steps.');
      return;
    }

    // Check if therapeutic properties have been enriched
    const hasEnrichedProperties = therapeuticProperties.some(prop => 
      prop.isEnriched && prop.suggested_oils && prop.suggested_oils?.length > 0
    );

    if (!hasEnrichedProperties) {
      console.warn('⚠️ No enriched therapeutic properties available');
      setFinalRecipesGlobalError('Please complete the properties enrichment step first.');
      return;
    }

    console.log('🚀 Starting enhanced final recipes generation with 3 parallel calls');
    
    setShowStreamingModal(true);
    setStreamingItems([]);
    setStreamingCompleted(false);
    
    // Update store state
    setStreamingFinalRecipes(true);
    setFinalRecipesGenerating(true);
    setFinalRecipesGlobalError(null);

    try {
      // NEW: Calculate post-enrichment scores (AFTER user's enrichment system)
      console.log('🔢 Calculating post-enrichment scores...');
      const scoringResult = calculatePostEnrichmentScores(therapeuticProperties);
      
      console.log('✅ Enhanced data prepared:', {
        scoredOilsCount: scoringResult.suggested_oils.length,
        topOilScore: scoringResult.suggested_oils[0]?.final_relevance_score || 0,
      });

      // NEW: Create enhanced final recipes streaming requests with scored oils
      const timeSlots: RecipeTimeSlot[] = ['morning', 'mid-day', 'night'];
      
      // Create base API request data following existing patterns
      const baseApiData = {
        health_concern: healthConcern.healthConcern.trim(),
        gender: demographics.gender,
        age_category: demographics.ageCategory,
        age_specific: demographics.specificAge.toString(),
        user_language: 'EN_US',
        selected_causes: selectedCauses,
        selected_symptoms: selectedSymptoms,
      };

      const requests = timeSlots.map(timeSlot => ({
        id: timeSlot,
        requestData: {
          feature: 'create-recipe' as const,
          step: 'final-recipes' as const,
          data: {
            ...baseApiData,
            // ENHANCED: Send scored oils instead of raw therapeutic_properties
            time_of_day: timeSlot, // CRITICAL: Time slot specific optimization
            suggested_oils: scoringResult.suggested_oils,
            safety_library: scoringResult.safety_library,
            safety_library_formatted: scoringResult.safety_library_formatted
          },
        },
        label: `${timeSlot} recipe`,
        // Note: responseParser removed - let streaming service handle data transformation
        // This ensures proper conversion from API response to FinalRecipeProtocol interface
      }));

      console.log('📡 Starting parallel streaming for', requests.length, 'time slots');

      // Use low-level streaming engine to send enhanced data
      const results = await startStreams(requests);
      
      // Process results for each time slot
      results.forEach((recipe, timeSlot) => {
        if (recipe) {
          console.log(`💾 Storing ${timeSlot} recipe:`, recipe.recipe_theme_localized);
          if (__DEV__) {
            console.log(`🔍 Recipe data being stored for ${timeSlot}:`, {
              hasSelectedOils: !!recipe.selected_oils,
              selectedOilsCount: recipe.selected_oils?.length || 0,
              recipeKeys: Object.keys(recipe)
            });
          }
          updateFinalRecipes(timeSlot as RecipeTimeSlot, recipe);
        } else {
          console.warn(`⚠️ No recipe received for ${timeSlot}`);
        }
      });

      console.log('✅ Enhanced final recipes generation completed');
      setFinalRecipesGenerating(false);
      setShowStreamingModal(false);

    } catch (error) {
      console.error('❌ Enhanced final recipes generation failed:', error);
      setShowStreamingModal(false);
      setFinalRecipesGenerating(false);
      setFinalRecipesGlobalError(error instanceof Error ? error.message : 'Failed to generate enhanced recipes. Please try again.');
    }
  }, [
    healthConcern, 
    demographics, 
    selectedCauses, 
    selectedSymptoms, 
    therapeuticProperties,
    setStreamingFinalRecipes,
    setFinalRecipesGenerating, 
    setFinalRecipesGlobalError, 
    updateFinalRecipes,
    startStreams
  ]);



  return {
    // Data (following existing patterns)
    finalRecipes,
    
    // Actions
    generateFinalRecipes,
    
    // Modal state (consistent with existing streaming patterns)
    showStreamingModal,
    setShowStreamingModal,
    streamingItems,
    streamingCompleted,
    
    // Streaming state (DRY - expose existing streaming state)
    streamingState,
    
    // Computed values
    hasAnyRecipe: finalRecipes.morning.recipe || finalRecipes.midDay.recipe || finalRecipes.night.recipe,
    isLoading: finalRecipes.isGenerating || showStreamingModal,
    hasError: !!finalRecipes.globalError,
  };
}
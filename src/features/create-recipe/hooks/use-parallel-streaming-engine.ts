import { useState, useCallback } from 'react';

export interface ParallelStreamRequest {
  id: string; // Unique identifier for tracking results
  requestData: {
    feature: string;
    step: string;
    data: any;
  }; // Request data for streaming API
  label?: string; // Optional label for debugging
  responseParser?: (finalData: any) => any; // Parse final streaming result
}

export interface ParallelStreamingState<T> {
  isStreaming: boolean;
  completedCount: number;
  totalCount: number;
  results: Map<string, T>;
  errors: Map<string, string>;
}

export interface UseParallelStreamingEngineReturn<T> {
  streamingState: ParallelStreamingState<T>;
  startStreams: (requests: ParallelStreamRequest[]) => Promise<Map<string, T>>;
  resetState: () => void;
}

/**
 * React Native Parallel Streaming Engine
 * Adapted from Next.js version for React Native environment
 * Handles multiple concurrent streaming API requests
 */
export function useParallelStreamingEngine<T = any>(): UseParallelStreamingEngineReturn<T> {
  const [streamingState, setStreamingState] = useState<ParallelStreamingState<T>>({
    isStreaming: false,
    completedCount: 0,
    totalCount: 0,
    results: new Map(),
    errors: new Map(),
  });

  const resetState = useCallback(() => {
    setStreamingState({
      isStreaming: false,
      completedCount: 0,
      totalCount: 0,
      results: new Map(),
      errors: new Map(),
    });
  }, []);

  const startStreams = useCallback(async (requests: ParallelStreamRequest[]): Promise<Map<string, T>> => {
    const finalResults = new Map<string, T>();
    
    setStreamingState({
      isStreaming: true,
      completedCount: 0,
      totalCount: requests.length,
      results: new Map(),
      errors: new Map(),
    });

    console.log(`🚀 [ParallelEngine] Starting ${requests.length} parallel streaming requests`);

    // Create staggered streaming promises with 3-second delays to avoid rate limiting
    const streamingPromises = requests.map((request, index) =>
      (async () => {
        // Add delay before starting each stream (except the first one)
        if (index > 0) {
          const delayMs = index * 3000; // 3 seconds per stream
          console.log(`⏳ [ParallelEngine] Delaying stream ${index + 1}/${requests.length} by ${delayMs}ms to avoid rate limiting`);
          await new Promise(resolve => setTimeout(resolve, delayMs));
        }
        const { id, requestData, label, responseParser } = request;
        let streamResult: T | null = null;

        try {
          console.log(`📡 [ParallelEngine] Starting stream for ID: ${id}${label ? ` (${label})` : ''}`);
          console.log(`📤 [ParallelEngine] Request data:`, {
            feature: requestData.feature,
            step: requestData.step,
            dataKeys: Object.keys(requestData.data || {})
          });
          
          // Import streaming API client
          const { streamRecipeStep } = await import('@/shared/services/api/rotinanatural-client');
          const { readSseStream } = await import('@/shared/utils/sse-reader');
          
          // Make streaming request
          const response = await streamRecipeStep(requestData);
          
          if (!response.body) {
            throw new Error(`No response body for request: ${id}`);
          }

          let hasProcessedResult = false;

          // Process streaming response
          for await (const dataString of readSseStream(response.body)) {
            try {
              const parsedData = JSON.parse(dataString);

              // Handle structured completion (final complete response)
              if (parsedData.type === 'structured_complete' && !hasProcessedResult) {
                console.log(`🏁 [ParallelEngine] Processing structured_complete for ${id}`);
                console.log(`📋 [ParallelEngine] Complete data keys:`, Object.keys(parsedData.data || {}));
                
                // Log the actual structure to debug
                if (parsedData.data) {
                  console.log(`📋 [ParallelEngine] Data structure preview:`, {
                    hasTherapeuticPropertyContext: !!parsedData.data.therapeutic_property_context,
                    hasSuggestedOils: !!parsedData.data.suggested_oils,
                    dataKeys: Object.keys(parsedData.data),
                    firstLevelData: Object.keys(parsedData.data).reduce((acc, key) => {
                      acc[key] = typeof parsedData.data[key];
                      return acc;
                    }, {} as Record<string, string>)
                  });
                }
                
                // Follow Next.js pattern: pass parsedData.data directly as finalData
                // Next.js does: onUpdate({ finalData: streamEvent.data as T });
                const finalData = parsedData.data;
                
                if (responseParser) {
                  streamResult = responseParser({ finalData });
                } else {
                  streamResult = finalData as T;
                }
                
                console.log(`✅ [ParallelEngine] Completed stream for ${id}:`, streamResult ? 'SUCCESS' : 'NO_RESULT');
                if (streamResult) {
                  console.log(`✅ [ParallelEngine] Stream result preview:`, {
                    resultKeys: Object.keys(streamResult as any),
                    hasOils: !!(streamResult as any).suggested_oils,
                    oilsCount: (streamResult as any).suggested_oils?.length || 0
                  });
                } else {
                  console.log(`❌ [ParallelEngine] No result - responseParser returned null`);
                  console.log(`❌ [ParallelEngine] finalData structure:`, JSON.stringify(finalData, null, 2).substring(0, 500));
                }
                hasProcessedResult = true;
                break;
              }

            } catch (parseError) {
              console.error(`❌ [ParallelEngine] Parse error for ${id}:`, parseError);
            }
          }

          // Update results if we got data
          if (streamResult && !finalResults.has(id)) {
            finalResults.set(id, streamResult);
            
            setStreamingState(prev => {
              const newResults = new Map(prev.results);
              newResults.set(id, streamResult as T);
              return {
                ...prev,
                results: newResults,
                completedCount: prev.completedCount + 1,
              };
            });
            
            console.log(`📋 [ParallelEngine] Added result for ${id}. Total completed: ${finalResults.size}`);
          }

        } catch (error) {
          console.error(`❌ [ParallelEngine] Streaming error for ID ${id}:`, error);
          
          // Update error state
          setStreamingState(prev => {
            const newErrors = new Map(prev.errors);
            newErrors.set(id, error instanceof Error ? error.message : String(error));
            return {
              ...prev,
              errors: newErrors,
              completedCount: prev.completedCount + 1,
            };
          });
        }
      })()
    );

    // Wait for all streaming requests to complete
    await Promise.allSettled(streamingPromises);

    // Update final streaming state
    setStreamingState(prev => ({
      ...prev,
      isStreaming: false
    }));

    console.log(`🎯 [ParallelEngine] All streams completed. Results: ${finalResults.size}, Errors: ${streamingState.errors.size}`);
    
    return finalResults;
  }, [streamingState.errors.size]);

  return {
    streamingState,
    startStreams,
    resetState
  };
}
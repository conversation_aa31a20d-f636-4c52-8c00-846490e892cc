/**
 * Streaming Service Layer
 * 
 * Handles streaming-related business logic for recipe creation.
 * This service is stateless and framework-agnostic.
 */

import type {
  TherapeuticProperty,
  HealthConcernData,
  DemographicsData,
  PotentialCause,
  PotentialSymptom
} from '../types/recipe.types';
import type { ScoredOil, SafetyLibrary } from '../utils/post-enrichment-scoring';

export interface ParallelStreamRequest {
  id: string; // Unique identifier for tracking results
  requestData: {
    feature: string;
    step: string;
    data: any;
  }; // Request data for streaming API
  label?: string; // Optional label for debugging
  responseParser?: (finalData: any) => any; // Parse final streaming result
}

/**
 * Create oil suggestion streaming requests
 * Business logic moved from use-create-recipe-streaming.ts
 */
export const createOilSuggestionStreamingRequests = (
  properties: TherapeuticProperty[],
  healthConcern: HealthConcernData,
  demographics: DemographicsData,
  selectedCauses: PotentialCause[],
  selectedSymptoms: PotentialSymptom[],
  apiLanguage: string
): ParallelStreamRequest[] => {
  console.log('🚀 [RN-Streaming] Starting parallel oil suggestion streaming for', properties.length, 'properties');
  console.log('🌐 [RN-Streaming] Using user language:', { apiLanguage });

  // Create base API request data (following Next.js createStreamRequest pattern)
  const baseApiData = {
    health_concern: healthConcern.healthConcern.trim(),
    gender: demographics.gender,
    age_category: demographics.ageCategory,
    age_specific: demographics.specificAge.toString(),
    user_language: apiLanguage,
    selected_causes: selectedCauses,
    selected_symptoms: selectedSymptoms,
  };

  // Create parallel requests for each property (Next.js pattern)
  const requests: ParallelStreamRequest[] = properties.map(property => ({
    id: property.property_id,
    requestData: {
      feature: 'create-recipe',
      step: 'suggested-oils',
      data: {
        ...baseApiData,
        therapeutic_property: property
      }
    },
    label: property.property_name_localized,
    responseParser: (updates) => {
      // Follow Next.js pattern exactly - finalData now contains the direct API response data
      console.log('🔍 [RN-Streaming] ResponseParser received:', {
        hasFinalData: !!updates.finalData,
        finalDataKeys: updates.finalData ? Object.keys(updates.finalData) : [],
        hasData: !!updates.finalData?.data,
        dataKeys: updates.finalData?.data ? Object.keys(updates.finalData.data) : []
      });

      // Check if we have the data in the nested structure
      const actualData = updates.finalData?.data;
      if (actualData?.suggested_oils) {
        console.log('✅ [RN-Streaming] Found suggested_oils:', {
          oilsCount: actualData.suggested_oils.length,
          hasContext: !!actualData.therapeutic_property_context
        });

        return {
          therapeutic_property_context: actualData.therapeutic_property_context,
          suggested_oils: actualData.suggested_oils
        };
      }

      console.log('❌ [RN-Streaming] No suggested_oils found');
      return null;
    }
  }));

  console.log('📤 [RN-Streaming] Created', requests.length, 'parallel streaming requests');
  return requests;
};

/**
 * Create final recipe streaming requests using pre-calculated scoring data
 * Business logic moved from use-create-recipe-streaming.ts
 * UPDATED: Now accepts pre-calculated scoring data to eliminate duplication
 */
export const createFinalRecipeStreamingRequests = (
  scoredOils: ScoredOil[],
  safetyLibrary: SafetyLibrary,
  safetyLibraryFormatted: string,
  healthConcern: HealthConcernData,
  demographics: DemographicsData,
  selectedCauses: PotentialCause[],
  selectedSymptoms: PotentialSymptom[],
  apiLanguage: string
): ParallelStreamRequest[] => {
  console.log('🚀 [RN-FinalRecipes] Starting parallel final recipe streaming for 3 time slots');

  // Verify we have scored oils
  if (!scoredOils || scoredOils.length === 0) {
    throw new Error('No scored oils available for final recipes generation');
  }

  console.log('🌐 [RN-FinalRecipes] Using user language:', { apiLanguage });
  console.log('✅ [RN-FinalRecipes] Using pre-calculated scoring data:', {
    scoredOilsCount: scoredOils.length,
    topOilScore: scoredOils[0]?.final_relevance_score || 'No oils available',
    hasSafetyLibrary: !!safetyLibrary,
    hasSafetyLibraryFormatted: !!safetyLibraryFormatted
  });

  // Create base API request data (following exact same pattern)
  const baseApiData = {
    health_concern: healthConcern.healthConcern.trim(),
    gender: demographics.gender,
    age_category: demographics.ageCategory,
    age_specific: demographics.specificAge.toString(),
    user_language: apiLanguage,
    selected_causes: selectedCauses,
    selected_symptoms: selectedSymptoms,
  };

  // Create parallel requests for each time slot (following properties pattern)
  const timeSlots: ('morning' | 'mid-day' | 'night')[] = ['morning', 'mid-day', 'night'];
  const requests: ParallelStreamRequest[] = timeSlots.map(timeSlot => ({
    id: timeSlot,
    requestData: {
      feature: 'create-recipe',
      step: 'final-recipes',
      data: {
        ...baseApiData,
        // Use pre-calculated scoring data (eliminates duplication)
        time_of_day: timeSlot, // CRITICAL: Time slot specific optimization
        suggested_oils: scoredOils,
        safety_library: safetyLibrary,
        safety_library_formatted: safetyLibraryFormatted
      }
    },
    label: `${timeSlot} recipe`,
    responseParser: (updates) => {
      // RAW API RESPONSE DEBUG LOGGING
      console.log('🔍 [RN-FinalRecipes] RAW API RESPONSE for', timeSlot, ':', JSON.stringify(updates.finalData, null, 2));
      console.log('🔍 [RN-FinalRecipes] ResponseParser received for', timeSlot, ':', {
        hasFinalData: !!updates.finalData,
        finalDataKeys: updates.finalData ? Object.keys(updates.finalData) : [],
        hasData: !!updates.finalData?.data,
      });

      // Extract and validate recipe protocol from API response
      if (updates.finalData?.data?.recipe_protocol) {
        const apiRecipe = updates.finalData.data.recipe_protocol;
        console.log('✅ [RN-FinalRecipes] Recipe received for', timeSlot, ':', apiRecipe.recipe_theme_localized);
        
        // Validate required API response structure
        if (!apiRecipe.ingredients?.essential_oils) {
          throw new Error(`API response missing ingredients.essential_oils for ${timeSlot}`);
        }
        if (!apiRecipe.formulation) {
          throw new Error(`API response missing formulation data for ${timeSlot}`);
        }
        if (!apiRecipe.preparation_steps_localized) {
          throw new Error(`API response missing preparation_steps_localized for ${timeSlot}`);
        }
        if (!apiRecipe.usage_instructions_localized) {
          throw new Error(`API response missing usage_instructions_localized for ${timeSlot}`);
        }
        
        // Transform API response to match our FinalRecipeProtocol interface
        const transformedRecipe = {
          recipe_id: apiRecipe.recipe_id,
          time_slot: apiRecipe.time_slot,
          recipe_name_localized: apiRecipe.recipe_theme_localized,
          description_localized: apiRecipe.description_localized,
          
          // Theme and holistic information
          recipe_theme_localized: apiRecipe.recipe_theme_localized,
          holistic_benefit_localized: apiRecipe.holistic_benefit_localized,
          ritual_suggestion_localized: apiRecipe.ritual_suggestion_localized,
          synergy_rationale_localized: apiRecipe.synergy_rationale_localized,
          disclaimer_localized: 'For external use only. Consult healthcare provider if pregnant, nursing, or have medical conditions.',
          
          // Transform ingredients - strict validation
          selected_oils: apiRecipe.ingredients.essential_oils.map(oil => {
            if (!oil.oil_id || !oil.name_localized || !oil.scientific_name || oil.drops === undefined) {
              throw new Error(`Invalid essential oil data in API response for ${timeSlot}`);
            }
            return {
              oil_id: oil.oil_id,
              name_localized: oil.name_localized,
              name_botanical: oil.scientific_name,
              drops_count: oil.drops,
              rationale_localized: apiRecipe.oil_rationales?.find(r => r.oil_id === oil.oil_id)?.rationale_localized || 'No rationale provided by API'
            };
          }),
          
          // Transform carrier oil - include both recommended and alternative
          carrier_oil: {
            name_localized: apiRecipe.ingredients.carrier_oil.recommended.name_localized,
            amount_ml: apiRecipe.formulation.bottle_size_ml,
            properties_localized: apiRecipe.ingredients.carrier_oil.recommended.properties_localized,
            alternative: {
              name_localized: apiRecipe.ingredients.carrier_oil.alternative.name_localized,
              properties_localized: apiRecipe.ingredients.carrier_oil.alternative.properties_localized
            }
          },
          
          // Transform formulation data - strict validation
          total_drops: apiRecipe.formulation.total_drops,
          total_volume_ml: apiRecipe.formulation.bottle_size_ml,
          application_method_localized: apiRecipe.application_type_localized,
          frequency_localized: apiRecipe.usage_instructions_localized[0]?.frequency || 'API did not specify frequency',
          duration_localized: 'Use within 6 months',
          
          // Container recommendation
          container_recommendation: {
            type: apiRecipe.formulation.bottle_type_localized,
            size_ml: apiRecipe.formulation.bottle_size_ml,
            material: 'Glass',
            color: 'Amber'
          },
          
          // Transform safety warnings - graceful handling for data quality
          safety_warnings: (updates.finalData.safety_warnings || []).map(warning => {
            // Handle empty or missing type gracefully - log but don't break
            if (!warning.type || !warning.type.trim()) {
              console.warn(`🟡 [RN-FinalRecipes] Empty safety warning type for ${timeSlot}, using 'general' default:`, {
                title: warning.title_localized || 'No title',
                content: warning.warning_text_localized?.substring(0, 100) + '...'
              });
            }
            
            // Require warning text - if missing, this is a serious API issue
            if (!warning.warning_text_localized) {
              throw new Error(`Invalid safety warning structure in API response for ${timeSlot}`);
            }
            
            return {
              warning_type: warning.type?.trim() || 'general',
              severity: 'medium' as const,
              message_localized: warning.warning_text_localized,
              guidance_localized: warning.title_localized
            };
          }),
          
          // Direct mappings - strict validation
          preparation_steps_localized: apiRecipe.preparation_steps_localized,
          usage_instructions_localized: apiRecipe.usage_instructions_localized
        };
        
        return {
          timeSlot,
          recipe: transformedRecipe
        };
      }

      console.log('❌ [RN-FinalRecipes] No recipe_protocol found for', timeSlot);
      throw new Error(`API response missing recipe_protocol for ${timeSlot}`);
    }
  }));

  console.log('📤 [RN-FinalRecipes] Created', requests.length, 'parallel streaming requests for time slots');
  return requests;
};
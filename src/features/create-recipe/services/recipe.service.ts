/**
 * Recipe Service Layer
 * 
 * Handles all business logic and API interactions for recipe creation.
 * This service is stateless and framework-agnostic.
 */

import { batchEnrichOils } from '@/shared/services/api/rotinanatural-client';
import type {
  PropertyOilSuggestions,
  HealthConcernData,
  DemographicsData,
  PotentialCause,
  PotentialSymptom
} from '../types/recipe.types';

/**
 * Fetch oil enrichment data for properties with suggested oils
 * Uses batch enrichment API for quick database lookups
 * Moved from use-create-recipe-streaming.ts
 */
export const fetchOilEnrichmentData = async (
  propertySuggestions: PropertyOilSuggestions[],
  healthConcern: HealthConcernData,
  demographics: DemographicsData,
  selectedCauses: PotentialCause[],
  selectedSymptoms: PotentialSymptom[],
  apiLanguage: string
) => {
  // Filter properties that have oils to enrich
  const propertiesToEnrich = propertySuggestions.filter(
    suggestion => suggestion.suggested_oils && suggestion.suggested_oils.length > 0
  );

  console.log('🔍 [RN-Fetch] Fetching oil enrichment data for', propertiesToEnrich.length, 'properties with oils');

  if (propertiesToEnrich.length === 0) {
    console.warn('🟡 [RN-Fetch] No properties with oils to fetch enrichment data for');
    return new Map();
  }

  console.log('🌐 [RN-Fetch] Using user language for enrichment data:', { apiLanguage });

  // Create enrichment results map
  const enrichmentResults = new Map();

  try {
    // Standardized try/catch error handling for all API interactions
    // Create all API calls simultaneously for parallel processing
    const enrichmentPromises = propertiesToEnrich.map(async (suggestion) => {
      console.log(`🔄 [RN-Fetch] Starting parallel fetch for property: ${suggestion.property_name_localized}`);
      
      // Validate suggested oils structure - no fallbacks
      if (!suggestion.suggested_oils) {
        throw new Error(`Property ${suggestion.property_name_localized} missing suggested_oils`);
      }
      
      // Prepare oils for batch enrichment - strict validation
      const suggestedOils = suggestion.suggested_oils.map(oil => {
        if (!oil.oil_id) {
          throw new Error(`Oil missing oil_id in property ${suggestion.property_name_localized}`);
        }
        if (!oil.name_localized) {
          throw new Error(`Oil ${oil.oil_id} missing name_localized in property ${suggestion.property_name_localized}`);
        }
        return {
          name: oil.name_localized,
          botanical_name: oil.name_botanical || '',
          oil_id: oil.oil_id,
          ...oil
        };
      });

      if (suggestedOils.length === 0) {
        throw new Error(`No valid oils to fetch data for ${suggestion.property_name_localized}`);
      }

      const enrichmentResponse = await batchEnrichOils({
        suggestedOils
      });

      return {
        propertyId: suggestion.property_id,
        propertyName: suggestion.property_name_localized,
        result: {
          property_id: suggestion.property_id,
          enriched_oils: enrichmentResponse?.enriched_oils || []
        }
      };
    });

    // Execute ALL API calls in parallel with graceful failure handling
    console.log('🚀 [RN-Fetch] Executing', enrichmentPromises.length, 'parallel API calls...');
    const settledResults = await Promise.allSettled(enrichmentPromises);

    let successCount = 0;
    let errorCount = 0;

    // Process each result individually
    settledResults.forEach((settledResult, index) => {
      const suggestion = propertiesToEnrich[index];
      
      if (settledResult.status === 'fulfilled') {
        const { propertyId, result } = settledResult.value;
        enrichmentResults.set(propertyId, result);
        successCount++;
        console.log(`✅ [RN-Fetch] Property ${suggestion.property_name_localized} enriched successfully`);
      } else {
        const errorResult = {
          property_id: suggestion.property_id,
          error: settledResult.reason?.message || 'API enrichment call failed'
        };
        enrichmentResults.set(suggestion.property_id, errorResult);
        errorCount++;
        console.error(`❌ [RN-Fetch] Property ${suggestion.property_name_localized} enrichment failed:`, settledResult.reason);
      }
    });

    console.log('📦 [RN-Fetch] PARALLEL enrichment completed:', {
      totalProperties: propertiesToEnrich.length,
      successCount,
      errorCount,
      successRate: `${Math.round((successCount / propertiesToEnrich.length) * 100)}%`
    });

    return enrichmentResults;

  } catch (error) {
    console.error('❌ [RN-Fetch] Oil enrichment data fetch process failed:', error);
    throw error;
  }
};
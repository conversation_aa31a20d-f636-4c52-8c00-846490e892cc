import type { TherapeuticProperty, EnrichedEssentialOil } from '../types/recipe.types';

export const MAX_RELEVANCY_SCORE = 5;

export interface ScoredOil extends EnrichedEssentialOil {
  final_relevance_score: number;
  specialization_score: number;  // Specialization component score (0-1 range)
  property_contexts: OilPropertyContext[];
}

export interface OilPropertyContext {
  property_id: string;
  property_name_localized: string;
  match_rationale_localized: string | null;
  relevancy_to_property_score: number | null;
  recommendation_instance_score: number;
}

export interface SafetyLibrary {
  internal_use: Record<string, any>;
  dilution: Record<string, any>;
  phototoxicity: Record<string, any>;
  pregnancy_nursing: Record<string, any>;
  child_safety: Record<string, any>;
}

export interface FinalAPIPayload {
  suggested_oils: ScoredOil[];
  safety_library: SafetyLibrary;
  safety_library_formatted: string;
}

/**
 * Calculate property coverage score (0-1 scale)
 */
function calculatePropertyCoverageScore(property: TherapeuticProperty): number {
  return property.relevancy_score / MAX_RELEVANCY_SCORE;
}

/**
 * Calculate recommendation instance score for oil within property context
 */
/**
 * DEPRECATED: This function contained critical mathematical flaws (quadratic bias).
 * Kept for API compatibility but no longer used in hybrid scoring.
 * @deprecated Use calculateHybridScoresForAllOils instead
 */
function calculateRecommendationInstanceScore(
  oil: EnrichedEssentialOil, 
  property: TherapeuticProperty
): number {
  // Legacy calculation for backward compatibility only
  const propertyCoverageScore = calculatePropertyCoverageScore(property);
  return oil.relevancy_to_property_score * property.relevancy_score * propertyCoverageScore;
}

/**
 * Calculate hybrid scores for all oils across all properties using mathematically sound algorithm.
 * Fixes critical flaws: quadratic bias, unbounded scaling, and coverage gaming.
 * 
 * @param filteredProperties - Properties that have enriched oils
 * @param alpha - Specialization weight (default: 0.6 = 60%)
 * @param gamma - Coverage bonus weight (default: 0.1 = 10%)
 * @returns Map of oil_id to object containing finalScore, specialization, holistic, and coverage scores
 */
function calculateHybridScoresForAllOils(
  filteredProperties: TherapeuticProperty[],
  alpha: number = 0.6,  // Specialization weight
  gamma: number = 0.1   // Coverage bonus weight
): Map<string, { finalScore: number; specialization: number; holistic: number; coverage: number }> {
  
  console.log('🧮 Starting hybrid scoring calculation...', {
    propertiesCount: filteredProperties.length,
    alpha,
    gamma,
    holisticWeight: 1 - alpha - gamma
  });

  // 1. Collect all oil-property contexts across ALL properties
  const allOilContexts = new Map<string, {
    oilScore: number;
    propertyScore: number;
    propertyId: string;
    propertyName: string;
  }[]>();

  let maxPropertyScore = 0;

  filteredProperties.forEach(property => {
    maxPropertyScore = Math.max(maxPropertyScore, property.relevancy_score);
    
    (property.suggested_oils || []).forEach(oil => {
      if (!oil.oil_id || oil.enrichment_status !== 'enriched') return;

      if (!allOilContexts.has(oil.oil_id)) {
        allOilContexts.set(oil.oil_id, []);
      }

      allOilContexts.get(oil.oil_id)!.push({
        oilScore: oil.relevancy_to_property_score,
        propertyScore: property.relevancy_score,
        propertyId: property.property_id,
        propertyName: property.property_name_localized
      });
    });
  });

  // 2. Robust threshold identification for high-priority properties
  const highPriorityThreshold = maxPropertyScore === 5 
    ? 5                                    // 99% cases: use grade 5
    : Math.max(maxPropertyScore - 1, 3);   // 1% cases: top tier or min 3

  console.log('📊 Scoring thresholds:', {
    maxPropertyScore,
    highPriorityThreshold,
    totalUniqueOils: allOilContexts.size
  });

  // 3. Calculate hybrid scores for each oil
  const hybridScores = new Map<string, { finalScore: number; specialization: number; holistic: number; coverage: number }>();

  Array.from(allOilContexts.entries()).forEach(([oilId, contexts]) => {
    // SPECIALIZATION COMPONENT (60%): Average performance in top-priority properties
    const highPriorityContexts = contexts.filter(ctx => ctx.propertyScore >= highPriorityThreshold);
    const specializationScore = highPriorityContexts.length > 0
      ? (highPriorityContexts.reduce((sum, ctx) => sum + ctx.oilScore, 0) / highPriorityContexts.length) / 5
      : 0;

    // HOLISTIC COMPONENT (30%): Weighted average across ALL properties oil appears in
    let weightedSum = 0;
    let totalWeight = 0;
    
    contexts.forEach(ctx => {
      const weight = ctx.propertyScore / 5; // Normalize property score to 0-1 as weight
      weightedSum += (ctx.oilScore / 5) * weight; // Normalize oil score to 0-1
      totalWeight += weight;
    });
    
    const holisticScore = totalWeight > 0 ? weightedSum / totalWeight : 0;

    // COVERAGE COMPONENT (10%): Diminishing returns for treating more properties
    const totalProperties = filteredProperties.length;
    const coverageRatio = contexts.length / totalProperties;
    const coverageBonus = Math.sqrt(coverageRatio); // Square root for diminishing returns

    // FINAL HYBRID SCORE: Guaranteed 0-5 range
    const raw = alpha * specializationScore + (1 - alpha - gamma) * holisticScore + gamma * coverageBonus;
    const finalScore = Math.max(0, Math.min(1, raw)) * 5;

    hybridScores.set(oilId, {
      finalScore,
      specialization: specializationScore,
      holistic: holisticScore,
      coverage: coverageBonus
    });

    console.log(`🎯 Oil ${oilId.substring(0, 8)}... hybrid score:`, {
      specialization: (specializationScore * 100).toFixed(1) + '%',
      holistic: (holisticScore * 100).toFixed(1) + '%',
      coverage: (coverageBonus * 100).toFixed(1) + '%',
      finalScore: finalScore.toFixed(2),
      propertiesCount: contexts.length
    });
  });

  console.log('✅ Hybrid scoring completed', {
    oilsScored: hybridScores.size,
    avgScore: Array.from(hybridScores.values()).reduce((sum, scores) => sum + scores.finalScore, 0) / hybridScores.size
  });

  return hybridScores;
}

/**
 * MAIN FUNCTION: Calculate scores for already enriched properties
 * This runs AFTER the user's existing enrichment system
 */
export function calculatePostEnrichmentScores(
  enrichedProperties: TherapeuticProperty[]
): FinalAPIPayload {
  
  // 1. Filter to only enriched properties (user's system sets isEnriched: true)
  const filteredProperties = enrichedProperties.filter(prop => 
    prop.isEnriched && 
    prop.suggested_oils && 
    prop.suggested_oils.length > 0
  );

  console.log('🔢 Post-enrichment scoring:', {
    totalProperties: enrichedProperties.length,
    enrichedProperties: filteredProperties.length,
    totalOilsAvailable: filteredProperties.reduce((total, prop) => 
      total + (prop.suggested_oils?.length || 0), 0),
    totalEnrichedOils: filteredProperties.reduce((total, prop) => 
      total + (prop.suggested_oils?.filter(oil => oil.enrichment_status === 'enriched').length || 0), 0)
  });

  // 2. Calculate hybrid scores for all oils using new algorithm
  const hybridScores = calculateHybridScoresForAllOils(filteredProperties);

  // 3. Build oil deduplication map and collect safety data  
  const oilMap = new Map<string, EnrichedEssentialOil>();
  const oilPropertyContexts = new Map<string, OilPropertyContext[]>();
  const allSafetyData: any[] = [];

  // 4. Process each property to collect data and build contexts
  filteredProperties.forEach(property => {
    (property.suggested_oils || []).forEach(oil => {
      if (!oil.oil_id || oil.enrichment_status !== 'enriched') return;

      // Deduplicate oils (keep the enriched version)
      if (!oilMap.has(oil.oil_id)) {
        oilMap.set(oil.oil_id, oil);
        oilPropertyContexts.set(oil.oil_id, []);
        
        // Collect safety data for library building
        if (oil.safety) {
          allSafetyData.push(oil.safety);
        }
      }

      // Add property context (keep legacy instance score for backward compatibility)
      const legacyInstanceScore = calculateRecommendationInstanceScore(oil, property);
      
      const contexts = oilPropertyContexts.get(oil.oil_id) || [];
      contexts.push({
        property_id: property.property_id,
        property_name_localized: property.property_name_localized,
        match_rationale_localized: oil.match_rationale_localized,
        relevancy_to_property_score: oil.relevancy_to_property_score,
        recommendation_instance_score: Math.round(legacyInstanceScore * 100) / 100
      });
      oilPropertyContexts.set(oil.oil_id, contexts);
    });
  });

  // 5. Create scored oils array with hybrid scores
  const scoredOils: ScoredOil[] = Array.from(oilMap.values()).map(oil => {
    const scores = hybridScores.get(oil.oil_id) || { finalScore: 0, specialization: 0, holistic: 0, coverage: 0 };
    return {
      ...oil,
      // Use new hybrid score as final relevance score
      final_relevance_score: Math.round(scores.finalScore * 100) / 100,
      specialization_score: scores.specialization,  // Attach specialization score
      property_contexts: oilPropertyContexts.get(oil.oil_id) || []
    };
  });

  // Sort by hybrid final relevance score (highest first)
  scoredOils.sort((a, b) => b.final_relevance_score - a.final_relevance_score);

  // 6. Build safety library from collected data
  const safetyLibrary = buildSafetyLibrary(allSafetyData);
  const safetyLibraryFormatted = formatSafetyLibraryForTemplate(safetyLibrary);

  console.log('✅ Hybrid scoring completed:', {
    scoredOilsCount: scoredOils.length,
    topOilScore: scoredOils[0]?.final_relevance_score || 0,
    topOilId: scoredOils[0]?.oil_id?.substring(0, 8) + '...',
    safetyLibraryStats: {
      internal_use: Object.keys(safetyLibrary.internal_use).length,
      dilution: Object.keys(safetyLibrary.dilution).length,
      phototoxicity: Object.keys(safetyLibrary.phototoxicity).length,
      pregnancy_nursing: Object.keys(safetyLibrary.pregnancy_nursing).length,
      child_safety: Object.keys(safetyLibrary.child_safety).length
    }
  });

  return {
    suggested_oils: scoredOils,
    safety_library: safetyLibrary,
    safety_library_formatted: safetyLibraryFormatted
  };
}

/**
 * Build deduplicated safety library from all oils
 */
function buildSafetyLibrary(allSafetyData: any[]): SafetyLibrary {
  const library: SafetyLibrary = {
    internal_use: {},
    dilution: {},
    phototoxicity: {},
    pregnancy_nursing: {},
    child_safety: {}
  };

  const safetyMap = new Map();

  function getSafetyId(safetyObj: any, category: string, actualId?: string): string | null {
    if (!safetyObj) return null;
    const dedupKey = actualId || `${category}_${JSON.stringify(safetyObj)}`;
    if (!safetyMap.has(dedupKey)) {
      safetyMap.set(dedupKey, { ...safetyObj, safety_id: dedupKey, category });
    }
    return dedupKey;
  }

  // Process each oil's safety data
  allSafetyData.forEach(safety => {
    // Internal use
    if (safety.internal_use) {
      const id = getSafetyId(safety.internal_use, 'internal_use', safety.internal_use_id);
      if (id && !library.internal_use[id]) {
        library.internal_use[id] = {
          id,
          code: safety.internal_use?.code || null,
          name: safety.internal_use?.name || null,
          description: safety.internal_use?.description || null,
          guidance: safety.internal_use?.guidance || null,
        };
      }
    }

    // Dilution
    if (safety.dilution) {
      const id = getSafetyId(safety.dilution, 'dilution', safety.dilution_id);
      if (id && !library.dilution[id]) {
        library.dilution[id] = {
          id,
          name: safety.dilution?.name || null,
          description: safety.dilution?.description || null,
          percentage_max: safety.dilution?.percentage_max || null,
          percentage_min: safety.dilution?.percentage_min || null,
          ratio: safety.dilution?.ratio || null,
        };
      }
    }

    // Phototoxicity
    if (safety.phototoxicity) {
      const id = getSafetyId(safety.phototoxicity, 'phototoxicity', safety.phototoxicity_id);
      if (id && !library.phototoxicity[id]) {
        library.phototoxicity[id] = {
          id,
          status: safety.phototoxicity?.status || null,
          guidance: safety.phototoxicity?.guidance || null,
          description: safety.phototoxicity?.description || null,
        };
      }
    }

    // Pregnancy/nursing
    if (Array.isArray(safety.pregnancy_nursing)) {
      safety.pregnancy_nursing.forEach((item: any) => {
        const id = getSafetyId(item, 'pregnancy_nursing', item?.id);
        if (id && !library.pregnancy_nursing[id]) {
          library.pregnancy_nursing[id] = {
            id,
            name: item?.name || null,
            status_description: item?.status_description || null,
            code: item?.code || null,
            usage_guidance: item?.usage_guidance || null,
            description: item?.description || null,
          };
        }
      });
    }

    // Child safety
    if (Array.isArray(safety.child_safety)) {
      safety.child_safety.forEach((item: any) => {
        const id = getSafetyId(item, 'child_safety', item?.age_range_id || item?.id);
        if (id && !library.child_safety[id]) {
          library.child_safety[id] = {
            age_range_id: id,
            age_range: item?.age_range || null,
            safety_notes: item?.safety_notes || null,
          };
        }
      });
    }
  });

  return library;
}

/**
 * Format safety library for AI prompt
 */
function formatSafetyLibraryForTemplate(library: SafetyLibrary): string {
  const sections: string[] = [];

  // Internal use safety
  const internalUseEntries = Object.values(library.internal_use);
  if (internalUseEntries.length > 0) {
    sections.push('**Internal Use Safety:**');
    internalUseEntries.forEach(item => {
      sections.push(`- ${item.name}: ${item.description} (${item.guidance})`);
    });
  }

  // Dilution safety
  const dilutionEntries = Object.values(library.dilution);
  if (dilutionEntries.length > 0) {
    sections.push('**Dilution Guidelines:**');
    dilutionEntries.forEach(item => {
      sections.push(`- ${item.name}: ${item.description} (Max: ${item.percentage_max}%, Min: ${item.percentage_min}%)`);
    });
  }

  // Pregnancy/nursing safety
  const pregnancyEntries = Object.values(library.pregnancy_nursing);
  if (pregnancyEntries.length > 0) {
    sections.push('**Pregnancy & Nursing Safety:**');
    pregnancyEntries.forEach(item => {
      sections.push(`- ${item.name}: ${item.status_description} (${item.usage_guidance})`);
    });
  }

  // Child safety
  const childEntries = Object.values(library.child_safety);
  if (childEntries.length > 0) {
    sections.push('**Child Safety:**');
    childEntries.forEach(item => {
      sections.push(`- Age ${item.age_range}: ${item.safety_notes}`);
    });
  }

  return sections.join('\n');
}
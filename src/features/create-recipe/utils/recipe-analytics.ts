/**
 * Recipe Wizard Analytics Tracking Utilities
 * 
 * This module provides PostHog tracking functions for the recipe wizard.
 * It follows a defensive pattern where tracking errors won't break the app.
 */

import { RecipeStep } from '../types/recipe.types';

// Step name mapping for analytics
const STEP_NAMES: Record<RecipeStep, string> = {
  [RecipeStep.HEALTH_CONCERN]: 'health-concern',
  [RecipeStep.DEMOGRAPHICS]: 'demographics',
  [RecipeStep.CAUSES]: 'causes',
  [RecipeStep.SYMPTOMS]: 'symptoms',
  [RecipeStep.PROPERTIES]: 'properties',
  [RecipeStep.FINAL_RECIPES]: 'final-recipes',
};

/**
 * Get PostHog instance safely
 * Returns null if PostHog is not available to prevent errors
 */
function getPostHog() {
  try {
    // Get the global PostHog instance from the provider context
    // This requires PostHog to be properly initialized via PostHogProvider
    const { default: posthog } = require('posthog-react-native');
    
    // Return the global instance that's set up by the provider
    return posthog.default || posthog;
  } catch (error) {
    if (__DEV__) {
      console.warn('PostHog not available for analytics tracking:', error);
    }
    return null;
  }
}

/**
 * Track recipe wizard session start
 */
export function trackWizardStarted(sessionId: string) {
  const posthog = getPostHog();
  if (!posthog) return;

  try {
    posthog.capture('recipe_wizard_started', {
      sessionId,
      timestamp: new Date().toISOString(),
      platform: 'mobile',
    });

    if (__DEV__) {
      console.log('📊 Recipe Analytics: Wizard started', { sessionId });
    }
  } catch (error) {
    if (__DEV__) {
      console.warn('Failed to track wizard started:', error);
    }
  }
}

/**
 * Track step navigation
 */
export function trackStepViewed(
  step: RecipeStep,
  sessionId: string,
  previousStep?: RecipeStep
) {
  const posthog = getPostHog();
  if (!posthog) return;

  try {
    posthog.capture('recipe_step_viewed', {
      sessionId,
      currentStep: step,
      stepName: STEP_NAMES[step],
      previousStep: previousStep ? STEP_NAMES[previousStep] : null,
      timestamp: new Date().toISOString(),
    });

    if (__DEV__) {
      console.log('📊 Recipe Analytics: Step viewed', {
        step: STEP_NAMES[step],
        sessionId,
      });
    }
  } catch (error) {
    if (__DEV__) {
      console.warn('Failed to track step viewed:', error);
    }
  }
}

/**
 * Track step completion
 */
export function trackStepCompleted(
  step: RecipeStep,
  sessionId: string,
  completionTime?: number
) {
  const posthog = getPostHog();
  if (!posthog) return;

  try {
    posthog.capture('recipe_step_completed', {
      sessionId,
      completedStep: step,
      stepName: STEP_NAMES[step],
      completionTime,
      timestamp: new Date().toISOString(),
    });

    if (__DEV__) {
      console.log('📊 Recipe Analytics: Step completed', {
        step: STEP_NAMES[step],
        sessionId,
        completionTime,
      });
    }
  } catch (error) {
    if (__DEV__) {
      console.warn('Failed to track step completed:', error);
    }
  }
}

/**
 * Track wizard completion
 */
export function trackWizardCompleted(
  sessionId: string,
  totalStepsCompleted: number,
  totalDuration?: number
) {
  const posthog = getPostHog();
  if (!posthog) return;

  try {
    posthog.capture('recipe_wizard_completed', {
      sessionId,
      totalStepsCompleted,
      totalDuration,
      timestamp: new Date().toISOString(),
    });

    if (__DEV__) {
      console.log('📊 Recipe Analytics: Wizard completed', {
        sessionId,
        totalStepsCompleted,
        totalDuration,
      });
    }
  } catch (error) {
    if (__DEV__) {
      console.warn('Failed to track wizard completed:', error);
    }
  }
}

/**
 * Track wizard abandonment (when user leaves without completing)
 */
export function trackWizardAbandoned(
  sessionId: string,
  lastStep: RecipeStep,
  stepsCompleted: number
) {
  const posthog = getPostHog();
  if (!posthog) return;

  try {
    posthog.capture('recipe_wizard_abandoned', {
      sessionId,
      lastStep: STEP_NAMES[lastStep],
      stepsCompleted,
      timestamp: new Date().toISOString(),
    });

    if (__DEV__) {
      console.log('📊 Recipe Analytics: Wizard abandoned', {
        sessionId,
        lastStep: STEP_NAMES[lastStep],
        stepsCompleted,
      });
    }
  } catch (error) {
    if (__DEV__) {
      console.warn('Failed to track wizard abandoned:', error);
    }
  }
}

/**
 * Track wizard reset
 */
export function trackWizardReset(sessionId: string, fromStep: RecipeStep) {
  const posthog = getPostHog();
  if (!posthog) return;

  try {
    posthog.capture('recipe_wizard_reset', {
      sessionId,
      fromStep: STEP_NAMES[fromStep],
      timestamp: new Date().toISOString(),
    });

    if (__DEV__) {
      console.log('📊 Recipe Analytics: Wizard reset', {
        sessionId,
        fromStep: STEP_NAMES[fromStep],
      });
    }
  } catch (error) {
    if (__DEV__) {
      console.warn('Failed to track wizard reset:', error);
    }
  }
}

/**
 * Track step validation failure
 */
export function trackStepValidationFailed(
  step: RecipeStep,
  sessionId: string,
  validationErrors: string[]
) {
  const posthog = getPostHog();
  if (!posthog) return;

  try {
    posthog.capture('recipe_step_validation_failed', {
      sessionId,
      step: STEP_NAMES[step],
      validationErrors,
      timestamp: new Date().toISOString(),
    });

    if (__DEV__) {
      console.log('📊 Recipe Analytics: Step validation failed', {
        step: STEP_NAMES[step],
        sessionId,
        validationErrors,
      });
    }
  } catch (error) {
    if (__DEV__) {
      console.warn('Failed to track step validation failed:', error);
    }
  }
}

/**
 * Track streaming status for AI steps
 */
export function trackStreamingStatus(
  step: RecipeStep,
  sessionId: string,
  status: 'started' | 'completed' | 'error',
  error?: string
) {
  const posthog = getPostHog();
  if (!posthog) return;

  try {
    posthog.capture('recipe_streaming_status', {
      sessionId,
      step: STEP_NAMES[step],
      status,
      error,
      timestamp: new Date().toISOString(),
    });

    if (__DEV__) {
      console.log('📊 Recipe Analytics: Streaming status', {
        step: STEP_NAMES[step],
        sessionId,
        status,
        error,
      });
    }
  } catch (error) {
    if (__DEV__) {
      console.warn('Failed to track streaming status:', error);
    }
  }
}
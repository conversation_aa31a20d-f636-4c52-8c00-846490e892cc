/**
 * Navigation utilities for the recipe wizard
 */
import { useCallback } from 'react';
import { useRouter } from 'expo-router';
import { useCombinedRecipeStore } from '../store/combined-store';

/**
 * Hook to handle recipe wizard reset functionality
 * Returns a function to reset the wizard and navigate to the start
 */
export const useRecipeReset = () => {
  const router = useRouter();
  const resetWizard = useCombinedRecipeStore(state => state.resetWizard);

  const handleResetWizard = useCallback(() => {
    resetWizard();
    router.push('/(tabs)/create-recipe/' as any);
  }, [resetWizard, router]);

  return handleResetWizard;
};

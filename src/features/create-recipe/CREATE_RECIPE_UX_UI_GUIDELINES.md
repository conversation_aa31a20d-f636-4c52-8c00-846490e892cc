# Create Recipe UX/UI Guidelines

## Overview

This document provides specific UX/UI guidelines for the AromaCHAT create-recipe feature wizard. These patterns were developed through iterative testing and implementation of SLC (Simple, Lovable, Complete) design principles.

**For comprehensive details**, see:
- **Full UX Guide**: `src/context/aromachat_ux_guide.md`
- **Design Principles**: `src/context/design-principles.md` 
- **Official Examples**: `template-projects/react-native-paper-demo`

## Core Philosophy: Content-First Recipe Wizard

The create-recipe wizard follows a **clean, chat-style interface** where content is the interface. We avoid heavy containers and focus on typography, spacing, and subtle surfaces to create structure.

### The SLC Transformation Pattern

Every create-recipe screen should follow this transformation approach:

1. **Simple**: Remove visual clutter, heavy Cards, unnecessary containers
2. **Lovable**: Add appropriate haptics, smooth interactions, delightful micro-animations
3. **Complete**: Ensure accessibility, proper translations, error states

---

## Component Selection Rules

### ✅ DO: Appropriate Component Usage

| Component | Use Case | Example |
|-----------|----------|---------|
| **`SegmentedButtons`** | Binary/small set selections (2-4 options) | Gender selection, view toggles |
| **`Chip`** | Suggestions, tags, removable selections | Health concern suggestions, selected symptoms |
| **`Surface` with `mode="flat"`** | Background differentiation without shadows | Age display container, info cards |
| **`TouchableRipple`** | Making any content interactive | Suggestion chips, custom interactive elements |
| **`List.Section + List.Item`** | Structured content organization | Settings, form sections |

### ❌ DON'T: Component Anti-Patterns

| Avoid | Why | Use Instead |
|-------|-----|-------------|
| **`Card`** for layout/grouping | Creates heavy, box-heavy interfaces | `Surface mode="flat"` or plain `View` |
| **`Chip`** for binary selections | Chips are for suggestions/tags only | `SegmentedButtons` or `RadioButton` |
| **High elevation surfaces** | Creates visual noise in clean interfaces | `Surface mode="flat"` |
| **`StyleSheet.create()`** | Prevents theme system usage | Inline styles with theme tokens |

---

## Layout & Spacing Patterns

### Screen Structure Template

**Note**: ScreenWrapper is implemented at the **route level** (in app/ directory), not in component files.

```jsx
// In component files - content structure only:
<TouchableWithoutFeedback onPress={Keyboard.dismiss}>
  <View style={{
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.screenPadding,
  }}>
    {/* Content-first, centered layout */}
  </View>
</TouchableWithoutFeedback>
```

### Spacing System
- **Screen padding**: `theme.spacing.screenPadding`
- **Section spacing**: `theme.spacing.xl`
- **Element spacing**: `theme.spacing.lg`  
- **Micro spacing**: `theme.spacing.md`, `theme.spacing.sm`

### Typography Hierarchy
- **Main titles**: `variant="headlineLarge"` with `theme.colors.primary`
- **Focus elements**: `variant="displaySmall"` (30% larger for main items)
- **Labels**: `variant="titleMedium"`
- **Body text**: `variant="bodyLarge"` or `variant="bodyMedium"`

---

## Interactive Element Patterns

### Touch Targets & Accessibility
```jsx
// All interactive elements MUST meet 44px minimum
<IconButton 
  style={{ 
    minHeight: TOUCH_TARGETS.MINIMUM_SIZE, 
    minWidth: TOUCH_TARGETS.MINIMUM_SIZE 
  }}
/>
```

### Haptic Feedback Rules
```jsx
import { haptics } from '@/shared/utils/haptics';

// Light: Basic interactions (button presses, selections)
onPress={() => {
  haptics.light();
  setValue(newValue);
}}

// Medium: Completion actions (form submissions, step transitions)
onComplete={() => {
  haptics.medium();
  completeStep();
}}

// Success: Successful operations
onSuccess={() => {
  haptics.success();
  showSuccessMessage();
}}
```

### Gender Selection Pattern
```jsx
<SegmentedButtons
  value={gender}
  onValueChange={(value) => {
    haptics.light();
    setGender(value);
  }}
  buttons={[
    { 
      value: 'male', 
      icon: 'human-male',
      label: t('demographics.gender.male'),
      style: { flex: 1 }
    },
    { 
      value: 'female', 
      icon: 'human-female',
      label: t('demographics.gender.female'),
      style: { flex: 1 }
    }
  ]}
  style={{ justifyContent: 'center' }}
/>
```

---

## Advanced Input Patterns

### Dual-Control Age Selector
For numeric inputs requiring both precision and quick changes:

```jsx
{/* Micro Control - Precise adjustments */}
<IconButton 
  icon="minus" 
  mode="contained-tonal"
  style={{ 
    minHeight: TOUCH_TARGETS.MINIMUM_SIZE, 
    minWidth: TOUCH_TARGETS.MINIMUM_SIZE 
  }}
  onPress={() => {
    haptics.light();
    setValue(Math.max(minValue, value - 1));
  }}
/>

{/* Main Display - Fixed width prevents layout shifts */}
<Surface 
  mode="flat"
  style={{
    paddingVertical: theme.spacing.lg,
    paddingHorizontal: theme.spacing.xl,
    width: theme.spacing.xl * 5, // Fixed width for stability
    alignItems: 'center',
    borderRadius: theme.spacing.md,
    backgroundColor: theme.colors.surfaceVariant,
  }}
>
  <Text 
    variant="displaySmall" 
    style={{ 
      color: theme.colors.onSurfaceVariant,
      fontWeight: 'bold',
      textAlign: 'center',
      fontVariant: ['tabular-nums'] // Prevents width changes
    }}
  >
    {value}
  </Text>
</Surface>

{/* Macro Control - Range adjustments */}
<Slider
  value={value}
  onValueChange={(newValue) => {
    const roundedValue = Math.round(newValue);
    if (roundedValue !== value) {
      haptics.light();
      setValue(roundedValue);
    }
  }}
  onSlidingComplete={() => haptics.medium()}
  minimumTrackTintColor={theme.colors.primary}
  maximumTrackTintColor={theme.colors.outline}
/>
```

### Layout Stability Rules
- **Use fixed widths** for containers that change content
- **Use `fontVariant: ['tabular-nums']`** for numeric displays
- **Calculate widths** using theme tokens: `theme.spacing.xl * 5`
- **Center content** within fixed containers: `textAlign: 'center'`

---

## Theme System Compliance

### Mandatory Theme Usage
```jsx
// ✅ CORRECT - Always use theme tokens
const theme = useTheme();

style={{
  padding: theme.spacing.lg,
  backgroundColor: theme.colors.surfaceVariant,
  borderRadius: theme.spacing.md,
  minHeight: TOUCH_TARGETS.MINIMUM_SIZE
}}

// ❌ FORBIDDEN - Never hardcode values
style={{
  padding: 16,           // Use theme.spacing.lg
  backgroundColor: '#F5F5F5',  // Use theme.colors.*
  borderRadius: 8,       // Use theme.spacing.sm
  minHeight: 44          // Use TOUCH_TARGETS.MINIMUM_SIZE
}}
```

### Import Pattern
```jsx
import { useTheme } from '@/shared/hooks/use-theme';
import { TOUCH_TARGETS, AGE_VALIDATION } from '@/features/create-recipe/constants';
```

---

## Internationalization (i18n) Pattern

### Translation Structure
```jsx
import { useTranslation } from 'react-i18next';

const { t } = useTranslation('create-recipe');

// Usage
{t('demographics.title')}
{t('healthConcern.suggestions.0.text')}
{t('forms.gender')}
```

### File Structure
- **English**: `src/shared/locales/en/create-recipe.json`
- **Portuguese**: `src/shared/locales/pt/create-recipe.json`

### Systematic Translation Linkage
1. **Component uses** `useTranslation('create-recipe')`
2. **Translation keys match** JSON structure exactly
3. **Helper functions** receive `t` function parameter
4. **Both languages** have identical key structures

---

## Error Prevention & Linting

### Custom Lint Rules
Our ESLint configuration prevents common mistakes:

- **`no-magic-numbers`**: Prevents hardcoded numeric values
- **`no-restricted-syntax`**: Prevents hardcoded colors and `StyleSheet.create()`
- **Typography compliance**: Enforces theme system usage

### Common Violations
```jsx
// ❌ Will trigger lint errors:
style={{ padding: 16, color: '#FF0000', width: 100 }}

// ✅ Lint-compliant:
style={{ 
  padding: theme.spacing.lg, 
  color: theme.colors.error,
  width: theme.spacing.xl * 5
}}
```

---

## Animation & Performance Guidelines

### Smooth Interactions
- **60fps target**: All interactions should maintain smooth framerates
- **Haptic timing**: Haptics should trigger immediately on touch
- **Layout stability**: Prevent layout shifts during content changes
- **Progressive disclosure**: Use animations to guide user attention

### Performance Patterns
```jsx
// Debounced value changes
const [value, setValue] = useState(initialValue);

onValueChange={(newValue) => {
  const roundedValue = Math.round(newValue);
  if (roundedValue !== value) { // Only update if value actually changed
    haptics.light();
    setValue(roundedValue);
  }
}}
```

---

## Quick Reference Checklist

### Before Creating a New Screen:
- [ ] ScreenWrapper is implemented at route level (app/ directory)
- [ ] Content-first, centered layout structure
- [ ] All interactive elements meet 44px minimum
- [ ] No hardcoded values (lint-compliant)
- [ ] Proper i18n namespace and translations
- [ ] Appropriate haptic feedback
- [ ] Theme system compliance
- [ ] Layout stability considerations

### Component Selection:
- [ ] `SegmentedButtons` for binary/small selections
- [ ] `Chip` only for suggestions/removable tags  
- [ ] `Surface mode="flat"` instead of `Card`
- [ ] `TouchableRipple` for custom interactions
- [ ] Proper typography variants for hierarchy

### Testing Requirements:
- [ ] Test with single digits (1-9)
- [ ] Test with double digits (10-99)  
- [ ] Test with triple digits (100-120)
- [ ] Verify no layout shifts during value changes
- [ ] Test haptic feedback on physical device
- [ ] Verify translations in both English and Portuguese

---

## Advanced Patterns

### Suggestion Chips Pattern
```jsx
{suggestions.map((suggestion, index) => (
  <TouchableRipple
    key={index}
    onPress={() => {
      haptics.light();
      onSuggestionSelect(suggestion.expanded);
    }}
    style={{ borderRadius: theme.spacing.sm }}
  >
    <Chip mode="outlined" compact>
      {suggestion.text}
    </Chip>
  </TouchableRipple>
))}
```

### Auto-determined Category Display
```jsx
<AgeCategoryChip 
  ageCategory={determineAgeCategory(age) || ''}
  visible={!!age && !!determineAgeCategory(age)}
  style={{ alignSelf: 'center' }}
/>
```

---

## Implementation Resources

### Essential Files
- **Main Constants**: `src/features/create-recipe/constants/recipe.constants.ts`
- **Theme Hook**: `src/shared/hooks/use-theme.ts`
- **Haptics Utility**: `src/shared/utils/haptics.ts`
- **Screen Wrapper**: `src/shared/components/layout/screen-wrapper.tsx`

### Reference Implementations
- **Health Concern Screen**: Clean suggestion chips pattern
- **Demographics Screen**: Dual-control age selector, gender segmented buttons
- **Official Examples**: `template-projects/react-native-paper-demo/src/Examples/`

### Key Dependencies
```json
{
  "react-native-paper": "^5.14.5",
  "@react-native-community/slider": "latest",
  "react-i18next": "^25.3.2",
  "react-hook-form": "latest"
}
```

---

This document provides battle-tested patterns from our successful screen transformations. Following these guidelines ensures consistency, accessibility, and delightful user experiences throughout the create-recipe wizard.
/**
 * Presence Utilities
 * 
 * Shared utilities for processing presence state across realtime hooks.
 * Eliminates code duplication while maintaining exact same behavior.
 */

import i18n from '@/shared/locales/i18n';

export interface PresenceUser {
  id: string;
  name: string;
  avatar?: string;
  online_at: string;
}

/**
 * Process Supabase presence state into PresenceUser array
 * 
 * Extracts the duplicated presence processing logic that was in:
 * - use-realtime-chat.ts:148-164  
 * - use-presence-tracker.ts:68-84
 * 
 * @param presenceState - Raw presence state from channel.presenceState()
 * @returns Array of processed PresenceUser objects
 */
export function processPresenceState(presenceState: any): PresenceUser[] {
  const users: PresenceUser[] = [];
  const seenUserIds = new Set<string>();

  Object.keys(presenceState).forEach(userId => {
    const presences = presenceState[userId];
    if (presences && presences.length > 0) {
      const presence = presences[0] as any;
      if (presence && typeof presence === 'object') {
        const actualUserId = presence.user_id || userId;
        
        // KISS: Deduplicate by user_id to handle multiple connections from same user
        if (!seenUserIds.has(actualUserId)) {
          seenUserIds.add(actualUserId);
          users.push({
            id: actualUserId,
            name: presence.user_name || i18n.t('realtime.users.anonymous'),
            avatar: presence.user_avatar,
            online_at: presence.online_at || new Date().toISOString(),
          });
        }
      }
    }
  });

  return users;
}
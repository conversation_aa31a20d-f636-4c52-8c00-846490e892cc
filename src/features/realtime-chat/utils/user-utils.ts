/**
 * User Utilities
 * 
 * Shared utilities for creating user tracking payloads across realtime hooks.
 * Eliminates code duplication while maintaining exact same behavior.
 */

import i18n from '@/shared/locales/i18n';

export interface UserTrackingPayload {
  user_id: string;
  user_name: string;
  user_avatar?: string;
  online_at: string;
}

/**
 * Create user tracking payload for Supabase presence
 * 
 * Extracts the duplicated user tracking logic that was previously 
 * scattered across multiple realtime hooks.
 * 
 * @param user - Clerk user object
 * @returns Standardized user tracking payload
 */
export function createUserTrackingPayload(user: any): UserTrackingPayload {
  return {
    user_id: user.id,
    user_name: user.fullName || user.primaryEmailAddress?.emailAddress || i18n.t('realtime.users.anonymous'),
    user_avatar: user.imageUrl || undefined,
    online_at: new Date().toISOString(),
  };
}
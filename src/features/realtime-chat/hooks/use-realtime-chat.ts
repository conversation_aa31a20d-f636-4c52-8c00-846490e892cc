/**
 * Realtime Chat Hook - Simplified API Layer
 * 
 * This hook provides a focused API for realtime chat functionality.
 * It now uses the global RealtimeProvider for all underlying functionality.
 */

import { useRealtime } from '@/shared/providers/realtime-provider';
import type { PresenceUser } from '../utils';

export interface RealtimeChatMessage {
  id: string;
  content: string;
  user_id: string;
  user_name: string;
  user_avatar?: string;
  created_at: string;
}

// PresenceUser interface moved to utils for shared usage

export interface RealtimeChatConfig {
  roomName: string;
  includeChatFeatures?: boolean;
  includePresenceTracking?: boolean;
  tableName?: string; // For message persistence
  shouldLoadHistory?: boolean;
  initialMessageLimit?: number;
  // Realtime connection options (passed through)
  keepAliveInterval?: number;
  maxConnectionRetries?: number;
  baseRetryDelay?: number;
}

export interface RealtimeChatResult {
  // Chat functionality
  messages: RealtimeChatMessage[];
  sendMessage: (content: string) => Promise<void>;
  clearMessages: () => void;
  
  // Presence functionality
  presentUsers: PresenceUser[];
  onlineCount: number;
  
  // Realtime connection functionality (passed through)
  isConnected: boolean;
  isLoading: boolean;
  isRetryingConnection: boolean;
  connectionRetryCount: number;
  connectionStatus: 'good' | 'poor' | 'disconnected';
  error: string | null;
  retry: () => Promise<void>;
  
  onlineUsers: PresenceUser[];
  isReconnecting: boolean;
  reconnectAttempts: number;
  connectionQuality: 'good' | 'poor' | 'disconnected';
}

/**
 * Simplified realtime chat hook that uses the global RealtimeProvider
 * 
 * This hook maintains the same API as before but now simply delegates
 * to the RealtimeProvider for all functionality.
 */
export function useRealtimeChat(
  config?: RealtimeChatConfig
): RealtimeChatResult {
  // Get all data from the global provider
  const realtimeData = useRealtime();

  // Return the same interface, ignoring the config since the provider
  // handles all configuration globally
  return {
    // Chat functionality
    messages: realtimeData.messages,
    sendMessage: realtimeData.sendMessage,
    clearMessages: realtimeData.clearMessages,
    
    // Presence functionality
    presentUsers: realtimeData.presentUsers,
    onlineCount: realtimeData.onlineCount,
    
    // Connection functionality
    isConnected: realtimeData.isConnected,
    isLoading: realtimeData.isLoading,
    isRetryingConnection: realtimeData.isRetryingConnection,
    connectionRetryCount: realtimeData.connectionRetryCount,
    connectionStatus: realtimeData.connectionStatus,
    error: realtimeData.error,
    retry: realtimeData.retry,
    
    // Compatibility aliases
    onlineUsers: realtimeData.onlineUsers,
    isReconnecting: realtimeData.isReconnecting,
    reconnectAttempts: realtimeData.reconnectAttempts,
    connectionQuality: realtimeData.connectionQuality,
  };
}
// src/features/realtime-chat/hooks/use-presence-tracker.ts

import { useRealtime } from '@/shared/providers/realtime-provider';
import type { PresenceUser } from '../utils';

export interface PresenceTrackerConfig {
  roomName: string;
  enabled?: boolean;
}

export interface PresenceTrackerResult {
  presentUsers: PresenceUser[];
  onlineCount: number;
  isConnected: boolean;
  isLoading: boolean;
  error: string | null;
  retry: () => Promise<void>;
}

/**
 * Presence tracker hook - now simplified to consume from RealtimeProvider
 * 
 * This hook is kept for backward compatibility and to provide a focused API
 * for components that only need presence tracking functionality.
 */
export function usePresenceTracker(
  config?: PresenceTrackerConfig,
): PresenceTrackerResult {
  const { presentUsers, onlineCount, isConnected, isLoading, error, retry } = useRealtime();

  // Return filtered data based on config if provided
  // For now, we ignore the config since we have a global provider
  // but keep the interface for potential future use
  return {
    presentUsers,
    onlineCount,
    isConnected,
    isLoading,
    error,
    retry,
  };
}
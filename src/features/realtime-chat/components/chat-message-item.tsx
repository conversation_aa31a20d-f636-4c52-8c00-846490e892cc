/**
 * Chat Message Component
 *
 * Simple, Lovable, Complete. Supports message grouping and optimistic UI.
 * This is a pure presentational component whose appearance is controlled by props.
 *
 * - UX: Implements message grouping via `isFirstInGroup` and `isLastInGroup` props
 * to conditionally show avatars, names, and apply "tailed" bubble styles.
 * - UX: Visually indicates optimistic messages (e.g., while sending) via opacity.
 * - PERF: Uses StyleSheet.create for optimized styling.
 */
import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Avatar } from 'react-native-paper';
import { useTheme } from '@/shared/hooks/use-theme';
import type { ThemeDetails } from '@/shared/hooks/use-theme'; // Assuming ThemeDetails is exported from your hook
import type { RealtimeChatMessage } from '../hooks';

export interface ChatMessageProps {
  message: RealtimeChatMessage & { isOptimistic?: boolean }; // Add isOptimistic for UX feedback
  currentUserId?: string;
  isFirstInGroup: boolean;
  isLastInGroup: boolean;
}

const ChatMessageComponent = React.memo(
  ({
    message,
    currentUserId,
    isFirstInGroup,
    isLastInGroup,
  }: ChatMessageProps) => {
    const { theme, spacing, borderRadius } = useTheme();
    const isOwnMessage = message.user_id === currentUserId;
    const styles = createStyles(theme, spacing, borderRadius, isOwnMessage);

    const bubbleColors = isOwnMessage
      ? {
          backgroundColor: theme.colors.primaryContainer,
          contentColor: theme.colors.onPrimaryContainer,
        }
      : {
          backgroundColor: theme.colors.surfaceVariant,
          contentColor: theme.colors.onSurface,
        };

    // Dynamically build the style array for the message bubble
    const bubbleStyle = [
      styles.bubbleBase,
      { backgroundColor: bubbleColors.backgroundColor },
      isLastInGroup ? styles.bubbleTailed : styles.bubbleGrouped,
      { opacity: message.isOptimistic ? 0.7 : 1 }, // Lovable: Visual feedback
    ];

    const avatarSource = message.user_avatar
      ? { uri: message.user_avatar }
      : {
          uri: `https://ui-avatars.com/api/?name=${encodeURIComponent(
            message.user_name,
          )}&background=random`,
        };

    return (
      <View
        style={[
          styles.wrapper,
          // Add more top margin to separate groups of messages
          isFirstInGroup && { marginTop: spacing.md },
        ]}
      >
        {/* Avatar Container: Always present for alignment, but avatar is conditional */}
        {!isOwnMessage && (
          <View style={styles.avatarContainer}>
            {/* Show avatar only for the last message in a group */}
            {isLastInGroup && (
              <Avatar.Image size={32} source={avatarSource} />
            )}
          </View>
        )}

        <View style={bubbleStyle}>
          {/* Sender's Name: Show only for the first message in a group */}
          {!isOwnMessage && isFirstInGroup && (
            <Text
              variant="labelSmall"
              style={[styles.userName, { color: theme.colors.primary }]}
            >
              {message.user_name}
            </Text>
          )}

          <Text
            variant="bodyMedium"
            style={[styles.content, { color: bubbleColors.contentColor }]}
          >
            {message.content}
          </Text>

          <View style={styles.metadataContainer}>
            <Text
              style={[
                styles.metadataText,
                { color: bubbleColors.contentColor },
              ]}
            >
              {new Date(message.created_at).toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit',
              })}
            </Text>
          </View>
        </View>
      </View>
    );
  },
);

// Using a function with StyleSheet.create for dynamic, performant styles
const createStyles = (
  theme: ThemeDetails['theme'],
  spacing: ThemeDetails['spacing'],
  borderRadius: ThemeDetails['borderRadius'],
  isOwnMessage: boolean,
) =>
  StyleSheet.create({
    wrapper: {
      flexDirection: 'row',
      justifyContent: isOwnMessage ? 'flex-end' : 'flex-start',
      alignItems: 'flex-end',
      marginHorizontal: spacing.md,
      marginBottom: spacing.xs,
    },
    avatarContainer: {
      width: 32, // Occupy space for alignment even when avatar is invisible
      marginRight: spacing.sm,
    },
    bubbleBase: {
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      maxWidth: '85%',
      minWidth: 80,
    },
    bubbleTailed: {
      borderTopLeftRadius: borderRadius.lg,
      borderTopRightRadius: borderRadius.lg,
      borderBottomLeftRadius: isOwnMessage
        ? borderRadius.lg
        : borderRadius.sm,
      borderBottomRightRadius: isOwnMessage
        ? borderRadius.sm
        : borderRadius.lg,
    },
    bubbleGrouped: {
      borderRadius: borderRadius.lg,
      // Give inner-group messages slightly different corners for cohesion
      borderBottomLeftRadius: isOwnMessage
        ? borderRadius.lg
        : borderRadius.sm,
      borderTopLeftRadius: isOwnMessage ? borderRadius.lg : borderRadius.sm,
      borderBottomRightRadius: isOwnMessage
        ? borderRadius.sm
        : borderRadius.lg,
      borderTopRightRadius: isOwnMessage ? borderRadius.sm : borderRadius.lg,
    },
    userName: {
      fontWeight: 'bold',
      marginBottom: spacing.xs,
    },
    content: {
      lineHeight: 20,
    },
    metadataContainer: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      marginTop: spacing.xs,
    },
    metadataText: {
      opacity: 0.7,
      fontSize: 11,
    },
  });

ChatMessageComponent.displayName = 'ChatMessageComponent';
export { ChatMessageComponent };
/**
 * Presence Indicator Component
 * 
 * Reusable online presence indicator component following AromaCHAT design patterns.
 * Extracts online users display logic from existing realtime code.
 */

import React from 'react';
import { View, ScrollView } from 'react-native';
import { Text, Avatar, Chip, Surface } from 'react-native-paper';
import { useTheme } from '@/shared/hooks/use-theme';
import { useTranslation } from 'react-i18next';
// Realtime user type definition
export interface OnlineUser {
  id: string;
  name: string;
  avatar?: string;
}

export interface PresenceIndicatorProps {
  onlineUsers: OnlineUser[];
  maxVisibleUsers?: number;
  showAvatars?: boolean;
  showUserNames?: boolean;
  compact?: boolean;
  variant?: 'chip' | 'list' | 'counter';
}

/**
 * Presence indicator component with multiple display variants
 * Follows existing online users display patterns but as reusable component
 */
export function PresenceIndicatorComponent({ 
  onlineUsers,
  maxVisibleUsers = 5,
  showAvatars = true,
  showUserNames = true,
  compact = false,
  variant = 'list'
}: PresenceIndicatorProps) {
  const { theme } = useTheme();
  const { t } = useTranslation();

  const onlineCount = onlineUsers.length;
  const visibleUsers = onlineUsers.slice(0, maxVisibleUsers);
  const hiddenCount = Math.max(0, onlineCount - maxVisibleUsers);

  if (onlineCount === 0) {
    return null;
  }

  // Counter variant - just show count
  if (variant === 'counter') {
    return (
      <View style={{ 
        flexDirection: 'row', 
        alignItems: 'center',
        paddingHorizontal: compact ? 8 : 16,
        paddingVertical: compact ? 4 : 8,
      }}>
        <Avatar.Icon 
          size={compact ? 20 : 24} 
          icon="account-multiple" 
          style={{ backgroundColor: theme.colors.primary, marginRight: 8 }} 
        />
        <Text variant={compact ? "bodySmall" : "bodyMedium"} style={{ color: theme.colors.onSurfaceVariant }}>
          {t('realtime.presence.online')}: {onlineCount} {onlineCount === 1 ? t('realtime.presence.user') : t('realtime.presence.users')}
        </Text>
      </View>
    );
  }

  // Chip variant - show users as chips
  if (variant === 'chip') {
    return (
      <View style={{ 
        paddingHorizontal: 16, 
        paddingVertical: 8,
        backgroundColor: compact ? 'transparent' : theme.colors.surfaceVariant
      }}>
        <Text variant="labelMedium" style={{ 
          color: theme.colors.onSurfaceVariant, 
          marginBottom: 8 
        }}>
          {t('realtime.presence.online')} ({onlineCount})
        </Text>
        
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={{ flexDirection: 'row', gap: 8 }}>
            {visibleUsers.map((user) => (
              <Chip
                key={user.id}
                avatar={showAvatars && user.avatar ? 
                  <Avatar.Image size={24} source={{ uri: user.avatar }} /> : 
                  <Avatar.Icon size={24} icon="account" />
                }
                compact={compact}
                style={{ backgroundColor: theme.colors.surface }}
              >
                {user.name}
              </Chip>
            ))}
            
            {hiddenCount > 0 && (
              <Chip
                compact={compact}
                style={{ backgroundColor: theme.colors.secondaryContainer }}
              >
                +{hiddenCount}
              </Chip>
            )}
          </View>
        </ScrollView>
      </View>
    );
  }

  // List variant (default) - show as vertical list
  return (
    <Surface style={{ 
      margin: compact ? 8 : 16,
      padding: compact ? 8 : 12,
      borderRadius: 12,
      elevation: 1,
    }}>
      <View style={{ 
        flexDirection: 'row', 
        alignItems: 'center',
        marginBottom: compact ? 4 : 8,
      }}>
        <Avatar.Icon 
          size={compact ? 20 : 24} 
          icon="account-multiple" 
          style={{ backgroundColor: theme.colors.primary, marginRight: 8 }} 
        />
        <Text variant={compact ? "labelMedium" : "titleSmall"} style={{ 
          color: theme.colors.onSurface,
          flex: 1,
        }}>
          {t('realtime.presence.online')}
        </Text>
        <Text variant={compact ? "bodySmall" : "bodyMedium"} style={{ 
          color: theme.colors.onSurfaceVariant,
        }}>
          {onlineCount}
        </Text>
      </View>

      {showUserNames && (
        <View>
          {visibleUsers.map((user, index) => (
            <View key={user.id} style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingVertical: compact ? 2 : 4,
              marginLeft: compact ? 16 : 20,
            }}>
              {showAvatars && (
                <Avatar.Image
                  size={compact ? 20 : 24}
                  source={{ uri: user.avatar }}
                  style={{ marginRight: 8 }}
                />
              )}
              <Text variant={compact ? "bodySmall" : "bodyMedium"} style={{ 
                color: theme.colors.onSurface,
                flex: 1,
              }}>
                {user.name}
              </Text>
              <View style={{
                width: 8,
                height: 8,
                borderRadius: 4,
                backgroundColor: theme.colors.primary,
              }} />
            </View>
          ))}

          {hiddenCount > 0 && (
            <Text variant="bodySmall" style={{ 
              color: theme.colors.onSurfaceVariant,
              marginLeft: compact ? 16 : 20,
              marginTop: 4,
              fontStyle: 'italic',
            }}>
              {t('realtime.presence.andMore', { count: hiddenCount })}
            </Text>
          )}
        </View>
      )}
    </Surface>
  );
}
import React from 'react';
import { View, ViewStyle } from 'react-native';
import { Text, Icon } from 'react-native-paper';
import { AvatarWithBadge } from '@/shared/components/ui/avatar-with-badge';
import { useTheme } from '@/shared/hooks/use-theme';
import { useTranslation } from 'react-i18next';
import type { PresenceUser } from '../hooks';

// Constants to avoid magic numbers
const AVATAR_SIZE = 28;
const AVATAR_OVERLAP_OFFSET = -8;
const AVATAR_BORDER_RADIUS = 14;

/**
 * Aroma Friends Status Bar Component
 * 
 * A fixed status area that shows "Make Aroma Friends" with online users information.
 * Positioned above the messages list in chat screens.
 * 
 * Features:
 * - Chat icon + "Make Aroma Friends" title
 * - "X users online" subtitle
 * - Row of user avatar placeholders with overflow indicator
 * - Outlined border with theme integration
 * - YAGNI approach: UI first, real data later
 * 
 * @example
 * <AromaFriendsStatusBar 
 *   onlineCount={3}
 *   maxVisibleAvatars={4}
 * />
 */

export interface AromaFriendsStatusBarProps {
  /** Other users currently online (excluding current user) */
  onlineUsers: PresenceUser[];
  
  /** Total count of all users online (including current user) */
  totalCount: number;
  
  /** Maximum number of avatars to show before +X indicator */
  maxVisibleAvatars?: number;
  
  /** Custom style overrides */
  style?: ViewStyle;
  
  /** Test ID for testing */
  testID?: string;
}

/**
 * AromaFriendsStatusBar - Fixed status area for chat screens
 * Follows DRY/KISS principles with theme integration
 */
export const AromaFriendsStatusBar: React.FC<AromaFriendsStatusBarProps> = ({
  onlineUsers,
  totalCount,
  maxVisibleAvatars = 4,
  style,
  testID,
}) => {
  const { theme, spacing, borderRadius } = useTheme();
  const { t } = useTranslation();

  // Generate avatars from real user data
  const generateUserAvatars = () => {
    const avatars = [];
    const visibleCount = Math.min(onlineUsers.length, maxVisibleAvatars);
    const visibleUsers = onlineUsers.slice(0, visibleCount);
    
    // Helper function to extract initials from user name
    const getUserInitials = (name: string): string => {
      return name
        .split(' ')
        .map(part => part.charAt(0).toUpperCase())
        .join('')
        .substring(0, 2); // Max 2 characters
    };
    
    // Create visible avatars with real user data
    visibleUsers.forEach((user, index) => {
      if (user.avatar) {
        // User has avatar image
        avatars.push(
          <AvatarWithBadge
            key={`avatar-${user.id}`}
            variant="image"
            source={{ uri: user.avatar }}
            size={AVATAR_SIZE}
            style={{
              marginLeft: index > 0 ? AVATAR_OVERLAP_OFFSET : 0,
            }}
          />
        );
      } else {
        // Use initials fallback
        const initials = getUserInitials(user.name);
        avatars.push(
          <View
            key={`avatar-${user.id}`}
            style={{
              width: AVATAR_SIZE,
              height: AVATAR_SIZE,
              borderRadius: AVATAR_BORDER_RADIUS,
              backgroundColor: theme.colors.primary,
              justifyContent: 'center',
              alignItems: 'center',
              marginLeft: index > 0 ? AVATAR_OVERLAP_OFFSET : 0,
              borderWidth: 2,
              borderColor: theme.colors.surface,
            }}
          >
            <Text
              variant="labelSmall"
              style={{
                color: theme.colors.onPrimary,
                fontSize: 11,
                fontWeight: '600',
              }}
            >
              {initials}
            </Text>
          </View>
        );
      }
    });
    
    // Add overflow indicator if needed
    const overflowCount = onlineUsers.length - maxVisibleAvatars;
    if (overflowCount > 0) {
      avatars.push(
        <View
          key="overflow"
          style={{
            width: AVATAR_SIZE,
            height: AVATAR_SIZE,
            borderRadius: AVATAR_BORDER_RADIUS,
            backgroundColor: theme.colors.surfaceVariant,
            justifyContent: 'center',
            alignItems: 'center',
            marginLeft: AVATAR_OVERLAP_OFFSET,
            borderWidth: 2,
            borderColor: theme.colors.surface,
          }}
        >
          <Text
            variant="labelSmall"
            style={{
              color: theme.colors.onSurfaceVariant,
              fontSize: 10,
              fontWeight: '600',
            }}
          >
            +{overflowCount}
          </Text>
        </View>
      );
    }
    
    return avatars;
  };

  return (
    <View
      style={[
        {
          marginHorizontal: spacing.md,
          marginTop: spacing.md,
          marginBottom: spacing.xs,
          paddingHorizontal: spacing.md,
          paddingVertical: spacing.md,
          borderRadius: borderRadius.card,
          borderWidth: 0.5,
          borderColor: theme.colors.outline,
          backgroundColor: theme.colors.surface,
        },
        style,
      ]}
      testID={testID}
    >
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        {/* Left side: Chat icon + text content */}
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            flex: 1,
          }}
        >
          {/* Chat Icon */}
          <View
            style={{
              width: 40,
              height: 40,
              borderRadius: 20,
              backgroundColor: theme.colors.primaryContainer,
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: spacing.sm,
            }}
          >
            <Icon
              source="chat"
              size={20}
              color={theme.colors.onPrimaryContainer}
            />
          </View>
          
          {/* Text Content */}
          <View style={{ flex: 1 }}>
            <Text
              variant="titleMedium"
              style={{
                color: theme.colors.onSurface,
                fontWeight: '600',
                marginBottom: 2,
              }}
            >
              {t('realtime.friends.makeAromaFriends')}
            </Text>
            <Text
              variant="bodySmall"
              style={{
                color: theme.colors.onSurfaceVariant,
              }}
            >
              {t('realtime.friends.usersOnlinePrefix')}{totalCount} {totalCount === 1 ? t('realtime.friends.userSingular') : t('realtime.friends.userPlural')}{t('realtime.friends.usersOnlineSuffix')}
            </Text>
          </View>
        </View>
        
        {/* Right side: Avatar row */}
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginLeft: spacing.sm,
          }}
        >
          {generateUserAvatars()}
        </View>
      </View>
    </View>
  );
};
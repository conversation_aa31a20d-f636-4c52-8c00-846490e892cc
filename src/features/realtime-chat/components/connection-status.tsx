/**
 * Connection Status Component
 * 
 * Reusable connection status display component following AromaCHAT design patterns.
 * Extracts status display logic from realtime-chat-test.tsx.
 */

import React from 'react';
import { View } from 'react-native';
import { Text, Button, IconButton } from 'react-native-paper';
import { useTheme } from '@/shared/hooks/use-theme';
import { useTranslation } from 'react-i18next';
export type RealtimeConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error';

export interface ConnectionStatusProps {
  status: RealtimeConnectionStatus;
  isLoading?: boolean;
  onRetry?: () => void;
  showRetryButton?: boolean;
  compact?: boolean;
}

/**
 * Connection status component with proper theming and i18n support
 * Follows existing status display patterns but as reusable component
 */
export function ConnectionStatusComponent({ 
  status, 
  isLoading = false,
  onRetry,
  showRetryButton = true,
  compact = false
}: ConnectionStatusProps) {
  const { theme } = useTheme();
  const { t } = useTranslation();

  const getStatusColor = () => {
    switch (status) {
      case 'connected':
        return theme.colors.primary;
      case 'connecting':
        return theme.colors.secondary;
      case 'error':
        return theme.colors.error;
      default:
        return theme.colors.onSurfaceVariant;
    }
  };

  const getStatusText = () => {
    if (isLoading) return t('realtime.status.connecting');
    
    switch (status) {
      case 'connected':
        return t('realtime.status.connected');
      case 'connecting':
        return t('realtime.status.connecting');
      case 'error':
        return t('realtime.status.error');
      default:
        return t('realtime.status.disconnected');
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'connected':
        return 'wifi';
      case 'connecting':
        return 'wifi-sync';
      case 'error':
        return 'wifi-off';
      default:
        return 'wifi-off';
    }
  };

  const isConnected = status === 'connected';
  const canRetry = !isConnected && !isLoading && onRetry;

  if (compact) {
    return (
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 8,
        paddingVertical: 4,
      }}>
        <IconButton
          icon={getStatusIcon()}
          iconColor={getStatusColor()}
          size={16}
          style={{ margin: 0 }}
        />
        <Text variant="bodySmall" style={{ color: getStatusColor(), marginLeft: 4 }}>
          {getStatusText()}
        </Text>
        {canRetry && showRetryButton && (
          <IconButton
            icon="refresh"
            size={16}
            onPress={onRetry}
            style={{ margin: 0, marginLeft: 8 }}
          />
        )}
      </View>
    );
  }

  return (
    <View style={{ 
      padding: 16, 
      backgroundColor: theme.colors.surfaceVariant,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center'
    }}>
      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
        <IconButton
          icon={getStatusIcon()}
          iconColor={getStatusColor()}
          size={20}
          style={{ margin: 0, marginRight: 8 }}
        />
        <Text variant="bodyMedium" style={{ color: getStatusColor() }}>
          {t('realtime.status.label')}: {getStatusText()}
        </Text>
      </View>
      
      {canRetry && showRetryButton && (
        <Button 
          mode="outlined" 
          compact 
          onPress={onRetry}
          disabled={isLoading}
          style={{ minWidth: 100 }}
        >
          {isLoading ? t('realtime.buttons.connecting') : t('realtime.buttons.connect')}
        </Button>
      )}
    </View>
  );
}
import { useState } from 'react';
import * as ImagePicker from 'expo-image-picker';
import { useUser } from '@clerk/clerk-expo';
import { Alert } from 'react-native';
import { useTranslation } from 'react-i18next';

export const useAvatarPicker = () => {
  const { user } = useUser();
  const [isUpdating, setIsUpdating] = useState(false);
  const { t } = useTranslation(['common', 'auth']);

  const showImageSourceSelection = () => {
    const options = [];

    // Add remove option first if user has avatar
    if (user?.imageUrl) {
      options.push({
        text: t('common:profile.removeAvatar'),
        onPress: removeAvatar,
        style: 'destructive',
      } as any);
    }

    // Add take photo option
    options.push({
      text: t('auth:profile.takePhoto'),
      onPress: () => launchCamera(),
    });

    // Add choose from library option
    options.push({
      text: t('auth:profile.chooseFromLibrary'),
      onPress: () => launchImageLibrary(),
    });

    // Add cancel option last
    options.push({
      text: t('common:modal.cancel'),
      style: 'cancel',
    });

    Alert.alert(
      t('auth:profile.selectImageSource'),
      t('auth:profile.selectImageSourceMessage'),
      options
    );
  };

  const launchCamera = async () => {
    if (__DEV__) {
      console.log('📷 [CAMERA] Starting camera capture process');
    }

    try {
      // Request camera permissions
      const { status } = await ImagePicker.requestCameraPermissionsAsync();

      if (__DEV__) {
        console.log('📷 [CAMERA] Camera permission status:', status);
      }

      if (status !== 'granted') {
        if (__DEV__) {
          console.log('📷 [CAMERA] ❌ Camera permission denied');
        }
        Alert.alert(t('common:permissions.camera'), t('common:permissions.cameraMessage'));
        return;
      }

      // Launch camera
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: 'images',
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
        base64: true,
      });

      await processImageResult(result, 'camera');
    } catch (error) {
      if (__DEV__) {
        console.log('📷 [CAMERA] ❌ Error occurred:', error);
      }
      console.error('Camera error:', error);
      Alert.alert(t('common:errors.generic'), t('auth:profile.cameraFailed'));
    }
  };

  const launchImageLibrary = async () => {
    if (__DEV__) {
      console.log('🖼️ [LIBRARY] Starting image library selection');
    }

    try {
      // Request media library permissions
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (__DEV__) {
        console.log('🖼️ [LIBRARY] Media library permission status:', status);
      }

      if (status !== 'granted') {
        if (__DEV__) {
          console.log('🖼️ [LIBRARY] ❌ Media library permission denied');
        }
        Alert.alert(t('common:permissions.photoLibrary'), t('common:permissions.photoLibraryMessage'));
        return;
      }

      // Launch image library
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images',
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
        base64: true,
      });

      await processImageResult(result, 'library');
    } catch (error) {
      if (__DEV__) {
        console.log('🖼️ [LIBRARY] ❌ Error occurred:', error);
      }
      console.error('Image library error:', error);
      Alert.alert(t('common:errors.generic'), t('common:profile.avatarUpdateFailed'));
    }
  };

  const processImageResult = async (result: ImagePicker.ImagePickerResult, source: 'camera' | 'library') => {
    if (__DEV__) {
      console.log(`🖼️ [${source.toUpperCase()}] Image result:`, {
        canceled: result.canceled,
        hasAssets: result.assets?.length > 0,
        firstAssetUri: result.assets?.[0]?.uri ? 'URI_PRESENT' : 'NO_URI'
      });
    }

    if (!result.canceled && result.assets[0]) {
      setIsUpdating(true);

      try {
        const asset = result.assets[0];

        if (__DEV__) {
          console.log(`🖼️ [${source.toUpperCase()}] Processing asset:`, {
            uri: asset.uri,
            width: asset.width,
            height: asset.height,
            fileSize: asset.fileSize,
            mimeType: asset.mimeType,
            hasBase64: !!asset.base64
          });
        }

        // Format for Clerk: data:mimeType;base64,base64String
        const base64Image = `data:${asset.mimeType};base64,${asset.base64}`;

        if (__DEV__) {
          console.log(`🖼️ [${source.toUpperCase()}] Formatted base64 image for Clerk`);
          console.log(`🖼️ [${source.toUpperCase()}] Calling user.setProfileImage()`);
        }

        // Use Clerk's setProfileImage method with base64 format
        await user.setProfileImage({ file: base64Image });

        if (__DEV__) {
          console.log(`🖼️ [${source.toUpperCase()}] ✅ Profile image updated successfully`);
        }
      } catch (error) {
        if (__DEV__) {
          console.log(`🖼️ [${source.toUpperCase()}] ❌ Error processing image:`, error);
        }
        console.error('Avatar update error:', error);
        Alert.alert(t('common:errors.generic'), t('common:profile.avatarUpdateFailed'));
      } finally {
        setIsUpdating(false);
        if (__DEV__) {
          console.log(`🖼️ [${source.toUpperCase()}] Setting isUpdating to false (cleanup)`);
        }
      }
    } else {
      if (__DEV__) {
        console.log(`🖼️ [${source.toUpperCase()}] ℹ️ User canceled or no asset selected`);
      }
    }
  };

  const updateAvatar = async () => {
    if (__DEV__) {
      console.log('🖼️ [AVATAR UPDATE] Starting avatar update process');
      console.log('🖼️ [AVATAR UPDATE] User available:', !!user);
      console.log('🖼️ [AVATAR UPDATE] Currently updating:', isUpdating);
    }

    if (!user || isUpdating) {
      if (__DEV__) {
        console.log('🖼️ [AVATAR UPDATE] ❌ Early exit - No user or already updating');
      }
      return;
    }

    showImageSourceSelection();
  };

  const removeAvatar = async () => {
    if (__DEV__) {
      console.log('🗑️ [AVATAR REMOVE] Starting avatar removal process');
      console.log('🗑️ [AVATAR REMOVE] User available:', !!user);
      console.log('🗑️ [AVATAR REMOVE] Currently updating:', isUpdating);
      console.log('🗑️ [AVATAR REMOVE] User has avatar:', !!user?.imageUrl);
    }

    if (!user || isUpdating) {
      if (__DEV__) {
        console.log('🗑️ [AVATAR REMOVE] ❌ Early exit - No user or already updating');
      }
      return;
    }

    if (__DEV__) {
      console.log('🗑️ [AVATAR REMOVE] Step 1: Showing confirmation dialog');
    }

    Alert.alert(
      t('common:profile.removeAvatar'),
      t('common:profile.removeAvatarConfirmation'),
      [
        {
          text: t('common:modal.cancel'),
          style: 'cancel',
          onPress: () => {
            if (__DEV__) {
              console.log('🗑️ [AVATAR REMOVE] ℹ️ User canceled removal');
            }
          }
        },
        {
          text: t('common:profile.remove'),
          style: 'destructive',
          onPress: async () => {
            if (__DEV__) {
              console.log('🗑️ [AVATAR REMOVE] Step 2: User confirmed removal');
            }

            try {
              setIsUpdating(true);

              if (__DEV__) {
                console.log('🗑️ [AVATAR REMOVE] Step 3: Setting isUpdating to true');
                console.log('🗑️ [AVATAR REMOVE] Step 4: Calling user.setProfileImage({ file: null })');
              }

              await user.setProfileImage({ file: null });

              if (__DEV__) {
                console.log('🗑️ [AVATAR REMOVE] ✅ Profile image removed successfully');
              }
            } catch (error) {
              if (__DEV__) {
                console.log('🗑️ [AVATAR REMOVE] ❌ Error occurred:', error);
                console.log('🗑️ [AVATAR REMOVE] Error details:', {
                  message: error.message,
                  stack: error.stack,
                  name: error.name
                });
              }
              console.error('Avatar removal error:', error);
              Alert.alert(t('common:errors.generic'), t('common:profile.avatarRemovalFailed'));
            } finally {
              setIsUpdating(false);
              if (__DEV__) {
                console.log('🗑️ [AVATAR REMOVE] Step 5: Setting isUpdating to false (cleanup)');
              }
            }
          },
        },
      ]
    );
  };

  return { updateAvatar, removeAvatar, isUpdating };
};
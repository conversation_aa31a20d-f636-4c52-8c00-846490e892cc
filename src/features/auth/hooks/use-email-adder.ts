import { useState } from 'react';
import { useUser } from '@clerk/clerk-expo';
import { Alert } from 'react-native';
import { useTranslation } from 'react-i18next';

export const useEmailAdder = () => {
  const { user } = useUser();
  const [isAdding, setIsAdding] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [newEmail, setNewEmail] = useState('');
  const [createdEmailAddress, setCreatedEmailAddress] = useState<any>(null);
  const { t } = useTranslation('common');

  const startAdding = () => {
    if (__DEV__) {
      console.log('📧 [EMAIL ADD] Starting email addition mode');
      console.log('📧 [EMAIL ADD] Current email count:', user?.emailAddresses?.length || 0);
    }

    setNewEmail('');
    setIsAdding(true);
  };

  const cancelAdding = () => {
    if (__DEV__) {
      console.log('❌ [EMAIL ADD] Canceling email addition');
    }

    setIsAdding(false);
    setNewEmail('');
    setCreatedEmailAddress(null);
  };

  const isValidEmail = (email: string) => {
    return /\S+@\S+\.\S+/.test(email);
  };

  const createEmailAddress = async () => {
    if (__DEV__) {
      console.log('📧 [EMAIL ADD] Starting email creation process');
      console.log('📧 [EMAIL ADD] User available:', !!user);
      console.log('📧 [EMAIL ADD] Currently creating:', isCreating);
      console.log('📧 [EMAIL ADD] New email:', newEmail);
    }

    if (!user || isCreating || !newEmail.trim()) {
      if (__DEV__) {
        console.log('📧 [EMAIL ADD] ❌ Early exit - No user, already creating, or no email');
      }
      return;
    }

    const trimmedEmail = newEmail.trim();

    // Validate email format
    if (!isValidEmail(trimmedEmail)) {
      if (__DEV__) {
        console.log('📧 [EMAIL ADD] ❌ Validation failed - Invalid email format');
      }
      Alert.alert(t('errors.validation'), t('profile.invalidEmailFormat'));
      return;
    }

    // Check if email already exists
    const emailExists = user.emailAddresses?.some(
      email => email.emailAddress.toLowerCase() === trimmedEmail.toLowerCase()
    );

    if (emailExists) {
      if (__DEV__) {
        console.log('📧 [EMAIL ADD] ❌ Email already exists');
      }
      Alert.alert(t('errors.validation'), t('profile.emailAlreadyExists'));
      return;
    }

    // Check email limit (max 2 emails per Clerk documentation)
    const currentEmailCount = user.emailAddresses?.length || 0;
    if (currentEmailCount >= 2) {
      if (__DEV__) {
        console.log('📧 [EMAIL ADD] ❌ Email limit reached');
      }
      Alert.alert(t('errors.validation'), t('profile.emailLimitReached'));
      return;
    }

    try {
      setIsCreating(true);

      if (__DEV__) {
        console.log('📧 [EMAIL ADD] Step 1: Setting isCreating to true');
        console.log('📧 [EMAIL ADD] Step 2: Calling user.createEmailAddress()');
      }

      // Create unverified email address
      const emailAddress = await user.createEmailAddress({
        email: trimmedEmail,
      });

      if (__DEV__) {
        console.log('📧 [EMAIL ADD] ✅ Email address created successfully');
        console.log('📧 [EMAIL ADD] Email ID:', emailAddress.id);
        console.log('📧 [EMAIL ADD] Verification status:', emailAddress.verification?.status);
      }

      // Store the created email address for verification
      setCreatedEmailAddress(emailAddress);

      // Prepare verification (send OTP code)
      if (__DEV__) {
        console.log('📧 [EMAIL ADD] Step 3: Preparing email verification');
        console.log('📧 [EMAIL ADD] EmailAddress object type:', typeof emailAddress);
        console.log('📧 [EMAIL ADD] EmailAddress methods available:', Object.getOwnPropertyNames(Object.getPrototypeOf(emailAddress)));
      }

      const verificationResult = await emailAddress.prepareVerification({
        strategy: 'email_code',
      });

      if (__DEV__) {
        console.log('📧 [EMAIL ADD] ✅ Verification code sent successfully');
        console.log('📧 [EMAIL ADD] Verification result:', verificationResult);
      }

      return emailAddress;
    } catch (error) {
      if (__DEV__) {
        console.log('📧 [EMAIL ADD] ❌ Error occurred:', error);
        console.log('📧 [EMAIL ADD] Error details:', {
          message: error.message,
          stack: error.stack,
          name: error.name
        });
      }
      console.error('Email creation error:', error);

      // Handle specific Clerk errors
      if (error.message?.includes('already exists')) {
        Alert.alert(t('errors.validation'), t('profile.emailAlreadyExists'));
      } else if (error.message?.includes('rate limit')) {
        Alert.alert(t('errors.validation'), t('profile.emailRateLimited'));
      } else {
        Alert.alert(t('errors.generic'), t('profile.emailCreationFailed'));
      }

      throw error;
    } finally {
      setIsCreating(false);
      if (__DEV__) {
        console.log('📧 [EMAIL ADD] Step 4: Setting isCreating to false (cleanup)');
      }
    }
  };

  const verifyEmailAddress = async (code: string) => {
    if (__DEV__) {
      console.log('🔐 [EMAIL VERIFY] Starting email verification');
      console.log('🔐 [EMAIL VERIFY] Code length:', code.length);
      console.log('🔐 [EMAIL VERIFY] Code value:', code);
      console.log('🔐 [EMAIL VERIFY] Email address available:', !!createdEmailAddress);
      console.log('🔐 [EMAIL VERIFY] Email address ID:', createdEmailAddress?.id);
      console.log('🔐 [EMAIL VERIFY] Email address verification status:', createdEmailAddress?.verification?.status);
    }

    if (!createdEmailAddress || !code.trim()) {
      if (__DEV__) {
        console.log('🔐 [EMAIL VERIFY] ❌ Early exit - No email address or code');
      }
      throw new Error(t('profile.verificationFailed'));
    }

    // Validate code format (6 digits)
    const trimmedCode = code.trim();
    if (!/^\d{6}$/.test(trimmedCode)) {
      if (__DEV__) {
        console.log('🔐 [EMAIL VERIFY] ❌ Invalid code format - expected 6 digits, got:', trimmedCode);
      }
      throw new Error(t('profile.invalidVerificationCode'));
    }

    try {
      if (__DEV__) {
        console.log('🔐 [EMAIL VERIFY] Step 1: Calling attemptVerification() on EmailAddress object');
        console.log('🔐 [EMAIL VERIFY] Using code:', trimmedCode);
        console.log('🔐 [EMAIL VERIFY] EmailAddress methods:', Object.getOwnPropertyNames(Object.getPrototypeOf(createdEmailAddress)));
      }

      const verificationAttempt = await createdEmailAddress.attemptVerification({
        code: trimmedCode,
      });

      if (__DEV__) {
        console.log('🔐 [EMAIL VERIFY] Raw verification result:', verificationAttempt);
        console.log('🔐 [EMAIL VERIFY] Verification object:', verificationAttempt.verification);
        console.log('🔐 [EMAIL VERIFY] Verification status:', verificationAttempt.verification?.status);
        console.log('🔐 [EMAIL VERIFY] Email ID:', verificationAttempt.id);
        console.log('🔐 [EMAIL VERIFY] Email address:', verificationAttempt.emailAddress);
      }

      if (verificationAttempt.verification?.status === 'verified') {
        if (__DEV__) {
          console.log('🔐 [EMAIL VERIFY] ✅ Email verified successfully');
        }

        // Reset state after successful verification
        setIsAdding(false);
        setNewEmail('');
        setCreatedEmailAddress(null);

        return verificationAttempt;
      } else {
        if (__DEV__) {
          console.log('🔐 [EMAIL VERIFY] ❌ Verification incomplete, status:', verificationAttempt.verification?.status);
        }
        throw new Error('Verification incomplete');
      }
    } catch (error) {
      if (__DEV__) {
        console.log('🔐 [EMAIL VERIFY] ❌ Verification failed:', error);
        console.log('🔐 [EMAIL VERIFY] Error message:', error.message);
        console.log('🔐 [EMAIL VERIFY] Error name:', error.name);
        console.log('🔐 [EMAIL VERIFY] Error details:', {
          message: error.message,
          stack: error.stack,
          name: error.name
        });
      }
      console.error('Email verification error:', error);

      // Handle specific verification errors
      if (error.message?.toLowerCase().includes('incorrect')) {
        if (__DEV__) {
          console.log('🔐 [EMAIL VERIFY] Detected incorrect code error');
        }
        throw new Error(t('profile.invalidVerificationCode'));
      } else if (error.message?.toLowerCase().includes('invalid')) {
        if (__DEV__) {
          console.log('🔐 [EMAIL VERIFY] Detected invalid code error');
        }
        throw new Error(t('profile.invalidVerificationCode'));
      } else if (error.message?.toLowerCase().includes('expired')) {
        if (__DEV__) {
          console.log('🔐 [EMAIL VERIFY] Detected expired code error');
        }
        throw new Error(t('profile.verificationCodeExpired'));
      } else {
        if (__DEV__) {
          console.log('🔐 [EMAIL VERIFY] Generic verification error');
        }
        throw new Error(t('profile.verificationFailed'));
      }
    }
  };

  const resendVerificationCode = async () => {
    if (__DEV__) {
      console.log('🔄 [EMAIL RESEND] Starting verification code resend');
      console.log('🔄 [EMAIL RESEND] Email address available:', !!createdEmailAddress);
      console.log('🔄 [EMAIL RESEND] Email address ID:', createdEmailAddress?.id);
    }

    if (!createdEmailAddress) {
      if (__DEV__) {
        console.log('🔄 [EMAIL RESEND] ❌ No email address to resend to');
      }
      throw new Error(t('profile.resendFailed'));
    }

    try {
      if (__DEV__) {
        console.log('🔄 [EMAIL RESEND] Step 1: Calling prepareVerification() on EmailAddress object');
      }

      const resendResult = await createdEmailAddress.prepareVerification({
        strategy: 'email_code',
      });

      if (__DEV__) {
        console.log('🔄 [EMAIL RESEND] ✅ Verification code resent successfully');
        console.log('🔄 [EMAIL RESEND] Resend result:', resendResult);
      }
    } catch (error) {
      if (__DEV__) {
        console.log('🔄 [EMAIL RESEND] ❌ Resend failed:', error);
        console.log('🔄 [EMAIL RESEND] Error message:', error.message);
        console.log('🔄 [EMAIL RESEND] Error details:', {
          message: error.message,
          stack: error.stack,
          name: error.name
        });
      }
      console.error('Email resend error:', error);

      // Handle specific resend errors
      if (error.message?.includes('rate limit')) {
        throw new Error(t('profile.resendRateLimited'));
      } else {
        throw new Error(t('profile.resendFailed'));
      }
    }
  };

  const cleanupFailedVerification = async () => {
    if (__DEV__) {
      console.log('🧹 [EMAIL CLEANUP] Cleaning up failed verification');
      console.log('🧹 [EMAIL CLEANUP] Email address available:', !!createdEmailAddress);
    }

    if (createdEmailAddress) {
      try {
        if (__DEV__) {
          console.log('🧹 [EMAIL CLEANUP] Step 1: Destroying unverified email');
        }

        await createdEmailAddress.destroy();

        if (__DEV__) {
          console.log('🧹 [EMAIL CLEANUP] ✅ Unverified email destroyed successfully');
        }
      } catch (error) {
        if (__DEV__) {
          console.log('🧹 [EMAIL CLEANUP] ❌ Cleanup failed:', error);
        }
        console.error('Email cleanup error:', error);
      } finally {
        setCreatedEmailAddress(null);
        setIsAdding(false);
        setNewEmail('');
      }
    }
  };

  return {
    isAdding,
    isCreating,
    newEmail,
    setNewEmail,
    createdEmailAddress,
    startAdding,
    cancelAdding,
    createEmailAddress,
    verifyEmailAddress,
    resendVerificationCode,
    cleanupFailedVerification,
  };
};
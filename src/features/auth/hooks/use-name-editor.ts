import { useState } from 'react';
import { useUser } from '@clerk/clerk-expo';
import { Alert } from 'react-native';
import { useTranslation } from 'react-i18next';

export const useNameEditor = () => {
  const { user } = useUser();
  const [isUpdating, setIsUpdating] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editedFirstName, setEditedFirstName] = useState('');
  const [editedLastName, setEditedLastName] = useState('');
  const { t } = useTranslation('common');

  const startEditing = () => {
    if (__DEV__) {
      console.log('✏️ [NAME EDIT] Starting name edit mode');
      console.log('✏️ [NAME EDIT] Current names:', {
        firstName: user?.firstName,
        lastName: user?.lastName
      });
    }

    setEditedFirstName(user?.firstName || '');
    setEditedLastName(user?.lastName || '');
    setIsEditing(true);
  };

  const cancelEditing = () => {
    if (__DEV__) {
      console.log('❌ [NAME EDIT] Canceling name edit');
    }

    setIsEditing(false);
    setEditedFirstName('');
    setEditedLastName('');
  };

  const saveName = async () => {
    if (!user || isUpdating) {
      if (__DEV__) {
        console.log('✏️ [NAME EDIT] ❌ Early exit - No user or already updating');
      }
      return;
    }

    const trimmedFirstName = editedFirstName.trim();
    const trimmedLastName = editedLastName.trim();

    if (!trimmedFirstName || !trimmedLastName) {
      if (__DEV__) {
        console.log('✏️ [NAME EDIT] ❌ Validation failed - Empty names');
      }
      Alert.alert(t('errors.validation'), t('profile.nameRequired'));
      return;
    }

    if (__DEV__) {
      console.log('✏️ [NAME EDIT] Step 1: Starting name update process');
      console.log('✏️ [NAME EDIT] New names:', {
        firstName: trimmedFirstName,
        lastName: trimmedLastName
      });
    }

    try {
      setIsUpdating(true);

      if (__DEV__) {
        console.log('✏️ [NAME EDIT] Step 2: Setting isUpdating to true');
        console.log('✏️ [NAME EDIT] Step 3: Calling user.update()');
      }

      await user.update({
        firstName: trimmedFirstName,
        lastName: trimmedLastName,
      });

      if (__DEV__) {
        console.log('✏️ [NAME EDIT] ✅ Name updated successfully');
      }

      setIsEditing(false);
      setEditedFirstName('');
      setEditedLastName('');
    } catch (error) {
      if (__DEV__) {
        console.log('✏️ [NAME EDIT] ❌ Error occurred:', error);
        console.log('✏️ [NAME EDIT] Error details:', {
          message: error.message,
          stack: error.stack,
          name: error.name
        });
      }
      console.error('Name update error:', error);
      Alert.alert(t('errors.generic'), t('profile.nameUpdateFailed'));
    } finally {
      setIsUpdating(false);
      if (__DEV__) {
        console.log('✏️ [NAME EDIT] Step 4: Setting isUpdating to false (cleanup)');
      }
    }
  };

  return {
    isEditing,
    isUpdating,
    editedFirstName,
    editedLastName,
    setEditedFirstName,
    setEditedLastName,
    startEditing,
    cancelEditing,
    saveName,
  };
};
import { useState } from 'react';
import { useUser } from '@clerk/clerk-expo';
import { Alert } from 'react-native';
import { useTranslation } from 'react-i18next';

export const useEmailManager = () => {
  const { user } = useUser();
  const [isUpdating, setIsUpdating] = useState(false);
  const { t } = useTranslation('common');

  const setEmailAsPrimary = async (emailAddressId: string) => {
    if (__DEV__) {
      console.log('⭐ [EMAIL PRIMARY] Starting set email as primary process');
      console.log('⭐ [EMAIL PRIMARY] User available:', !!user);
      console.log('⭐ [EMAIL PRIMARY] Currently updating:', isUpdating);
      console.log('⭐ [EMAIL PRIMARY] Email ID:', emailAddressId);
    }

    if (!user || isUpdating || !emailAddressId) {
      if (__DEV__) {
        console.log('⭐ [EMAIL PRIMARY] ❌ Early exit - No user, already updating, or no email ID');
      }
      return;
    }

    // Check if email is already primary
    if (user.primaryEmailAddressId === emailAddressId) {
      if (__DEV__) {
        console.log('⭐ [EMAIL PRIMARY] ℹ️ Email is already primary');
      }
      return;
    }

    try {
      setIsUpdating(true);

      if (__DEV__) {
        console.log('⭐ [EMAIL PRIMARY] Step 1: Setting isUpdating to true');
        console.log('⭐ [EMAIL PRIMARY] Step 2: Calling user.update() with primaryEmailAddressId');
      }

      await user.update({
        primaryEmailAddressId: emailAddressId,
      });

      if (__DEV__) {
        console.log('⭐ [EMAIL PRIMARY] ✅ Email set as primary successfully');
      }
    } catch (error) {
      if (__DEV__) {
        console.log('⭐ [EMAIL PRIMARY] ❌ Error occurred:', error);
        console.log('⭐ [EMAIL PRIMARY] Error details:', {
          message: error.message,
          stack: error.stack,
          name: error.name
        });
      }
      console.error('Set email as primary error:', error);

      // Handle specific Clerk error for unverified email
      if (error.message?.includes('needs to be verified')) {
        Alert.alert(t('errors.validation'), t('profile.emailMustBeVerifiedFirst'));
      } else {
        Alert.alert(t('errors.generic'), t('profile.emailPrimaryUpdateFailed'));
      }
    } finally {
      setIsUpdating(false);
      if (__DEV__) {
        console.log('⭐ [EMAIL PRIMARY] Step 3: Setting isUpdating to false (cleanup)');
      }
    }
  };

  const removeEmail = async (emailAddressId: string) => {
    if (__DEV__) {
      console.log('🗑️ [EMAIL REMOVE] Starting email removal process');
      console.log('🗑️ [EMAIL REMOVE] User available:', !!user);
      console.log('🗑️ [EMAIL REMOVE] Currently updating:', isUpdating);
      console.log('🗑️ [EMAIL REMOVE] Email ID:', emailAddressId);
      console.log('🗑️ [EMAIL REMOVE] Total emails:', user?.emailAddresses?.length);
    }

    if (!user || isUpdating || !emailAddressId) {
      if (__DEV__) {
        console.log('🗑️ [EMAIL REMOVE] ❌ Early exit - No user, already updating, or no email ID');
      }
      return;
    }

    // Prevent removing primary email or when only one email exists
    const totalEmails = user.emailAddresses?.length || 0;
    const isPrimary = user.primaryEmailAddressId === emailAddressId;

    if (totalEmails <= 1) {
      if (__DEV__) {
        console.log('🗑️ [EMAIL REMOVE] ❌ Cannot remove - only one email exists');
      }
      Alert.alert(t('errors.validation'), t('profile.cannotRemoveLastEmail'));
      return;
    }

    if (isPrimary) {
      if (__DEV__) {
        console.log('🗑️ [EMAIL REMOVE] ❌ Cannot remove - email is primary');
      }
      Alert.alert(t('errors.validation'), t('profile.cannotRemovePrimaryEmail'));
      return;
    }

    // Find the email address object
    const emailAddress = user.emailAddresses?.find(email => email.id === emailAddressId);
    if (!emailAddress) {
      if (__DEV__) {
        console.log('🗑️ [EMAIL REMOVE] ❌ Email address not found');
      }
      return;
    }

    if (__DEV__) {
      console.log('🗑️ [EMAIL REMOVE] Step 1: Showing confirmation dialog');
    }

    Alert.alert(
      t('profile.removeEmail'),
      t('profile.removeEmailConfirmation', { email: emailAddress.emailAddress }),
      [
        {
          text: t('modal.cancel'),
          style: 'cancel',
          onPress: () => {
            if (__DEV__) {
              console.log('🗑️ [EMAIL REMOVE] ℹ️ User canceled removal');
            }
          }
        },
        {
          text: t('profile.remove'),
          style: 'destructive',
          onPress: async () => {
            if (__DEV__) {
              console.log('🗑️ [EMAIL REMOVE] Step 2: User confirmed removal');
            }

            try {
              setIsUpdating(true);

              if (__DEV__) {
                console.log('🗑️ [EMAIL REMOVE] Step 3: Setting isUpdating to true');
                console.log('🗑️ [EMAIL REMOVE] Step 4: Calling emailAddress.destroy()');
              }

              await emailAddress.destroy();

              if (__DEV__) {
                console.log('🗑️ [EMAIL REMOVE] ✅ Email removed successfully');
              }
            } catch (error) {
              if (__DEV__) {
                console.log('🗑️ [EMAIL REMOVE] ❌ Error occurred:', error);
                console.log('🗑️ [EMAIL REMOVE] Error details:', {
                  message: error.message,
                  stack: error.stack,
                  name: error.name
                });
              }
              console.error('Email removal error:', error);
              Alert.alert(t('errors.generic'), t('profile.emailRemovalFailed'));
            } finally {
              setIsUpdating(false);
              if (__DEV__) {
                console.log('🗑️ [EMAIL REMOVE] Step 5: Setting isUpdating to false (cleanup)');
              }
            }
          },
        },
      ]
    );
  };

  return { setEmailAsPrimary, removeEmail, isUpdating };
};
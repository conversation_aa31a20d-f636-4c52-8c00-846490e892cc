import { PasswordResetErrors, PasswordResetFormData, PasswordValidationResult } from '../types/password-reset.types';

export const validateEmail = (email: string): string | null => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  
  if (!email) {
    return 'Email is required';
  }
  
  if (!emailRegex.test(email)) {
    return 'Please enter a valid email address';
  }
  
  return null;
};

export const validatePassword = (password: string): string | null => {
  if (!password) {
    return 'Password is required';
  }
  
  if (password.length < 8) {
    return 'Password must be at least 8 characters long';
  }
  
  // Note: We removed custom complexity requirements since Clerk handles this
  // Clerk will return server-side validation errors for password complexity
  
  return null;
};

export const validatePasswordMatch = (
  password: string, 
  confirmPassword: string
): string | null => {
  if (!confirmPassword) {
    return 'Please confirm your password';
  }
  
  if (password !== confirmPassword) {
    return 'Passwords do not match';
  }
  
  return null;
};

export const validateVerificationCode = (code: string): string | null => {
  if (!code) {
    return 'Verification code is required';
  }
  
  if (code.length !== 6) {
    return 'Verification code must be 6 digits';
  }
  
  if (!/^\d+$/.test(code)) {
    return 'Verification code must contain only numbers';
  }
  
  return null;
};

/**
 * Validates all password reset form fields at once
 * Returns all validation errors, allowing user to see all issues simultaneously
 */
export const validateResetPasswordForm = (
  data: PasswordResetFormData
): PasswordValidationResult => {
  const errors: PasswordResetErrors = {};

  // Validate verification code
  const codeError = validateVerificationCode(data.code);
  if (codeError) {
    errors.code = codeError;
  }

  // Validate new password
  const passwordError = validatePassword(data.newPassword);
  if (passwordError) {
    errors.newPassword = passwordError;
  }

  // Validate password confirmation
  const matchError = validatePasswordMatch(data.newPassword, data.confirmPassword);
  if (matchError) {
    errors.confirmPassword = matchError;
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

/**
 * Validates email field for forgot password form
 */
export const validateEmailForm = (email: string): PasswordValidationResult => {
  const errors: PasswordResetErrors = {};
  
  const emailError = validateEmail(email);
  if (emailError) {
    errors.email = emailError;
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};
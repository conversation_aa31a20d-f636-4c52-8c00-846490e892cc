import React, { useCallback, useEffect } from 'react';
import { View } from 'react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';
import {
  TextInput,
  Button,
  Text,
  HelperText,
  TouchableRipple,
} from 'react-native-paper';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/shared/hooks/use-theme';
import { useButtonStyles, useButtonContentStyles } from '@/shared/styles/button-styles';
import { haptics } from '@/shared/utils/haptics';
import { GradientLogo } from '@/shared/components/branding/gradient-logo';
import { usePasswordResetContext } from '../context/password-reset.context';
import { useEmailSignInContext } from '../context/email-signin.context';
import { GenericOtpModal } from '@/shared/components/modals';

// Animation delay constants - consistent with AuthForm
const ANIMATION_DELAYS = {
  HEADER: 100,
  EMAIL_FORM: 200,
  SUBMIT_BUTTON: 300,
  BACK_LINK: 400
} as const;

export const ForgotPasswordForm: React.FC = () => {
  const { state, updateField, requestPasswordReset, handleOtpVerify, handleOtpResend, hideOtpModal, resetState } = usePasswordResetContext();
  const emailSignIn = useEmailSignInContext();
  const router = useRouter();
  const { t } = useTranslation('auth');
  const { theme, spacing, borderRadius } = useTheme();
  const buttonStyles = useButtonStyles();
  const buttonContentStyles = useButtonContentStyles();

  const canSubmit = state.email.length > 0 && !state.isLoading && !emailSignIn.state.isLoading;

  // Sync email between password reset and email sign-in contexts
  const handleEmailChange = (text: string) => {
    updateField('email', text);
    emailSignIn.updateField('email', text);
  };

  // Reset state when component mounts to ensure clean flow
  useEffect(() => {
    resetState();
  }, [resetState]);

  const handleBack = useCallback(() => {
    haptics.light();
    router.back();
  }, [router]);

  const handleRequestEmailSignIn = () => {
    if (!canSubmit) {
      haptics.light();
      return;
    }
    emailSignIn.requestEmailSignIn();
  }

  const handleRequestPasswordReset = () => {
    if (!canSubmit) {
      haptics.light();
      return;
    }
    requestPasswordReset();
  }

  return (
    <View style={{ flex: 1 }}>
      {/* Top section */}
      <View style={{ flex: 1, paddingHorizontal: spacing.lg, paddingTop: spacing.xl }}>
        {/* Logo and Welcome Message */}
        <Animated.View entering={FadeInDown.delay(ANIMATION_DELAYS.HEADER)} style={{
          alignItems: 'center',
          marginBottom: spacing.xl
        }}>
          <GradientLogo width={48} height={48} />

          <Text variant="headlineSmall" style={{
            color: theme.colors.onSurface,
            marginTop: spacing.md,
            textAlign: 'center'
          }}>
            {t('forgotPassword.title')}
          </Text>

          <Text variant="bodyMedium" style={{
            color: theme.colors.onSurfaceVariant,
            marginTop: spacing.xs,
            textAlign: 'center',
            maxWidth: 280
          }}>
            {t('forgotPassword.emailInstructions')}
          </Text>
        </Animated.View>

        {/* Form */}
        <Animated.View entering={FadeInDown.delay(ANIMATION_DELAYS.EMAIL_FORM)} style={{ gap: spacing.sm }}>
        <View>
          <TextInput
            label={t('form.email.label')}
            value={state.email}
            onChangeText={handleEmailChange}
            keyboardType="email-address"
            autoCapitalize="none"
            autoComplete="email"
            textContentType="emailAddress"
            mode="outlined"
            style={{ backgroundColor: theme.colors.surface }}
            outlineColor={theme.colors.outline}
            activeOutlineColor={theme.colors.primary}
            disabled={state.isLoading || emailSignIn.state.isLoading}
            error={!!state.errors.email || !!emailSignIn.state.errors.email}
          />

          {(!!state.errors.email || !!emailSignIn.state.errors.email) && (
            <HelperText type="error" visible={true}>
              {state.errors.email || emailSignIn.state.errors.email}
            </HelperText>
          )}

          {(!!state.errors.general || !!emailSignIn.state.errors.general) && (
            <HelperText type="error" visible={true}>
              {state.errors.general || emailSignIn.state.errors.general}
            </HelperText>
          )}
        </View>

        </Animated.View>
      </View>

      {/* Bottom Section - Actions */}
      <View style={{
        paddingHorizontal: spacing.lg,
        paddingBottom: spacing.xl,
        paddingTop: spacing.md,
        gap: spacing.sm
      }}>
        {/* Action Buttons Stacked */}
        <Animated.View entering={FadeInDown.delay(ANIMATION_DELAYS.SUBMIT_BUTTON)} style={{ gap: spacing.sm }}>
          <Button
            mode="outlined"
            onPress={handleRequestEmailSignIn}
            loading={emailSignIn.state.isLoading}
            disabled={emailSignIn.state.isLoading}
            style={[
              buttonStyles.large, 
              {
                borderColor: theme.colors.outline,
                borderRadius: borderRadius.button,
                opacity: canSubmit ? 1 : 0.9
              }
            ]}
            contentStyle={buttonContentStyles.large}
          >
            {t('emailSignIn.button', 'Sign in with code')}
          </Button>

          <Button
            mode="contained"
            onPress={handleRequestPasswordReset}
            loading={state.isLoading}
            disabled={state.isLoading}
            style={[
              buttonStyles.large, 
              {
                borderRadius: borderRadius.button,
                opacity: canSubmit ? 1 : 0.9
              }
            ]}
            contentStyle={buttonContentStyles.large}
          >
            {t('forgotPassword.sendCode')}
          </Button>
        </Animated.View>

        {/* Back to Sign In */}
        <Animated.View entering={FadeInDown.delay(ANIMATION_DELAYS.BACK_LINK)} style={{ alignItems: 'center' }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center', flexWrap: 'wrap' }}>
            <Text variant="bodyMedium" style={{
              color: theme.colors.onSurfaceVariant,
              marginRight: spacing.xs
            }}>
              {t('forgotPassword.rememberPassword')}
            </Text>

            <TouchableRipple
              style={{
                paddingVertical: spacing.sm,
                paddingHorizontal: spacing.md,
                borderRadius: borderRadius.md,
                minHeight: 44,
                justifyContent: 'center'
              }}
              onPress={handleBack}
              disabled={state.isLoading || emailSignIn.state.isLoading}
            >
              <Text variant="labelLarge" style={{
                color: theme.colors.primary
              }}>
                {t('forgotPassword.backToSignIn')}
              </Text>
            </TouchableRipple>
          </View>
        </Animated.View>
      </View>

      {/* Generic OTP Modal for Password Reset */}
      <GenericOtpModal
        isVisible={state.showOtpModal}
        onClose={hideOtpModal}
        title={t('forgotPassword.verifyCode')}
        subtitle={t('forgotPassword.instructions', { email: state.email })}
        onVerify={handleOtpVerify}
        onResend={handleOtpResend}
        isLoading={state.isLoading}
        error={state.otpError}
      />

      {/* Generic OTP Modal for Email Sign-In */}
      <GenericOtpModal
        isVisible={emailSignIn.state.showOtpModal}
        onClose={emailSignIn.hideOtpModal}
        title={t('emailSignIn.title', 'Sign in with Email Code')}
        subtitle={t('emailSignIn.subtitle', { email: emailSignIn.state.email }) || `We'll send a code to ${emailSignIn.state.email} to sign you in`}
        onVerify={emailSignIn.handleOtpVerify}
        onResend={emailSignIn.handleOtpResend}
        isLoading={emailSignIn.state.isLoading}
        error={emailSignIn.state.otpError}
      />
    </View>
  );
};


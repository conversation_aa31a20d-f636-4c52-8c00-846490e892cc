import React, { useState } from 'react';
import { Alert, Platform, View } from 'react-native';
import { BottomSheetView } from '@gorhom/bottom-sheet';
import {
  List,
  Divider,
  TouchableRipple,
  Text,
  Avatar,
  Dialog,
  Button,
  ActivityIndicator,
  TextInput,
  IconButton,
  Menu,
} from 'react-native-paper';
import { useAuth, useUser } from '@clerk/clerk-expo';
import { useRouter } from 'expo-router';
import { useTheme } from '@/shared/hooks';
import { haptics } from '@/shared/utils/haptics';
import { useTranslation } from 'react-i18next';
import { useAvatarPicker, useNameEditor, useEmailManager, useEmailAdder } from '@/features/auth/hooks';
import { EmailVerificationProvider, useEmailVerificationContext } from '@/shared/contexts';
import { GenericOtpModal } from '@/shared/components/modals';

interface ProfileModalContentProps {
  onDismiss: () => void;
}

/**
 * Profile Modal Content - Adapted for the new modal system
 * Contains user info and sign out functionality
 */
const ProfileModalContentInner: React.FC<ProfileModalContentProps> = ({ onDismiss }) => {
  const themeHook = useTheme();
  const { theme, toggleTheme, isDarkTheme, hapticsEnabled, toggleHaptics, themeMode } = themeHook;
  const { user } = useUser();
  const { signOut } = useAuth();
  const router = useRouter();
  const styles = getStyles(theme);

  const [showSignOutDialog, setShowSignOutDialog] = useState(false);
  const [emailMenuVisible, setEmailMenuVisible] = useState<{[key: string]: boolean}>({});
  const { t } = useTranslation(['common', 'auth']);

  // Avatar picker functionality
  const { updateAvatar, isUpdating } = useAvatarPicker();

  // Name editor functionality
  const {
    isEditing: isEditingName,
    isUpdating: isUpdatingName,
    editedFirstName,
    editedLastName,
    setEditedFirstName,
    setEditedLastName,
    startEditing: startEditingName,
    cancelEditing: cancelEditingName,
    saveName,
  } = useNameEditor();

  // Email management functionality
  const { setEmailAsPrimary, removeEmail, isUpdating: isUpdatingEmail } = useEmailManager();

  // Email addition functionality
  const {
    isAdding: isAddingEmail,
    isCreating: isCreatingEmail,
    newEmail,
    setNewEmail,
    startAdding: startAddingEmail,
    cancelAdding: cancelAddingEmail,
    createEmailAddress,
    cleanupFailedVerification,
  } = useEmailAdder();

  // Email verification context
  const { state: verificationState, showVerificationModal, hideVerificationModal, handleVerification, handleResend } = useEmailVerificationContext();


  const getThemeLabel = () => {
    switch (themeMode) {
      case 'light': return t('theme.light');
      case 'dark': return t('theme.dark');
      case 'system': return t('theme.system');
      default: return t('theme.system');
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      onDismiss();
      router.replace('/welcome');
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  const confirmSignOut = () => {
    haptics.destructive(); // Destructive action haptic
    
    if (Platform.OS === 'web') {
      setShowSignOutDialog(true);
    } else {
      Alert.alert(
        t('modal.signOut'),
        t('profile.signOutConfirmation'),
        [
          { text: t('modal.cancel'), style: 'cancel' },
          { text: t('modal.signOut'), style: 'destructive', onPress: handleSignOut },
        ]
      );
    }
  };

  const handleThemeToggle = () => {
    haptics.toggle(); // Theme toggle haptic
    toggleTheme();
  };

  const handleHapticsToggle = async () => {
    haptics.toggle(); // Haptics toggle haptic
    await toggleHaptics();
  };

  const handleEmailCreation = async () => {
    try {
      const emailAddress = await createEmailAddress();
      if (emailAddress) {
        // Show verification modal
        showVerificationModal(newEmail, emailAddress);
      }
    } catch (error) {
      // Error is already handled in the hook
      console.error('Email creation failed:', error);
    }
  };

  const handleVerificationSuccess = () => {
    haptics.success();
    // The UI will automatically update since the user object will be refreshed
  };

  const handleVerificationCancel = () => {
    // Clean up the unverified email address
    cleanupFailedVerification();
  };

  const handleExistingEmailVerification = async (emailAddress: any) => {
    try {
      if (__DEV__) {
        console.log('🔐 [EXISTING EMAIL VERIFY] Starting verification for existing email');
        console.log('🔐 [EXISTING EMAIL VERIFY] Email ID:', emailAddress.id);
        console.log('🔐 [EXISTING EMAIL VERIFY] Email address:', emailAddress.emailAddress);
      }

      // Prepare verification for the existing email
      await emailAddress.prepareVerification({
        strategy: 'email_code',
      });

      // Show verification modal with the existing email address
      showVerificationModal(emailAddress.emailAddress, emailAddress);

      if (__DEV__) {
        console.log('🔐 [EXISTING EMAIL VERIFY] ✅ Verification code sent to existing email');
      }
    } catch (error) {
      if (__DEV__) {
        console.log('🔐 [EXISTING EMAIL VERIFY] ❌ Failed to send verification code:', error);
      }
      console.error('Existing email verification setup failed:', error);
      Alert.alert(t('errors.generic'), t('profile.verificationFailed'));
    }
  };

  // Mutual exclusivity handlers - KISS approach
  const handleStartEditingName = () => {
    // Cancel email adding if active
    if (isAddingEmail) {
      cancelAddingEmail();
    }
    // Start name editing
    startEditingName();
  };

  const handleStartAddingEmail = () => {
    // Cancel name editing if active
    if (isEditingName) {
      cancelEditingName();
    }
    // Start email adding
    startAddingEmail();
  };


  return (
    <BottomSheetView style={styles.container}>
      {/* User Info Section */}
      <View style={styles.userSection}>
        <View style={styles.avatarWrapper}>
          <View style={styles.avatarContainer}>
            {user?.imageUrl ? (
              <Avatar.Image
                size={80}
                source={{ uri: user.imageUrl }}
              />
            ) : (
              <Avatar.Icon
                size={80}
                icon="account"
                style={{ backgroundColor: theme.colors.primary }}
              />
            )}
            {isUpdating && (
              <View style={styles.avatarOverlay}>
                <ActivityIndicator size="small" color={theme.colors.primary} />
              </View>
            )}
          </View>
          {/* Edit Badge - positioned at bottom-right */}
          <View style={styles.editBadge}>
            <TouchableRipple
              borderless={true}
              style={[styles.editBadgeButton, { backgroundColor: theme.colors.primary }]}
              onPress={updateAvatar}
              disabled={isUpdating}
            >
              <IconButton
                icon="camera-outline"
                size={16}
                iconColor={theme.colors.onPrimary}
                style={styles.editBadgeIconButton}
                disabled={isUpdating}
              />
            </TouchableRipple>
          </View>
        </View>
        <View style={styles.userInfo}>
          {/* Name Display/Edit Section */}
          {isEditingName ? (
            // Edit Mode: Two TextInputs + Save/Cancel
            <View style={styles.nameEditContainer}>
              <View style={styles.nameInputsContainer}>
                <TextInput
                  label={t('auth:form.firstName.label')}
                  value={editedFirstName}
                  onChangeText={setEditedFirstName}
                  autoCapitalize="words"
                  autoComplete="given-name"
                  textContentType="givenName"
                  mode="outlined"
                  style={{ backgroundColor: theme.colors.surface, flex: 1 }}
                  disabled={isUpdatingName}
                  accessibilityRole="text"
                  accessibilityLabel={t('auth:form.firstName.accessibilityLabel')}
                  returnKeyType="next"
                  autoFocus
                />
                <TextInput
                  label={t('auth:form.lastName.label')}
                  value={editedLastName}
                  onChangeText={setEditedLastName}
                  autoCapitalize="words"
                  autoComplete="family-name"
                  textContentType="familyName"
                  mode="outlined"
                  style={{ backgroundColor: theme.colors.surface, flex: 1 }}
                  disabled={isUpdatingName}
                  accessibilityRole="text"
                  accessibilityLabel={t('auth:form.lastName.accessibilityLabel')}
                  returnKeyType="done"
                  onSubmitEditing={saveName}
                />
              </View>
              <View style={styles.nameEditActions}>
                <IconButton
                  icon="close"
                  size={20}
                  iconColor={theme.colors.onSurfaceVariant}
                  onPress={cancelEditingName}
                  disabled={isUpdatingName}
                />
                <IconButton
                  icon="check"
                  size={20}
                  iconColor={theme.colors.primary}
                  onPress={saveName}
                  disabled={isUpdatingName || !editedFirstName.trim() || !editedLastName.trim()}
                />
              </View>
              {isUpdatingName && (
                <ActivityIndicator size="small" color={theme.colors.primary} style={styles.nameUpdateLoader} />
              )}
            </View>
          ) : (
            // Display Mode: Name + Pencil Icon
            <View style={styles.nameDisplayContainer}>
              <Text variant="headlineSmall" style={styles.userName}>
                {user?.firstName || t('profile.demoFirstName')} {user?.lastName || t('profile.demoLastName')}
              </Text>
              <IconButton
                icon="pencil"
                size={20}
                iconColor={theme.colors.onSurfaceVariant}
                onPress={handleStartEditingName}
                disabled={isUpdating || isUpdatingName}
                style={styles.editNameButton}
              />
            </View>
          )}

          <View style={styles.emailChipsContainer}>
            {user?.emailAddresses
              ?.sort((a, b) => {
                // Primary email first, then by original order
                if (a.id === user?.primaryEmailAddressId) return -1;
                if (b.id === user?.primaryEmailAddressId) return 1;
                return 0;
              })
              ?.map((email, index) => {
              const isPrimary = email.id === user?.primaryEmailAddressId;
              const isVerified = email.verification?.status === 'verified';
              // const sortedEmails = user?.emailAddresses?.sort((a, b) => {
              //   if (a.id === user?.primaryEmailAddressId) return -1;
              //   if (b.id === user?.primaryEmailAddressId) return 1;
              //   return 0;
              // });
              // const isLast = index === (sortedEmails?.length || 0) - 1;

              return (
                <View key={email.id || index} style={styles.emailRowContainer}>
                  {/* Star icon for primary email - only show when multiple emails */}
                  {isPrimary && (user?.emailAddresses?.length || 0) > 1 ? (
                    <IconButton
                      icon="star-outline"
                      size={20}
                      iconColor={theme.colors.primary}
                      style={styles.primaryStarIcon}
                    />
                  ) : (user?.emailAddresses?.length || 0) > 1 ? (
                    <View style={styles.primaryStarIcon} />
                  ) : null}

                  {/* Email text */}
                  <Text style={[styles.emailText, { color: theme.colors.onSurface }]} numberOfLines={1}>
                    {email.emailAddress}
                  </Text>

                  {/* Verification status icon */}
                  <IconButton
                    icon={isVerified ? "check-circle" : "alert-circle"}
                    size={18}
                    iconColor={isVerified ? theme.colors.tertiary : theme.colors.error}
                    style={styles.statusIconInline}
                    onPress={!isVerified ? () => {
                      haptics.light();
                      // TODO: Trigger verification flow
                    } : undefined}
                  />


                  {/* Email actions menu */}
                  {isPrimary && isVerified ? (
                    // Hidden placeholder to maintain spacing for primary verified emails
                    <View style={styles.emailMenuButtonCompact} />
                  ) : (
                    <Menu
                      visible={emailMenuVisible[email.id || index.toString()] || false}
                      onDismiss={() => {
                        setEmailMenuVisible(prev => ({
                          ...prev,
                          [email.id || index.toString()]: false
                        }));
                      }}
                      anchor={
                        <IconButton
                          icon="dots-vertical"
                          size={24}
                          iconColor={theme.colors.onSurfaceVariant}
                          onPress={() => {
                            haptics.light();
                            setEmailMenuVisible(prev => ({
                              ...prev,
                              [email.id || index.toString()]: true
                            }));
                          }}
                          style={styles.emailMenuButtonCompact}
                        />
                      }
                      contentStyle={{ backgroundColor: theme.colors.surface }}
                    >
                    {!isPrimary && (
                      <Menu.Item
                        onPress={async () => {
                          haptics.light();
                          setEmailMenuVisible(prev => ({
                            ...prev,
                            [email.id || index.toString()]: false
                          }));
                          await setEmailAsPrimary(email.id);
                        }}
                        title={t('profile.setAsPrimary')}
                        leadingIcon="star-outline"
                        titleStyle={{ color: theme.colors.onSurface }}
                        disabled={isUpdatingEmail}
                      />
                    )}
                    {!isVerified && (
                      <Menu.Item
                        onPress={async () => {
                          haptics.light();
                          setEmailMenuVisible(prev => ({
                            ...prev,
                            [email.id || index.toString()]: false
                          }));
                          await handleExistingEmailVerification(email);
                        }}
                        title={t('profile.verifyEmail')}
                        leadingIcon="shield-check-outline"
                        titleStyle={{ color: theme.colors.onSurface }}
                        disabled={isUpdatingEmail}
                      />
                    )}
                    {!isPrimary && (
                      <Menu.Item
                        onPress={async () => {
                          haptics.destructive();
                          setEmailMenuVisible(prev => ({
                            ...prev,
                            [email.id || index.toString()]: false
                          }));
                          await removeEmail(email.id);
                        }}
                        title={t('profile.removeEmail')}
                        leadingIcon="delete-outline"
                        titleStyle={{ color: theme.colors.error }}
                        disabled={isUpdatingEmail}
                      />
                    )}
                    </Menu>
                  )}
                </View>
              );
            })}

            {/* Add Email Section - only show if user has exactly 1 email (max 2 emails allowed) */}
            {(user?.emailAddresses?.length || 0) === 1 && (
              isAddingEmail ? (
                // Email Addition Mode: TextInput + Save/Cancel
                <View style={styles.addEmailEditContainer}>
                  <TextInput
                    label={t('profile.emailAddress')}
                    value={newEmail}
                    onChangeText={setNewEmail}
                    autoCapitalize="none"
                    autoComplete="email"
                    textContentType="emailAddress"
                    keyboardType="email-address"
                    mode="outlined"
                    style={{ backgroundColor: theme.colors.surface }}
                    disabled={isCreatingEmail}
                    accessibilityRole="text"
                    accessibilityLabel={t('profile.emailAddress')}
                    returnKeyType="done"
                    onSubmitEditing={handleEmailCreation}
                    autoFocus
                  />
                  <View style={styles.addEmailActions}>
                    <IconButton
                      icon="close"
                      size={20}
                      iconColor={theme.colors.onSurfaceVariant}
                      onPress={() => {
                        haptics.light();
                        cancelAddingEmail();
                      }}
                      disabled={isCreatingEmail}
                    />
                    <IconButton
                      icon="check"
                      size={20}
                      iconColor={theme.colors.primary}
                      onPress={handleEmailCreation}
                      disabled={isCreatingEmail || !newEmail.trim()}
                    />
                  </View>
                  {isCreatingEmail && (
                    <ActivityIndicator size="small" color={theme.colors.primary} style={styles.addEmailLoader} />
                  )}
                </View>
              ) : (
                // Add Email Button
                <TouchableRipple
                  onPress={() => {
                    haptics.light();
                    handleStartAddingEmail();
                  }}
                  style={[styles.addEmailButtonCompact, { borderColor: theme.colors.outline }]}
                  disabled={isUpdatingEmail || isCreatingEmail}
                >
                  <View style={styles.addEmailButtonContentCompact}>
                    <IconButton
                      icon="plus"
                      size={16}
                      iconColor={theme.colors.primary}
                      style={styles.addEmailIconCompact}
                    />
                    <Text style={[styles.addEmailTextCompact, { color: theme.colors.primary }]}>
                      {t('profile.addEmail')}
                    </Text>
                  </View>
                </TouchableRipple>
              )
            )}
          </View>
        </View>
      </View>

      <Divider style={styles.divider} />

      {/* Settings Section */}
      <View style={styles.settingsSection}>
        <Text variant="titleMedium" style={styles.sectionTitle}>
          {t('modal.settings')}
        </Text>

        {/* Theme Selection */}
        <TouchableRipple onPress={handleThemeToggle}>
          <View style={styles.settingItem}>
            <View style={styles.settingLeft}>
              <List.Icon
                icon={isDarkTheme ? 'weather-night' : 'weather-sunny'}
                color={theme.colors.onSurface}
              />
              <Text variant="labelLarge" style={styles.settingLabel}>{t('modal.theme')}</Text>
            </View>
            <Text variant="bodyMedium" style={styles.settingValue}>
              {getThemeLabel()}
            </Text>
          </View>
        </TouchableRipple>

        {/* Haptics Toggle */}
        <TouchableRipple onPress={handleHapticsToggle}>
          <View style={styles.settingItem}>
            <View style={styles.settingLeft}>
              <List.Icon
                icon={hapticsEnabled ? 'vibrate' : 'vibrate-off'}
                color={theme.colors.onSurface}
              />
              <Text variant="labelLarge" style={styles.settingLabel}>{t('profile.hapticFeedback')}</Text>
            </View>
            <Text variant="bodyMedium" style={styles.settingValue}>
              {hapticsEnabled ? t('modal.hapticsEnabled') : t('modal.hapticsDisabled')}
            </Text>
          </View>
        </TouchableRipple>


        {/* Additional Menu Items */}
        <List.Item
          title={t('modal.accountSettings')}
          titleStyle={styles.settingLabel}
          left={props => <List.Icon {...props} icon="cog" color={theme.colors.onSurface} />}
          right={props => <List.Icon {...props} icon="chevron-right" color={theme.colors.onSurfaceVariant} />}
          onPress={() => {
            haptics.light();
            Alert.alert(t('modal.comingSoon'), t('profile.accountSettingsMessage'));
          }}
        />

        <List.Item
          title={t('modal.privacySecurity')}
          titleStyle={styles.settingLabel}
          left={props => <List.Icon {...props} icon="shield-account" color={theme.colors.onSurface} />}
          right={props => <List.Icon {...props} icon="chevron-right" color={theme.colors.onSurfaceVariant} />}
          onPress={() => {
            haptics.light();
            Alert.alert(t('modal.comingSoon'), t('profile.privacyMessage'));
          }}
        />

        <List.Item
          title={t('modal.helpSupport')}
          titleStyle={styles.settingLabel}
          left={props => <List.Icon {...props} icon="help-circle" color={theme.colors.onSurface} />}
          right={props => <List.Icon {...props} icon="chevron-right" color={theme.colors.onSurfaceVariant} />}
          onPress={() => {
            haptics.light();
            Alert.alert(t('modal.comingSoon'), t('profile.helpMessage'));
          }}
        />

        <Divider style={styles.divider} />

        {/* Sign Out */}
        <List.Item
          title={t('modal.signOut')}
          left={props => <List.Icon {...props} icon="logout" color={theme.colors.error} />}
          onPress={confirmSignOut}
          titleStyle={{ color: theme.colors.error }}
        />
      </View>

      {/* Sign Out Confirmation Dialog */}
      <Dialog visible={showSignOutDialog} onDismiss={() => setShowSignOutDialog(false)}>
        <Dialog.Title>{t('modal.signOut')}</Dialog.Title>
        <Dialog.Content>
          <Text variant="bodyMedium">{t('profile.signOutConfirmation')}</Text>
        </Dialog.Content>
        <Dialog.Actions>
          <Button onPress={() => setShowSignOutDialog(false)}>{t('modal.cancel')}</Button>
          <Button
            onPress={() => {
              setShowSignOutDialog(false);
              handleSignOut();
            }}
            textColor={theme.colors.error}
          >
            {t('modal.signOut')}
          </Button>
        </Dialog.Actions>
      </Dialog>

      {/* Email Verification Modal */}
      <GenericOtpModal
        isVisible={verificationState.isVisible}
        onClose={() => {
          hideVerificationModal();
          handleVerificationCancel();
        }}
        title={t('profile.verifyEmail')}
        subtitle={t('profile.verifyEmailInstructions', { email: verificationState.emailAddress })}
        onVerify={handleVerification}
        onResend={handleResend}
        isLoading={verificationState.isLoading}
        error={verificationState.error}
        codeLength={6}
        resendTimeoutSeconds={60}
        maxAttempts={3}
      />

    </BottomSheetView>
  );
};

const getStyles = (theme: any) => ({
  container: {
    flex: 1,
  },
  userSection: {
    padding: 16,
    alignItems: 'center' as const,
    gap: 12,
  },
  userInfo: {
    alignItems: 'center' as const,
  },
  avatarWrapper: {
    position: 'relative' as const,
    width: 96, // 80 + 16 (badge extends 16px beyond)
    height: 96, // 80 + 16 (badge extends 16px beyond)
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  avatarContainer: {
    alignItems: 'center' as const,
    position: 'relative' as const,
    width: 80,
    height: 80,
  },
  avatarOverlay: {
    position: 'absolute' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    backgroundColor: theme.colors.backdrop,
    borderRadius: 40,
  },
  userName: {
    color: theme.colors.onSurface,
  },
  userEmail: {
    color: theme.colors.onSurfaceVariant,
    fontSize: 14,
  },
  emailChip: {
    backgroundColor: 'transparent',
    minHeight: 36,
  },
  emailChipsContainer: {
    gap: 4,
    marginTop: 8,
    width: '100%' as const,
  },
  emailRowContainer: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    width: '100%' as const,
    paddingVertical: 4,
    paddingHorizontal: 0,
    marginBottom: 2,
    gap: 8,
  },
  primaryStarIcon: {
    margin: 0,
    width: 20,
    height: 20,
  },
  emailText: {
    fontSize: 15,
    flex: 1,
  },
  statusIconInline: {
    margin: 0,
    width: 20,
    height: 20,
  },
  emailMenuButtonCompact: {
    margin: 0,
    width: 24,
    height: 24,
  },
  verificationIcon: {
    marginLeft: 4,
  },
  statusIcon: {
    margin: 0,
    width: 24,
    height: 24,
  },
  primaryChip: {
    backgroundColor: 'transparent',
    height: 24,
    marginLeft: 4,
  },
  primaryChipText: {
    fontSize: 10,
    lineHeight: 12,
    fontWeight: '600' as const,
  },
  addEmailButtonCompact: {
    borderWidth: 1,
    borderStyle: 'dashed' as const,
    borderRadius: 6,
    paddingVertical: 6,
    paddingHorizontal: 12,
    marginTop: 4,
    backgroundColor: 'transparent',
  },
  addEmailButtonContentCompact: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    gap: 6,
  },
  addEmailIconCompact: {
    margin: 0,
    width: 20,
    height: 20,
  },
  addEmailTextCompact: {
    fontSize: 13,
    fontWeight: '500' as const,
  },
  divider: {
    marginVertical: 8,
  },
  settingsSection: {
    flex: 1,
    padding: 16,
  },
  sectionTitle: {
    color: theme.colors.onSurface,
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
    paddingVertical: 12,
    paddingHorizontal: 16,
    height: 56,
  },
  settingLeft: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 12,
  },
  settingLabel: {
    color: theme.colors.onSurface,
  },
  settingValue: {
    color: theme.colors.onSurfaceVariant,
  },
  nameDisplayContainer: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    gap: 8,
  },
  nameEditContainer: {
    alignItems: 'center' as const,
    gap: 12,
    width: '100%' as const,
    position: 'relative' as const,
  },
  nameInputsContainer: {
    flexDirection: 'row' as const,
    gap: 8,
    width: '100%' as const,
  },
  nameEditActions: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 4,
  },
  nameUpdateLoader: {
    position: 'absolute' as const,
    top: 0,
    right: -40,
  },
  editNameButton: {
    margin: 0,
    width: 32,
    height: 32,
  },
  addEmailEditContainer: {
    marginTop: 4,
    gap: 12,
  },
  addEmailActions: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    gap: 4,
  },
  addEmailLoader: {
    position: 'absolute' as const,
    top: 0,
    right: -40,
  },
  editBadge: {
    position: 'absolute' as const,
    bottom: 6,
    right: 6,
    borderRadius: 16,
    elevation: 3,
  },
  editBadgeButton: {
    margin: 0,
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  editBadgeIconButton: {
    margin: 0,
    width: 32,
    height: 32,
  },
});

// Wrapper component with EmailVerificationProvider
export const ProfileModalContent: React.FC<ProfileModalContentProps> = ({ onDismiss }) => {
  return (
    <EmailVerificationProvider>
      <ProfileModalContentInner onDismiss={onDismiss} />
    </EmailVerificationProvider>
  );
};;
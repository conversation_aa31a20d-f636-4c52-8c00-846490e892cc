import { useState, useEffect } from 'react';
import { View, Alert } from 'react-native';
import { Button, TextInput, Text, TouchableRipple, List, Surface, HelperText } from 'react-native-paper';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { useSignIn, useSignUp, useSSO } from '@clerk/clerk-expo';
import { useRouter } from 'expo-router';
import * as AuthSession from 'expo-auth-session';
import * as WebBrowser from 'expo-web-browser';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/shared/hooks/use-theme';
import { useButtonStyles, useButtonContentStyles } from '@/shared/styles/button-styles';
import { haptics } from '@/shared/utils/haptics';
import { GradientLogo } from '@/shared/components/branding/gradient-logo';
import PasswordInput from '@/shared/components/ui/password-input';
import { GenericOtpModal } from '@/shared/components/modals';
import { handleAuthError, logAuthError } from '@/shared/utils/auth-errors';
import { useLocalCredentials } from '@clerk/clerk-expo/local-credentials';

// Handle any pending authentication sessions
WebBrowser.maybeCompleteAuthSession();

export const useWarmUpBrowser = () => {
  useEffect(() => {
    // Preloads the browser for Android devices to reduce authentication load time
    // See: https://docs.expo.dev/guides/authentication/#improving-user-experience
    void WebBrowser.warmUpAsync();
    return () => {
      // Cleanup: closes browser when component unmounts
      void WebBrowser.coolDownAsync();
    };
  }, []);
};

type AuthMode = 'sign-in' | 'sign-up';
// Verification mode now only needed for sign-up (email sign-in moved to forgot password)

// Animation delay constants
const ANIMATION_DELAYS = {
  HEADER: 100,
  BIOMETRIC: 200,
  SOCIAL: 300,
  EMAIL_FORM: 200,
  PROGRESSIVE_DISCLOSURE: 400,
  SUBMIT_BUTTON: 500,
  TERTIARY_ACTIONS: 600,
  MODE_SWITCH: 700
} as const;

interface AuthFormProps {
  mode: AuthMode;
}

const AuthHeader = ({ mode }: AuthFormProps) => {
  const { theme, spacing } = useTheme();
  const { t } = useTranslation(['auth', 'common']);

  return (
    <Animated.View
      entering={FadeInDown.delay(ANIMATION_DELAYS.HEADER)}
      style={{ alignItems: 'center', marginBottom: spacing.xl }}
    >
      <GradientLogo width={48} height={48} />

      <Text
        variant="headlineSmall"
        style={{
          color: theme.colors.onSurface,
          marginTop: spacing.md,
          textAlign: 'center',
        }}
        accessibilityRole="header"
        accessibilityLevel={1}
      >
        {mode === 'sign-in' ? t('auth:signIn.title') : t('auth:signUp.title')}
      </Text>

      <Text
        variant="bodyMedium"
        style={{
          color: theme.colors.onSurfaceVariant,
          marginTop: spacing.xs,
          textAlign: 'center',
          maxWidth: 280,
        }}
      >
        {mode === 'sign-in' ? t('auth:signIn.subtitle') : t('auth:signUp.subtitle')}
      </Text>
    </Animated.View>
  );
};

/**
 * A unified form component that handles both sign-in and sign-up flows.
 * This follows the DRY principle by reusing form logic and UI for both auth modes.
 * 
 * This component is ONLY used for authentication and belongs in the auth feature.
 */
export const AuthForm = ({ mode }: AuthFormProps) => {
  // Form state
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [isMicrosoftLoading, setIsMicrosoftLoading] = useState(false);

  
  // Email verification modal state (simplified - only for sign-up)
  const [showEmailVerificationModal, setShowEmailVerificationModal] = useState(false);
  const [verificationEmail, setVerificationEmail] = useState('');
  const [verificationError, setVerificationError] = useState('');

  // Smart authentication method state (removed progressive disclosure)
  // Email OTP functionality moved to forgot password screen
  const [emailValidation, setEmailValidation] = useState({ isValid: true, message: '' });


  // Hooks
  const { signIn, setActive } = useSignIn();
  const { signUp, setActive: setActiveSignUp } = useSignUp();
  const { startSSOFlow } = useSSO();
  const {
    hasCredentials,
    userOwnsCredentials,
    biometricType,
    setCredentials,
    clearCredentials,
    authenticate
  } = useLocalCredentials();
  const router = useRouter();
  const { theme, spacing, borderRadius } = useTheme();
  const buttonStyles = useButtonStyles();
  const buttonContentStyles = useButtonContentStyles();
  const { t } = useTranslation(['auth', 'common']);

  // Preload browser for better OAuth performance
  useWarmUpBrowser();

  // Smart authentication method prioritization implemented in UI layout

  // Navigation helper function for consistent auth success handling
  const handleAuthSuccess = async (
    sessionId: string,
    setActiveFunction: any
  ) => {
    await setActiveFunction({
      session: sessionId,
      navigate: async ({ session }: { session: any }) => {
        if (session?.currentTask) {
          console.log('🔐 Session has pending tasks:', session.currentTask);
          return;
        }
        router.replace('/(drawer)/(tabs)');
      },
    });
  };

  // Form validation
  const isValidEmail = (email: string) => {
    return /\S+@\S+\.\S+/.test(email);
  };

  const MIN_PASSWORD_LENGTH = 6;
  const canSubmit = mode === 'sign-in'
    ? email.length > 0 && password.length >= MIN_PASSWORD_LENGTH && isValidEmail(email)
    : email.length > 0 && password.length >= MIN_PASSWORD_LENGTH && isValidEmail(email) && firstName.trim().length > 0 && lastName.trim().length > 0;

  // Simple email validation
  const validateEmail = (emailValue: string) => {
    const isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailValue);

    setEmailValidation({
      isValid: isValid || !emailValue,
      message: emailValue && !isValid ? t('auth:form.email.formatError') : ''
    });
  };

  // Enhanced biometric sign-in handler with haptics
  const onBiometricSignInPress = async () => {
    haptics.light();
    setIsLoading(true);
    try {
      const signInAttempt = await authenticate();

      if (signInAttempt.status === 'complete') {
        haptics.success();
        await handleAuthSuccess(signInAttempt.createdSessionId!, setActive);
      }
    } catch (err: any) {
      haptics.error();
      logAuthError(err, 'Biometric Sign In');
      const authError = handleAuthError(err);
      Alert.alert(t('auth:signIn.error.title'), authError.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Enhanced sign in handler with haptics and smart credential saving
  const onSignInPress = async () => {
    if (!signIn) return;

    haptics.light();
    setIsLoading(true);
    try {
      const result = await signIn.create({
        identifier: email,
        password,
      });

      if (result.status === 'complete') {
        // Save credentials for biometric authentication if available
        if (biometricType && !hasCredentials) {
          try {
            await setCredentials({
              identifier: email,
              password,
            });
          } catch (credentialError) {
            // Don't block sign-in if credential saving fails
            console.log('Failed to save credentials for biometric auth:', credentialError);
          }
        }

        haptics.success();
        await handleAuthSuccess(result.createdSessionId!, setActive);
      }
    } catch (err: any) {
      haptics.error();
      logAuthError(err, 'Sign In');
      const authError = handleAuthError(err);
      Alert.alert(t('auth:signIn.error.title'), authError.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Email OTP sign-in moved to forgot password screen

  // Enhanced sign up handler with haptics
  const onSignUpPress = async () => {
    if (!signUp) return;

    haptics.light();
    setIsLoading(true);
    try {
      await signUp.create({
        emailAddress: email,
        password,
        firstName: firstName.trim(),
        lastName: lastName.trim(),
      });

      await signUp.prepareEmailAddressVerification({ strategy: 'email_code' });

      haptics.success();
      // Show OTP modal for sign-up verification
      setVerificationEmail(email);
      setShowEmailVerificationModal(true);
    } catch (err: any) {
      haptics.error();
      logAuthError(err, 'Sign Up');
      const authError = handleAuthError(err);
      Alert.alert(t('auth:signUp.error.title'), authError.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Sign-up email verification handler (clean separation)
  const handleSignUpEmailVerification = async (code: string) => {
    try {
      const completeSignUp = await signUp!.attemptEmailAddressVerification({ code });

      if (completeSignUp.status === 'complete') {
        await handleAuthSuccess(
          completeSignUp.createdSessionId!,
          setActiveSignUp
        );
        setShowEmailVerificationModal(false);
      } else {
        throw new Error(`Sign-up verification incomplete. Status: ${completeSignUp.status}`);
      }
    } catch (err: any) {
      logAuthError(err, 'Sign-Up Email Verification');
      const authError = handleAuthError(err);
      setVerificationError(authError.message);
      throw new Error(authError.message);
    }
  };

  // Main verification handler (now only for sign-up)
  const handleEmailVerification = async (code: string) => {
    return handleSignUpEmailVerification(code);
  };

  // Email verification resend handler (now only for sign-up)
  const handleResendVerification = async () => {
    try {
      await signUp!.prepareEmailAddressVerification({ strategy: 'email_code' });
      setVerificationError(''); // Clear any previous errors
    } catch (err: any) {
      logAuthError(err, 'Email Verification Resend');
      const authError = handleAuthError(err);
      setVerificationError(authError.message);
      throw new Error(authError.message);
    }
  };

  const onGoogleSignInPress = async () => {
    if (!startSSOFlow) return;

    haptics.light();
    setIsGoogleLoading(true);
    try {
      console.log('🔐 Starting OAuth flow with useSSO and Expo redirect...');

      const redirectUrl = AuthSession.makeRedirectUri({
        scheme: 'aromachat',
        path: 'sso-callback'
      });
      console.log('🔐 OAuth redirect URI:', redirectUrl);

      const { createdSessionId, signUp, setActive } = await startSSOFlow({
        strategy: 'oauth_google',
        redirectUrl,
      });

      console.log('🔐 OAuth handshake complete. Now inspecting results...');

      if (createdSessionId) {
        console.log('✅ OAuth SUCCESS: Session created directly. Setting session active.');
        haptics.success();
        await handleAuthSuccess(createdSessionId, setActive!);
      } else {
        console.log('🟡 OAuth PENDING: Additional steps may be required.');
        if (signUp && signUp.emailAddress === null) {
          console.log('❌ OAuth FAILED: No email provided by Google. Cannot create user.');
          haptics.error();
          Alert.alert(
            t('auth:oauth.google.error.title'),
            'Google sign-in succeeded but no email was provided. Please check your Google account settings or try another method.'
          );
        }
      }
    } catch (err: any) {
      console.log('❌ OAuth FAILED: The handshake process threw an error.');
      haptics.error();
      logAuthError(err, 'OAuth');
      const authError = handleAuthError(err);
      Alert.alert(t('auth:oauth.google.error.title'), authError.message);
    } finally {
      setIsGoogleLoading(false);
    }
  };

  const onMicrosoftSignInPress = async () => {
    if (!startSSOFlow) return;

    haptics.light();
    setIsMicrosoftLoading(true);
    try {
      console.log('🔐 Starting Microsoft OAuth flow with useSSO and Expo redirect...');

      const redirectUrl = AuthSession.makeRedirectUri({
        scheme: 'aromachat',
        path: 'sso-callback'
      });
      console.log('🔐 Microsoft OAuth redirect URI:', redirectUrl);

      const { createdSessionId, signUp, setActive } = await startSSOFlow({
        strategy: 'oauth_microsoft',
        redirectUrl,
      });

      console.log('🔐 Microsoft OAuth handshake complete. Now inspecting results...');

      if (createdSessionId) {
        console.log('✅ Microsoft OAuth SUCCESS: Session created directly. Setting session active.');
        haptics.success();
        await handleAuthSuccess(createdSessionId, setActive!);
      } else {
        console.log('🟡 Microsoft OAuth PENDING: Additional steps may be required.');
        if (signUp && signUp.emailAddress === null) {
          console.log('❌ Microsoft OAuth FAILED: No email provided by Microsoft. Cannot create user.');
          haptics.error();
          Alert.alert(
            t('auth:oauth.microsoft.error.title'),
            'Microsoft sign-in succeeded but no email was provided. Please check your Microsoft account settings or try another method.'
          );
        }
      }
    } catch (err: any) {
      console.log('❌ Microsoft OAuth FAILED: The handshake process threw an error.');
      haptics.error();
      logAuthError(err, 'Microsoft OAuth');
      const authError = handleAuthError(err);
      Alert.alert(t('auth:oauth.microsoft.error.title'), authError.message);
    } finally {
      setIsMicrosoftLoading(false);
    }
  };

  const handleSubmit = mode === 'sign-in' ? onSignInPress : onSignUpPress;

  const handlePressSubmit = () => {
    if (!canSubmit) {
      haptics.light();
      return;
    }
    handleSubmit();
  };

  return (
    <>
      <View
      style={{ flex: 1 }}
      accessibilityLabel={t('auth:form.accessibilityLabel')}
    >
      {/* Mobile-First Layout: Top section for secondary content */}
      <View style={{ flex: 1, paddingHorizontal: spacing.lg, paddingTop: spacing.xl }}>
        <AuthHeader mode={mode} />

        {/* Primary Authentication Methods - Thumb Zone Optimized */}
        <List.Section>
          {/* Biometric Authentication - Highest Priority */}
          {hasCredentials && userOwnsCredentials && biometricType && (
            <Animated.View entering={FadeInDown.delay(ANIMATION_DELAYS.BIOMETRIC)} style={{ marginBottom: spacing.lg }}>
              <Surface
                mode="elevated"
                elevation={1}
                style={{
                  borderRadius: borderRadius.button,
                  overflow: 'hidden'
                }}
              >
                <TouchableRipple
                  onPress={onBiometricSignInPress}
                  disabled={isLoading}
                  style={{
                    padding: spacing.lg,
                    minHeight: 56,
                    justifyContent: 'center',
                    alignItems: 'center'
                  }}
                  accessibilityRole="button"
                  accessibilityLabel={biometricType === 'face-recognition'
                    ? t('auth:signIn.biometric.faceId.accessibility')
                    : t('auth:signIn.biometric.fingerprint.accessibility')
                  }
                >
                  <View style={{ flexDirection: 'row', alignItems: 'center', gap: spacing.md }}>
                    <Text variant="titleMedium" style={{ color: theme.colors.primary }}>
                      {biometricType === 'face-recognition' ? '🫱' : '👆'}
                    </Text>
                    <Text variant="labelLarge" style={{ color: theme.colors.primary }}>
                      {biometricType === 'face-recognition'
                        ? t('auth:signIn.biometric.faceId', 'Sign in with Face ID')
                        : t('auth:signIn.biometric.fingerprint', 'Sign in with Fingerprint')
                      }
                    </Text>
                  </View>
                </TouchableRipple>
              </Surface>
            </Animated.View>
          )}

          {/* Social Authentication - High Priority */}
          <Animated.View entering={FadeInDown.delay(ANIMATION_DELAYS.SOCIAL)} style={{ gap: spacing.sm }}>
            <Button
              mode="outlined"
              onPress={onGoogleSignInPress}
              disabled={isLoading || isGoogleLoading || isMicrosoftLoading}
              loading={isGoogleLoading}
              icon="google"
              style={[buttonStyles.large, {
                borderColor: theme.colors.outline,
                borderRadius: borderRadius.button
              }]}
              contentStyle={buttonContentStyles.large}
              accessibilityRole="button"
              accessibilityLabel={mode === 'sign-in'
                ? t('auth:signIn.googleSignIn.accessibility')
                : t('auth:signUp.googleSignUp.accessibility')
              }
            >
              {mode === 'sign-in' ? t('auth:signIn.googleSignIn') : t('auth:signUp.googleSignUp')}
            </Button>

            <Button
              mode="outlined"
              onPress={onMicrosoftSignInPress}
              disabled={isLoading || isGoogleLoading || isMicrosoftLoading}
              loading={isMicrosoftLoading}
              icon="microsoft"
              style={[buttonStyles.large, {
                borderColor: theme.colors.outline,
                borderRadius: borderRadius.button
              }]}
              contentStyle={buttonContentStyles.large}
              accessibilityRole="button"
              accessibilityLabel={mode === 'sign-in'
                ? t('auth:signIn.microsoftSignIn.accessibility')
                : t('auth:signUp.microsoftSignUp.accessibility')
              }
            >
              {mode === 'sign-in' ? t('auth:signIn.microsoftSignIn') : t('auth:signUp.microsoftSignUp')}
            </Button>
          </Animated.View>
        </List.Section>

        {/* Clean Divider */}
        <Animated.View entering={FadeInDown.delay(ANIMATION_DELAYS.EMAIL_FORM)} style={{ alignItems: 'center', marginVertical: spacing.lg }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', width: '100%' }}>
            <View style={{ flex: 1, height: 1, backgroundColor: theme.colors.outline }} />
            <Text variant="bodyMedium" style={{
              marginHorizontal: spacing.md,
              color: theme.colors.onSurfaceVariant
            }}>
              ou
            </Text>
            <View style={{ flex: 1, height: 1, backgroundColor: theme.colors.outline }} />
          </View>
        </Animated.View>

        {/* Email/Password Form - Always Visible */}
        <Animated.View entering={FadeInDown.delay(ANIMATION_DELAYS.EMAIL_FORM)} style={{ gap: spacing.sm }}>
          {/* Sign-up specific fields - First Name and Last Name */}
          {mode === 'sign-up' && (
            <View style={{ flexDirection: 'row', gap: spacing.sm }}>
              <TextInput
                label={t('auth:form.firstName.label') || 'First Name'}
                value={firstName}
                onChangeText={setFirstName}
                autoCapitalize="words"
                autoComplete="given-name"
                textContentType="givenName"
                mode="outlined"
                style={{ backgroundColor: theme.colors.surface, flex: 1 }}
                disabled={isLoading}
                accessibilityRole="text"
                accessibilityLabel={t('auth:form.firstName.accessibilityLabel')}
                returnKeyType="next"
              />
              <TextInput
                label={t('auth:form.lastName.label') || 'Last Name'}
                value={lastName}
                onChangeText={setLastName}
                autoCapitalize="words"
                autoComplete="family-name"
                textContentType="familyName"
                mode="outlined"
                style={{ backgroundColor: theme.colors.surface, flex: 1 }}
                disabled={isLoading}
                accessibilityRole="text"
                accessibilityLabel={t('auth:form.lastName.accessibilityLabel')}
                returnKeyType="next"
              />
            </View>
          )}

          {/* Smart Email Input with Validation */}
          <TextInput
            label={t('auth:form.email.label')}
            value={email}
            onChangeText={setEmail}
            onBlur={() => validateEmail(email)}
            keyboardType="email-address"
            autoCapitalize="none"
            autoComplete="email"
            textContentType="emailAddress"
            mode="outlined"
            style={{ backgroundColor: theme.colors.surface }}
            disabled={isLoading}
            error={!emailValidation.isValid && email.length > 0}
            accessibilityRole="text"
            accessibilityLabel={t('auth:form.email.accessibilityLabel')}
            accessibilityHint={t('auth:form.email.hint')}
            returnKeyType="next"
          />
          {emailValidation.message && (
            <HelperText type="error" visible={!emailValidation.isValid}>
              {emailValidation.message}
            </HelperText>
          )}

          {/* Password Input - Always Visible */}
          <PasswordInput
            label={t('auth:form.password.label')}
            value={password}
            onChangeText={setPassword}
            secureTextEntry
            autoCapitalize="none"
            autoComplete={mode === 'sign-in' ? 'password' : 'new-password'}
            textContentType={mode === 'sign-in' ? 'password' : 'newPassword'}
            mode="outlined"
            style={{ backgroundColor: theme.colors.surface }}
            disabled={isLoading}
            returnKeyType="done"
            onSubmitEditing={canSubmit ? handleSubmit : undefined}
            accessibilityRole="text"
            accessibilityLabel={t('auth:form.password.accessibilityLabel')}
          />
        </Animated.View>
      </View>

      {/* Bottom Section - Thumb Zone Optimized for Primary Actions */}
      <View style={{
        paddingHorizontal: spacing.lg,
        paddingBottom: spacing.xl,
        paddingTop: spacing.lg,
        gap: spacing.md
      }}>
        {/* Primary Action Area - Submit Button + Forgot Password */}
        <Animated.View entering={FadeInDown.delay(ANIMATION_DELAYS.SUBMIT_BUTTON)} style={{ gap: spacing.xs }}>
          <Button
            mode="contained"
            onPress={handlePressSubmit}
            disabled={isLoading}
            loading={isLoading}
            style={[
              buttonStyles.large,
              {
                borderRadius: borderRadius.button,
                opacity: canSubmit ? 1 : 0.9,
              },
            ]}
            contentStyle={buttonContentStyles.large}
            accessibilityRole="button"
            accessibilityLabel={
              canSubmit
                ? mode === 'sign-in'
                  ? t('auth:signIn.submit.accessibility')
                  : t('auth:signUp.submit.accessibility')
                : t('auth:form.completeFieldsAccessibility')
            }
          >
            {mode === 'sign-in' ? t('auth:signIn.button') : t('auth:signUp.button')}
          </Button>

          {/* Forgot Password - Part of Primary Action Area */}
          {mode === 'sign-in' && (
            <View style={{ alignItems: 'center' }}>
              <TouchableRipple
                style={{
                  paddingVertical: spacing.sm,
                  paddingHorizontal: spacing.md,
                  borderRadius: borderRadius.button,
                  minHeight: 44,
                  justifyContent: 'center'
                }}
                onPress={() => router.push('/(auth)/forgot-password')}
                disabled={isLoading}
                accessibilityRole="button"
                accessibilityLabel={t('auth:forgotPassword.accessibility')}
              >
                <Text variant="labelLarge" style={{
                  color: theme.colors.primary,
                  textAlign: 'center'
                }}>
                  {t('auth:forgotPassword.link')}
                </Text>
              </TouchableRipple>
            </View>
          )}
        </Animated.View>

        {/* Tertiary Actions - Clear Credentials Only */}
        {mode === 'sign-in' && hasCredentials && userOwnsCredentials && (
          <Animated.View entering={FadeInDown.delay(ANIMATION_DELAYS.TERTIARY_ACTIONS)} style={{ alignItems: 'center' }}>
            <TouchableRipple
              style={{
                paddingVertical: spacing.sm,
                paddingHorizontal: spacing.md,
                borderRadius: borderRadius.button,
                minHeight: 44,
                justifyContent: 'center'
              }}
              onPress={async () => {
                try {
                  await clearCredentials();
                  haptics.success();
                  Alert.alert(
                    t('auth:signIn.biometric.cleared.title', 'Credentials Cleared'),
                    t('auth:signIn.biometric.cleared.message', 'Stored biometric credentials have been removed.')
                  );
                } catch {
                  haptics.error();
                  Alert.alert(
                    t('auth:signIn.error.title'),
                    t('auth:signIn.biometric.clearError', 'Failed to clear stored credentials.')
                  );
                }
              }}
              disabled={isLoading}
              accessibilityRole="button"
              accessibilityLabel={t('auth:signIn.biometric.clearCredentials.accessibility')}
            >
              <Text variant="labelLarge" style={{
                color: theme.colors.error,
                textAlign: 'center'
              }}>
                {t('auth:signIn.biometric.clearCredentials', 'Clear Biometric Sign-in')}
              </Text>
            </TouchableRipple>
          </Animated.View>
        )}

        {/* Mode Switch */}
        <Animated.View entering={FadeInDown.delay(ANIMATION_DELAYS.MODE_SWITCH)} style={{ alignItems: 'center', marginTop: spacing.xl }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center', flexWrap: 'wrap' }}>
            <Text variant="bodyMedium" style={{
              color: theme.colors.onSurfaceVariant,
              marginRight: spacing.xs
            }}>
              {mode === 'sign-in'
                ? t('auth:signIn.switchPrompt')
                : t('auth:signUp.switchPrompt')
              }
            </Text>

            <TouchableRipple
              style={{
                paddingVertical: spacing.sm,
                paddingHorizontal: spacing.md,
                borderRadius: borderRadius.button,
                minHeight: 44,
                justifyContent: 'center'
              }}
              onPress={() => router.push(mode === 'sign-in' ? '/(auth)/sign-up' : '/(auth)/sign-in')}
              disabled={isLoading}
              accessibilityRole="button"
              accessibilityLabel={mode === 'sign-in'
                ? t('auth:signUp.link.accessibility')
                : t('auth:signIn.link.accessibility')
              }
            >
              <Text variant="labelLarge" style={{
                color: theme.colors.primary
              }}>
                {mode === 'sign-in' ? t('auth:signUp.link') : t('auth:signIn.link')}
              </Text>
            </TouchableRipple>
          </View>
        </Animated.View>
      </View>
    </View>

    {/* Email Verification Modal - Enhanced */}
    <GenericOtpModal
      isVisible={showEmailVerificationModal}
      onClose={() => {
        setShowEmailVerificationModal(false);
        setVerificationError('');
      }}
      title={t('auth:verification.title') || 'Verify Your Email'}
      subtitle={t('auth:verification.subtitle', { email: verificationEmail }) || `Enter the verification code sent to ${verificationEmail}`}
      onVerify={handleEmailVerification}
      onResend={handleResendVerification}
      isLoading={isLoading || isGoogleLoading}
      error={verificationError}
    />
    </>
  );
};

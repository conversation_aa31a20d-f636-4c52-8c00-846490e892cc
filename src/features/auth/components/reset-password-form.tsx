import React, { useState, useCallback } from 'react';
import { View } from 'react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { Button, Text, HelperText } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/shared/hooks/use-theme';
import { useButtonStyles, useButtonContentStyles } from '@/shared/styles/button-styles';
import { haptics } from '@/shared/utils/haptics';
import { GradientLogo } from '@/shared/components/branding/gradient-logo';
import PasswordInput from '@/shared/components/ui/password-input';
import { usePasswordResetContext } from '../context/password-reset.context';

// Animation delay constants - consistent with other auth forms
const ANIMATION_DELAYS = {
  HEADER: 100,
  FORM_FIELDS: 200,
  SUBMIT_BUTTON: 300
} as const;

export const ResetPasswordForm: React.FC = () => {
  const { state, completePasswordReset } = usePasswordResetContext();

  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  const { t } = useTranslation('auth');
  const { theme, spacing, borderRadius } = useTheme();
  const buttonStyles = useButtonStyles();
  const buttonContentStyles = useButtonContentStyles();

  const isFormValid = newPassword.length > 0 && confirmPassword.length > 0;

  const handleSubmit = useCallback(() => {
    if (!isFormValid) {
      haptics.light();
      return;
    }
    completePasswordReset(newPassword, confirmPassword);
  }, [completePasswordReset, newPassword, confirmPassword, isFormValid]);

  return (
    <View style={{ flex: 1 }}>
      {/* Top section */}
      <View style={{ flex: 1, paddingHorizontal: spacing.lg, paddingTop: spacing.xl }}>
        <Animated.View entering={FadeInDown.delay(ANIMATION_DELAYS.HEADER)} style={{
          alignItems: 'center',
          marginBottom: spacing.xl
        }}>
        <GradientLogo width={48} height={48} />

        <Text
          variant="headlineSmall"
          style={{
            color: theme.colors.onSurface,
            marginTop: spacing.md,
            textAlign: 'center'
          }}
        >
          {t('forgotPassword.createNewPassword')}
        </Text>

        <Text
          variant="bodyMedium"
          style={{
            color: theme.colors.onSurfaceVariant,
            marginTop: spacing.xs,
            textAlign: 'center',
            maxWidth: 280
          }}
        >
          {t('forgotPassword.passwordInstructions')}
        </Text>
        </Animated.View>

      <Animated.View entering={FadeInDown.delay(ANIMATION_DELAYS.FORM_FIELDS)} style={{ gap: spacing.sm }}>
        <PasswordInput
          label={t('forgotPassword.newPassword')}
          value={newPassword}
          onChangeText={setNewPassword}
          secureTextEntry
          autoCapitalize="none"
          mode="outlined"
          style={{ backgroundColor: theme.colors.surface }}
          outlineColor={theme.colors.outline}
          activeOutlineColor={theme.colors.primary}
          disabled={state.isLoading}
          error={!!state.errors.newPassword}
        />
        {!!state.errors.newPassword &&
          <HelperText type="error" visible={!!state.errors.newPassword}>
            {state.errors.newPassword}
          </HelperText>
        }
        <PasswordInput
          label={t('forgotPassword.confirmPassword')}
          value={confirmPassword}
          onChangeText={setConfirmPassword}
          secureTextEntry
          autoCapitalize="none"
          mode="outlined"
          style={{ backgroundColor: theme.colors.surface }}
          outlineColor={theme.colors.outline}
          activeOutlineColor={theme.colors.primary}
          disabled={state.isLoading}
          error={!!state.errors.confirmPassword || !!state.errors.general}
        />
        {!!state.errors.confirmPassword &&
          <HelperText type="error" visible={!!state.errors.confirmPassword}>
            {state.errors.confirmPassword}
          </HelperText>
        }
        {!!state.errors.general &&
          <HelperText type="error" visible={!!state.errors.general}>
            {state.errors.general}
          </HelperText>
        }
      </Animated.View>
      </View>

      {/* Bottom Section - Submit Button */}
      <View style={{
        paddingHorizontal: spacing.lg,
        paddingBottom: spacing.xl,
        paddingTop: spacing.md
      }}>
        <Animated.View entering={FadeInDown.delay(ANIMATION_DELAYS.SUBMIT_BUTTON)}>
          <Button
            mode="contained"
            onPress={handleSubmit}
            loading={state.isLoading}
            disabled={state.isLoading}
            style={[
              buttonStyles.large, 
              {
                borderRadius: borderRadius.button,
                opacity: isFormValid ? 1 : 0.9
              }
            ]}
            contentStyle={buttonContentStyles.large}
          >
            {t('forgotPassword.resetPassword')}
          </Button>
        </Animated.View>
      </View>
    </View>
  );
};


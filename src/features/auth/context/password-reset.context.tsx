import React, { createContext, useContext, ReactNode, useState, useCallback } from 'react';
import { useSignIn } from '@clerk/clerk-expo';
import { useRouter } from 'expo-router';
import { Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import { haptics } from '@/shared/utils/haptics';
// --- FIX: Direct imports to break require cycle ---
import { validateResetPasswordForm, validateEmailForm } from '../utils/password-reset.validators';
import { PasswordResetErrors } from '../types/password-reset.types';

// State managed by the context
interface PasswordResetState {
  email: string;
  isLoading: boolean;
  errors: PasswordResetErrors;
  showOtpModal: boolean;
  otpError: string;
  verifiedCode: string | null; // Store the code after modal verification
}

// Functions provided by the context
// --- FIX: Ty<PERSON> corrected from PasswordReset-context-type ---
interface PasswordResetContextType {
  state: PasswordResetState;
  updateField: (field: keyof PasswordResetState, value: any) => void;
  requestPasswordReset: () => Promise<void>;
  handleOtpVerify: (code: string) => Promise<void>;
  handleOtpResend: () => Promise<void>;
  completePasswordReset: (newPassword: string, confirmPassword: string) => Promise<void>;
  resetState: () => void;
  hideOtpModal: () => void;
}

const initialState: PasswordResetState = {
  email: '',
  isLoading: false,
  errors: {},
  showOtpModal: false,
  otpError: '',
  verifiedCode: null,
};

// --- FIX: Typo corrected from PasswordReset-context-type ---
const PasswordResetContext = createContext<PasswordResetContextType | undefined>(undefined);

export const PasswordResetProvider = ({ children }: { children: ReactNode }) => {
  const [state, setState] = useState<PasswordResetState>(initialState);
  const { signIn, setActive, isLoaded } = useSignIn();
  const router = useRouter();
  const { t } = useTranslation(['auth', 'common']);

  const hideOtpModal = () => setState(prev => ({ 
    ...prev, 
    showOtpModal: false, 
    otpError: '', 
    errors: {},
    email: '', // Clear email when modal is dismissed - user can retry with different email
    isLoading: false, // Clear loading state so user can retry
  }));

  const updateField = useCallback((field: keyof PasswordResetState, value: any) => {
    setState(prev => ({ ...prev, [field]: value, errors: {} }));
  }, []);

  const requestPasswordReset = useCallback(async () => {
    if (!signIn || !isLoaded) return;

    const validation = validateEmailForm(state.email);
    if (!validation.isValid) {
      setState(prev => ({ ...prev, errors: validation.errors }));
      haptics.error();
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, errors: {} }));
    haptics.light();
    console.log(`🔑 Password reset requested for email: ${state.email}`);

    try {
      await signIn.create({
        strategy: 'reset_password_email_code',
        identifier: state.email,
      });
      console.log('🔑 Password reset code sent. Showing OTP modal.');
      setState(prev => ({ ...prev, isLoading: false, showOtpModal: true }));
      haptics.success();
    } catch (err: any) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        errors: { general: err.errors?.[0]?.message || t('auth:forgotPassword.error.sendFailed') },
      }));
      haptics.error();
    }
  }, [signIn, isLoaded, state.email, t]);
  
  // Enhanced OTP verification for generic modal (deferred verification pattern)
  const handleOtpVerify = useCallback(async (code: string) => {
    const EXPECTED_CODE_LENGTH = 6;
    if (code.length !== EXPECTED_CODE_LENGTH || !/^\d+$/.test(code)) {
      setState(prev => ({ ...prev, otpError: t('auth:forgotPassword.error.invalidCode') }));
      haptics.error();
      throw new Error(t('auth:forgotPassword.error.invalidCode'));
    }
    
    console.log(`🔑 OTP code deferred for verification: ${code}`);
    // Store code for later use (deferred verification pattern for password reset)
    setState(prev => ({ ...prev, verifiedCode: code, otpError: '', errors: {}, isLoading: false }));
    hideOtpModal();
    haptics.success();
    router.push('/(auth)/reset-password');
  }, [router, t]);


  const completePasswordReset = useCallback(async (newPassword: string, confirmPassword: string) => {
    if (!signIn || !setActive || !isLoaded || !state.verifiedCode) {
        setState(prev => ({ ...prev, errors: { general: t('auth:forgotPassword.error.generic') } }));
        return;
    };

    const validation = validateResetPasswordForm({
      code: state.verifiedCode,
      newPassword,
      confirmPassword,
    });

    if (!validation.isValid) {
      setState(prev => ({ ...prev, errors: validation.errors }));
      haptics.error();
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, errors: {} }));
    const hasSignIn = !!signIn;
    const hasSetActive = !!setActive;
    const hasVerifiedCode = !!state.verifiedCode;
    console.log('🔑 Completing password reset...', { hasSignIn, hasSetActive, hasVerifiedCode });

    try {
      const result = await signIn.attemptFirstFactor({
        strategy: 'reset_password_email_code',
        code: state.verifiedCode,
        password: newPassword,
      });

      if (result.status === 'complete') {
        await setActive({ session: result.createdSessionId });
        console.log(`🔑 Password reset successful. Session active. ${result.createdSessionId}`);
        haptics.success();
        Alert.alert(
          t('auth:forgotPassword.success.title'),
          t('auth:forgotPassword.success.message'),
          [{ text: t('common:labels.ok'), onPress: () => {
            resetState(); // Reset all state for future flows
            router.replace('/(drawer)/(tabs)');
          } }]
        );
      } else {
        setState(prev => ({ ...prev, isLoading: false, errors: { general: 'Password reset could not be completed.'}}));
      }
    } catch (err: any) {
      console.log('🔑 Password reset failed.', err);
      setState(prev => ({
        ...prev,
        isLoading: false,
        errors: { general: err.errors?.[0]?.message || t('auth:forgotPassword.error.generic') },
      }));
      haptics.error();
    }
  }, [signIn, setActive, isLoaded, state.verifiedCode, t, router]);

  // Enhanced resend handler for generic modal
  const handleOtpResend = useCallback(async () => {
    if (!signIn) {
      throw new Error('SignIn not available');
    }
    
    setState(prev => ({ ...prev, isLoading: true, otpError: '' }));
    
    try {
      // Re-send the code using the same signIn instance
      await signIn.create({
        strategy: 'reset_password_email_code',
        identifier: state.email,
      });
      haptics.success();
      // Success - no need to show alert, generic modal will handle UI feedback
    } catch (err: any) {
      const errorMessage = err.errors?.[0]?.message || t('auth:forgotPassword.error.resendFailed');
      setState(prev => ({ ...prev, otpError: errorMessage }));
      haptics.error();
      throw new Error(errorMessage);
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [signIn, state.email, t]);

  const resetState = useCallback(() => setState(initialState), []);

  return (
    <PasswordResetContext.Provider value={{
      state, updateField, requestPasswordReset, handleOtpVerify, handleOtpResend, completePasswordReset, resetState, hideOtpModal
    }}>
      {children}
    </PasswordResetContext.Provider>
  );
};

export const usePasswordResetContext = () => {
  const context = useContext(PasswordResetContext);
  if (context === undefined) {
    throw new Error('usePasswordResetContext must be used within a PasswordResetProvider');
  }
  return context;
};


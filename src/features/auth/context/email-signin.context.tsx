import React, { createContext, useContext, ReactNode, useState, useCallback } from 'react';
import { useSignIn } from '@clerk/clerk-expo';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { haptics } from '@/shared/utils/haptics';

// State managed by the context
interface EmailSignInState {
  email: string;
  isLoading: boolean;
  errors: { [key: string]: string };
  showOtpModal: boolean;
  otpError: string;
}

// Functions provided by the context
interface EmailSignInContextType {
  state: EmailSignInState;
  updateField: (field: keyof EmailSignInState, value: any) => void;
  requestEmailSignIn: () => Promise<void>;
  handleOtpVerify: (code: string) => Promise<void>;
  handleOtpResend: () => Promise<void>;
  resetState: () => void;
  hideOtpModal: () => void;
}

const initialState: EmailSignInState = {
  email: '',
  isLoading: false,
  errors: {},
  showOtpModal: false,
  otpError: '',
};

const EmailSignInContext = createContext<EmailSignInContextType | undefined>(undefined);

export const EmailSignInProvider = ({ children }: { children: ReactNode }) => {
  const [state, setState] = useState<EmailSignInState>(initialState);
  const { signIn, setActive, isLoaded } = useSignIn();
  const router = useRouter();
  const { t } = useTranslation(['auth', 'common']);

  const hideOtpModal = () => setState(prev => ({
    ...prev,
    showOtpModal: false,
    otpError: '',
    errors: {},
    isLoading: false,
  }));

  const updateField = useCallback((field: keyof EmailSignInState, value: any) => {
    setState(prev => ({ ...prev, [field]: value, errors: {} }));
  }, []);

  // Navigation helper function for consistent auth success handling
  const handleAuthSuccess = useCallback(async (sessionId: string) => {
    await setActive!({
      session: sessionId,
      navigate: async ({ session }: { session: any }) => {
        if (session?.currentTask) {
          console.log('🔐 Session has pending tasks:', session.currentTask);
          return;
        }
        router.replace('/(drawer)/(tabs)');
      },
    });
  }, [setActive, router]);

  // Email validation helper
  const isValidEmail = (email: string) => {
    return /\S+@\S+\.\S+/.test(email);
  };

  const requestEmailSignIn = useCallback(async () => {
    if (!signIn || !isLoaded) return;

    if (!state.email || !isValidEmail(state.email)) {
      setState(prev => ({ ...prev, errors: { email: t('auth:forgotPassword.error.invalidEmail') } }));
      haptics.error();
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, errors: {} }));
    haptics.light();
    console.log(`🔐 Email sign-in requested for: ${state.email}`);

    try {
      // Create sign-in attempt with email only
      const { supportedFirstFactors } = await signIn.create({
        identifier: state.email,
      });

      // Find email code factor
      const emailCodeFactor = supportedFirstFactors?.find(
        (factor: any) => factor.strategy === 'email_code'
      ) as any;

      if (emailCodeFactor) {
        // Prepare email verification
        await signIn.prepareFirstFactor({
          strategy: 'email_code',
          emailAddressId: emailCodeFactor.emailAddressId,
        });

        console.log('🔐 Email sign-in code sent. Showing OTP modal.');
        setState(prev => ({ ...prev, isLoading: false, showOtpModal: true }));
        haptics.success();
      } else {
        throw new Error('Email code sign-in not available for this account');
      }
    } catch (err: any) {
      console.log('🔐 Email sign-in request failed:', err);
      setState(prev => ({
        ...prev,
        isLoading: false,
        errors: { general: err.errors?.[0]?.message || t('auth:emailSignIn.error.sendFailed') },
      }));
      haptics.error();
    }
  }, [signIn, isLoaded, state.email, t, handleAuthSuccess]);

  // Email OTP verification handler
  const handleOtpVerify = useCallback(async (code: string) => {
    const EXPECTED_CODE_LENGTH = 6;
    if (code.length !== EXPECTED_CODE_LENGTH || !/^\d+$/.test(code)) {
      setState(prev => ({ ...prev, otpError: t('auth:forgotPassword.error.invalidCode') }));
      haptics.error();
      throw new Error(t('auth:forgotPassword.error.invalidCode'));
    }

    try {
      console.log(`🔐 Attempting email sign-in with code: ${code}`);
      const signInAttempt = await signIn!.attemptFirstFactor({
        strategy: 'email_code',
        code: code
      });

      if (signInAttempt.status === 'complete') {
        console.log('🔐 Email sign-in successful');
        await handleAuthSuccess(signInAttempt.createdSessionId!);
        setState(prev => ({ ...prev, showOtpModal: false, otpError: '' }));
        haptics.success();
      } else {
        throw new Error(`Email sign-in incomplete. Status: ${signInAttempt.status}`);
      }
    } catch (err: any) {
      console.log('🔐 Email sign-in verification failed:', err);
      const errorMessage = err.errors?.[0]?.message || t('auth:emailSignIn.error.verificationFailed');
      setState(prev => ({ ...prev, otpError: errorMessage }));
      haptics.error();
      throw new Error(errorMessage);
    }
  }, [signIn, setActive, router, t]);

  // Enhanced resend handler
  const handleOtpResend = useCallback(async () => {
    if (!signIn) {
      throw new Error('SignIn not available');
    }

    setState(prev => ({ ...prev, isLoading: true, otpError: '' }));

    try {
      // Get the current sign-in attempt and find the email code factor
      const { supportedFirstFactors } = signIn;
      const emailCodeFactor = supportedFirstFactors?.find(
        (factor: any) => factor.strategy === 'email_code'
      ) as any;

      if (emailCodeFactor) {
        // Re-prepare the email verification
        await signIn.prepareFirstFactor({
          strategy: 'email_code',
          emailAddressId: emailCodeFactor.emailAddressId,
        });
        haptics.success();
        console.log('🔐 Email sign-in code resent successfully');
      } else {
        throw new Error('Email code factor not available');
      }
    } catch (err: any) {
      const errorMessage = err.errors?.[0]?.message || t('auth:emailSignIn.error.resendFailed');
      setState(prev => ({ ...prev, otpError: errorMessage }));
      haptics.error();
      throw new Error(errorMessage);
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [signIn, t]);

  const resetState = useCallback(() => setState(initialState), []);

  return (
    <EmailSignInContext.Provider value={{
      state, updateField, requestEmailSignIn, handleOtpVerify, handleOtpResend, resetState, hideOtpModal
    }}>
      {children}
    </EmailSignInContext.Provider>
  );
};

export const useEmailSignInContext = () => {
  const context = useContext(EmailSignInContext);
  if (context === undefined) {
    throw new Error('useEmailSignInContext must be used within an EmailSignInProvider');
  }
  return context;
};
# AromaCHAT UX/UI Developer Guide v2 - Complete (With File References)

## Overview

This guide establishes the AromaCHAT-specific development patterns for creating consistent, accessible, and performant user interfaces. Every screen follows our established architecture patterns that ensure proper theme integration, internationalization, navigation, and user experience standards.

## Our Philosophy: Content-First, Clean, Mobile-Only, Platform-Aligned

This document is the single source of truth for building user interfaces in the AromaCHAT application. Our goal is to create a premium, modern mobile experience defined by clarity, intentionality, and consistency.

The core philosophy is simple: **content is the interface**. We move away from heavy, container-based layouts ("boxes within boxes") and toward a clean aesthetic where typography, spacing, and subtle surfaces create structure and guide the user. Every decision must serve the content it presents.

This is a **mobile-only** guide. We prioritize perfecting the mobile experience, aligning with native platform conventions for interactions and feel (e.g., haptic feedback, ripple effects) to ensure our app feels responsive and intuitive.

## The 5 Core Principles (Our Golden Cross)

All UI implementation must adhere to these five principles.

1. **Content is the Interface (Deprecate Heavy Containers)**
   Users interact directly with content, not the boxes around it. We avoid using `Card` components as a crutch for layout and interaction. A simple `View` or `Surface` with a `TouchableRipple` is almost always the better, cleaner choice.

2. **Hierarchy Through Typography & Emphasis (Not Boxes)**
   A well-defined type scale is our primary tool for creating visual hierarchy. We use semantic `Text` variants and different `Button` modes to signal importance, eliminating the need for containers to create separation.

3. **The Theme is the Single Source of Truth (No Hardcoded Values)**
   All styling—colors, spacing, fonts, border-radius—**must** be derived from our global theme system via the unified `useTheme()` hook. **The use of hardcoded values in styles is strictly forbidden.** For performance, styles should be created with `StyleSheet.create`, and the `useThemedStyles` hook is provided to ensure this is done correctly and efficiently.

4. **Structure Through Intentional Spacing (Whitespace is a Tool)**
   We use a systematic spacing system to create visual grouping and separation. Generous, consistent whitespace is our "invisible container," guiding the user's eye and reducing cognitive load far more effectively than borders or shadows.

5. **Interactions are Consistent & Accessible (Platform-Aligned Feedback)**
   Every touchable element must provide consistent visual feedback (`TouchableRipple`) and meet accessibility standards. All touch targets **must have a minimum size of 44x44px**.

---

## Core Architecture Components

### ScreenWrapper - Universal Screen Container
**File Location**: `src/components/layout/screen-wrapper.tsx`

**Every screen MUST be wrapped with ScreenWrapper** - this is non-negotiable for maintaining consistency and functionality.

#### Essential Features Provided:
- **Safe Area Management**: Automatic handling of device notches, status bars, and navigation areas
- **Theme Integration**: Consistent background colors and theme-aware styling
- **Navigation Integration**: Conditional AppBar and BottomNavigation rendering
- **Modal Provider**: Instagram-style modal system integration
- **Pull-to-Refresh**: Native refresh control with Material Design 3 theming
- **Keyboard Management**: Animated keyboard handling for chat inputs
- **Scroll Triggers**: Progressive disclosure for enhanced navigation experiences
- **Haptic Feedback**: Integrated haptic responses for user interactions

#### Required Usage Pattern:
```jsx
<ScreenWrapper 
  showAppBar={boolean} 
  showBottomNav={boolean}
  scrollable={true}
  enableRefresh={true}
  refreshing={isRefreshing}
  onRefresh={handleRefresh}
  enableModals={true}
>
  {/* Your screen content */}
</ScreenWrapper>
```

#### Navigation Control:
- **AppBar**: Parent screens control all AppBar props (title, actions, back button)
- **BottomNavigation**: Parent screens provide routes and handle navigation logic
- **ScreenWrapper handles NO navigation logic** - purely presentational

### Theme System - Single Source of Truth
**File Locations**:
- Theme Configuration: `src/styles/material-theme.json`
- Theme Context: `src/shared/contexts/UserPreferencesContext.tsx`
- Theme Hook: `src/shared/hooks/use-theme.ts`

#### Core Principles:
- **Material Design 3 Foundation**: All colors derive from `src/styles/material-theme.json`
- **Never Hardcode Values**: Always use theme values for colors, spacing, typography, etc.
- **Consistent Theme Access**: Use the unified `useTheme()` hook for all theme and design token access.
- **Themed Stylesheets**: Use the `useThemedStyles` hook to create performant, themed `StyleSheet` objects.

#### Implementation Pattern:
```jsx
import { useTheme, useThemedStyles } from '@/shared/hooks/use-theme';

// Example Component
const MyComponent = () => {
  // Access all theme properties and tokens from one hook
  const { colors, spacing, typography } = useTheme();

  // Create themed styles using the dedicated hook for performance
  const styles = useThemedStyles(theme => ({
    container: {
      backgroundColor: theme.colors.surface,
      padding: theme.spacing.lg,
    },
    title: {
      color: theme.colors.onSurface,
      fontSize: theme.typography.titleLarge.fontSize,
    },
  }));

  return (
    <View style={styles.container}>
      <Text style={styles.title}>My Component</Text>
    </View>
  );
};
```

#### Dark/Light Theme Switching:
- **Automatic System Sync**: Respects device system preference by default
- **Manual Override**: Users can force light/dark mode via `useThemeToggle()`
- **Context Integration**: Uses `UserPreferencesContext` for persistent theme state
- **Analytics Integration**: Theme changes tracked via PostHog for user behavior analysis

### Internationalization (i18n) - Namespace Architecture
**File Locations**:
- i18n Configuration: `src/locales/i18n.ts`
- English Translations: `src/locales/en/`
- Portuguese Translations: `src/locales/pt/`

#### Organizational Structure:
- **Namespace-Based**: Each feature has dedicated translation namespace
- **Fallback Strategy**: English (`en`) serves as fallback for missing translations
- **Supported Languages**: English (`en`), Portuguese (`pt`)

#### Implementation Pattern:
```jsx
// Feature-specific translations
const { t } = useTranslation('auth');          // For auth screens
const { t } = useTranslation('createRecipe');  // For recipe wizard
const { t: tCommon } = useTranslation('common'); // For shared terms

// Usage in components
{t('signIn.title')}           // "Sign In" / "Entrar"
{tCommon('labels.email')}     // "Email" / "E-mail"
```

#### Translation File Organization:
- `src/locales/en/auth.json` - Authentication flow translations
- `src/locales/en/common.json` - Shared labels, buttons, errors
- `src/locales/en/create-recipe.json` - Recipe wizard translations
- Mirror structure exists for Portuguese (`pt`)

### Modal System - Instagram-Style Implementation
**File Locations**:
- Modal Provider: `src/shared/contexts/ModalContext.tsx`
- Base Modal: `src/shared/components/modals/BaseModal.tsx`
- Modal Hook: `src/shared/hooks/use-modal.ts`

**CRITICAL**: We use a custom modal system, NOT React Native Paper modals or dialogs.

#### Custom Modal Architecture: (`src/shared/components/modals/`)
- **Provider-Based**: `ModalProvider` manages modal state and animations
- **Gesture-Driven**: Swipe-to-dismiss with velocity-based thresholds
- **Haptic Integration**: WhatsApp/Instagram-style haptic feedback patterns
- **Animation System**: Spring-based animations with background scaling effects
- **Blur Support**: Optional blur backdrop for modern visual hierarchy

#### Implementation Components:
- **ModalProvider**: Context provider for modal state management (`src/shared/contexts/ModalContext.tsx`)
- **BaseModal**: Core modal component with gesture handling and animations (`src/shared/components/modals/BaseModal.tsx`)
- **ScreenWrapper Integration**: Enable via `enableModals={true}` prop

#### Modal Configuration Options:
- **Presentation Styles**: `sheet` (bottom sheet) or `alert` (center dialog)
- **Gesture Controls**: Configurable swipe-to-dismiss behavior
- **Backdrop Options**: Touch-outside-to-dismiss and blur effects
- **Haptic Types**: Contextual haptic feedback (success, error, warning, medium, light)

#### Usage Pattern:
```jsx
import { useModal } from '@/shared/hooks/use-modal';
import { ConfirmationModalContent, BottomSheetContent } from '@/components/modals';

const { showModal, hideModal } = useModal();

// Show alert-style confirmation modal
showModal(
  <ConfirmationModalContent
    title="Delete Account?"
    message="This action cannot be undone. Are you sure?"
    confirmText="Delete"
    cancelText="Cancel"
    onConfirm={() => {
      // Handle confirmation
      hideModal();
    }}
    onCancel={hideModal}
  />,
  { 
    presentationStyle: 'alert', 
    dismissibleWithGesture: false,
    hapticType: 'warning',
    enableBlur: false
  }
);

// Show bottom sheet modal
showModal(
  <BottomSheetContent title="Choose an Option">
    <YourCustomContent />
  </BottomSheetContent>,
  { 
    hapticType: 'light',
    enableBlur: true
  }
);
```

---

## The Approved Component Toolkit

This is the practical guide to implementing our principles using React Native Paper, organized by UI function.

### Section 1: Foundational Layout & Structure

These are the building blocks for every screen.

| Component(s) | Purpose & Golden Pattern | Anti-Pattern | Developer Reference |
| :--- | :--- | :--- | :--- |
| **`List.Section`** + **`List.Item`** | **The "List-as-Screen" Pattern.** Our primary tool for creating structured vertical layouts. `List.Section` provides standardized padding, acting as an "implicit card" to group related `List.Item` rows. | Using a `ScrollView` with a `.map()` of custom `View` components that have manual, inconsistent padding. | React Native Paper Documentation |
| **`Surface`** | **The Modern Container.** Use this when content needs a distinct background or subtle depth. **Golden Pattern**: Use `mode="flat"` for a borderless, shadowless background change. Use `mode="elevated"` with a low `elevation={1}` for a soft, modern shadow. | Using `Card` with its default heavy shadow and border radius. Using high `elevation` values that create visual clutter. | React Native Paper Documentation |
| **`Divider`** | **Clean Separation.** Use this to separate items within a `List.Section` or to divide distinct content areas. The `leftInset` prop is preferred for aligning with list content. | Using a `<View style={{ height: 1, backgroundColor: 'grey' }}>` or the border properties of a `Card`. | React Native Paper Documentation |
| **`Text`** | **Semantic Typography.** Use the `variant` prop (e.g., `headlineSmall`, `bodyLarge`) to establish a clear and consistent information hierarchy. | Manually setting `fontSize` or `fontWeight` in a style object. Using the default `Text` variant for everything. | React Native Paper Documentation |

### Section 2: User Interaction & Input

Components that users directly manipulate.

| Component(s) | Purpose & Golden Pattern | Anti-Pattern | Developer Reference |
| :--- | :--- | :--- | :--- |
| **`TouchableRipple`** | **The Standard for All Interactions.** Wrap any component with `TouchableRipple` to make it interactive. This ensures consistent, platform-aligned feedback on every tap. | Using `TouchableOpacity` or the `onPress` prop of a `Card`, which have inconsistent or less refined feedback. | React Native Paper Documentation |
| **`Button`** | **Clear Action Hierarchy.** Use the `mode` prop to define the visual importance of an action. `contained-tonal` is an excellent default for primary actions in a clean UI. | Using only one button style for all actions. Using color alone to signify importance. | React Native Paper Documentation |
| **`TextInput`** | **Clean & Clear Forms.** Use `mode="outlined"` for a modern, structured feel. Group related inputs within a `List.Section` instead of a `Card`. Use `left`/`right` adornments (`TextInput.Icon`) to save space and keep actions contextual. | Using the default `flat` mode if it doesn't fit the clean aesthetic. Not grouping form fields logically. | React Native Paper Documentation |

### Section 3: Managing Information & Data Display

For screens with complex or dense content.

| Component(s) | Purpose & Golden Pattern | Anti-Pattern | Developer Reference |
| :--- | :--- | :--- | :--- |
| **`List.Accordion`** | **Progressive Disclosure.** Use to hide secondary or lengthy information (e.g., "Advanced Settings"), keeping the default view clean. This is a superior alternative to stacking multiple cards with optional content. | Placing critical, must-see information inside an accordion. Nesting accordions too deeply, creating confusion. | React Native Paper Documentation |
| **`DataTable`** | **Presenting Tabular Data.** The correct semantic and visual choice for dense, spreadsheet-like data. Place it on a simple `Surface` for a clean presentation. | Forcing tabular data into a vertical `List` of `List.Item`s, which is cluttered, hard to scan, and inefficient. | React Native Paper Documentation |

### Section 4: Custom Contextual Overlays & Feedback

Components that appear temporarily over the primary UI using our custom modal system.

| Component(s) | Purpose & Golden Pattern | Anti-Pattern | Developer Reference |
| :--- | :--- | :--- | :--- |
| **Custom Modal (Alert Style)** | **Critical, Blocking Actions.** Use our custom modal system with `ConfirmationModalContent` and `presentationStyle: 'alert'` for actions that require explicit user confirmation (e.g., "Delete this item?"). Modals should be simple, with a clear question and 2-3 actions. | Using React Native Paper `Dialog` component. Overloading modals with too much content. | `src/components/modals/specific-modals.tsx` |
| **Custom Modal (Sheet Style)** | **Focused Tasks & Context Menus.** Use `BottomSheetContent` with our custom modal system for displaying contextual actions, selection lists, or sub-flows. The content *inside* the modal must still follow all Golden Cross principles. | Using React Native Paper `Modal` component. Using full-screen modals when a bottom sheet would suffice. | `src/components/modals/specific-modals.tsx` |
| **`Snackbar`** | **Non-Intrusive Feedback.** The **preferred method** for brief, temporary, and non-blocking feedback about an operation (e.g., "Profile saved," "Message sent"). It informs the user without interrupting their workflow. | Using our custom modal system (which is blocking) to show simple success messages. | React Native Paper Documentation |
| **`Banner`** | **Persistent, High-Priority Information.** More prominent than a `Snackbar`, it remains until dismissed. Use for important, non-blocking information the user needs to be aware of (e.g., "You are currently offline," "Subscription expiring soon"). | Using a `Banner` for low-priority information that should be a `Snackbar`. | React Native Paper Documentation |

---

## Navigation Patterns
**File Locations**:
- Expo Router Configuration: `src/app/_layout.tsx`
- Tab Navigation: `src/app/(tabs)/`
- Navigation Components: `src/components/navigation/`

### Tab Navigation:
- **Expo Router Integration**: File-based routing in `src/app/(tabs)/`
- **Material Design 3**: Bottom navigation follows MD3 specifications
- **Haptic Feedback**: Tab switches include selection haptics
- **Badge Support**: Notification badges for unread states

### Stack Navigation:
- **AppBar Integration**: Back buttons, titles, and action buttons
- **Route Protection**: Authentication-based routing via Clerk
- **Navigation State**: Parent components manage all navigation logic

### Progressive Disclosure:
- **Scroll-Triggered Navigation**: Enhanced bottom navigation appears based on scroll position
- **Dynamic Content**: Contextual actions and information revealed progressively
- **Performance Optimized**: Smooth 60fps animations with spring physics

## Pull-to-Refresh Implementation
**Implementation Location**: Integrated in `src/components/layout/screen-wrapper.tsx`

### Integration Method:
- **ScreenWrapper Native**: Built into ScreenWrapper via `enableRefresh={true}`
- **Theme-Aware**: Refresh indicators use Material Design 3 color scheme
- **Platform Optimized**: Respects iOS and Android platform conventions

### Usage Pattern:
```jsx
<ScreenWrapper
  scrollable={true}
  enableRefresh={true}
  refreshing={isRefreshing}
  onRefresh={handleRefresh}
>
  {/* Scrollable content */}
</ScreenWrapper>
```

## Haptic Feedback System
**File Location**: `src/shared/utils/haptic-utils.ts`

### Haptic Categories:
- **Interface Actions**: Light haptics for button presses and selections
- **Navigation**: Medium haptics for tab switches and modal presentations
- **Success States**: Success pattern for completed actions
- **Error States**: Error pattern for failed operations
- **Destructive Actions**: Heavy haptics for delete operations

### Platform Integration:
- **User Preference Aware**: Respects system haptic settings and app preferences
- **Platform-Specific**: iOS and Android optimized feedback patterns
- **Graceful Degradation**: Silent fallback when haptics unavailable

## Typography and Text Scaling
**File Locations**:
- Design Tokens: `src/shared/utils/design-tokens.ts`
- Typography Utils: `src/shared/utils/typography-utils.ts`

### Accessibility Compliance:
- **Dynamic Type Support**: Respects user's accessibility text size preferences
- **Semantic Typography**: Uses Paper's variant system for consistent hierarchy
- **Line Height Management**: Proper line spacing for readability across sizes
- **Color Contrast**: Maintains WCAG 2.1 AA compliance across theme modes

### Typography Scale:
- **Material Design 3**: Follows MD3 typography specifications
- **Design Tokens**: Consistent typography values via `useDesignTokens()`
- **Responsive Scaling**: Adapts to different screen sizes and orientations

## Accessibility Guidelines
**File Location**: `src/shared/utils/accessibility-utils.ts`

### Color Contrast Requirements:
- **WCAG 2.1 AA Minimum**: 4.5:1 contrast ratio for normal text
- **Enhanced Contrast**: 7:1 ratio for better accessibility
- **Theme Compliance**: Both light and dark themes meet contrast requirements
- **Status Indicators**: Non-color-dependent status communication

### Semantic Labeling:
- **Accessibility Labels**: All interactive elements have descriptive labels
- **Screen Reader Support**: Proper heading hierarchy and navigation landmarks
- **Role Definitions**: Clear button, link, and input role assignments
- **State Communication**: Loading, error, and success states announced to screen readers

### Focus Management:
- **Keyboard Navigation**: Logical tab order throughout application
- **Focus Indicators**: Visible focus states for keyboard users
- **Modal Focus**: Focus trapping within modal components
- **Return Focus**: Proper focus restoration when modals close

### Touch Target Guidelines:
- **Minimum Size**: 44pt minimum touch target size (iOS/Android standards)
- **Proper Spacing**: Adequate spacing between interactive elements
- **Gesture Accessibility**: Alternative methods for complex gestures

## Animation Principles
**File Locations**:
- Animation Utils: `src/shared/utils/animation-utils.ts`
- Spring Animations: `src/shared/animations/`

### React Native Paper Animations:
- **Built-in Transitions**: Leverage Paper's component animations for consistency
- **Theme Integration**: Animations respect user's reduced motion preferences
- **Performance Consideration**: 60fps target for all interface animations

### Custom Animation Guidelines:
- **React Native Reanimated**: Use Reanimated 3 for complex custom animations
- **Spring Physics**: Natural spring-based animations for organic feel
- **Gesture Integration**: Smooth gesture-driven animations with proper velocity handling
- **Platform Optimization**: Native driver usage for optimal performance

### Performance Considerations:
- **60fps Target**: Maintain smooth animations across all devices
- **Memory Management**: Proper cleanup of animation resources
- **Battery Optimization**: Efficient animation algorithms to preserve battery life
- **Reduced Motion**: Respect accessibility preferences for reduced animations

## Component Integration Patterns

### Form Components:
**File Locations**:
- Controlled Components: `src/shared/components/forms/`
- Form Hooks: `src/shared/hooks/use-form.ts`
- Validation Schemas: `src/features/*/schemas/`

- **Controlled Components**: Consistent form state management patterns
- **Validation Integration**: React Hook Form + Zod schema validation
- **Error Handling**: Consistent error display and accessibility communication
- **Theme Integration**: Form elements follow Material Design 3 specifications

### List Components:
**File Locations**:
- List Components: `src/shared/components/lists/`
- Virtual List Utils: `src/shared/utils/virtual-list-utils.ts`

- **Performance Optimization**: VirtualizedList usage for large datasets
- **Pull-to-Refresh**: Native refresh integration via ScreenWrapper
- **Empty States**: Consistent empty state presentations with proper messaging
- **Loading States**: Skeleton loading patterns for improved perceived performance

### Card Components:
**File Location**: `src/shared/components/cards/`

- **Material Design 3**: Elevation and surface color usage
- **Interactive States**: Proper pressed, focused, and disabled states
- **Content Organization**: Consistent padding, spacing, and typography hierarchy

---

## Quick Reference: Do's & Don'ts

| ✅ DO | ❌ DON'T |
| :--- | :--- |
| **Use `ScreenWrapper` for every screen.** | Don't build screens without ScreenWrapper integration. |
| **Use `Surface` with `mode="flat"` or low `elevation`.** | Don't use `Card` for basic layout and grouping. |
| **Use `List.Section` and `List.Item` for lists and forms.** | Don't build lists from `map()`'d `View`s with manual styles. |
| **Use `TouchableRipple` to make content interactive.** | Don't use the `onPress` prop of a `Card`. |
| **Use theme colors and design tokens for ALL styles.** | Don't use `StyleSheet.create()` or hardcoded values like `'#FFF'` or `16`. |
| **Use `Text` variants (`headlineSmall`, etc.) for hierarchy.** | Don't manually set `fontSize` and `fontWeight`. |
| **Use our custom modal system with `ConfirmationModalContent` and `BottomSheetContent`.** | Don't use React Native Paper `Modal` or `Dialog` components - we have our own system. |
| **Use `Snackbar` for non-blocking feedback (e.g., "Saved").** | Don't use modals for simple success messages. |
| **Ensure all touch targets are a minimum of 44x44px.** | Don't create small, hard-to-press buttons or icons. |
| **Use namespace-based i18n translations.** | Don't hardcode text strings in components. |

Of course. This is an excellent next step. By exploring the "second layer" of components in React Native Paper, you empower your developers to build more complex and feature-rich UIs without having to reinvent the wheel, all while staying perfectly aligned with your Golden Cross principles.

Here is a curated selection of additional components from the React Native Paper examples, framed within the context of the AromaCHAT application. This section can be added directly to your main guide.

---

## Advanced Component Patterns & UI Helpers

This section covers powerful, pre-built components that solve common UI problems, saving development time and ensuring consistency. Use these to enhance data organization, provide richer user interaction, and communicate status effectively.

### Enhancing Data Organization & Hierarchy

| Component(s) | Purpose & Golden Pattern | Real-World Use Cases for AromaCHAT | Developer Reference |
| :--- | :--- | :--- | :--- |
| **`Menu`** | **Contextual Actions.** Provides a pop-up menu of options, typically anchored to an icon button (the "three-dot menu"). This is the ultimate tool for keeping interfaces clean by hiding secondary actions until they are needed. | • On each item in the "Saved Recipes" list, a `Menu` can offer "Edit," "Share," and "Delete." <br/> • In the AppBar, a `Menu` can hold "Settings," "Log Out," etc. | [MenuExample.tsx](template-projects/react-native-paper-demo/src/Examples/MenuExample.tsx) |
| **`Chip`** | **Filtering, Tags, and Status.** Displays information as a compact, "pill-shaped" element. Can be used for toggling filters, displaying tags, or showing a status. Excellent for organizing and interacting with collections of data. | • On a recipe details screen, use `Chip`s to display tags like "Vegan," "High-Protein," or "Under 30 mins." <br/> • On the search screen, use selectable `Chip`s to let users filter by ingredient or category. | [ChipExample.tsx](template-projects/react-native-paper-demo/src/Examples/ChipExample.tsx) |
| **`SegmentedButtons`** | **View Switching.** A row of connected buttons that allows users to switch between different views or contexts of the same data. It's a cleaner, more modern alternative to tabs in many scenarios. | • On the "Saved Recipes" screen, let users switch between a "Grid View" and a "List View." <br/> • On a recipe detail page, switch between "Ingredients" and "Instructions." | [SegmentedButtonsExample.tsx](template-projects/react-native-paper-demo/src/Examples/SegmentedButtonsExample.tsx) |
| **`Badge`** | **Notification Indicators.** A small dot or counter that appears on another element (like an icon or tab) to indicate a status or notification, such as an unread count. | • Place a `Badge` on the "Saved Recipes" tab icon in the bottom navigation to indicate new recipes have been shared with the user. <br/> • A `Badge` on a user's profile icon could indicate a new comment. | [BadgeExample.tsx](template-projects/react-native-paper-demo/src/Examples/BadgeExample.tsx) |

#### Advanced User Interaction & Input

| Component(s) | Purpose & Golden Pattern | Real-World Use Cases for AromaCHAT | Developer Reference |
| :--- | :--- | :--- | :--- |
| **`FAB`** (Floating Action Button) | **The Primary Screen Action.** A circular button that "floats" above the UI, representing the most common or important action on a screen. Use it to provide a clear, high-emphasis call to action. | • On the main homescreen or recipe list, a `FAB` is the perfect component for the "Create New Recipe" action. <br/> • The `AnimatedFAB` variant can extend to show more options. | [FABExample.tsx](template-projects/react-native-paper-demo/src/Examples/FABExample.tsx) <br/> [AnimatedFABExample.tsx](template-projects/react-native-paper-demo/src/Examples/AnimatedFABExample.tsx) |
| **`Checkbox`**, **`Switch`**, **`RadioButton`** | **Selection Controls.** These are essential for forms and settings screens. **Golden Pattern**: Integrate them seamlessly into a `List.Item` by passing them to the `right` prop. This creates perfectly aligned, tappable rows without any custom styling. | • Use `Switch` for on/off settings like "Enable Dark Mode" or "Receive Notifications." <br/> • Use `Checkbox` for multi-select options in the recipe wizard, like "Select dietary restrictions." <br/> • Use `RadioButton.Group` for single-select choices, like "Unit of Measurement (Metric/Imperial)." | [CheckboxExample.tsx](template-projects/react-native-paper-demo/src/Examples/CheckboxExample.tsx) <br/> [SwitchExample.tsx](template-projects/react-native-paper-demo/src/Examples/SwitchExample.tsx) <br/> [RadioButtonExample.tsx](template-projects/react-native-paper-demo/src/Examples/RadioButtonExample.tsx) |
| **`Searchbar`** | **Search and Filtering.** A self-contained, themed search input component with optional clear and back buttons. | • The primary component for a "Search Recipes" screen. <br/> • Can be placed in the `AppBar` for a global search experience. | [SearchbarExample.tsx](template-projects/react-native-paper-demo/src/Examples/SearchbarExample.tsx) |

#### Providing Contextual Information & Status

| Component(s) | Purpose & Golden Pattern | Real-World Use Cases for AromaCHAT | Developer Reference |
| :--- | :--- | :--- | :--- |
| **`ProgressBar`** & **`ActivityIndicator`** | **Progress & Loading States.** `ActivityIndicator` is for indeterminate loading (when you don't know how long it will take). `ProgressBar` is for determinate progress (when you can show completion, like a percentage). | • Use `ActivityIndicator` while fetching recipes from the server. <br/> • Use `ProgressBar` at the top of the multi-step "Create Recipe" wizard to show the user's progress through the flow. | [ProgressBarExample.tsx](template-projects/react-native-paper-demo/src/Examples/ProgressBarExample.tsx) <br/> [ActivityIndicatorExample.tsx](template-projects/react-native-paper-demo/src/Examples/ActivityIndicatorExample.tsx) |
| **`HelperText`** | **Form Field Context & Errors.** Provides crucial, contextual information directly below a `TextInput`. It can display validation errors or simply give hints. This is essential for creating usable and accessible forms. | • Displaying "Password must be at least 8 characters" below the password field. <br/> • Showing a real-time validation error like "Email is invalid" from Zod/React Hook Form. | [HelperTextExample.tsx](template-projects/react-native-paper-demo/src/Examples/HelperTextExample.tsx) |
| **`Tooltip`** | **Explaining UI Elements.** A small pop-up label that explains what a button or element does. Use sparingly for icon-only buttons or less-obvious features to improve usability without cluttering the interface. | • An icon-only `IconButton` in the recipe calculator could have a `Tooltip` that explains, "Calculates oil dilution ratio." | [TooltipExample.tsx](template-projects/react-native-paper-demo/src/Examples/TooltipExample.tsx) |

## Development Workflow Integration

### Component Creation Checklist:
1. **ScreenWrapper Integration**: Ensure proper ScreenWrapper usage
2. **Theme Compliance**: Use theme colors and design tokens exclusively
3. **Internationalization**: Implement proper i18n namespace usage
4. **Accessibility Testing**: Verify contrast, labeling, and focus management
5. **Haptic Integration**: Add appropriate haptic feedback for interactions
6. **Animation Testing**: Ensure smooth 60fps performance across devices

### Quality Assurance Standards:
- **Cross-Platform**: Use Platform if it is needed for differente behavior on iOS and Android
- **Accessibility Audit**: Regular accessibility compliance
- **Performance Monitoring**: Monitor animation performance and memory usage
- **Internationalization Testing**: Verify proper text rendering in all supported languages

## Integration with External Systems

### Analytics Integration:
**File Locations**:
- PostHog Integration: `src/shared/components/posthog-clerk-integration.tsx`
- Analytics Utils: `src/shared/utils/analytics-utils.ts`
- Feature Flags: `src/shared/hooks/use-feature-flags.ts`

- **PostHog Events**: Consistent event naming and property structures
- **User Journey Tracking**: Complete funnel tracking through features
- **Error Monitoring**: Enhanced error boundaries with analytical context
- **Performance Metrics**: Animation performance and user interaction tracking

### Authentication Integration:
**File Locations**:
- Auth Components: `src/features/auth/components/`
- User Preferences: `src/shared/contexts/UserPreferencesContext.tsx`

- **Clerk Integration**: Seamless authentication state management
- **User Preferences**: Persistent theme and preference synchronization
- **Session Management**: Proper session handling and security considerations

---

This guide ensures consistency across all AromaCHAT interfaces while maintaining our commitment to accessibility, performance, and user experience excellence. Every component should follow these established patterns to maintain our application's cohesive design language and functionality standards.
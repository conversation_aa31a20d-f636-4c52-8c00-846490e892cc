# AromaCHAT Design Principles & Strategic Guidelines

## I. Core Design Philosophy & Strategy

**AromaCHAT Design Vision**: Create a premium, mobile-first experience that prioritizes content over containers, accessibility over aesthetics, and user efficiency over developer convenience.

Our goal is to create a premium, modern mobile experience defined by clarity, intentionality, and consistency.

The core philosophy is simple: **content is the interface**. We move away from heavy, container-based layouts ("boxes within boxes") and toward a clean aesthetic where typography, spacing, and subtle surfaces create structure and guide the user. Every decision must serve the content it presents.

## Avoid commom erros
- Always use boxShadow instead of individual shadow* properties in React Native styles.

### Strategic Design Principles

*   [ ] **Users First:** Prioritize user needs, workflows, and ease of use in every design decision. Principle: Simple, Lovable, and Complete (SLC)
*   [ ] **Meticulous Craft:** Aim for precision, polish, and high quality in every UI element and interaction
*   [ ] **Speed & Performance:** Design for fast load times and smooth 60fps animations
*   [ ] **Simplicity & Clarity:** Strive for a clean, uncluttered interface. Ensure labels, instructions, and information are unambiguous
*   [ ] **Focus & Efficiency:** Help users achieve their goals quickly and with minimal friction. Minimize unnecessary steps or distractions
*   [ ] **Consistency:** Maintain a uniform design language (colors, typography, components, patterns) across the entire application
*   [ ] **Accessibility:** Design for inclusivity. Ensure sufficient color contrast, proper touch targets, and screen reader compatibility
*   [ ] **Touch-First Design:** Prioritize thumb-friendly layouts and touch interactions

## II. Implementation Architecture Overview

### Design System Foundation
*   [ ] **Material Design 3 Base:** Built on Google's Material Design 3 specification
*   [ ] **React Native Paper:** Primary component library for consistent Material Design implementation
*   [ ] **Custom Extensions:** AromaCHAT-specific patterns and enhanced components where needed

### Key Architectural Decisions
*   [ ] **Content-First Philosophy:** Deprecate heavy container-based layouts in favor of content-driven interfaces
*   [ ] **Theme as Single Source of Truth:** All styling derived from centralized theme system
*   [ ] **Component Composition:** Prefer composition over abstraction for maintainability
*   [ ] **Mobile-Only Focus:** Optimize exclusively for mobile touch interactions

## III. Implementation Standards Reference

**📋 Complete Implementation Guide:** All technical implementation details, component usage patterns, file locations, and code examples are documented in:

**➡️ [`context/aromachat_ux_guide.md`](./aromachat_ux_guide.md)**

### What You'll Find in the UX Guide:
*   [ ] **The 5 Core Principles (Golden Cross):** Detailed implementation of our design philosophy
*   [ ] **ScreenWrapper Requirements:** Mandatory usage patterns for all screens
*   [ ] **Theme System:** Complete theme access patterns and file locations
*   [ ] **Modal System:** Custom Instagram-style modal implementation
*   [ ] **Component Toolkit:** Approved React Native Paper components with usage patterns
*   [ ] **Navigation Patterns:** Expo Router integration and navigation architecture
*   [ ] **Accessibility Guidelines:** WCAG compliance and mobile accessibility standards
*   [ ] **Animation Principles:** 60fps performance standards and animation patterns
*   [ ] **File Location References:** Specific paths to all implementation files

## IV. Quality Assurance Standards

### Development Workflow Requirements
*   [ ] **ScreenWrapper Mandatory:** Every screen MUST use ScreenWrapper - no exceptions
*   [ ] **Theme Compliance:** Never hardcode colors, spacing, or typography values
*   [ ] **Accessibility Testing:** All interactive elements must meet 44px minimum touch targets
*   [ ] **Performance Standards:** 60fps animations, proper memory management, battery optimization

### Code Review Checkpoints
*   [ ] **Theme Usage:** Verify `usePaperTheme()` and `useDesignTokens()` usage
*   [ ] **Component Selection:** Ensure approved component toolkit usage
*   [ ] **File Locations:** Follow established file organization patterns
*   [ ] **Modal Implementation:** Use custom modal system, never React Native Paper modals
*   [ ] **Internationalization:** Implement namespace-based i18n translations

## V. Strategic Guidelines for Decision Making

### When to Use This Document
*   [ ] **High-Level Design Decisions:** Product strategy and design philosophy alignment
*   [ ] **Architecture Planning:** Understanding overall system design approach
*   [ ] **Quality Standards:** Ensuring consistency across development team
*   [ ] **Decision Framework:** Evaluating design trade-offs against core principles

### When to Use the UX Guide
*   [ ] **Component Implementation:** Specific component usage and patterns
*   [ ] **Technical Integration:** File locations, import patterns, and code examples
*   [ ] **Development Workflow:** Day-to-day development standards and practices
*   [ ] **Troubleshooting:** Finding specific implementation details and file references

## VI. Success Metrics & Goals

### User Experience Targets
*   [ ] **Performance:** Sub-100ms interaction response times
*   [ ] **Accessibility:** WCAG 2.1 AA compliance across all interfaces
*   [ ] **Consistency:** 100% ScreenWrapper adoption across all screens
*   [ ] **Mobile Optimization:** Thumb-friendly interactions and optimal touch targets

### Development Efficiency Goals
*   [ ] **Component Reuse:** Maximize React Native Paper component usage
*   [ ] **Theme Consistency:** Zero hardcoded style values in production code
*   [ ] **Documentation Compliance:** All implementations follow established patterns
*   [ ] **Quality Assurance:** Systematic design review process for all UI changes

---

**📚 For all implementation details, component usage, and technical specifications:**  
**See: [`context/aromachat_ux_guide.md`](./aromachat_ux_guide.md)**
# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Quick Reference

AromaCHAT Go is a premium AI-powered mobile aromatherapy app built with React Native/Expo. For comprehensive details, see the documentation files referenced below.

## Development Commands

```bash
npm run dev          # Start development server  (never run otherwise specifically asked for)
npm start           # Start with tunnel for device testing (never run otherwise specifically asked for)
npm run android     # Run on Android (never run otherwise specifically asked for)
npm run ios         # Run on iOS (never run otherwise specifically asked for)
```

## Architecture & Documentation Map

### Core Architecture
- **Complete Tech Stack**: See `docs/architecture/tech-stack.md`
- **Coding Standards**: See `docs/architecture/coding-standards.md`  
- **Project Structure**: See `docs/architecture/source-tree.md`

### Project Requirements
- **Product Requirements**: See `docs/prd.md`
- **UX Guidelines**: See `src/context/aromachat_ux_guide.md`
- **Design Principles**: See `src/context/design-principles.md`

### Key Technology Decisions
- **Framework**: React Native 0.79.5 + Expo 53.0.0 managed workflow
- **UI**: React Native Paper 5.14.5 (Material Design 3)
- **Navigation**: Expo Router 5.0.2 with typed routes
- **State**: Zustand 5.0.7 for features, React Context for global
- **Auth**: Clerk 2.14.14
- **i18n**: i18next 25.3.2 (namespace-based: en/pt)

## Critical Architecture Rules

### Theme System (MANDATORY)
- **Rule**: NEVER use hardcoded styling values
- **Import**: Always use `import { useTheme } from '@/shared/hooks';` (not direct path)
- **Components**: Theme all React Native Paper components properly:
  ```tsx
  // Menu theming example
  <Menu contentStyle={{ backgroundColor: theme.colors.surface }}>
    <Menu.Item
      titleStyle={{ color: theme.colors.onSurface }}
      leadingIconColor={theme.colors.onSurfaceVariant}
    />
  </Menu>
  ```
- **[DEPRECATED]** `useDesignTokens()` is deprecated. Use `useTheme()` instead.

### ScreenWrapper Pattern (MANDATORY)
- **Rule**: Every screen MUST use ScreenWrapper
- **Location**: `src/shared/components/layout/screen-wrapper.tsx`
- **Usage**: Provides consistent layout, modals, navigation

### Route vs Component Layout (MANDATORY)
- **Route Files**: Handle ONLY routing + ScreenWrapper configuration
- **Component Files**: Handle their own layout (flex, centering, padding)
- **Rule**: NO layout Views in route files - components are self-contained
- **Example**:
  ```jsx
  // ❌ Wrong - Layout in route file
  <ScreenWrapper>
    <View style={{flex: 1, justifyContent: 'center'}}>
      <MyForm />
    </View>
  </ScreenWrapper>

  // ✅ Correct - Layout in component
  <ScreenWrapper>
    <MyForm /> // MyForm handles its own layout internally
  </ScreenWrapper>
  ```

### Modal System (MANDATORY)
- **Rule**: Use custom Instagram-style modals via `useModal()` hook
- **Location**: `src/shared/hooks/use-modal.ts`
- **Forbidden**: React Native Paper Modal components

## Modal Architecture Map

### Create Recipe Feature Modals
- **Final Recipe Details Modal**: `FinalRecipeDetailsModal` in `/features/create-recipe/components/modals/final-recipe-details-modal/`
  - **Triggered by**: Protocol summary cards in final-recipes screen
  - **Purpose**: Display full recipe details with time slot navigation and oil substitution
  - **Type**: Feature-specific, not reusable
  - **Components**: Main modal + header + content components
  - **renderBackdrop**: ✅ Implemented with proper opacity and close behavior
  - **dynamicSize**: ✅ Disabled (`enableDynamicSizing={false}`)

- **Oil Substitution Modal**: `OilSubstitutionBottomSheet` in `/features/create-recipe/components/modals/`  
  - **Triggered by**: Substitute button in recipe details
  - **Purpose**: Alternative oil selection with relevance scoring
  - **Type**: Feature-specific, not reusable
  - **renderBackdrop**: ✅ Implemented with proper opacity and close behavior
  - **dynamicSize**: ✅ Disabled (`enableDynamicSizing={false}`)

### Modal Discovery Guidelines
- **Feature-specific modals**: Located in `/features/{feature-name}/components/modals/`
- **Shared modals**: Located in `/shared/components/modals/`
- **Naming convention**: Component name should clearly indicate purpose (avoid generic names like "RecipeModal")
- **Import pattern**: Use barrel exports from modal directories

### Component Patterns
- **Interactive Elements**: Wrap with TouchableRipple (44px min touch target)
- **Lists**: Use List.Section + List.Item instead of Cards for data
- **Styling**: Surface + proper theming instead of Card overuse

## Project Structure Overview

```
src/
├── app/                    # Expo Router screens (auth, tabs)
├── features/               # Feature modules
│   ├── create-recipe/      # Main wizard flow with Zustand store
│   ├── oil-calculator/     # Dilution calculator utility  
│   └── auth/              # Authentication flows
├── shared/                # Reusable components, hooks, services
│   ├── components/layout/  # ScreenWrapper, AppBar, etc.
│   ├── providers/         # HapticsProvider, ModalProvider
│   ├── contexts/          # UserPreferencesProvider  
│   ├── hooks/             # use-theme, use-modal, use-ai-streaming
│   ├── services/          # API client, SSE streaming
│   └── locales/           # i18n files (en/, pt/)
└── context/               # Documentation (design principles, UX guide)
```

## Feature-Specific Notes

### Create Recipe Wizard
- **State Management**: Zustand store in `src/features/create-recipe/store/`
- **AI Streaming**: Real-time SSE responses via `src/shared/services/`
- **Screens**: 6-step wizard (health-concern → demographics → causes → symptoms → properties → final-recipes)

### Development Guidelines

**Before Making Changes:**
1. Read existing patterns in the feature you're modifying
2. Check coding standards doc for mandatory patterns
3. Verify theme system usage (no hardcoded values)
4. Test on both iOS/Android simulators

**Performance Requirements:**
- 60fps animations (use React Native Reanimated)
- <100ms interaction response times
- WCAG 2.1 AA accessibility compliance

**For Detailed Implementation:**
- **Comprehensive coding standards**: `docs/architecture/coding-standards.md`
- **Complete tech stack details**: `docs/architecture/tech-stack.md`
- **UX implementation guide**: `src/context/aromachat_ux_guide.md`
- "useDesignTokens is deprecated and should use useTheme() instead."
- "All console statements protected with DEV checks"
- "Always verify Screen reader accessibility support"
- "ScreenWrapper usage is at the route files, not on components files."
- "Style property 'shadowOffset' is not supported by native animated module"
- "# UX/UI Design Style Guide

## Core Philosophy
**Content is structure** - let information architecture define the visual hierarchy, not decorative containers.

## Key Principles

### ❌ Avoid at All Costs
- **Cards and card-like containers** - no elevated surfaces, rounded boxes, or background wrappers
- **Card mimics** - anything that creates artificial "floating" or contained appearances
- **Excessive decoration** - shadows, multiple layers, ornamental elements

### ✅ Embrace Instead
- **Typography as primary structure** - use font weight, size, and color to create hierarchy
- **Strategic spacing** - consistent spacing patterns to group and separate content
- **Minimal accent elements** - single lines, borders, or color blocks for emphasis
- **Content-driven layout** - arrange information based on importance and flow
- **Clean color usage** - primary colors for emphasis, muted tones for secondary info

## Implementation Guidelines
- Structure emerges from content arrangement and typography
- Use borders/lines for separation, not containers
- Bold typography and color contrast for visual hierarchy
- Consistent spacing system for rhythm and organization
- Let white space and content flow define the interface

## Result
Clean, minimal interfaces where content and information architecture drive the design, not decorative UI elements."
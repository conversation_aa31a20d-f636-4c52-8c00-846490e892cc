// tsconfig.json - VERSÃO CORRIGIDA
{
  "extends": "expo/tsconfig.base",
  "compilerOptions": {
    "strict": true,
    // ADICIONE ESTA LINHA:
    // Isso diz ao TypeScript para tratar o JSX de uma forma que o React Native entende.
    // É a solução para o erro "Cannot use JSX unless the '--jsx' flag is provided".
    "jsx": "react-native",

    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  },
  "include": [
    "**/*.ts",
    "**/*.tsx",
    ".expo/types/**/*.ts",
    "expo-env.d.ts",
  ]
}